$replaceArray = @(
	'return getPrefs("sys_sign") || "zx";',
	'myjs.iszx = true;',
	'https://productpc.cszysoft.com:8081/lzt/'
)

function Replace-StringInFiles {
	[CmdletBinding()]
	param(
		[Parameter(Mandatory = $true)]
		[string]$Path,
		[Parameter(Mandatory = $true)]
		[string[]]$Search,
		[Parameter(Mandatory = $true)]
		[string[]]$Replace
	)
	$files = Get-ChildItem -Path $Path -Filter *.js -Recurse
	Write-Host "start task length:$($files.Count)"
	foreach ($file in $files) {
		Write-Host "task ing: $($file.FullName)"
		# 使用 -join "`n" 来拼接多行内容
		$content = (Get-Content -Path $file.FullName -Encoding UTF8) -join "`n"

		for ($i = 0; $i -lt $Search.Length; $i++) {
			$content = $content -replace $Search[$i], $Replace[$i]
		}
		$bytes = [System.Text.Encoding]::UTF8.GetBytes($content)
		[System.IO.File]::WriteAllBytes($file.FullName, $bytes)
	}
}

$searchArray = @(
	'return getPrefs\("sys_sign"\) \|\| "(.*?)";',
	'myjs.iszx = (.*?);',
	'https://productpc.cszysoft.com:(.*?)/lzt/'
)
$startTime = Get-Date
Replace-StringInFiles -Path "pages" -Search $searchArray -Replace $replaceArray
Replace-StringInFiles -Path "html/script" -Search $searchArray -Replace $replaceArray
$endTime = Get-Date
$timeTaken = New-TimeSpan -Start $startTime -End $endTime
Write-Host ""
Write-Host "start task ok! Press any key to exit, time consuming:$($timeTaken.TotalSeconds)s"
$null = $Host.UI.RawUI.ReadKey('NoEcho,IncludeKeyDown')