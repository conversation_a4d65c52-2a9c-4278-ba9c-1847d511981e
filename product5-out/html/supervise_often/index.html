<!DOCTYPE html>
<html>
<script>
//字体适配 ===========================================================
(function (doc, win) {var docEl = doc.documentElement,resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize',recalc = function () {var clientWidth = docEl.clientWidth;if(clientWidth > 480){clientWidth = 480;}if (!clientWidth) return;docEl.style.fontSize = 100 * (clientWidth / 360) + 'px';};if (!doc.addEventListener) return;win.addEventListener(resizeEvt, recalc, false);doc.addEventListener('DOMContentLoaded', recalc, false);})(document, window);

//动态加载文件
var dynamicLoading = {css : function(path) {if (!path || path.length === 0) {throw new Error('argument "path" is required !');}var head = document.getElementsByTagName('head')[0];var link = document.createElement('link');link.href = path;link.rel = 'stylesheet';link.type = 'text/css';head.appendChild(link);},js : function(path) {if (!path || path.length === 0) {throw new Error('argument "path" is required !');}var head = document.getElementsByTagName('head')[0];var script = document.createElement('script');script.src = path;script.type = 'text/javascript';script.async = false;head.appendChild(script);}}

//接收页面参数
var datas={};var param=location.href.split('?');if(param.length>1){param=decodeURIComponent(param[1]);var params=param.split("&");if(params){for(var i=0;i<params.length;i++){if(params[i]){var data_key=params[i].substring(0,params[i].indexOf("="));var data_value=params[i].substring(params[i].indexOf("=")+1);if(data_value.indexOf("{")==0||data_value.indexOf("[")==0){data_value=JSON.parse(data_value)}datas[data_key]=data_value}}}};
var app_version = datas.only || Math.floor(Math.random()*(100000-10000+1)+10000);

//外部文件 动态加载		css	js
dynamicLoading.css('../css/general.css?app_version='+app_version+'');
dynamicLoading.css('https://cdn.staticfile.org/vant/2.12.5/index.min.css?app_version='+app_version+'');

dynamicLoading.js('https://cdn.staticfile.org/jquery/3.6.0/jquery.min.js?app_version='+app_version+'');
dynamicLoading.js('https://cdn.staticfile.org/vue/2.6.14/vue.min.js?app_version='+app_version+'');
dynamicLoading.js('https://cdn.staticfile.org/vant/2.12.5/vant.min.js?app_version='+app_version+'');
dynamicLoading.js('https://cdn.staticfile.org/dayjs/1.11.0/dayjs.min.js?app_version='+app_version+'');
dynamicLoading.js('../script/api.js?app_version='+app_version+'');	
dynamicLoading.js('../script/myjs.js?app_version='+app_version+'');	
dynamicLoading.js('../script/t.js?app_version='+app_version+'');	
dynamicLoading.js('../script/encryption.js?app_version='+app_version+'');	
dynamicLoading.js('../script/general.js?app_version='+app_version+'');
//在apicloud中打开	就再加载一次配置
apiready = function(){
	window.appLoading = true;
	initApp();
}
function initApp(){
	if(window.appLoading && window.webLoad){
		setTimeout(function(){
			try{vm.initApp();}catch(e){console.error(e.message)}
		},0);
	}
}
</script>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0,viewport-fit=cover"/>
	<meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
	<title></title>
	<style>
		.none{display:none;}
		.main_bg{ position: relative; height: 2.02rem; width: 100%; }
		.main_bg_img{ width: 100%; height: 100%; object-fit: cover; }
		.main_year_box{ padding: 0.04rem 0.08rem; background: rgba(0, 0, 0, 0.15); border-radius: 0.02rem; font-weight: 600; color: #FFFFFF; position:absolute; right: 0.15rem; top:0.09rem; }
		.main_bg_line{position: relative; height: 0.11rem; width: 100%; background: #FFF; margin-top: -0.11rem; border-top-left-radius: 0.1rem; border-top-right-radius: 0.1rem;}
		.main_header_box{background-color: #FFF;}
		.main_btn_item{ width:100%; padding: 0.16rem 0.14rem; background: #FFFFFF; box-shadow: 0px 1px 6px 1px rgba(24,64,118,0.12); border-radius: 0.04rem; }
		.main_btn_item +.main_btn_item{margin-left: 0.11rem;}
		.main_btn_item_img{ width: 0.34rem; height: 0.34rem; object-fit: cover; }
		.main_btn_item_text{ margin-left:0.12rem; font-weight: 600; color: #333333; }
		.main_btn_statistics{ padding: 0 0.15rem;width: 100%;height: 0.58rem; border-radius: 0.08rem; overflow: hidden; position: relative; margin-bottom: 0.25rem; }
		.main_btn_statistics_text{ font-weight: 600; line-height: 0px; text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.15); position:absolute; left:0.36rem; top:0.26rem; }
		.main_text{ padding: 0.04rem 0.15rem; font-weight: 600; color: #333333; }
		.childrenItem_item{line-height: 1;padding: 0.07rem 0.14rem;border-radius: 0.02rem; background: #F8F8F8; color: #666666;}
		.main_list_item{ border-top:0.1rem solid #F8F8F8; padding: 0.1rem 0.15rem; background-color: #FFF; }
		#app .van-tabs__nav--line{padding-bottom: 0px;}
	</style>
</head>
<body>
	<div id="app" class="none">
		<div class="main_bg">
			<img class="main_bg_img" src="../images/supervise_often_main_bg.png" alt="" />
			<div class="main_year_box flex_box flex_align_center">
				<van-popover get-container="#app" v-model="years.showPopover" trigger="click" placement="bottom-end">
					<div v-for="(item,index) in years.data" @click="onPopMoreSelect(item)" role="menuitem" class="van-popover__action">
						<div class="van-popover__action-text">{{item}}年度</div>
					</div>
					<template #reference>
						<div style="position: relative;" class="flex_box flex_align_center">
							<div :style="loadConfiguration(-3)+'margin-right:0.03rem;'" >{{years.value}}年度</div>
							<van-icon :color="'#FFF'" :size="((appFontSize-4)*0.01)+'rem'" :name="'arrow-down'"></van-icon>
						</div>
					</template>
				</van-popover>
				
			</div>
		</div>
		<div class="main_bg_line"></div>
		<div class="main_header_box">
			<div @click="openStatistics()" class="main_btn_statistics" style="margin-bottom: 0.1rem;">
				<img class="main_bg_img" src="../images/supervise_main_statistics.png" alt="" />
				<div class="main_btn_statistics_text" :style="loadConfiguration(-1)+'color:'+appTheme" >常委会监督议题统计分析</div>
			</div>
			<div @click="openOKFile()" class="main_btn_statistics">
				<img class="main_bg_img" src="../images/supervise_often_main_report.png" alt="" />
				<div class="main_btn_statistics_text" :style="loadConfiguration(-1)+'color:'+appTheme" >审议报告</div>
			</div>

			<div class="main_text" :style="loadConfiguration()">{{years.value}}年 {{areaName}}人大常委会监督议题</div>

			<div v-if="tabs.data.length > 1" class="" :class="tabs.data.length > 3?'':'flex_box'" style="background: #FFF;">
				<van-tabs v-model="tabs.value" @click="tabClick(1)" :ellipsis="false" line-width="0" :lazy-render="false">
					<van-tab v-for="(item,index) in tabs.data" :name="item.value">
						<div slot="title" class="childrenItem_item" :style="loadConfiguration(-1)+(tabs.value==item.value?('color:#FFF;background:'+appTheme):'')">{{item.label}}</div>
					</van-tab>
				</van-tabs>
			</div>
			<!--搜索-->
			<div id="search" class="search_box" :style="loadConfiguration()">
				<div class="search_warp flex_box" >
					<div @click="btnSearch();" class="search_btn flex_box flex_align_center flex_justify_content"><van-icon :size="((appFontSize)*0.01)+'rem'" :color="'#666'" :name="'search'"></van-icon></div>
					<form class="flex_placeholder flex_box flex_align_center search_input" action="javascript:return true;"> <input id="searchInput" class="flex_placeholder" :style="loadConfiguration(-1)" :placeholder="seachPlaceholder" maxlength="100" type="search" ref="btnSearch" @keyup.enter="btnSearch()" v-model="seachText" /> <div v-if="seachText" @click="seachText='';btnSearch();" class="search_btn flex_box flex_align_center flex_justify_content"><van-icon :size="((appFontSize)*0.01)+'rem'" :color="'#ccc'" :name="'clear'"></van-icon></div> </form> 
				</div>
			</div>
		</div>
		<div v-if="listData.length" class="main_list_box">
			<div v-for="(item,index) in listData" @click="openDetails(item)" class="main_list_item">
				<div v-if="item.typeName" :style="loadConfiguration(-3)"><van-tag :color="appTheme" plain type="primary" style="padding:0.01rem 0.11rem;">{{item.typeName}}</van-tag></div>
				<div class="text_two" :style="loadConfiguration(-1)+'color:#333;font-weight: 600;margin-top:0.08rem;'">{{item.title}}</div>
				<template v-if="item.dynamicFiles && item.dynamicFiles.length">
					<div v-for="(nItem) in item.dynamicFiles" class="main_list_file_item flex_box flex_align_center" style="margin-top:0.1rem;">
						<div style="width: 0.07rem; height: 0.07rem; opacity: 1; border: 1px solid #C61414;border-radius:50%;margin-right:0.04rem;"></div>
						<div :style="loadConfiguration(-3)+'color:#666;'" class="flex_placeholder text_one2">{{nItem}}</div>
					</div>
				</template>
			</div>
		</div>
		<!--加载中提示 首次为骨架屏-->
		<div v-if="showSkeleton" class="notText">
			<van-skeleton v-for="(item,index) in 3" title :row="3"></van-skeleton>
		</div>
		<template v-else-if="!listData.length">
			<van-empty :style="loadConfiguration(-2)" :image="pageNot.url || pageNot.data[pageNot.type].url">
				<template #description>
					<div class="van-empty__description_text" :style="loadConfiguration(-1)" v-html="pageNot.text || pageNot.data[pageNot.type].text"></div>
					<div class="van-empty__description_summary" :style="loadConfiguration(-3)" v-html="pageNot.summary || pageNot.data[pageNot.type].summary"></div>
				</template>
				<div v-if="pageNot.hasBtn" :style="loadConfiguration(-1)">
					<van-button v-if="(pageNot.type==2||pageNot.type==3)&&pageType=='page'" @click.stop="T.closeWin()" round type="info" size="large" :color="appTheme">{{'返回'}}</van-button>
					<van-button v-else-if="pageNot.type==1||pageNot.type==4" @click.stop="getData();" round type="info" size="large" :color="appTheme">{{'刷新'}}</van-button>
				</div>
			</van-empty>
		</template>
		<div v-else class="notText" :style="loadConfiguration(-4)" v-html="pageNot.text" @click="loadMore()"></div>
	</div>
</body>
<script type="text/javascript">
	var vmData = {
		years:{showPopover:false,value:"",data:[]},//当前筛选年份
		areaName:"",//当前地区名字
		tabs:{value:"",data:[
		]},
		listData:[]
	};
	var minYear = 2018,maxYear = new Date().getFullYear();
	for(var i = maxYear; i >= minYear; i--){
		vmData.years.data.push(i+"");
	}
	vmData.years.value = vmData.years.data[0];
	var vmWatch={
		"years.value":function(_val){
			window.sessionStorage.setItem(this.pathname + "_years2_value",_val);
		},
		"tabs.value":function(_val){
			window.sessionStorage.setItem(this.pathname + "_tabs2_value",_val);
		},
	};
	var vmMethods = {
		init:function(){
			var that = this;
			that.years.value = window.sessionStorage.getItem(that.pathname + "_years2_value") || that.years.data[0];
			that.tabs.value = window.sessionStorage.getItem(that.pathname + "_tabs2_value") || "";
			//刷新返回当前对象
			document.title = "常委会监督";
			that.getInit();
			that.getData();
		},
		//获取初始化数据
		getInit:function(){
			var that = this;
			// T.ajax({u:zyUrl.getAppUrl() + 'readonfig/nologin?codes=areaName'}, "", function(ret, err) {
			// 	if(ret && ret.errcode == 200){
			// 		var data = ret.data || {};
			// 		that.areaName = data.areaName || "";
			// 	}
			// }, "");
			T.ajax({ u: zyUrl.getAppUrl() + "dictionary/pubkvs?" }, 'getTabs', function (ret, err) {
				var code = ret ? ret.errcode : "";
				var data = ret ? ret.data || {} : {};
				var discuss_jd_type = data.discuss_jd_type || [];
				that.tabs.data = [{ value: "", label: "所有" }];
				if (discuss_jd_type.length != 0) {
					discuss_jd_type.forEach(function (_eItem, _eIndex, _eArr) {
						that.tabs.data.push({ value: _eItem.id, label: _eItem.value });
					});
				}
			}, '分类', "post", {"types": "discuss_jd_type"});
		},
		//打开pop框 顶部更多回调
		onPopMoreSelect: function (_action) {
			var that = this;
			that.years.showPopover = false;
			that.years.value = _action;
			T.showProgress();
			that.getData();
		},
		//栏目切换
		tabClick:function(){
			var that = this;
			T.showProgress();
			that.getData();
		},
		getData:function(_type){
			var that = this;
			if(!_type){
				that.pageNo = 1;
			}
			var url = zyUrl.getAppUrl() + "superviseDiscussComposite/list?";
			var postParam = {
				"pageNo":that.pageNo,
				"pageSize": !_type ? that.refreshPageSize > that.pageSize ? that.refreshPageSize : that.pageSize : that.pageSize,
				"keyword":that.seachText,
				"belongYear":that.years.value,
				"discussType":that.tabs.value,
			};
			T.ajax({ u: url }, 'superviseDiscussComposite/list', function (ret, err) {
				T.refreshHeaderLoadDone();
				T.hideProgress();
				that.showSkeleton = false;
				var code = ret ? ret.errcode : "";
				var data = ret?ret.data||[]:[];
				var dataLength = data ? data.length : 0;
				that.pageNot.type = ret ? (code == 200 ? 0 : 1) : 1;//类型 列表中只有有网和无网的情况
				that.pageNot.text = ret && code != 200 ? ret.errmsg || ret.data : "";//只有接口报的异常才改文字
				if (!_type) {//有时候 会出现加载2次网络的情况(缓存1次 网络1次) 导致页数不正确 这里再重置为1
					that.pageNo = 1;
					that.listData = [];
					if(!window.noScroll){
						window.noScroll = true;
						scrollToView();
					}
				}
				if (T.isArray(data) && data.length != 0) {
					data.forEach(function (_eItem, _eIndex, _eArr) {//item index 原数组对象
						_eItem.typeName = _eItem.discussTypeName || "";
						_eItem.title = _eItem.discussName || "";
						_eItem.id = _eItem.discussId || "";
						that.listData.push(_eItem);
					});
					that.pageNo++;
					that.pageNot.text = that.listData.length == 0 ? "" : data.length >= postParam.pageSize ? T.LOAD_MORE : T.LOAD_ALL;//当前返回的数量 等于 请求的数量 说明可能还有 少于说明没有了
					that.cleanIosDelay();
					that.refreshPageSize = Math.ceil(that.listData.length / that.pageSize) * that.pageSize, that.pageNo = Math.ceil(that.listData.length / that.pageSize) + 1;
				} else if (_type == 1) {//加载更多的时候 底部显示文字
					that.pageNot.text = ret ? code == 200 ? T.LOAD_ALL : ret.errmsg : T.NET_ERR;
				}
			}, '列表', "get", postParam);
		},
		//点击加载更多 或 上拉
		loadMore: function () {
			var that = this;
			if ((that.pageNot.text == T.LOAD_MORE || that.pageNot.text == T.NET_ERR) && that.pageNo != 1) {
				that.pageNot.text = T.LOAD_ING;
				that.getData(that.listData.length != 0 ? 1 : 0);//列表没数据时 算下拉 有数据上拉
			}
		},
		//打开征集详情
		openDetails:function(_item){
			var that = this;
			window.sessionStorage.removeItem("details_tabs2_value");
			T.openWin('',"widget://html/supervise_often/details.html",{recordId:_item.id});
		},
		//打开统计分析
		openStatistics:function(){
			var that = this;
			T.openWin('',"widget://html/supervise_often/statistics.html",{year:that.years.value});
		},
		//打开项目确定文件
		openOKFile:function(){
			var that = this;
			T.openWin('',"widget://html/supervise_often/list_ok.html",{year:that.years.value});
		},

	};
</script>
</html>