<!DOCTYPE html>
<html>
<script>
//字体适配 ===========================================================
(function (doc, win) {var docEl = doc.documentElement,resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize',recalc = function () {var clientWidth = docEl.clientWidth;if(clientWidth > 480){clientWidth = 480;}if (!clientWidth) return;docEl.style.fontSize = 100 * (clientWidth / 360) + 'px';};if (!doc.addEventListener) return;win.addEventListener(resizeEvt, recalc, false);doc.addEventListener('DOMContentLoaded', recalc, false);})(document, window);

//动态加载文件
var dynamicLoading = {css : function(path) {if (!path || path.length === 0) {throw new Error('argument "path" is required !');}var head = document.getElementsByTagName('head')[0];var link = document.createElement('link');link.href = path;link.rel = 'stylesheet';link.type = 'text/css';head.appendChild(link);},js : function(path) {if (!path || path.length === 0) {throw new Error('argument "path" is required !');}var head = document.getElementsByTagName('head')[0];var script = document.createElement('script');script.src = path;script.type = 'text/javascript';script.async = false;head.appendChild(script);}}

//接收页面参数
var datas={};var param=location.href.split('?');if(param.length>1){param=decodeURIComponent(param[1]);var params=param.split("&");if(params){for(var i=0;i<params.length;i++){if(params[i]){var data_key=params[i].substring(0,params[i].indexOf("="));var data_value=params[i].substring(params[i].indexOf("=")+1);if(data_value.indexOf("{")==0||data_value.indexOf("[")==0){data_value=JSON.parse(data_value)}datas[data_key]=data_value}}}};
var app_version = datas.only || Math.floor(Math.random()*(100000-10000+1)+10000);

//外部文件 动态加载		css	js
dynamicLoading.css('../css/general.css?app_version='+app_version+'');
dynamicLoading.css('https://cdn.staticfile.org/vant/2.12.5/index.min.css?app_version='+app_version+'');

dynamicLoading.js('https://cdn.staticfile.org/jquery/3.6.0/jquery.min.js?app_version='+app_version+'');
dynamicLoading.js('https://cdn.staticfile.org/vue/2.6.14/vue.min.js?app_version='+app_version+'');
dynamicLoading.js('https://cdn.staticfile.org/vant/2.12.5/vant.min.js?app_version='+app_version+'');
dynamicLoading.js('https://cdn.staticfile.org/dayjs/1.11.0/dayjs.min.js?app_version='+app_version+'');
dynamicLoading.js('../script/api.js?app_version='+app_version+'');	
dynamicLoading.js('../script/myjs.js?app_version='+app_version+'');	
dynamicLoading.js('../script/t.js?app_version='+app_version+'');	
dynamicLoading.js('../script/encryption.js?app_version='+app_version+'');	
dynamicLoading.js('../script/general.js?app_version='+app_version+'');
//在apicloud中打开	就再加载一次配置
apiready = function(){
	window.appLoading = true;
	initApp();
}
function initApp(){
	if(window.appLoading && window.webLoad){
		setTimeout(function(){
			try{vm.initApp();}catch(e){console.error(e.message)}
		},0);
	}
}
</script>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0,viewport-fit=cover"/>
	<meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
	<title></title>
	<style>
		.none{display:none;}
		.details_main_box{ padding: 0.1rem 0.15rem; background: #FFF; }
		.details_title{ font-weight: 600; color: #333333; }
		.details_item_hint_box{ margin-top: 0.19rem; }
		.details_item_hint_text{ font-weight: 600; color: #333333; margin-left: 0.05rem; }
		.details_content{ font-weight: 400; color: #333333; margin-top: 0.1rem; line-height: 1.45; }
		.details_content *{font-size: inherit;font-family: inherit;}
		.details_file_box{ margin-top: 0.1rem; }
		.details_file_item{ color: #144FC6; }
		.details_file_item +.details_file_item{ margin-top: 0.06rem; }
		.details_group_title_max{ color: #333;font-weight: 600;margin-top:0.1rem; }
		.details_group_title_box{ margin-top: 0.06rem; }
		#app .van-tabs__nav--line{ padding-bottom: 0.03rem; }
		#app .van-tab--active{ font-weight: bold; }
		#app .van-tabs__line{ bottom: 0; }

		.details_list_line_box{ width: 0.38rem; position: relative; }
		.details_list_circle{ width: 0.13rem; height: 0.13rem; background: #FFFFFF; opacity: 1; border: 0.04rem solid #C61414; border-radius: 50%; position:absolute; top: 0.12rem; left: 0.15rem; }
		.details_list_line_top{ position:absolute; width: 0.01rem; top: 0; left: 0.21rem; height: 0.15rem; background: #EEEEEE; }
		.details_list_line_bottom{ position:absolute; width: 0.01rem; top: 0.15rem; bottom: 0; left: 0.21rem; background: #EEEEEE; }
		.details_list_item{ margin: 0 0.15rem 0.1rem 0; padding: 0.1rem; background: #FFFFFF; box-shadow: 0px 2px 10px 1px rgba(24,64,118,0.08); border-radius: 0.04rem; }
		#app .van-tag--plain{ background-color:rgba(0,0,0,0); }
		#app .van-tag--plain::before{ border: 0px; }
		
	</style>
</head>
<body>
	<div id="app" class="none">
		<div v-if="showSkeleton" class="notText">
			<van-skeleton v-for="(item,index) in 3" title :row="3"></van-skeleton>
		</div>
		<template v-else-if="title">
			<div class="details_main_box">
				<div class="details_title" :style="loadConfiguration(2)">{{title}}</div>
				<template v-if="discussHeadDept">
					<div class="details_item_hint_box flex_box flex_align_center">
						<div :style="loadConfigurationSize(-2,'h')+'width:0.03rem;border-radius:0.1rem;background:'+appTheme"></div>
						<div class="details_item_hint_text flex_placeholder" :style="loadConfiguration(-1)">牵头部门</div>
					</div>
					<div class="details_content" :style="loadConfiguration(1)" v-html="discussHeadDept"></div>
				</template>
				<template v-if="discussComplateDate">
					<div class="details_item_hint_box flex_box flex_align_center">
						<div :style="loadConfigurationSize(-2,'h')+'width:0.03rem;border-radius:0.1rem;background:'+appTheme"></div>
						<div class="details_item_hint_text flex_placeholder" :style="loadConfiguration(-1)">计划执行时间</div>
					</div>
					<div class="details_content" :style="loadConfiguration(1)" v-html="dayjs(discussComplateDate).format('YYYY-MM-DD')"></div>
				</template>
				<div class="details_item_hint_box flex_box flex_align_center">
					<div :style="loadConfigurationSize(-2,'h')+'width:0.03rem;border-radius:0.1rem;background:'+appTheme"></div>
					<div class="details_item_hint_text flex_placeholder" :style="loadConfiguration(-1)">事项情况介绍</div>
				</div>
				<div class="details_content" :style="loadConfiguration(1)" v-html="content"></div>
				<div v-if="attachment.length" class="details_file_box flex_box">
					<div :style="loadConfiguration(-1)+'color: #999;'">附件：</div>
					<div class="flex_placeholder">
						<div v-for="(item,index) in attachment" @click="annexOpen(item)" class="details_file_item" :style="loadConfiguration(-1)">{{item.name}}</div>
					</div>
				</div>
				
				<template v-if="groupList.length">
					<div class="details_item_hint_box flex_box flex_align_center">
						<div :style="loadConfigurationSize(-2,'h')+'width:0.03rem;border-radius:0.1rem;background:'+appTheme"></div>
						<div class="details_item_hint_text flex_placeholder" :style="loadConfiguration(-1)">监督小组</div>
					</div>
					<template v-for="(item,index) in groupList">
						<div class="details_group_title_max" :style="loadConfiguration(-1)">{{item.groupName}}（{{item.memberNames.length+1}}人）</div>
						<div class="details_group_title_box flex_box">
							<div :style="loadConfiguration()+'color: #333;'">组长：</div>
							<div class="flex_placeholder" :style="loadConfiguration()+'color:#333;'">{{item.leaderName}}</div>
						</div>
						<div class="details_group_title_box flex_box">
							<div :style="loadConfiguration()+'color: #333;'">组员：</div>
							<div class="flex_placeholder" :style="loadConfiguration()+'color:#333;'">{{item.memberNames.join("、")}}</div>
						</div>
					</template>
				</template>
			</div>
	
			<div v-if="tabs.data.length > 1" class="" :class="tabs.data.length > 3?'':'flex_box'" :style="loadConfiguration()+'color:#333;background:#FFF;margin:0.1rem 0'">
				<van-tabs v-model="tabs.value" @click="tabClick(1)" :ellipsis="false" :color="appTheme" line-width="0.38rem" line-height="0.03rem">
					<van-tab v-for="(item,index) in tabs.data" :name="item.value" :title="item.label"></van-tab>
				</van-tabs>
			</div>
	
			<div v-if="listData.length" class="details_list_box">
				<div v-for="(item,index) in listData" class="details_list_item_warp flex_box">
					<div class="details_list_line_box">
						<div v-if="index" class="details_list_line_top"></div>
						<div v-if="index != listData.length-1" class="details_list_line_bottom"></div>
						<div class="details_list_circle"></div>
					</div>
					<div @click="openOtherDetails(item)" class="flex_placeholder details_list_item">
						<div class="flex_box flex_align_center">
							<div v-if="item.tag" :style="loadConfiguration(-3)+'background:'+T.colorRgba(appTheme,'0.15')"><van-tag :color="appTheme" plain type="primary" style="padding:0.01rem 0.11rem;">{{item.tag}}</van-tag></div>
							<div class="flex_placeholder"></div>
							<div :style="loadConfiguration(-3)+'color: #666;'">{{dayjs(item.time).format('YYYY-MM-DD')}}</div>
						</div>
						<div class="text_two" :style="loadConfiguration(-1)+'color: #333;margin-top:0.1rem;'">{{item.title}}</div>
						<div v-if="item.content" class="text_two" :style="loadConfiguration(-3)+'color: #666;margin-top:0.06rem;'">{{item.content}}</div>
					</div>
				</div>
			</div>
			<template v-else-if="!listData.length">
				<van-empty :style="loadConfiguration(-2)" :image="pageNot.url || pageNot.data[pageNot.type].url">
					<template #description>
						<div class="van-empty__description_text" :style="loadConfiguration(-1)" v-html="pageNot.text || pageNot.data[pageNot.type].text"></div>
						<div class="van-empty__description_summary" :style="loadConfiguration(-3)" v-html="pageNot.summary || pageNot.data[pageNot.type].summary"></div>
					</template>
					<div v-if="pageNot.hasBtn" :style="loadConfiguration(-1)">
						<van-button v-if="(pageNot.type==2||pageNot.type==3)&&pageType=='page'" @click.stop="T.closeWin()" round type="info" size="large" :color="appTheme">{{'返回'}}</van-button>
						<van-button v-else-if="pageNot.type==1||pageNot.type==4" @click.stop="getData();" round type="info" size="large" :color="appTheme">{{'刷新'}}</van-button>
					</div>
				</van-empty>
			</template>
		</template>
		<template v-else>
			<van-empty :style="loadConfiguration(-2)" :image="pageNot.url || pageNot.data[pageNot.type].url">
				<template #description>
					<div class="van-empty__description_text" :style="loadConfiguration(-1)" v-html="pageNot.text || pageNot.data[pageNot.type].text"></div>
				</template>
			</van-empty>
		</template>

	</div>
</body>
<script type="text/javascript">
	var vmData = {
		title:"",
		content:"",
		attachment:[],
		groupList:[],
		discussHeadDept:"",
		discussComplateDate:"",
		tabs:{value:"1",data:[
			{ value: "1", label: "活动开展情况" },
			{ value: "2", label: "调研报告" },
			{ value: "3", label: "整改反馈" },
		]},
		listData:[]
	};
	var vmWatch={
		"tabs.value":function(_val){
			window.sessionStorage.setItem(this.pathname + "_tabs2_value",_val);
		},
	};
	var vmMethods = {
		init:function(){
			var that = this;
			that.tabs.value = window.sessionStorage.getItem(that.pathname + "_tabs2_value") || "1";
			//刷新返回当前对象
			document.title = "议题详情";
			that.getData();
		},
		//栏目切换
		tabClick:function(){
			var that = this;
			var list = [];
			switch(that.tabs.value){
				case "1":list = window.discussActivities;break;
				case "2":list = window.discussReports;break;
				case "3":list = window.discussAbarbeitung;break;
			}
			that.listData = [];
			(list||[]).forEach(function (_eItem) {
				_eItem.content = (_eItem.content||"").replace(/<!--[\w\W\r\n]*?-->/gmi, '').replace(/<[^>]+>/g, '').replace(/(<[^\s\/>]+)\b[^>]*>/gi, '$1>').replace(/\s*/g, '')
				that.listData.push(_eItem);
			});
		},
		getData:function(_type){
			var that = this;
			var url = zyUrl.getAppUrl() + "superviseDiscussComposite/detail?";
			var postParam = {
				"discussId":that.pageParam.recordId,
			};
			T.ajax({ u: url }, 'superviseDiscussComposite/detail', function (ret, err) {
				T.refreshHeaderLoadDone();
				T.hideProgress();
				that.showSkeleton = false;
				var code = ret ? ret.errcode : "";
				var data = ret?ret.data||{}:{};
				var dataLength = data ? data.length : 0;
				that.pageNot.type = ret ? (code == 200 ? 0 : 1) : 1;//类型 列表中只有有网和无网的情况
				that.pageNot.text = ret && code != 200 ? ret.errmsg || ret.data : "";//只有接口报的异常才改文字
				that.attachment = [];
				that.groupList = [];
				if(!window.noScroll){
					window.noScroll = true;
					scrollToView();
				}
				that.title = data.discussName || "";
				that.content = data.discussContent || "";
				that.discussHeadDept = data.discussHeadDept || "";
				that.discussComplateDate = data.discussComplateDate || "";
				var attachments = data.discussAttachments || [];
				var groups = data.discussGroups || [];
				attachments.forEach(function (_eItem) {
					that.attachment.push({id:_eItem.id,name:_eItem.oldName,url:_eItem.id});
				});
				groups.forEach(function (_eItem) {
					that.groupList.push(_eItem);
				});
				window.discussActivities = data.discussActivities || [];//	活动开展情况
				window.discussReports = data.discussReports || [];//	调研报告
				window.discussAbarbeitung = data.discussAbarbeitung || [];//	整改反馈
				that.tabClick();
			}, '详情', "get", postParam);
		},
		//打开征集详情
		openOtherDetails:function(_item){
			var that = this;
			T.openWin('',"widget://html/supervise_often/details_other.html",{recordId:_item.currentDataId || _item.id,pageType:that.tabs.value});
		},
	};
</script>
</html>