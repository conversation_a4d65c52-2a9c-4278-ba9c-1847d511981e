<!DOCTYPE html>
<html>
<script>
//字体适配 ===========================================================
(function (doc, win) {var docEl = doc.documentElement,resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize',recalc = function () {var clientWidth = docEl.clientWidth;if(clientWidth > 480){clientWidth = 480;}if (!clientWidth) return;docEl.style.fontSize = 100 * (clientWidth / 360) + 'px';};if (!doc.addEventListener) return;win.addEventListener(resizeEvt, recalc, false);doc.addEventListener('DOMContentLoaded', recalc, false);})(document, window);

//动态加载文件
var dynamicLoading = {css : function(path) {if (!path || path.length === 0) {throw new Error('argument "path" is required !');}var head = document.getElementsByTagName('head')[0];var link = document.createElement('link');link.href = path;link.rel = 'stylesheet';link.type = 'text/css';head.appendChild(link);},js : function(path) {if (!path || path.length === 0) {throw new Error('argument "path" is required !');}var head = document.getElementsByTagName('head')[0];var script = document.createElement('script');script.src = path;script.type = 'text/javascript';script.async = false;head.appendChild(script);}}

//接收页面参数
var datas={};var param=location.href.split('?');if(param.length>1){param=decodeURIComponent(param[1]);var params=param.split("&");if(params){for(var i=0;i<params.length;i++){if(params[i]){var data_key=params[i].substring(0,params[i].indexOf("="));var data_value=params[i].substring(params[i].indexOf("=")+1);if(data_value.indexOf("{")==0||data_value.indexOf("[")==0){data_value=JSON.parse(data_value)}datas[data_key]=data_value}}}};
var app_version = datas.only || Math.floor(Math.random()*(100000-10000+1)+10000);

//外部文件 动态加载		css	js
dynamicLoading.css('../css/general.css?app_version='+app_version+'');
dynamicLoading.css('https://cdn.staticfile.org/vant/2.12.5/index.min.css?app_version='+app_version+'');

dynamicLoading.js('https://cdn.staticfile.org/jquery/3.6.0/jquery.min.js?app_version='+app_version+'');
dynamicLoading.js('https://cdn.staticfile.org/vue/2.6.14/vue.min.js?app_version='+app_version+'');
dynamicLoading.js('https://cdn.staticfile.org/vant/2.12.5/vant.min.js?app_version='+app_version+'');
dynamicLoading.js('https://cdn.staticfile.org/dayjs/1.11.0/dayjs.min.js?app_version='+app_version+'');
dynamicLoading.js('https://cdn.staticfile.org/echarts/5.0.0/echarts.min.js?app_version='+app_version+'');
dynamicLoading.js('../script/api.js?app_version='+app_version+'');
dynamicLoading.js('../script/myjs.js?app_version='+app_version+'');
dynamicLoading.js('../script/t.js?app_version='+app_version+'');
dynamicLoading.js('../script/encryption.js?app_version='+app_version+'');
dynamicLoading.js('../script/general.js?app_version='+app_version+'');
dynamicLoading.js('../script/vue-echarts.min.js?app_version='+app_version+'');
//在apicloud中打开	就再加载一次配置
apiready = function(){
	window.appLoading = true;
	initApp();
}
function initApp(){
	if(window.appLoading && window.webLoad){
		setTimeout(function(){
			try{vm.initApp();}catch(e){console.error(e.message)}
		},0);
	}
}
</script>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0,viewport-fit=cover"/>
	<meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
	<title></title>
	<style>
		.none{display:none;}
		.statistics_box{ padding: 0.14rem 0.15rem; }
		.statistics_item_box{ padding: 0.1rem; background: #FFFFFF; box-shadow: 0px 2px 10px 1px rgba(24,64,118,0.08); border-radius: 0.04rem; }
		.statistics_item_box+.statistics_item_box{margin-top:0.14rem;}
		.statistics_num_img{ width: 0.32rem; height: 0.33rem; object-fit: cover; }
		.statistics_num_all_box{ margin-right: 0.16rem; }

		.main_year_box{ padding: 0.04rem 0.08rem; background: rgba(0, 0, 0, 0.15); border-radius: 0.02rem; font-weight: 600; color: #FFFFFF; margin-bottom: 0.1rem;}
	</style>
</head>
<body>
	<div id="app" class="none">
		<div class="statistics_box">
			<div class="flex_box">
				<div class="flex_placeholder"></div>
				<div class="main_year_box flex_box flex_align_center">
					<van-popover get-container="#app" v-model="years.showPopover" trigger="click" placement="bottom-end">
						<div v-for="(item,index) in years.data" @click="onPopMoreSelect(item)" role="menuitem" class="van-popover__action">
							<div class="van-popover__action-text">{{item}}年度</div>
						</div>
						<template #reference>
							<div style="position: relative;" class="flex_box flex_align_center">
								<div :style="loadConfiguration(-3)+'margin-right:0.03rem;'" >{{years.value}}年度</div>
								<van-icon :color="'#FFF'" :size="((appFontSize-4)*0.01)+'rem'" :name="'arrow-down'"></van-icon>
							</div>
						</template>
					</van-popover>
				</div>
			</div>
			<div class="statistics_item_box flex_box flex_align_center" style="padding:0.17rem 0.22rem;">
				<img class="statistics_num_img" src="../images/supervise_icon_project.png" alt="" />
				<div class="flex_placeholder" :style="loadConfiguration()+'color:#333;margin-left:0.13rem;font-weight: 600;'">监督议题</div>
				<div v-if="projectAmount" class="statistics_num_all_box T-flexbox-vertical flex_align_center">
					<div :style="loadConfiguration(13)+'font-weight: 600;color:'+appTheme">{{projectAmount}}</div>
					<div :style="'width:0.39rem;height:0.04rem;background:'+T.colorRgba(appTheme,0.15)"></div>
				</div>
			</div>

			<div class="statistics_item_box">
				<div class="flex_box flex_align_center">
					<div :style="'width: 0.08rem; height: 0.08rem; border-radius:50%;border: 1px solid '+appTheme"></div>
					<div class="flex_placeholder" :style="loadConfiguration(-1)+'color:#333;margin-left:0.05rem;font-weight: 600;'">议题分类</div>
				</div>
				<div v-if="!categoryShow" style="padding: 0.8rem 0;" class="flex_box flex_align_center flex_flex_direction_column"><van-loading :size="appFontSize" type="spinner"></van-loading></div>
				<v-chart v-else ref="category" style="width:3.12rem;height:1.76rem;" :options="category"></v-chart>
			</div>

			<div class="statistics_item_box">
				<div class="flex_box flex_align_center">
					<div :style="'width: 0.08rem; height: 0.08rem; border-radius:50%;border: 1px solid '+appTheme"></div>
					<div class="flex_placeholder" :style="loadConfiguration(-1)+'color:#333;margin-left:0.05rem;font-weight: 600;'">整体反馈情况</div>
				</div>
				<div v-if="!completionShow" style="padding: 0.8rem 0;" class="flex_box flex_align_center flex_flex_direction_column"><van-loading :size="appFontSize" type="spinner"></van-loading></div>
				<v-chart v-else ref="completion" style="width:3.12rem;height:1.76rem;" :options="completion"></v-chart>
			</div>

			<div class="statistics_item_box">
				<div class="flex_box flex_align_center">
					<div :style="'width: 0.08rem; height: 0.08rem; border-radius:50%;border: 1px solid '+appTheme"></div>
					<div class="flex_placeholder" :style="loadConfiguration(-1)+'color:#333;margin-left:0.05rem;font-weight: 600;'">议题数量年度对比</div>
				</div>
				<div v-if="!comparedShow" style="padding: 0.8rem 0;" class="flex_box flex_align_center flex_flex_direction_column"><van-loading :size="appFontSize" type="spinner"></van-loading></div>
				<v-chart v-else ref="compared" style="width:3.12rem;height:1.76rem;" :options="compared"></v-chart>
			</div>
		</div>
	</div>
</body>
<script type="text/javascript">
	var vmData = {
		years:{showPopover:false,value:"",data:[]},//当前筛选年份
		projectAmount:"0",

		categoryShow: false,//类别
		category: {
			legend: { show: false, },
			color: ['#C61414'],//线条颜色 
			grid: { left: '0%', right: '5%', bottom: '5%', top: '10%', containLabel: true },
			tooltip: { trigger: 'axis' },
			yAxis: {
				type: 'category',
				data: ["环保卫生", "环保卫生", "环生", "环保卫生", "环保卫生", "环保卫生", "环保卫生", "环保卫生", "环保卫生", "环保卫生"],
				axisLabel: { show: true, textStyle: { color: '#333333', fontSize: 13 } }, axisTick: { show: false },
				axisLine: { show: false }
			},
			xAxis: {
				type: 'value',
				axisLine: { show: false },
				axisTick: { show: false, },
				splitLine: { lineStyle: { type: 'dashed', color: 'rgba(233, 233, 233, 0)' } },
				axisLabel: { show: false }
			},
			series: [{
				type: 'bar',
				name: "数量",
				label: { normal: { show: true, position: 'right',color: '#C61414', fontSize: 13 } },
				barWidth: 10,//柱图宽度
				itemStyle: { normal: { barBorderRadius: [0, 0, 0, 0] } },
				data: [33, 42, 55, 12, 33, 12, 33, 42, 55, 12],
			}]
		},

		completionShow: false,//完成情况
		completion: {
			legend: {
				show: false,
			},
			color: [ '#E04B11','#F4D47B'],
			series: [{
				type: 'pie',
				radius: ['50%', '75%'],
				center: ['50%', '50%'],
				data: [{ "value": 1, "name": "文化" }, { "value": 7, "name": "社会" }],
				label:{
					align: 'left',
					normal:{
						textStyle : {
							fontSize : 12  //显示文字的大小
						}
					}
				}
			}]
		},

		comparedShow:false,//项目数量年度对比
		compared:{
			color:["#F55130"],//线条颜色 
			grid: {left: '5%',right: '5%',bottom: '5%',top: '10%',containLabel: true},
			tooltip: {trigger: 'axis'},
			xAxis: {
				type: 'category',
				data: ["一月","一月","一月","一月","一月","一月"],
				axisLabel: {show: true,textStyle: {color: '#333333',fontSize: 13,}},axisTick:{show:false},
				axisLine: {show: false},
			},
			yAxis: {
				type: 'value',
				minInterval: 1,
				axisLine: {show: false},
				axisTick: {show: false,},
				splitLine: {lineStyle: {type: 'solid',color: '#eee'}},
				axisLabel: {show: true,textStyle: {color: '#333',fontSize: 13,}},axisTick:{show:false}
			},
			series: [{
				type: 'line',
				name:"数量",
				smooth: false,//是否圆角
				showSymbol: true,//每个点
				symbolSize:5,
				data: [11,44,23,55,6,77],
			}]
		},
	};
	var minYear = 2018,maxYear = new Date().getFullYear();
	for(var i = maxYear; i >= minYear; i--){
		vmData.years.data.push(i+"");
	}
	vmData.years.value = vmData.years.data[0];
	var vmWatch={
		"years.value":function(_val){
			window.sessionStorage.setItem(this.pathname + "_years2_value",_val);
		},
	};
	var vmMethods = {
		init:function(){
			var that = this;
			that.years.value = window.sessionStorage.getItem(that.pathname + "_years2_value") || that.years.data[0];
			that.category.color[0] = T.colorRgba(that.appTheme,0.8);
			that.category.series[0].label.normal.color = that.appTheme;
			that.compared.color[0] = T.colorRgba(that.appTheme,0.8);

			that.years.value = that.pageParam.year;
			//刷新返回当前对象
			document.title = "统计分析";
			that.getData();
		},
		//打开pop框 顶部更多回调
		onPopMoreSelect: function (_action) {
			var that = this;
			that.years.showPopover = false;
			that.years.value = _action;
			T.showProgress();
			that.getData();
		},
		getData:function(_type){
			var that = this;
			var url = zyUrl.getAppUrl() + "superviseDiscussCount/analyse?";
			var postParam = {
				"belongYear":that.years.value,
				"countYear":maxYear - minYear
			};
			T.ajax({ u: url }, 'superviseDiscussCount/analyse', function (ret, err) {
				T.refreshHeaderLoadDone();
				T.hideProgress();
				var code = ret ? ret.errcode : "";
				var data = ret?ret.data||{}:{};
				var dataLength = data ? data.length : 0;
				that.pageNot.type = ret ? (code == 200 ? 0 : 1) : 1;//类型 列表中只有有网和无网的情况
				that.pageNot.text = ret && code != 200 ? ret.errmsg || ret.data : "";//只有接口报的异常才改文字
				that.projectAmount = data.discussAmount || "0";

				setTimeout(() => {
					vm.$refs["category"].refresh();
				}, 0);
				that.categoryShow = true;
				var typeCountInfo = data.typeCountInfo || [];
				that.category.yAxis.data = [], that.category.series[0].data = [];
				typeCountInfo.forEach(function (_eItem, _eIndex, _eArr) {
					that.category.yAxis.data.push(_eItem.name || "");
					that.category.series[0].data.push(_eItem.amount);
				});

				setTimeout(() => {
					vm.$refs["completion"].refresh();
				}, 0);
				that.completionShow = true;
				var completeCountInfo = data.abarbeitungCountInfo || [];
				that.completion.series[0].data = [];
				completeCountInfo.forEach(function (_eItem, _eIndex, _eArr) {
					that.completion.series[0].data.push({ "value": _eItem.amount, "name": _eItem.name+"\n("+_eItem.amount+")("+_eItem.ratio+")" });
				});

				setTimeout(() => {
					vm.$refs["compared"].refresh();
				}, 0);
				that.comparedShow = true;
				var yearCountInfo = data.yearCountInfo || [];
				that.compared.xAxis.data = [],that.compared.series[0].data = [];
				yearCountInfo.forEach(function(_eItem,_eIndex,_eArr){
					that.compared.xAxis.data.push(_eItem.year || "");
					that.compared.series[0].data.push(_eItem.amount);
				});

			}, '统计', "get", postParam);
		},
	};
</script>
</html>