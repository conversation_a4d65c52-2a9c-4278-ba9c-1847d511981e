<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="minimum-scale=1,initial-scale=1,width=device-width,shrink-to-fit=no,maximum-scale=1,user-scalable=0,viewport-fit=cover">
	<script src="./script/vue.global.js"></script>
	<script src="./script/url.js"></script>
	<title>会议详情</title>
</head>
<body></body>
<script>
	//接收页面参数
	var datas={};var param=location.href.split('?');if(param.length>1){param=decodeURIComponent(param[1]);var params=param.split("&");if(params){for(var i=0;i<params.length;i++){if(params[i]){var data_key=params[i].substring(0,params[i].indexOf("="));var data_value=params[i].substring(params[i].indexOf("=")+1);if(data_value.indexOf("{")==0||data_value.indexOf("[")==0){data_value=JSON.parse(data_value)}datas[data_key]=data_value}}}};
	var hasClose = false;
	window.onload = () => {
		initMeeting();
	};

	function initMeeting(){
		hasClose = false;
		openMeetingXY({
			extId: datas.extId || '606e6604e882a63891f1471e3e1d9d32af26e918',
			clientId: datas.clientId || 'lleI2gfbcKaszk9dyUzFxIZ8',
			clientSecret: datas.clientSecret || 'TrXAokrTBUHRXZ2HdJsWJioZHx6ZOdKT',
			meeting: datas.meetingNum || '90003928623',
			meetingPassword: datas.pwd || '',
			meetingName: datas.displayName || 'lin',
		});
	}
	
	var openMeetingXY = (data) => {
		var iframe = document.createElement('iframe')
		iframe.id = 'XYVideoMeetingIframe'
		iframe.setAttribute('style', 'position: fixed;top: 0;left: 0;width: 100%;height: 100%;border:0;overflow: auto;z-index: 9999;')
		iframe.setAttribute('src', `${appUrl.replace('lzt/','xy/')}`)
		iframe.setAttribute('allow', 'microphone *;camera *;geolocation *;')
		document.body.appendChild(iframe)
		if (iframe.attachEvent) {
			iframe.attachEvent('onload', () => {
				iframe.contentWindow.postMessage(data, '*')
			})
		} else {
			iframe.onload = () => {
				iframe.contentWindow.postMessage(data, '*')
			}
		}
		window.addEventListener('message', (event)=> {
			document.body.removeChild(iframe)
			var hasApi = false;
			try{
				hasApi = api && api.closeWin;
			}catch(e){
				hasApi = false;
			}
			if(hasApi){
				api.sendEvent({ name:'url_message' });
			}else{
				window.parent.postMessage("close", '*');
			}
		});
	}
	
</script>
</html>
