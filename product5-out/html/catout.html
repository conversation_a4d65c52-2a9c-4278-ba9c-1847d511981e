<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0,viewport-fit=cover"/>
		<meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
		<title></title>
		<link rel="stylesheet" href="css/cropper.css" />
		<style>
			html,body{padding:0;margin: 0;}
		</style>
	</head>
<body>
	<div id="containerDiv" style="position:fixed;display: none;top: 0;left: 0;right:0;bottom:0;">
		<img id="containerImg" src="" alt="Picture">
	</div>
</body>
<script type="text/javascript" src="./script/cropper.js"></script>
<script type="text/javascript">
	//接收页面参数
	var datas={};var param=location.href.split('?');if(param.length>1){param=decodeURIComponent(param[1]);var params=param.split("&");if(params){for(var i=0;i<params.length;i++){if(params[i]){var data_key=params[i].substring(0,params[i].indexOf("="));var data_value=params[i].substring(params[i].indexOf("=")+1);if(data_value.indexOf("{")==0||data_value.indexOf("[")==0){data_value=JSON.parse(data_value)}datas[data_key]=data_value}}}};
	var app_version = datas.only || Math.floor(Math.random()*(100000-10000+1)+10000);

	var pageWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
	var pageHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;

	var cropper = null;

	window.onload = function() {
		console.log(JSON.stringify(datas));
		// localStorage.setItem("sdt_signin_phone", datas.phone||datas.tel||datas.mobile||"");
		// window.location.href = "../pages/index/index.html";
	}

	window.addEventListener('message', function(event) {
		var data = event.data;
		if(data == "save"){
			var result = cropper.getCroppedCanvas({ width: 200, height: 200 });
			var imgName = 'FNImageClip_' + Math.round(new Date() / 1000) + '.png';
			var file = base64ToFile(result.toDataURL('image/png'),imgName);
			window.parent.postMessage(file, '*');
			return;
		}
		cropper = new Cropper(document.getElementById("containerImg"), {
			minContainerWidth : pageWidth,
			minContainerHeight :  pageHeight,
			aspectRatio: 1 / (data.scale||1),//裁剪框比例 1：1
			viewMode : 1,//显示
			guides :false,//裁剪框虚线 默认true有
			dragMode : "move",
			center:false,
			build: function (e) { //加载开始
			},
			built: function (e) { //加载完成
				document.getElementById("containerDiv").style.display = "block";
			},
			zoom: function (e) {
				console.log(e.type, e.detail.ratio);
			},
			background : true,// 容器是否显示网格背景
			movable : true,//是否能移动图片
			cropBoxMovable :false,//是否允许拖动裁剪框
			cropBoxResizable :false,//是否允许拖动 改变裁剪框大小
		});

		cropper.reset().replace(URL.createObjectURL(event.data.url));
	});

	//将base64转换为File
	function base64ToFile (urlData, fileName) { 
		const arr = urlData.split(',');
		const mime = arr[0].match(/:(.*?);/)[1];
		const bytes = atob(arr[1]);
		let n = bytes.length;
		const ia = new Uint8Array(n);
		while (n--) {
			ia[n] = bytes.charCodeAt(n);
		}
		return new File([ia], fileName, { type: mime });
	}
</script>
</html>