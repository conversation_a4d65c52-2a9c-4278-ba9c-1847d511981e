<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="minimum-scale=1,initial-scale=1,width=device-width,shrink-to-fit=no,maximum-scale=1,user-scalable=0,viewport-fit=cover">
	<link rel="stylesheet" type="text/css" href="./css/yl-style.css" />
	<script src="./script/vue.global.js"></script>
	<script src="./script/yl-sdk.js"></script>
	<title>会议详情</title>
</head>
<body></body>
<script>
	//接收页面参数
	var datas={};var param=location.href.split('?');if(param.length>1){param=decodeURIComponent(param[1]);var params=param.split("&");if(params){for(var i=0;i<params.length;i++){if(params[i]){var data_key=params[i].substring(0,params[i].indexOf("="));var data_value=params[i].substring(params[i].indexOf("=")+1);if(data_value.indexOf("{")==0||data_value.indexOf("[")==0){data_value=JSON.parse(data_value)}datas[data_key]=data_value}}}};
	var hasClose = false;
	window.onload = () => {
		initMeeting();
		if(datas.joinT){
			setTimeout(()=>{
				YlinkMeeting.join();
			},Number(datas.joinT));
		}
	};

	function initMeeting(){
		hasClose = false;
		const { YlinkMeeting } = window.YlinkMobileWeb;
		YlinkMeeting.init({
			clientId: datas.clientId || '1ff67f7b996848608ed80ef7097a20cc',
			accessToken: datas.accessToken || '',
			baseURL: `https://`+(datas.baseURL || "meetings.ylyun.com"),
			leaveURL: '',
			meetingNum: datas.meetingNum || '63013925364',
			pwd: datas.pwd || '933761',
			displayName: datas.displayName || '移动张三',
			mediaType: 'webrtc',
			isPrivateCloud:true,
			onJoined:()=>{
				hasClose = true;
				console.log("mycallback:onJoined");
			},
			onReady:()=>{
				console.log("mycallback:onReady");
			},
			onClose:()=>{
				console.log("mycallback:onClose");
				setTimeout(()=>{
					initMeeting();
				},0);
				if(!hasClose){
					return;
				}
				var hasApi = false;
				try{
					hasApi = api && api.closeWin;
				}catch(e){
					hasApi = false;
				}
				if(hasApi){
					api.sendEvent({ name:'url_message' });
				}else{
					window.parent.postMessage("close", '*');
				}
			},
			custom:{
				showPreview:datas.preview!="false",
				showFeedbackButton:false,
			},
			featureConfig:{
				audio:{sendOn: datas.audio=="true"},
				video:{sendOn: datas.video=="true"},
			},
			theme:{
				vc:"#"+(datas.theme || "cd0000")
			}
		});
	}
	
</script>
</html>
