<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0,viewport-fit=cover"/>
		<meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
		<title></title>
		<style>
			html,body{padding:0;margin: 0;width:100%;height:100%;}
		</style>
	</head>
<body id="mapContainer"></body>
<script type="text/javascript">
	//接收页面参数
	var datas={};var param=location.href.split('?');if(param.length>1){param=decodeURIComponent(param[1]);var params=param.split("&");if(params){for(var i=0;i<params.length;i++){if(params[i]){var data_key=params[i].substring(0,params[i].indexOf("="));var data_value=params[i].substring(params[i].indexOf("=")+1);if(data_value.indexOf("{")==0||data_value.indexOf("[")==0){data_value=JSON.parse(data_value)}datas[data_key]=data_value}}}};
	var app_version = datas.only || Math.floor(Math.random()*(100000-10000+1)+10000);

	var map,geocoder;
	function onApiLoaded(){
		map = new AMap.Map('mapContainer', {
			viewMode: '3D', // 默认使用 2D 模式，如果希望使用带有俯仰角的 3D 模式，请设置 viewMode: '3D'
			zoom:13,
			resizeEnable: true
		});
		//map.on('complete', function() {
		//	getrect();
		//});
		map.on('moveend', function() {
			getrect();
		});
		map.on('zoomend', function() {
			getrect();
		});
		geocoder = new AMap.Geocoder({
			city: "全国", //城市设为北京，默认：“全国”
			radius: 500 //范围，默认：500
		});
		AMap.plugin('AMap.Geolocation', function() {
			var geolocation = new AMap.Geolocation({
				enableHighAccuracy: true,//是否使用高精度定位，默认:true
				timeout: 10000,//超过10秒后停止定位，默认：5s
				position:'RB',//定位按钮的停靠位置
				offset: [10, 30],//定位按钮与设置的停靠位置的偏移量，默认：[10, 20]
				panToLocation: true,
				zoomToAccuracy:true,
				showCircle:true,
				showMarker:true,
			});
			map.addControl(geolocation);
			geolocation.getCurrentPosition(function(status,result){
				//console.log("getCurrentPosition"+JSON.stringify(result));
				if (status === 'complete' && result.info == "SUCCESS") {
					map.setCenter([result.position.getLng(),result.position.getLat()]);
				}
			});
		});
		window.isInit = true;
		if(window.openData){
			initData(window.openData);
		}
	}

	function task(){
		clearTimeout(window.taskId);
		window.taskId = setTimeout(() => {
			window.getIng = false;
		}, 300);
	}

	function getrect(){
		task();
		if(window.getIng){
			return;
		}
		window.getIng = true;
		var bounds = map.getBounds();
		var nw = bounds.getNorthWest();// 获取左上角的经纬度（西北角）
		var se = bounds.getSouthEast();// 获取右下角的经纬度（东南角）
		console.log('左上角（西北角）经纬度:', nw.lng, nw.lat);
		console.log('右下角（东南角）经纬度:', se.lng, se.lat);
		sendFrameMsg({nwLng:nw.lng,nwLat:nw.lat,seLng:se.lng,seLat:se.lat},"getData");
	}

	window.addEventListener('message', function(event) {
		initData(event.data);
	});

	var showPoint = [];
	function initData(msgData){
		console.log(JSON.stringify(msgData));
		switch(msgData.mType){
			case "init":
				window._AMapSecurityConfig = { securityJsCode: msgData.secret || '17e84d06b40bb4e954148261d0f7c79b' }
				var url = `https://webapi.amap.com/maps?v=2.0&key=${msgData.key||'1616e6da9a7e3a00c6cae53119e7705e'}&plugin=AMap.Geocoder&callback=onApiLoaded`;
				var jsapi = document.createElement('script');
				jsapi.charset = 'utf-8';
				jsapi.src = url;
				document.head.appendChild(jsapi);
				break;
			case "setData":
				if(!window.isInit){
					window.openData = msgData;
					initData({mType:"init"});
					return;
				}
				var isFirst = showPoint.length == 0;
				msgData.list.forEach(function(point) {
					if(!point.longitude || !point.latitude){
						return;
					}
					var nowKey = "" + point.longitude + point.latitude;
					if(getItemForKey(nowKey,showPoint)){
						return;
					}
					var position = [Number(point.longitude),Number(point.latitude)];
					//<div style="background:#FFF;width:100px;padding:15px;">${point.title}</div>
					var marker = new AMap.Marker({
						content:`<img style="width: 40px;height: 40px;" src="./images/icon_mark_map.png" />`,
						offset: new AMap.Pixel(-20, -40),
						position: position,
						map: map,
					});
					marker.setLabel({
						offset: new AMap.Pixel(1, 5),
						content: point.title,
						direction: 'top-center'
					});
					marker.on('click', function () {
						if(window.openPoint){
							return;
						}
						window.openPoint = true;
						setTimeout(() => {
							window.openPoint = false;
						}, 300);
						// console.log('点击了标注点：' + JSON.stringify(point));
						sendFrameMsg(point,"openWork");
					}, {once: true});
					showPoint.push({key:nowKey,marker:marker,position:position});
					console.log("已添加点："+point.title);
				});
				if(isFirst && showPoint.length){
					map.panTo(showPoint[0].position);
				}
				// console.log();
				break;
		}
	};

	var pageType = "web";
	apiready = function(){
		pageType = "app";
		api.addEventListener({
			name:'mapframeChild'
		}, function(e){
			initData(e.value);
		});
		sendFrameMsg({type:pageType},'init');
	}

	function sendFrameMsg(_obj,_type){
		var sendMsg = {};
		if(typeof _obj === "object"){
			for(var value in _obj){
				if(typeof _obj[value] != "function"){
					sendMsg[value] = _obj[value];
				}
			}
		}
		sendMsg.mType = _type;
		if(pageType == "app"){
			api.sendEvent({
				name:'mapframeBase',
				extra:sendMsg
			})
		}else{
			window.parent.postMessage(sendMsg, '*');
		}
	}
	
	function getItemForKey(_value, _list, _key, _child){
		var hasChild = false,listLength = _list.length;
		for (var i = 0; i < listLength; i++) {
			var listItem = _list[i];
			var listItemKey = listItem[_key || "key"];
			if(listItemKey === _value){
				listItem['_i'] = i;
				return listItem;
			}
		}
		return false;
	}
</script>
</html>