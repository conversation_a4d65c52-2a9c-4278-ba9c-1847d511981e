<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0"/>
	<meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
	<title>demo</title>
	<link rel="stylesheet" href="./css/vant.css" />
	<style type="text/css">
		html,body,#app{ width: 100%; height: 100%; margin: 0; padding: 0;background:#000; }
		[v-cloak] { display: none; }
		.van-image-preview__index{
			top:36px;
		}
	</style>
</head>
<body>
	<div id="app">
		
	</div>
</body>
<script type="text/javascript" src="./script/vue.min.js"></script>
<script type="text/javascript" src="./script/vant.min.js"></script>
<script type="text/javascript">
	var vm,pageParam={};
	var vmData = {
		images:[],
		startPosition:0,
		watermark:"",
	};
	var methods = {
		init:function(){
			vant.ImagePreview({
				images: this.images,
				startPosition: this.startPosition,
				onClose() {
					try{
						api.sendEvent({
							name:pageParam.name+'_msg',
							extra:{type:"close"}
						})
					}catch(e){
						window.parent.postMessage("close", '*');
					}
				},
			});
			setTimeout(()=>{
				if(vmData.watermark){
					var imgs = document.querySelectorAll(".van-image-preview__swipe-item");
					imgs.forEach((obj)=>{
						var backgroundImage = document.createElement('img');
						backgroundImage.src = vmData.watermark;
						backgroundImage.style = "position: absolute;z-index:1;";
						backgroundImage.class = "watermarkImg";
						obj.appendChild(backgroundImage);
					});
				}
			},100);
		},
	};
	var pageParam = {};
	window.onload = function() {
		setTimeout(() => {
			try{
				pageParam = api.pageParam;
				api.removeEventListener({ name:pageParam.name+'_open'})
				api.addEventListener({
					name:pageParam.name+'_open'
				}, function(ret){
					vmData.watermark = ret.value.watermark;
					vmData.images = ret.value.imgs || [];
					vmData.startPosition = ret.value.index || 0;
					showChart();
				})
			}catch(e){
				
			}
		}, 300);
	};
	
	function showChart(){
		vm = new Vue({
			el: '#app',
			data: vmData,
			mounted: function () {
				this.init();
			},
			methods: methods
		});
	}
	
	window.addEventListener('message', function(event) {
		try{
			var data = (event.data);
			vmData.watermark = data.watermark;
			vmData.images = data.imgs || [];
			vmData.startPosition = data.index || 0;
			showChart();
		}catch(e){
		}
		
	});
</script>
</html>