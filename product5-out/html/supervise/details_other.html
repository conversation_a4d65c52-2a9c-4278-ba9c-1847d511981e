<!DOCTYPE html>
<html>
<script>
//字体适配 ===========================================================
(function (doc, win) {var docEl = doc.documentElement,resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize',recalc = function () {var clientWidth = docEl.clientWidth;if(clientWidth > 480){clientWidth = 480;}if (!clientWidth) return;docEl.style.fontSize = 100 * (clientWidth / 360) + 'px';};if (!doc.addEventListener) return;win.addEventListener(resizeEvt, recalc, false);doc.addEventListener('DOMContentLoaded', recalc, false);})(document, window);

//动态加载文件
var dynamicLoading = {css : function(path) {if (!path || path.length === 0) {throw new Error('argument "path" is required !');}var head = document.getElementsByTagName('head')[0];var link = document.createElement('link');link.href = path;link.rel = 'stylesheet';link.type = 'text/css';head.appendChild(link);},js : function(path) {if (!path || path.length === 0) {throw new Error('argument "path" is required !');}var head = document.getElementsByTagName('head')[0];var script = document.createElement('script');script.src = path;script.type = 'text/javascript';script.async = false;head.appendChild(script);}}

//接收页面参数
var datas={};var param=location.href.split('?');if(param.length>1){param=decodeURIComponent(param[1]);var params=param.split("&");if(params){for(var i=0;i<params.length;i++){if(params[i]){var data_key=params[i].substring(0,params[i].indexOf("="));var data_value=params[i].substring(params[i].indexOf("=")+1);if(data_value.indexOf("{")==0||data_value.indexOf("[")==0){data_value=JSON.parse(data_value)}datas[data_key]=data_value}}}};
var app_version = datas.only || Math.floor(Math.random()*(100000-10000+1)+10000);

//外部文件 动态加载		css	js
dynamicLoading.css('../css/general.css?app_version='+app_version+'');
dynamicLoading.css('https://cdn.staticfile.org/vant/2.12.5/index.min.css?app_version='+app_version+'');

dynamicLoading.js('https://cdn.staticfile.org/jquery/3.6.0/jquery.min.js?app_version='+app_version+'');
dynamicLoading.js('https://cdn.staticfile.org/vue/2.6.14/vue.min.js?app_version='+app_version+'');
dynamicLoading.js('https://cdn.staticfile.org/vant/2.12.5/vant.min.js?app_version='+app_version+'');
dynamicLoading.js('https://cdn.staticfile.org/dayjs/1.11.0/dayjs.min.js?app_version='+app_version+'');
dynamicLoading.js('../script/api.js?app_version='+app_version+'');	
dynamicLoading.js('../script/myjs.js?app_version='+app_version+'');	
dynamicLoading.js('../script/t.js?app_version='+app_version+'');	
dynamicLoading.js('../script/encryption.js?app_version='+app_version+'');	
dynamicLoading.js('../script/general.js?app_version='+app_version+'');
//在apicloud中打开	就再加载一次配置
apiready = function(){
	window.appLoading = true;
	initApp();
}
function initApp(){
	if(window.appLoading && window.webLoad){
		setTimeout(function(){
			try{vm.initApp();}catch(e){console.error(e.message)}
		},0);
	}
}
</script>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0,viewport-fit=cover"/>
	<meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
	<title></title>
	<style>
		.none{display:none;}
		.details_main_box{ padding: 0.1rem 0.15rem; background: #FFF; }
		.details_title{ font-weight: 600; color: #333333; }
		.details_item_hint_box{ margin-top: 0.19rem; }
		.details_item_hint_text{ font-weight: 600; color: #333333; margin-left: 0.05rem; }
		.details_content{ font-weight: 400; color: #333333; margin-top: 0.1rem; line-height: 1.45; }
		.details_content *{font-size: inherit;font-family: inherit;}
		.details_file_box{ margin-top: 0.1rem; }
		.details_file_item{ color: #144FC6; }
		.details_file_item +.details_file_item{ margin-top: 0.06rem; }

		#app .van-tag--plain{ background-color:rgba(0,0,0,0); }
		#app .van-tag--plain::before{ border: 0px; }
	</style>
</head>
<body>
	<div id="app" class="none">
		<div v-if="showSkeleton" class="notText">
			<van-skeleton v-for="(item,index) in 3" title :row="3"></van-skeleton>
		</div>
		<template v-else-if="title">
			<div class="details_main_box">
				<div v-if="typeName || date || year" style="margin-bottom: 0.19rem;" class="flex_box flex_align_center">
					<div v-if="typeName" :style="loadConfiguration(-3)+'background:'+T.colorRgba(appTheme,'0.15')"><van-tag :color="appTheme" plain type="primary" style="padding:0.01rem 0.11rem;">{{typeName}}</van-tag></div>
					<div class="flex_placeholder"></div>
					<div v-if="date" :style="loadConfiguration(-3)+'color: #666;'">{{dayjs(date).format('YYYY-MM-DD')}}</div>
					<div v-else-if="year" :style="loadConfiguration(-3)+'color: #666;'">年度：{{year}}</div>

				</div>
				<div class="details_title" :style="loadConfiguration(2)">{{title}}</div>
				<div class="details_content" :style="loadConfiguration(1)" v-html="content"></div>
				<div v-if="attachment.length" class="details_file_box flex_box">
					<div :style="loadConfiguration(-1)+'color: #333;'">附件：</div>
					<div class="flex_placeholder">
						<div v-for="(item,index) in attachment" @click="annexOpen(item)" class="details_file_item" :style="loadConfiguration(-1)">{{item.name}}</div>
					</div>
				</div>
				<template v-if="users && users.length">
					<div class="details_item_hint_box flex_box flex_align_center">
						<div :style="loadConfigurationSize(-2,'h')+'width:0.03rem;border-radius:0.1rem;background:'+appTheme"></div>
						<div class="details_item_hint_text flex_placeholder" :style="loadConfiguration(-2)">参与人员</div>
					</div>
					<div class="flex_placeholder" :style="loadConfiguration()+'color:#666;margin-top:0.05rem;'">{{users.map(i => i.name).join("、")}}</div>
				</template>
			</div>
		</template>
		<template v-else>
			<van-empty :style="loadConfiguration(-2)" :image="pageNot.url || pageNot.data[pageNot.type].url">
				<template #description>
					<div class="van-empty__description_text" :style="loadConfiguration(-1)" v-html="pageNot.text || pageNot.data[pageNot.type].text"></div>
				</template>
			</van-empty>
		</template>

	</div>
</body>
<script type="text/javascript">
	var vmData = {
		typeName:"",
		year:"",
		date:"",
		title:"",
		content:"",
		attachment:[],
		users:[]
	};
	var vmWatch={
		
	};
	var vmMethods = {
		init:function(){
			var that = this;
			// //刷新返回当前对象
			// document.title = "详情";
			that.getData();
		},
		getData:function(_type){
			var that = this;
			var url = "";
			var postParam = {
			};
			switch(that.pageParam.pageType){
				case "1":
					url = zyUrl.getAppUrl() + "supervisePepoleActivity/info/"+that.pageParam.recordId;
					document.title = "开展情况详情";
					break;
				case "2":
					url = zyUrl.getAppUrl() + "superviseDocument/info/"+that.pageParam.recordId;
					document.title = "相关资料详情";
					break;
				case "3":
					url = zyUrl.getAppUrl() + "supervisePepoleReport/info/"+that.pageParam.recordId;
					document.title = "监督成果详情";
					break;
			}
			T.ajax({ u: url }, 'detail', function (ret, err) {
				T.refreshHeaderLoadDone();
				T.hideProgress();
				that.showSkeleton = false;
				var code = ret ? ret.errcode : "";
				var data = ret?ret.data||{}:{};
				var dataLength = data ? data.length : 0;
				that.pageNot.type = ret ? (code == 200 ? 0 : 1) : 1;//类型 列表中只有有网和无网的情况
				that.pageNot.text = ret && code != 200 ? ret.errmsg || ret.data : "";//只有接口报的异常才改文字
				that.attachment = [];
				if(!window.noScroll){
					window.noScroll = true;
					scrollToView();
				}
				that.typeName = data.activityTypeName || data.documentTypeName || data.reportTypeName || "";
				that.date = data.activityDate || "";
				that.year = data.belongYear || "";
				that.title = data.title || data.submitFace || "";
				that.content = data.content || "";
				var attachments = data.attachmentInfos || [];
				attachments.forEach(function (_eItem) {
					that.attachment.push({id:_eItem.id,name:_eItem.oldName,url:_eItem.id});
				});
				that.users = data.joinUsers || [];
			}, '详情', "get", postParam);
		},
	};
</script>
</html>