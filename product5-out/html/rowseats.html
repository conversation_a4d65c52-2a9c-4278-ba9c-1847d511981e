<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0"/>
	<meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
	<title>排座</title>
	<style type="text/css">
		[v-cloak] { display: none; }
		html,body,#app{ width: 100%; height: 100%; margin: 0; padding: 0;overflow: scroll; }
		.list_item{ position:absolute; display: flex; align-items: center; justify-content: center; border:1px solid #ccc; font-size: 14px; }
	</style>
</head>
<body>
	<div v-cloak id="app">
		<div style="padding:16px;display:flex;align-items: center;position:absolute;top:0;left:0;right:0;z-index:1;background:#FFF;">
			<div @click="switchType" :style="`padding:10px 20px;background:${theme};color:#FFF;`">显示{{btnType==1?'全部':'我的'}}</div>
			<div style="margin-left: 20px;;">{{myseat.name=='座位'?(myseat.seatLine+'排'+myseat.seatColumn+'座'):myseat.name}}</div>
			<!--  {{myseat.seatLine}}排{{myseat.seatColumn}}座 -->
		</div>
		<div style="height: 73px;width: 100%;"></div>
		<div v-if="showCanvas" id="seatWarp" :style="`width:${canvasW}px;height:${canvasH}px;${transform?'position:absolute;top:73px;transformOrigin:top left;transform:scale('+transform+');':''}`">
			<div id="seatBox" :style="`width:${canvasW}px;height:${canvasH}px;position: relative;`">
				<div v-for="(item,index) in showList" :id="`user${item.userId}`" :ref="myInfo.id==item.userId ? 'activeRef':'otherRef'" class="list_item" :ref="myInfo.id==item.userId?'activeRef':'otherRef'" :style="`left:${item.seatX}px;top:${item.seatY}px;width:${item.seatW}px;height:${item.seatH}px;background:${myInfo.id==item.userId?theme:item.defaultColor};color:${myInfo.id==item.userId?'#fff':'#333'};`">
					{{item.userName || item.name}}
				</div>
			</div>
		</div>
	</div>
</body>
<script type="text/javascript" src="./script/vue.min.js"></script>
<script type="text/javascript" src="./script/axios.min.js"></script>
<script type="text/javascript" src="./script/url.js"></script>
<script type="text/javascript">
	//接收页面参数
	var datas={};var param=location.href.split('?');if(param.length>1){param=decodeURIComponent(param[1]);var params=param.split("&");if(params){for(var i=0;i<params.length;i++){if(params[i]){var data_key=params[i].substring(0,params[i].indexOf("="));var data_value=params[i].substring(params[i].indexOf("=")+1);if(data_value.indexOf("{")==0||data_value.indexOf("[")==0){data_value=JSON.parse(data_value)}datas[data_key]=data_value}}}};
	var vm,pageParam={};
	var vmData = {
		icon:"",
		appName:"",
		apkUrl:"",
		qr:"",
		appUrl:appUrl,
		downErr:"请稍候",//异常提示
		showCanvas:false,
		canvasW:0,
		canvasH:0,
		btnType:1,//1为我的 2为所有
		transform:"",
		showList:[],
		myInfo:{},
		myseat:{},
		theme:"#BC1D1D",
		pageColorList:[],
	};
	var methods = {
		init:function(){
			var that = this;
			axios.post(`${that.appUrl}login/user`,{
			},{ headers: {'Authorization':datas.token} }).catch(function (err) {
				that.downErr = err;
				alert(that.downErr);
			}).then(function (ret) {
				ret = ret.data || {};
				dealAjaxContent(ret);
				if(ret.code == 200){
					that.myInfo = ret.data||{};
					that.showSeat();
				}else{
					that.downErr = ret.message || ret.data;
					alert(that.downErr);
				}
			});
			axios.post(`${that.appUrl}arrangeSeatPlaceScope/list`, {
				pageNo:1,
				pageSize:999,
				query:{
					placeId: datas.id
				}
			}, { headers: { 'Authorization': datas.token } }).catch(function (err) {
				that.downErr = err;
				alert(that.downErr);
			}).then(function (res) {
				that.pageColorList = res.data.data
			});
			axios.post(`${that.appUrl}seatPlace/info`,{
				detailId: datas.id,
			},{ headers: {'Authorization':datas.token} }).catch(function (err) {
				that.downErr = err;
				alert(that.downErr);
			}).then(function (ret) {
				ret = ret.data || {};
				dealAjaxContent(ret);
				if(ret.code == 200){
					var data = (ret.data||{}).arranges || [];
					var ml = -1,mt = -1,mr = -1,mb = -1;
					var list = [];
					var columnList = [],lineList = [];
					data.forEach((_eItem)=> {
						var colorItem = that.pageColorList.find(v=>{return v.id == _eItem.seatScopeNumber})
						var seatX = _eItem.seatX;
						var seatY = _eItem.seatY;
						var seatW = _eItem.seatW;
						var seatH = _eItem.seatH;
						_eItem.defaultColor = colorItem?colorItem.scopeColor:_eItem.defaultColor;
						var name = _eItem.name;
						var userName = _eItem.userName;
						if(_eItem.type == "empty"){
							return;
						}
						if(ml == -1 || ml > seatX){
							ml = seatX;
						}
						if(mt == -1 || mt > seatY){
							mt = seatY;
						}
						if(mr == -1 || mr < (seatX+seatW)){
							mr = seatX+seatW;
						}
						if(mb == -1 || mb < (seatY+seatH)){
							mb = seatY+seatH;
						}
						list.push(_eItem);
						if(_eItem.seatColumn == 1){
							columnList.push(JSON.parse(JSON.stringify(_eItem)));
						}
						if(_eItem.seatLine == 1){
							lineList.push(JSON.parse(JSON.stringify(_eItem)));
						}
					});
					if(columnList.length){
						columnList.forEach((_eItem)=> {
							_eItem.defaultColor = "#FDF6EC";
							_eItem.userId = "";
							_eItem.seatX = _eItem.seatX - _eItem.seatW;
							_eItem.userName = `${_eItem.seatLine}排`;
						});
						list = list.concat(columnList);
						if(ml == -1 || ml > columnList[0].seatX){
							ml = columnList[0].seatX;
						}
					}
					if(lineList.length){
						lineList.forEach((_eItem)=> {
							_eItem.defaultColor = "#F0F9EB";
							_eItem.userId = "";
							_eItem.seatY = _eItem.seatY - _eItem.seatH;
							_eItem.userName = `${_eItem.seatColumn}列`;
						});
						list = list.concat(lineList);
						if(mt == -1 || mt > columnList[0].seatY){
							mt = columnList[0].seatY;
						}
					}
					that.showList = list;
					that.showSeat();
					that.offsetUser();
					// console.log("当前区域为："+ml + " " + mt + " " + mr + " " + mb);
					window.maxW = mr + ml;
					window.maxH = mb + mt;
					that.canvasW = window.maxW;
					that.canvasH = window.maxH;
					that.showCanvas = true;
				}else{
					that.downErr = ret.message || ret.data;
					alert(that.downErr);
				}
			});
		},
		showSeat(){
			var that = this;
			if(that.myInfo.id && that.showList.length){
				that.showList.forEach((_eItem)=> {
					if(that.myInfo.id == _eItem.userId){
						that.myseat = _eItem;
					}
				});
				that.moveMy();
			}
		},
		offsetUser(){
			setTimeout(()=>{
				var that = this;
				var el = that.$refs.activeRef[0];
				el.scrollIntoView({
					behavior: 'smooth', // 平滑过渡
					block: 'start' // 上边框与视窗顶部平齐。默认值
				})
			},0);
		},
		switchType(){
			var that = this;
			that.btnType = that.btnType == 1?2:1;
			var div = document.getElementById("seatBox");
			if(that.btnType == 2){
				// 获取div的原始宽度和高度
				const originalWidth = div.offsetWidth;
				const originalHeight = div.offsetHeight;
				// 获取屏幕的宽度和高度
				const screenWidth = window.innerWidth;
				const screenHeight = window.innerHeight;
				// 计算宽度和高度的缩放比例
				const widthRatio = screenWidth / originalWidth;
				const heightRatio = screenHeight / originalHeight;
				// 取较小的比例，以确保div不会超出屏幕
				const scaleRatio = Math.min(widthRatio, heightRatio);
				that.transform = scaleRatio+"";
			}else{
				that.transform = "";
				that.moveMy();
			}
		},
		moveMy(){
			var that = this;
			setTimeout(() => {
				var seatBox = document.getElementById("app");
				var myId = document.getElementById("user"+that.myInfo.id);
				if(myId && that.myseat){
					seatBox.scrollLeft = that.myseat.seatX - document.body.offsetWidth/2 + that.myseat.seatW/2;
					seatBox.scrollTop = that.myseat.seatY - (document.body.offsetHeight-73)/2 + that.myseat.seatH/2;
				}
			}, 0);
		},
	};

	window.onload = function() {
		showVue();
	};
	
	function showVue(){
		vm = new Vue({
			el: '#app',
			data: vmData,
			mounted: function () {
				this.init();
			},
			methods: methods
		});
	}
	
</script>
</html>
