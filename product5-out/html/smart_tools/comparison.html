<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0"/>
	<meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
	<title>文本比对</title>
	<link rel="stylesheet" href="../css/vant.css" />
	<style type="text/css">
		[v-cloak] { display: none; }
		html,body,#app{ width: 100%; height: 100%; margin: 0; padding: 0;background-color: #F8FAFB;overflow-y: auto; }
		.pageWarp{ display: flex; flex-direction: column; width: 100%; height: 100%; }
		.van-field__body,.van-cell{height: 100%;}
		textarea {font-size: 14px;line-height: 24px;height:100%;color: #333;}
		.textareaDiv {font-size: 14px;height:100%;overflow-y: auto;color: #333;}
		p{ line-height: 24px;margin-block-start: 0; margin-block-end: 0; }
		.button_box{ display: flex; align-items: center;justify-content: center; padding: 16px; background-color: #FFF; }
		.van-button--normal{ font-size: 16px; height: 38px; width: 140px; }
		.result_warp{ padding: 5px 0; }
		.result_item{ background-color: #FFF; border-radius: 10px; padding: 10px; }
		.van-button--mini{ padding: 0 12px; height: 20px; }

		.h80 .van-popup__close-icon{ position: fixed; top: 20%; right: 0; padding: 11px; background-color: rgba(255,255,255,0.7); border-radius: 0 16px 0 0; }
	</style>
</head>
<body>
	<div v-cloak id="app">
		<div class="pageWarp">
			<div class="pageWarp" style="flex:1;height: 1px;">
				<div style="flex:1;height: 1px;">
					<van-field
						v-model="content"
						rows="16"
						type="textarea"
						placeholder="请输入或者粘贴内容(旧版本)"
						show-word-limit
					/>
				</div>
				<div style="border-bottom: 1px solid #ddd;"></div>
				<div style="flex:1;height: 1px;">
					<van-field
						v-model="content2"
						rows="16"
						type="textarea"
						placeholder="请输入或者粘贴内容(新版本)"
						show-word-limit
					/>
				</div>
				<div class="button_box">
					<van-button :disabled="!content || !content2" :loading="correctionIng" loading-text="比对中..." @click="correction" type="primary" round size="normal" :color="appTheme">一键比对</van-button>
				</div>
			</div>
		</div>

		<van-popup class="h80" v-model:show="resultShow" round position="bottom" closeable :style="{ height: '80%' }">
			<div style="width: 100%;height:34px;text-align: center;"></div>
			<div style="padding: 12px;font-size: 14px;color: #333;" v-html="result"></div>
		</van-popup>
	</div>
</body>
<script type="text/javascript" src="../script/vue.min.js"></script>
<script type="text/javascript" src="../script/vant.min.js"></script>
<script type="text/javascript" src="../script/axios.min.js"></script>
<script type="text/javascript" src="../script/url.js"></script>
<script type="text/javascript" src="../script/api.js"></script>
<script type="text/javascript">
	var datas={};var param=location.href.split('?');if(param.length>1){param=decodeURIComponent(param[1]);var params=param.split("&");if(params){for(var i=0;i<params.length;i++){if(params[i]){var data_key=params[i].substring(0,params[i].indexOf("="));var data_value=params[i].substring(params[i].indexOf("=")+1);if(data_value.indexOf("{")==0||data_value.indexOf("[")==0){data_value=JSON.parse(data_value)}datas[data_key]=data_value}}}};
	var vm,pageParam={};
	var vmData = {
		appUrl:appUrl,
		appTheme:datas.appTheme||"#3657C0",
		token:datas.token,
		areaId:datas.areaId,
		content:"",
		content2:"",
		correctionIng:false,

		resultShow:false,
		result:"",
	};
	var methods = {
		init:function(){
			var that = this;
		},
		convertRichText(value){
			var textList = value.split('\n');
			var str = '';
			for (var i = 0; i < textList.length; i++) {
				var addText = textList[i];
				if (addText) {
					str += '<p>' + addText + '</p>';
				}
			}
			return str;
		},
		correction(){
			var that = this;
			that.correctionIng = true;
			let formData = new FormData();
			formData.append("text1", that.content);
			formData.append("text2", that.content2);
			formData.append("areaCode", that.areaId);
			axios.post(
				`${that.appUrl}wordApi/textContrastWord`,
				formData,
				{
					headers: {
						"u-login-areaId": that.areaId,
						"content-type":"application/x-www-form-urlencoded",
						"Authorization": that.token,
						"u-terminal":"APP",
					} 
				}
			).catch(function (err) {
				that.correctionIng = false;
				alert(JSON.stringify(err));
			}).then(function (ret) {
				ret = ret.data || {};
				that.correctionIng = false;
				if(ret.code == 200){
					that.resultShow = true;
					that.result = (ret.data||{}).html;
				}else{
					alert(ret.message || ret.data);
				}
			});
		},
	};
	


	window.onload = function() {
		showVue();
	};
	
	function showVue(){
		vm = new Vue({
			el: '#app',
			data: vmData,
			mounted: function () {
				this.init();
			},
			methods: methods
		});
	}
	
</script>
</html>