<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0"/>
	<meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
	<title>智能工具</title>
	<link rel="stylesheet" href="../css/<EMAIL>" />
	<style type="text/css">
		[v-cloak] { display: none; }
		html,body,#app{ width: 100%; height: 100%; margin: 0; padding: 0;background-color: #F8FAFB;overflow-y: auto;position: relative; }
		.smart_warp{ padding:20px 18px 0;position: relative; }
		.smart_item{  border-radius: 15px; padding: 18px 20px 18px 20px; display: flex; align-items: center;
			background: rgba(255,255,255,0.8);
			box-shadow: 0px 2px 10px 0px rgba(132,135,139,0.1);
			border-radius: 6px 6px 6px 6px;
			border: 1px solid #FFFFFF;
		}
		.smart_icon{ width:48px; height: auto; }
		.smart_text{ font-size: 18px; color:#333; margin-left: 16px;font-weight: 500;flex:1; }
		.btn_box{
			position: absolute;
			top: 30px;
			right: 0px;
			background: linear-gradient( 180deg, #D9EBF7 0%, #F9FCFF 100%);
			border-radius: 20px 0px 0px 20px;
			border: 1px solid #FFFFFF;
			padding: 6px 10px;
			display: flex; align-items: center;
		}
	</style>
</head>
<body>
	<div v-cloak id="app">
		<!-- <div class="btn_box" @click="smartHelp()">
			<img style="width:20px;margin-right:4px;" :src="`./img/icon_smart_btn.png`" />
			<div style="font-size: 14px;font-weight: 500;color: #1F72DF;">智能工具使用说明</div>
		</div> -->
		<img style="width:100%;height: 268px;object-fit: cover;margin-bottom: -40px;" :src="`${appUrl}pageImg/open/IntelligentToolBackground?Authorization=${token}`" />
		<template v-for="item in listData">
			<div v-if="item.show" class="smart_warp">
				<div @click="openTool(item)" class="smart_item">
					<img class="smart_icon" :src="`./img/icon_smart_${item.id}.png`" />
					<div class="smart_text">{{item.title}}</div>
					<van-icon name="arrow" />
				</div>
			</div>
		</template>
		<div style="height: 30px;"></div>
	</div>
</body>
<script type="text/javascript" src="../script/<EMAIL>"></script>
<script type="text/javascript" src="../script/<EMAIL>"></script>
<script type="text/javascript" src="../script/axios.min.js"></script>
<script type="text/javascript" src="../script/url.js"></script>
<script type="text/javascript" src="./js/aiGeneral.js"></script>
<script type="text/javascript">
	var datas={};var param=location.href.split('?');if(param.length>1){param=decodeURIComponent(param[1]);var params=param.split("&");if(params){for(var i=0;i<params.length;i++){if(params[i]){var data_key=params[i].substring(0,params[i].indexOf("="));var data_value=params[i].substring(params[i].indexOf("=")+1);if(data_value.indexOf("{")==0||data_value.indexOf("[")==0){data_value=JSON.parse(data_value)}datas[data_key]=data_value}}}};
	var vmData = {
		appUrl:appUrl,
		appTheme:datas.appTheme||"#3657C0",
		token:datas.token,
		areaId:datas.areaId,
		listData:[
			{show:true,id:"correction",title:"一键校正",icon:""},
			{show:true,id:"recommend",title:"资料推荐",icon:""},
			{show:true,id:"comparison",title:"文本比对",icon:""},
			{show:false,id:"polish",title:"智能润色",icon:"",new:true},
			{show:false,id:"extension",title:"智能扩写",icon:"",new:true},
			{show:false,id:"summary",title:"智能摘要",icon:"",new:true},
			{show:false,id:"extract",title:"关键词提取",icon:"",new:true},
		]
	};
	var vmWatch = {

	};
	var methods = {
		init:function(){
			var that = this;
			that.ajax(`${that.appUrl}aigptChatScene/list`,
			{
				pageNo:1,pageSize:999,needTools:1,
				query:{
					chatType:null,
				}
			},(ret,err)=>{
				if(ret && ret.code == 200){
					var data = ret.data || [];
					var myRecommend = that.listData.find(item => item.id === "recommend");
					var remoteRecommend = data.find(item => item.chatSceneCode === "ai-zx-meterials-chat");
					if(myRecommend && remoteRecommend){
						if(!remoteRecommend.isUsing){
							myRecommend.closeAI = true;
						}
					}
					var remoteTools = data.find(item => item.chatSceneCode === "test_chat");
					var myPolish = that.listData.find(item => item.id === "polish");
					var myExtension = that.listData.find(item => item.id === "extension");
					var mySummary = that.listData.find(item => item.id === "summary");
					var myExtract = that.listData.find(item => item.id === "extract");
					if(remoteTools){
						var remoteData = remoteTools.tools || [];
						var remotePolish = remoteData.find(item => item.id === "3_ZX");
						var remoteExtension = remoteData.find(item => item.id === "4_ZX");
						var remoteSummary = remoteData.find(item => item.id === "8_ZX");
						var remoteExtract = remoteData.find(item => item.id === "2_ZX");
						if(myPolish && remotePolish){
							myPolish.show = remotePolish.isUsing == 1;
						}
						if(myExtension && remoteExtension){
							myExtension.show = remoteExtension.isUsing == 1;
						}
						if(mySummary && remoteSummary){
							mySummary.show = remoteSummary.isUsing == 1;
						}
						if(myExtract && remoteExtract){
							myExtract.show = remoteExtract.isUsing == 1;
						}
					}
					
				}
			});
		},
		openTool:function(_item){
			var that = this;
			var url = "";
			if(_item.new){
				url = getBaseUrl()+`/aiAssistant.html?def=${_item.id}&token=${encodeURIComponent(that.token||"")}&appTheme=${encodeURIComponent(that.appTheme||"")}&areaId=${that.areaId||""}`;
			}else{
				url = getBaseUrl()+`/${_item.id}.html?token=${encodeURIComponent(that.token||"")}&appTheme=${encodeURIComponent(that.appTheme||"")}&areaId=${that.areaId||""}`;
			}
			if(_item.closeAI){
				url += "&closeAI=true";
			}
			openWin(url,{title:_item.title});
		},
		smartHelp:function(){
			url = `./smartHelp.html`;
		}
	};
</script>
</html>