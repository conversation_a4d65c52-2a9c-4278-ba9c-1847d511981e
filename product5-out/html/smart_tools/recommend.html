<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0"/>
	<meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
	<title>资料推荐</title>
	<link rel="stylesheet" href="../css/<EMAIL>" />
	<style type="text/css">
		[v-cloak] { display: none; }
		html,body,#app{ width: 100%; height: 100%; margin: 0; padding: 0;background-color: #FFF;overflow-y: auto; }
		.van-search__action{ display: flex; align-items: center; justify-content: center; padding: 0 10px; }
		.van-button--small{ height: 32px; padding: 0 15px; }
		.filter_box{ display: flex; flex-flow: row; overflow-x: auto; padding: 0 5px; }
		.filter_item{ padding: 2px; }
		.filter_warp{ width: 100%; padding: 5px 3px; border: 1px solid #BDBDBD; border-radius: 5px; display: flex; flex-flow: row; align-items: center; box-sizing: border-box; }
		.filter_item2{ min-width:67px; padding: 5px 3px; display: flex; flex-flow: row; align-items: center; box-sizing: border-box; }
		.single_text { white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }

		.assistant_box{ position:fixed; bottom: 50px; left:3%; right:3%; width: 94%; padding: 8px; box-sizing: border-box; background-color: #FFF; border-radius: 12px; box-shadow: rgba(0, 0, 0, 0.15) 0px 0px 5px 2px; }
		.h80 .van-popup__close-icon{ position: fixed; top: 20%; right: 0; padding: 11px; background-color: rgba(255,255,255,0.7); border-radius: 0 16px 0 0; }
	</style>
</head>
<body>
	<div v-cloak id="app">
		<van-search v-model="search" placeholder="请输入搜索关键词" show-action @search="onSearch">
			<template #action>
				<van-button @click="onSearch" round hairline size="small" :color="appTheme">搜索</van-button>
			</template>
		</van-search>
		<div :style="`color:${appTheme};padding:5px 8px;font-size:14px;`">以下数据来源于中国司法大数据研究院（最高人民法院信息中心下属研究院） 共 {{listLength}} 条</div>
		<div class="filter_box">
			<div @click="effecLevelShow = true;" class="filter_item" style="min-width:70px;">
				<div class="filter_warp">
					<div class="single_text" :style="`color:#${effecLevelId?'333':'888'};font-size:11px;flex:1;text-overflow:clip;`">{{effecLevelText || '效力级别'}}</div>
					<van-icon @click.stop="if(effecLevelId){effecLevelId='';effecLevelText='';getData();}else{effecLevelShow = true;}" size="12px" color="#888" :name="effecLevelId?'close':'arrow-down'" />
				</div>
			</div>
			<div @click="publishOfficeShow = true;" class="filter_item" style="min-width: 70px;">
				<div class="filter_warp">
					<div class="single_text" :style="`color:#${publishOffice?'333':'888'};font-size:11px;flex:1;text-overflow:clip;`">{{publishOfficeText || '发布机构'}}</div>
					<van-icon @click.stop="if(publishOffice){publishOffice='';publishOfficeText='';getData();}else{publishOfficeShow = true;}" size="12px" color="#888" :name="publishOffice?'close':'arrow-down'" />
				</div>
			</div>
			<div @click="timeLinessShow = true;" class="filter_item" style="min-width: 63px;">
				<div class="filter_warp">
					<div class="single_text" :style="`color:#${timeLiness?'333':'888'};font-size:11px;flex:1;text-overflow:clip;`">{{timeLinessText || '时效性'}}</div>
					<van-icon @click.stop="if(timeLiness){timeLiness='';timeLinessText='';getData();}else{timeLinessShow = true;}" size="12px" color="#888" :name="timeLiness?'close':'arrow-down'" />
				</div>
			</div>
			<div @click="switchTime('publish_date_time')" class="filter_item2">
				<div :style="`color:#333;font-size:11px;flex:1;white-space: nowrap; overflow: hidden;`">发布日期</div>
				<div style="display: flex;flex-flow: column;">
					<van-icon @click.stop="switchTime('publish_date_time','asc')" size="12px" style="transform: rotate(-90deg);margin-bottom: -3px;" :color="orderBy=='publish_date_time'&&direction=='asc'?appTheme:'#888'" name="play" ></van-icon>
					<van-icon @click.stop="switchTime('publish_date_time','desc')" size="12px" style="transform: rotate(90deg);margin-top: -2px;margin-left: -2px;" :color="orderBy=='publish_date_time'&&direction=='desc'?appTheme:'#888'" name="play" ></van-icon>
				</div>
			</div>
			<div @click="switchTime('implement_date')" class="filter_item2">
				<div :style="`color:#333;font-size:11px;flex:1;white-space: nowrap; overflow: hidden;`">实施日期</div>
				<div style="display: flex;flex-flow: column;">
					<van-icon @click.stop="switchTime('implement_date','asc')" size="12px" style="transform: rotate(-90deg);margin-bottom: -3px;" :color="orderBy=='implement_date'&&direction=='asc'?appTheme:'#888'" name="play" ></van-icon>
					<van-icon @click.stop="switchTime('implement_date','desc')" size="12px" style="transform: rotate(90deg);margin-top: -2px;margin-left: -2px;" :color="orderBy=='implement_date'&&direction=='desc'?appTheme:'#888'" name="play" ></van-icon>
				</div>
			</div>
		</div>

		<van-pull-refresh v-model="refreshing" @refresh="getData(0)">
			<van-list
				v-model:loading="listLoading"
				:finished="listFinished"
				:finished-text="listFinishedText"
				@load="getData(1)"
			>
				<div v-for="item in listData" @click="openItem(item)" style="padding: 12px 10px;">
					<div :style="`color:#101010;font-size:16px;`" v-html="item.articleTitle"></div>
					<div :style="`color:#888;font-size:12px;margin-top:5px;`">{{item.statuteInfo.timeLinessName}} | {{item.statuteInfo.publishOfficeName}} | {{item.statuteInfo.publishNum?item.statuteInfo.publishNum+' | ':''}}{{item.statuteInfo.publishDate+'发布'}} | {{item.statuteInfo.implementDate+'实施'}}</div>
				</div>
			</van-list>
		</van-pull-refresh>

		<!-- <div @click="openAI()" class="assistant_box">
			<div style="display: flex;flex-flow: row;align-items: center;">
				<img style="width: auto;height: 25px;" :src="`./img/icon_IntelligentAssistant.gif`" alt="" />
				<div class="single_text" :style="`flex:1;margin-left:10px;color:#101010;font-size:12px;color:#${assistantHintText?'333':'bbb'};`" v-html="assistantHintText || '嗨！我是智能小助手，为您推荐~'"></div>
			</div>
		</div> -->


		<van-popup v-model:show="effecLevelShow" round position="bottom">
			<van-cascader
				v-model="effecLevelId"
				title="请选择效力级别"
				:options="effecLevelData"
				@close="effecLevelShow = false"
				@finish="effecLevelFinish"
				@change="effecLevelChange"
			>
			<template #options-top>
				<van-search
				  v-model="effecLevelIdSearch"
				  placeholder="请输入搜索关键词"
				/>
			</template>
			</van-cascader>
		</van-popup>
		<van-popup v-model:show="timeLinessShow" round position="bottom">
			<van-cascader
				v-model="timeLiness"
				title="请选择时效性"
				:options="timeLinessData"
				@close="timeLinessShow = false"
				@finish="timeLinessFinish"
				@change="timeLinessChange"
			/>
		  </van-popup>
		<van-popup v-model:show="publishOfficeShow" round position="bottom">
			<van-cascader
				v-model="publishOffice"
				title="请选择发布机构"
				:options="publishOfficeData"
				@close="publishOfficeShow = false"
				@finish="publishOfficeFinish"
				@change="publishOfficeChange"
			>
			<template #options-top>
				<van-search
				  v-model="publishOfficeSearch"
				  placeholder="请输入搜索关键词"
				/>
			</template>
			</van-cascader>
		  </van-popup>

		<van-popup class="h80" v-model:show="details" round position="bottom" closeable :style="{ height: '80%' }">
			<template v-if="detailsData">
				<div style="width: 100%;height:34px;text-align: center;"></div>
				<div style="padding: 12px;font-size: 16px;color: #101010;text-align: center;">{{detailsData.article.articleTitle}}</div>
				<div style="padding: 12px;font-size: 14px;color: #333;" v-html="detailsData.article.articleContent"></div>
			  </template>
		  </van-popup>

		<!-- <van-popup id="assistant" class="h80" v-model:show="assistantShow" round position="bottom" closeable :style="{ height: '80%' }">
			<div style="position: fixed;top: 20%;width: 100%;line-height:44px;text-align: center;border-bottom: 1px solid #efefef;background-color: #FFF;border-radius: 16px 16px 0 0;">智能小助手为您推荐</div>
			<div id="assistantBody">
				<div style="width: 100%;height:44px;"></div>
				<div v-for="item in assistantData" @click="openAssociation(item)" :style="`margin-top:${item.type=='association'?10:20}px;padding: 0 10px;display: flex;flex-flow: ${item.type=='send'?'row-reverse':'row'};`">
					<div v-if="item.type!='send'" style="width:35px;height: 25px;" >
						<img v-if="item.type=='receive'" style="width: auto;height: 25px;" :src="`./img/icon_IntelligentAssistant.gif`" alt="" />
					</div>
	
					<div v-if="item.text" :style="`padding:6px 10px;box-sizing: border-box;max-width: calc(100% - 50px);border-radius: 8px;white-space: pre-wrap;word-wrap: break-word;background-color:${item.type=='send'?'rgba(54, 87, 192, 0.1)':'rgb(240, 240, 240)'};font-size:${item.type=='association'?'14':'16'}px;color:${item.type=='association'?appTheme:'#333'};`" v-html="item.text"></div>
					<div v-else>
						<van-loading />
					</div>
				</div>
				<div style="height: 74px;"></div>
			</div>
			<div style="position: fixed;bottom:0;width: 100%;">
				<van-search v-model="assistantSearch" placeholder="请输入" show-action @search="getAssociationData">
					<template #left-icon><div></div></template>
					<template #action>
						<van-button @click="getAssociationData();" round hairline size="small" :color="appTheme">发送</van-button>
					</template>
				</van-search>
			</div>
		</van-popup> -->

		  <van-popup style="width: 100%;height: 100%;background-color: rgba(255,255,255,0.6);" v-model:show="loading" :overlay="false" :close-on-click-overlay="false">
				<div style="width: 100%;height: 100%;display: flex;align-items: center;justify-content: center;">
					<van-loading size="34px"/>
				</div>
		  </van-popup>
	</div>
</body>
<script type="text/javascript" src="../script/<EMAIL>"></script>
<script type="text/javascript" src="../script/<EMAIL>"></script>
<script type="text/javascript" src="../script/axios.min.js"></script>
<script type="text/javascript" src="../script/url.js"></script>
<script type="text/javascript" src="../script/jquery-3.6.4.min.js"></script>
<script type="text/javascript">
	var datas={};var param=location.href.split('?');if(param.length>1){param=decodeURIComponent(param[1]);var params=param.split("&");if(params){for(var i=0;i<params.length;i++){if(params[i]){var data_key=params[i].substring(0,params[i].indexOf("="));var data_value=params[i].substring(params[i].indexOf("=")+1);if(data_value.indexOf("{")==0||data_value.indexOf("[")==0){data_value=JSON.parse(data_value)}datas[data_key]=data_value}}}};
	var vm,pageParam={};
	var vmData = {
		appUrl:appUrl,
		appTheme:datas.appTheme||"#3657C0",
		token:datas.token,
		areaId:datas.areaId,
		search:"",
		assistantSearch:"",

		effecLevelText:"",
		effecLevelId:"",//效力级别
		effecLevelIdSearch:"",

		timeLinessText:"",
		timeLiness:"",//时效性

		publishOfficeText:"",
		publishOffice:"",
		publishOfficeSearch:"",

		orderBy:"publish_date_time",//排序  发布publish_date_time 实施implement_date
		direction:"desc",//正asc 倒desc

		effecLevelShow:false,
		effecLevelData:[],

		timeLinessShow:false,
		timeLinessData:[],

		publishOfficeShow:false,
		publishOfficeData:[],

		listData:[],

		loading:false,

		refreshing:false,
		listLoading:false,
		listFinished:false,
		listFinishedText:"",
		pageNo:1,
		listLength:0,

		details:false,
		detailsData:null,

		assistantShow:false,
		assistantHintText:"",
		assistantData:[
			// {type:"send",text:"劳动法"},
			// {type:"receive",text:"劳动法是XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXx"},
			// {type:"association",text:"你还想问？ ➔"},


		],
	};
	window.effecLevelData = [];
	window.publishOfficeData = [];
	var vmWatch = {
		assistantShow(_value){
			var that = this;
			if(_value){
				setTimeout(() => {
					that.assistantSearch = "";
					that.goButtom();
					function stop(){
						window.dotScroll = true;
						$("#assistant").stop();
						clearTimeout(window.dotTask);
						window.dotTask = setTimeout(() => {
							window.dotScroll = false;
						}, 2000);
					}
					$("#assistantBody").on("touchstart", stop);
					$("#assistantBody").on("touchmove", stop);
					$("#assistantBody").on("touchend", stop);
					$("#assistantBody").on("click", stop);
					$("#assistantBody").on("wheel", stop);
				}, 0);
			}
		},
		effecLevelIdSearch(_value){
			var that = this;
			if(_value){
				that.effecLevelData = that.getSearchData(_value,window.effecLevelData.concat([]),0);
			}else{
				that.effecLevelData = window.effecLevelData.concat([]);
			}
		},
		publishOfficeSearch(_value){
			var that = this;
			if(_value){
				that.publishOfficeData = that.getSearchData(_value,window.publishOfficeData.concat([]),0);
			}else{
				that.publishOfficeData = window.publishOfficeData.concat([]);
			}
		}
	};
	var methods = {
		openAI(){
			var that = this;
			window.location = `./aiAssistant.html?def=recommend&token=${encodeURIComponent(that.token||"")}&appTheme=${encodeURIComponent(that.appTheme||"")}&areaId=${that.areaId||""}`;
		},
		init:function(){
			var that = this;
			
			// that.getData();
		},
		onSearch(){
			var that = this;
			if(that.search && !that.assistantShow){
				that.assistantShow = true;
				that.assistantSearch = that.search;
			}
			if(that.assistantShow){
				window.dotScroll = false;
			}
			
			that.getData();
			that.getAssociationData();
		},
		effecLevelChange({ selectedOptions }){
			var that = this;
			that.effecLevelText = selectedOptions[selectedOptions.length-(selectedOptions.length>1&&selectedOptions[selectedOptions.length-1].text=="全部"?2:1)].text;
		},
		effecLevelFinish({ selectedOptions }){
			var that = this;
			that.effecLevelShow = false;
			that.getData();
		},
		timeLinessChange({ selectedOptions }){
			var that = this;
			that.timeLinessText = selectedOptions[selectedOptions.length-(selectedOptions.length>1&&selectedOptions[selectedOptions.length-1].text=="全部"?2:1)].text;
		},
		timeLinessFinish({ selectedOptions }){
			var that = this;
			that.timeLinessShow = false;
			that.getData();
		},
		publishOfficeChange({ selectedOptions }){
			var that = this;
			that.publishOfficeText = selectedOptions[selectedOptions.length-(selectedOptions.length>1&&selectedOptions[selectedOptions.length-1].text=="全部"?2:1)].text;
		},
		publishOfficeFinish({ selectedOptions }){
			var that = this;
			that.publishOfficeShow = false;
			that.getData();
		},
		switchTime(_type,_sort){
			var that = this;
			console.log(_type + ":" + _sort);
			that.orderBy = _type;
			that.direction = _sort || (that.orderBy == _type?(that.direction == "asc"?"desc":"asc"):"desc");
			that.getData();
		},
		getTimeLiness(){
			var that = this;
			axios.post(
				`${that.appUrl}hadoop_api/datax/lawsees/timeLiness`,
				{
					articleTitle:that.search,
					effecLevelId:that.effecLevelId,
					timeLiness:that.timeLiness,
					publishOfficeId:that.publishOffice,
				},
				{
					headers: {
						"u-login-areaId": that.areaId,
						"content-type":"application/json",
						"Authorization": that.token,
						"u-terminal":"APP",
					} 
				}
			).catch(function (err) {
			}).then(function (ret) {
				ret = ret.data || {};
				if(ret.code == 200){
					var data = ret.data||[];
					that.timeLinessData = that.getDealData(data,0);
				}
			});
		},
		getPublishOffice(){
			var that = this;
			axios.post(
				`${that.appUrl}hadoop_api/datax/lawsees/publishOffice`,
				{
					articleTitle:that.search,
					effecLevelId:that.effecLevelId,
					timeLiness:that.timeLiness,
					publishOfficeId:that.publishOffice,
				},
				{
					headers: {
						"u-login-areaId": that.areaId,
						"content-type":"application/json",
						"Authorization": that.token,
						"u-terminal":"APP",
					} 
				}
			).catch(function (err) {
			}).then(function (ret) {
				ret = ret.data || {};
				if(ret.code == 200){
					var data = ret.data||[];
					window.publishOfficeData = that.getDealData(data,0);
					if(that.publishOfficeSearch){
						that.publishOfficeData = that.getSearchData(that.publishOfficeSearch,window.publishOfficeData.concat([]),0);
					}else{
						that.publishOfficeData = window.publishOfficeData.concat([]);
					}
				}
			});
		},
		getEffecLevel(){
			var that = this;
			axios.post(
				`${that.appUrl}hadoop_api/datax/lawsees/effecLevel`,
				{
					articleTitle:that.search,
					effecLevelId:that.effecLevelId,
					timeLiness:that.timeLiness,
					publishOfficeId:that.publishOffice,
				},
				{
					headers: {
						"u-login-areaId": that.areaId,
						"content-type":"application/json",
						"Authorization": that.token,
						"u-terminal":"APP",
					} 
				}
			).catch(function (err) {
			}).then(function (ret) {
				ret = ret.data || {};
				if(ret.code == 200){
					var data = ret.data||[];
					window.effecLevelData = that.getDealData(data,0);
					if(that.effecLevelIdSearch){
						that.effecLevelData = that.getSearchData(that.effecLevelIdSearch,window.effecLevelData.concat([]),0);
					}else{
						that.effecLevelData = window.effecLevelData.concat([]);
					}
				}
			});
		},
		getDealData(_list,_level){
			var that = this;
			var nlist = [];
			_list.forEach((_item,_index)=>{
				var item = {};
				item.text = _item.dictLabel;
				item.value = _item.dictCode;
				if(_item.children && _item.children.length){
					_item.children.unshift({dictLabel:"全部",dictCode:item.value});
					item.children = that.getDealData(_item.children||[],1);
				}
				nlist.push(item);
			});
			return nlist;
		},
		getSearchData(_text,_list,_level){
			var that = this;
			var nlist = [];
			_list.forEach((_item,_index)=>{
				var item = {};
				item.text = _item.text;
				item.value = _item.value;
				if(item.text.indexOf(_text) != -1){
					nlist.push(item);
				}
				if(_item.children && _item.children.length){
					nlist = nlist.concat(that.getSearchData(_text,_item.children||[],1));
				}
			});
			return nlist;
		},
		getData(_type){
			var that = this;
			that.getEffecLevel();
			that.getTimeLiness();
			that.getPublishOffice();
			if(!that.assistantShow){
				that.loading = true;
			}
			if(!_type){
				that.listFinished = false;
				that.listLoading = true;
				that.pageNo = 1;
			}
			axios.post(
				`${that.appUrl}hadoop_api/datax/lawsees/list`,
				{
					articleTitle:that.search,
					effecLevelId:that.effecLevelId,
					timeLiness:that.timeLiness,
					publishOfficeId:that.publishOffice,
					orderBy:that.orderBy,
					direction:that.direction,
					page:that.pageNo,
					size:20,
				},
				{
					headers: {
						"u-login-areaId": that.areaId,
						"content-type":"application/json",
						"Authorization": that.token,
						"u-terminal":"APP",
					} 
				}
			).catch(function (err) {
				that.refreshing = false;
				that.listLoading = false;
				that.loading = false;
				that.listFinished = true;
				that.listFinishedText = JSON.stringify(err);
			}).then(function (ret) {
				ret = ret.data || {};
				that.refreshing = false;
				that.listLoading = false;
				that.loading = false;
				if(ret.code == 200){
					var data = ret.data || {};
					var nData = data.content || [];
					that.listLength = data.totalElements || 0;
					if(nData && nData.length){
						if(!_type){
							that.listData = data.content;
						}else{
							that.listData = that.listData.concat(data.content);
						}
						if(that.pageNo >= data.totalPages){
							that.listFinished = true;
							that.listFinishedText = "没有更多了";
						}
						that.pageNo++;
					}else{
						if(!_type){
							that.listData = [];
						}
						that.listFinished = true;
						that.listFinishedText = "没有更多了";
					}
				}else{
					that.listFinished = true;
					that.listFinishedText = ret.message || ret.data || JSON.stringify(ret);
				}
			});
		},
		openItem(_item){
			var that = this;
			that.loading = true;
			axios.post(
				`${that.appUrl}hadoop_api/datax/lawsees/info`,
				{
					articleId:_item.articleId
				},
				{
					headers: {
						"u-login-areaId": that.areaId,
						"content-type":"application/json",
						"Authorization": that.token,
						"u-terminal":"APP",
					} 
				}
			).catch(function (err) {
				that.loading = false;
				alert(JSON.stringify(err));
			}).then(function (ret) {
				ret = ret.data || {};
				that.loading = false;
				if(ret.code == 200){
					var data = ret.data || {};
					that.detailsData = data;
					that.details = true;
				}else{
					alert(ret.message || ret.data);
				}
			});
		},
		getAssociationData(){
			var that = this;
			if(!that.assistantSearch){
				return;
			}
			that.assistantData.push({type:"send",text:that.assistantSearch});
			setTimeout(() => {
				that.assistantSearch = '';
			}, 0);
			that.goButtom();
			var receiveBody = {type:"receive",text:""};
			that.assistantData.push(receiveBody);
			that.goButtom();
			window.showText = "";
			window.isTask = false;
			window.showAssociation = [];
			if(window.controller){
				try{
					window.controller.abort();
				}catch(e){
					console.log(e);
				}
			}
			window.controller = new AbortController();
			fetch(
				`${that.appUrl}chat/intelligentStream`,
				{
					method: 'POST',
					headers: {
						'Accept': 'text/event-stream',
						'Content-Type': 'application/json',
						"u-login-areaId": that.areaId,
						"Authorization": that.token,
						"u-terminal":"APP",
					},
					body: JSON.stringify({
						content:that.assistantSearch,
						bot_type:"5"
					}),
					signal: window.controller.signal,  // 传递 signal
				}
			).catch(function (err) {
				console.log("abc:"+err.name);
				receiveBody.text = err.name == "TypeError"?"网络异常":" ";
				that.goButtom();
			}).then(function (response) {
				if(!response)return;
				const reader = response.body.getReader();  // 获取流的读取器
				const decoder = new TextDecoder();  // 用于解码字节数据
				let buffer = '';
				function showTextTask(){
					if(window.showText.length){
						window.isTask = true;
						let firstChar = window.showText.charAt(0);
						receiveBody.text += firstChar;
						receiveBody.text = receiveBody.text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
						that.assistantHintText = receiveBody.text;
						that.goButtom();
						window.showText = window.showText.slice(1);
						setTimeout(() => {
							showTextTask();
						}, 30);
					}else{
						window.isTask = false;
						showAssociationTask();
					}
				}
				function showAssociationTask(){
					if(window.showAssociation.length){
						let firstElement = window.showAssociation.shift();
						that.assistantData.push({type:"association",text:firstElement + " ➔",oldText:firstElement});
						that.goButtom();
						setTimeout(() => {
							showAssociationTask();
						}, 100);
					}
				}
				// 逐步处理流数据
				reader.read().then(function processText({ done, value }) {
					if (done) {
						return;
					}
					buffer += decoder.decode(value, { stream: true });
					// 根据换行符分割事件数据
					const lines = buffer.split('\n\n');
					if(lines.length == 1 && lines[0].startsWith('{')){
						var data = JSON.parse(lines[0]);
						window.showText += data.message || data.data;
						if(!window.isTask){
							showTextTask();
						}
					}
					for (let i = 0; i < lines.length - 1; i++) {
						const line = lines[i];
						if (line.startsWith('data:')) {
							try{
								const eventData = JSON.parse(line.substring(5).trim() || "{}");
								if(eventData.event == "conversation.message.delta"){
									window.showText += eventData.data.content;
									if(!window.isTask){
										showTextTask();
									}
								}else if(eventData.event == "conversation.message.completed" && eventData.data.type == "follow_up"){
									window.showAssociation.push(eventData.data.content);
									if(!window.isTask){
										showAssociationTask();
									}
								}
							}catch(e){
								console.log(e)
							}
						}
					}
					buffer = lines[lines.length - 1];
					reader.read().then(processText);
				});
			});
		},
		goButtom(){
			try{
				if($("#assistant") && !window.dotScroll){
					$("#assistant").stop();
					$("#assistant").animate({scrollTop:document.getElementById("assistant").scrollHeight},200);
				}
			}catch(e){

			}
		},
		openAssociation(_item){
			var that = this;
			if(_item.type == "association"){
				window.dotScroll = false;
				that.assistantSearch = _item.oldText;
				that.getAssociationData();
			}
		}
	};

	window.onload = function() {
		showVue();
	};
	
	function showVue(){
		// 创建Vue应用
		const { createApp } = Vue;
		const app = createApp({
			data: ()=>vmData,
			watch:vmWatch,
			mounted: function () {
				document.documentElement.style.setProperty('--van-primary-color', this.appTheme);
				this.init();
			},
			methods: methods
		});
		// 使用Vant组件
		app.use(vant);
		// 挂载Vue应用
		app.mount('#app');
	}
	
</script>
</html>