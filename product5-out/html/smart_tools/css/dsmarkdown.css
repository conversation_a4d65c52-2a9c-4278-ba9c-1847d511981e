
body,page,.ds-theme {
    --ds-input-height-l: 42px;
    --ds-input-height-m: 34px;
    --ds-input-height-s: 30px;
    --ds-input-height-xs: 26px;
    --ds-font-weight-strong: 600;
    --ds-font-size-l: 16px;
    --ds-line-height-l: 28px;
    --ds-font-size-m: 14px;
    --ds-line-height-m: 25px;
    --ds-font-size-sp: 13px;
    --ds-line-height-sp: 23px;
    --ds-font-size-s: 12px;
    --ds-line-height-s: 21px;
    --ds-font-size-xsp: 11px;
    --ds-line-height-xsp: 19.5px;
    --ds-font-size-xs: 10px;
    --ds-line-height-xs: 18px;
    --ds-ease-in-out: cubic-bezier(.4,0,.2,1);
    --ds-ease-in: cubic-bezier(.4,0,1,1);
    --ds-ease-out: cubic-bezier(0,0,.2,1);
    --ds-font-family-code: <PERSON><PERSON>,"Roboto Mono","Courier New",Courier,monospace,"Inter",sans-serif;
    --ds-transition-duration: .2s;
    --ds-transition-duration-fast: .1s;
    --ds-transition-duration-slow: .3s
}

body {
    font-size: var(--ds-font-size-m);
    line-height: var(--ds-line-height-m);
    color: rgb(var(--ds-rgb-label-1));
    -webkit-text-size-adjust: none;
    margin: 0
}

.ds-markdown {
    --ds-md-zoom: 1.143;
    --ds-md-font-size: calc(var(--ds-md-zoom)*var(--ds-font-size-m));
    --ds-md-line-height: calc(var(--ds-md-zoom)*var(--ds-line-height-m));
    font-size: var(--ds-md-font-size);
    min-height: var(--ds-md-font-size);
    line-height: var(--ds-md-line-height)
}

.ds-markdown img {
    max-width: 100%
}

.ds-markdown h1,.ds-markdown h2,.ds-markdown h3,.ds-markdown h4,.ds-markdown h5,.ds-markdown h6 {
    font-weight: var(--ds-font-weight-strong);
    font-size: var(--ds-md-font-size);
    line-height: var(--ds-md-line-height);
    margin: calc(var(--ds-md-zoom)*16px)0 calc(var(--ds-md-zoom)*12px)0
}

.ds-markdown h1 {
    font-size: calc(var(--ds-md-zoom)*24px);
    line-height: 1.5
}

.ds-markdown h2 {
    font-size: calc(var(--ds-md-zoom)*20px);
    line-height: 1.5
}

.ds-markdown h3 {
    font-size: calc(var(--ds-md-zoom)*16px);
    line-height: 1.5
}

.ds-markdown h1 .header-anchor,.ds-markdown h2 .header-anchor,.ds-markdown h3 .header-anchor,.ds-markdown h4 .header-anchor,.ds-markdown h5 .header-anchor,.ds-markdown h6 .header-anchor {
    opacity: 0;
    margin-left: 4px
}

.ds-markdown h1:hover .header-anchor,.ds-markdown h2:hover .header-anchor,.ds-markdown h3:hover .header-anchor,.ds-markdown h4:hover .header-anchor,.ds-markdown h5:hover .header-anchor,.ds-markdown h6:hover .header-anchor {
    opacity: 1
}

.ds-markdown p {
    margin: calc(var(--ds-md-zoom)*12px)0;
    font-size: var(--ds-md-font-size);
    line-height: var(--ds-md-line-height)
}

.ds-markdown a:not(.ds-a) {
    color: var(--van-primary-color);
    transition: box-shadow var(--ds-transition-duration)var(--ds-ease-in-out);
    border-radius: calc(var(--ds-md-zoom)*6px);
    text-decoration: none;
    position: relative
}

.ds-markdown a:not(.ds-a):focus {
    outline: none
}

.ds-markdown a:not(.ds-a):focus-visible {
    box-shadow: 0 0 0 2px rgb(var(--ds-rgb-primary))
}

.ds-markdown li>ul,.ds-markdown li>ol {
    margin-top: 4px
}

.ds-markdown ul,.ds-markdown ol {
    margin: calc(var(--ds-md-zoom)*12px)0;
    padding-left: calc(var(--ds-md-zoom)*12px);
}

.ds-markdown li:not(:first-child) {
    margin-top: 4px
}

.ds-markdown li::marker {
    line-height: var(--ds-md-line-height);
    color: rgb(var(--ds-rgb-label-2))
}

.ds-markdown hr {
    height: 1px;
    margin: calc(var(--ds-md-zoom)*12px)0;
    background: rgb(var(--ds-rgb-label-3));
    border: none;
    display: block
}

.ds-markdown blockquote {
    border-left: 2px solid rgb(var(--ds-rgb-label-3));
    padding-left: calc(var(--ds-md-zoom)*16px);
    margin: 0
}

.ds-markdown table {
    border-collapse: collapse
}

.ds-markdown th {
    color: rgb(var(--ds-rgb-label-1));
    padding: calc(var(--ds-md-zoom)*6px)calc(var(--ds-md-zoom)*12px);
    border-bottom: 1px solid rgb(var(--ds-rgb-label-3));
    border-top: 1px solid rgb(var(--ds-rgb-label-3));
    font-weight: 600
}

.ds-markdown th:not(:-webkit-any(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi))) {
    text-align: left
}

.ds-markdown th:not(:-moz-any(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi))) {
    text-align: left
}

.ds-markdown th:not(:is(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi))) {
    text-align: left
}

.ds-markdown th:-webkit-any(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi)) {
    text-align: right
}

.ds-markdown th:-moz-any(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi)) {
    text-align: right
}

.ds-markdown th:is(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi)) {
    text-align: right
}

.ds-markdown th:first-child {
    padding-left: 0
}

.ds-markdown td {
    padding: calc(var(--ds-md-zoom)*6px)calc(var(--ds-md-zoom)*12px);
    border-bottom: 1px solid rgb(var(--ds-rgb-label-3))
}

.ds-markdown td:first-child {
    padding-left: 0
}

.ds-markdown pre {
    margin: calc(var(--ds-md-zoom)*12px)0;
    font-family: var(--ds-font-family-code);
    overflow: auto
}

.ds-markdown code {
    font-size: .875em;
    font-weight: var(--ds-font-weight-strong);
    font-family: var(--ds-font-family-code);
    background-color: var(--ds-md-inline-code-color,#ececec);
    border-radius: 4px;
    padding: .15rem .3rem
}

.ds-markdown code:before {
    margin-left: 4px
}

.ds-markdown code:after {
    margin-right: 4px
}

.ds-markdown-math {
    text-align: center;
    display: block;
    overflow-x: auto;
    overflow-y: hidden
}

.ds-markdown>* .ds-markdown-math {
    margin: calc(var(--ds-md-zoom)*12px)0
}

.ds-markdown>* .ds-markdown-math:first-child {
    margin-top: 0
}

.ds-markdown>* .ds-markdown-math:last-child {
    margin-bottom: 0
}

.ds-markdown-code-copy-button {
    background-color: rgba(var(--ds-rgba-transparent));
    color: inherit;
    cursor: pointer;
    border: none;
    margin: 0;
    padding: 0
}

.ds-markdown li>p {
    margin: 4px 0
}

.ds-markdown li>:first-child {
    margin-top: 0
}

.ds-markdown li>:last-child {
    margin-bottom: 0
}

.ds-markdown p:last-child {
    /* margin-bottom:0!important; */
}

.ds-markdown>:first-child {
    margin-top: 0!important
}

.ds-markdown>:last-child {
    margin-bottom: 0!important;
}

.ds-markdown.ds-markdown--page h1 {
    font-size: calc(var(--ds-md-zoom)*24px);
    margin-top: calc(var(--ds-md-zoom)*32px);
    line-height: calc(var(--ds-md-zoom)*32px);
    margin-bottom: calc(var(--ds-md-zoom)*20px)
}

.ds-markdown.ds-markdown--page h2 {
    font-size: calc(var(--ds-md-zoom)*20px);
    margin-top: calc(var(--ds-md-zoom)*26px);
    line-height: calc(var(--ds-md-zoom)*26px);
    margin-bottom: calc(var(--ds-md-zoom)*16px)
}

.ds-markdown.ds-markdown--page h3 {
    font-size: calc(var(--ds-md-zoom)*18px);
    margin-top: calc(var(--ds-md-zoom)*22px);
    line-height: calc(var(--ds-md-zoom)*22px);
    margin-bottom: calc(var(--ds-md-zoom)*14px)
}

.ds-markdown.ds-markdown--page h4 {
    font-size: calc(var(--ds-md-zoom)*16px);
    margin-top: calc(var(--ds-md-zoom)*20px);
    line-height: calc(var(--ds-md-zoom)*20px);
    margin-bottom: calc(var(--ds-md-zoom)*12px)
}

.ds-markdown.ds-markdown--page>p,.ds-markdown.ds-markdown--page>pre,.ds-markdown.ds-markdown--page>.md-code-block {
    margin: calc(var(--ds-md-zoom)*16px)0 calc(var(--ds-md-zoom)*23px)0
}

.ds-markdown.ds-markdown--page hr {
    margin: calc(var(--ds-md-zoom)*18px)0
}

.ds-markdown.ds-markdown--page th {
    color: rgb(var(--ds-rgb-label-1));
    padding: calc(var(--ds-md-zoom)*8px)calc(var(--ds-md-zoom)*10px);
    border-bottom: 1px solid rgb(var(--ds-rgb-separator));
    border-top: 1px solid rgb(var(--ds-rgb-separator))
}

.ds-markdown.ds-markdown--page th:not(:-webkit-any(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi))) {
    text-align: left
}

.ds-markdown.ds-markdown--page th:not(:-moz-any(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi))) {
    text-align: left
}

.ds-markdown.ds-markdown--page th:not(:is(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi))) {
    text-align: left
}

.ds-markdown.ds-markdown--page th:-webkit-any(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi)) {
    text-align: right
}

.ds-markdown.ds-markdown--page th:-moz-any(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi)) {
    text-align: right
}

.ds-markdown.ds-markdown--page th:is(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi)) {
    text-align: right
}

.ds-markdown.ds-markdown--page td {
    padding: calc(var(--ds-md-zoom)*8px)calc(var(--ds-md-zoom)*10px);
    border-bottom: 1px solid rgb(var(--ds-rgb-separator))
}

.ds-markdown.ds-markdown--page td:first-child,.ds-markdown.ds-markdown--page th:first-child {
    padding-left: 0
}

.ds-markdown.ds-markdown--page td:last-child,.ds-markdown.ds-markdown--page th:last-child {
    padding-right: 0
}

.ds-markdown-html {
    font-size: .875em;
    font-family: var(--ds-font-family-code)
}

.ds-markdown-cite {
    vertical-align: middle;
    font-variant: tabular-nums;
    box-sizing: border-box;
    color: #404040;
    cursor: pointer;
    background: #e5e5e5;
    border-radius: 9px;
    flex-shrink: 0;
    justify-content: center;
    align-items: center;
    height: 18px;
    margin-left: 4px;
    padding: 0 6px;
    font-size: 12px;
    font-weight: 400;
    display: inline-flex;
    position: relative;
    top: -2px
}

[data-ds-dark-theme] .ds-markdown-cite {
    color: #f8faff;
    background: #52525b
}
