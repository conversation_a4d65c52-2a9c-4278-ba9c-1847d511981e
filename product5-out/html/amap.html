<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0,viewport-fit=cover"/>
		<meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
		<title></title>
		<style>
			html,body{padding:0;margin: 0;width:100%;height:100%;}
		</style>
	</head>
<body id="mapContainer"></body>
<script type="text/javascript">
	//接收页面参数
	var datas={};var param=location.href.split('?');if(param.length>1){param=decodeURIComponent(param[1]);var params=param.split("&");if(params){for(var i=0;i<params.length;i++){if(params[i]){var data_key=params[i].substring(0,params[i].indexOf("="));var data_value=params[i].substring(params[i].indexOf("=")+1);if(data_value.indexOf("{")==0||data_value.indexOf("[")==0){data_value=JSON.parse(data_value)}datas[data_key]=data_value}}}};
	var app_version = datas.only || Math.floor(Math.random()*(100000-10000+1)+10000);

	var map,msgData,marker,geocoder;
	function onApiLoaded(){
		map = new AMap.Map('mapContainer', {
			viewMode: '3D', // 默认使用 2D 模式，如果希望使用带有俯仰角的 3D 模式，请设置 viewMode: '3D'
			zoom:14,
		});
		geocoder = new AMap.Geocoder({
			city: "全国", //城市设为北京，默认：“全国”
			radius: 500 //范围，默认：500
		});
		marker = new AMap.Marker({
			content:`<img style="width: 40px;height: 40px;" src="./images/icon_mark_map.png" />`,
			offset: new AMap.Pixel(-20, -40),
		});
		infoWindow = new AMap.InfoWindow({
			offset: new AMap.Pixel(0, -42)
		});
		marker.on('click', function () {
			infoWindow.open(map,marker.getPosition());
		});
		function setCenter(_lon,_lat){
			map.setCenter([_lon,_lat]);
			// marker.setPosition([_lon,_lat]);
			geocoder.getAddress([_lon,_lat], function(status, result) {
				console.log(JSON.stringify(result));
				if (status === 'complete' && result.regeocode) {
					var addressComponent = result.regeocode.addressComponent;
					window.sendContent = result.regeocode.formattedAddress;
					window.infoTitle = `${addressComponent.city} ${addressComponent.district}`;
				}else{
					window.sendContent = "未知地址";
					window.infoTitle = "";
				}
				map.add(marker);
				marker.setPosition([_lon,_lat]);
				infoWindow.setContent(`<div style="width: 22rem;">${window.infoTitle?'<div style="padding:5px 10px;text-align: center;">'+window.infoTitle+'</div>':''}<div style="padding:5px 10px;text-align: center;">${window.sendContent}</div></div>`);
				infoWindow.open(map, marker.getPosition());
				sendFrameMsg({lon:_lon,lat:_lat,poi:window.sendContent},"setData");
			});
		}
		if(!window.initData || !window.initData.item){
			// 声明点击事件的回调函数
			function onClick(e){
				map.remove(marker);
				setCenter(e.lnglat.getLng(),e.lnglat.getLat());
			}
			map.on('click', onClick);
			AMap.plugin('AMap.Geolocation', function() {
				var geolocation = new AMap.Geolocation({
					enableHighAccuracy: true,//是否使用高精度定位，默认:true
					timeout: 10000,//超过10秒后停止定位，默认：5s
					position:'RB',//定位按钮的停靠位置
					offset: [10, 30],//定位按钮与设置的停靠位置的偏移量，默认：[10, 20]
					panToLocation: false,
					zoomToAccuracy:false,
					showCircle:false,
					showMarker:false,
				});
				map.addControl(geolocation);
				geolocation.getCurrentPosition(function(status,result){
					// console.log(result);
					if (status === 'complete' && result.info == "SUCCESS") {
						setCenter(result.position.getLng(),result.position.getLat());
					}
				});
			});
		}else{
			var dm = window.initData;
			setCenter(dm.item.content.longitude,dm.item.content.latitude);
		}
	}

	window.addEventListener('message', function(event) {
		msgData = event.data;
		console.log(msgData);
		switch(msgData.mType){
			case "init":
				window.initData = msgData;
				window._AMapSecurityConfig = { securityJsCode: msgData.secret || '17e84d06b40bb4e954148261d0f7c79b' }
				var url = `https://webapi.amap.com/maps?v=2.0&key=${msgData.key||'1616e6da9a7e3a00c6cae53119e7705e'}&plugin=AMap.Geocoder&callback=onApiLoaded`;
				var jsapi = document.createElement('script');
				jsapi.charset = 'utf-8';
				jsapi.src = url;
				document.head.appendChild(jsapi);
				break;
		}
	});

	function sendFrameMsg(_obj,_type){
		var sendMsg = {};
		if(typeof _obj === "object"){
			for(var value in _obj){
				if(typeof _obj[value] != "function"){
					sendMsg[value] = _obj[value];
				}
			}
		}
		sendMsg.mType = _type;
		window.parent.postMessage(sendMsg, '*');
	}
</script>
</html>