<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="maximum-scale=1.0,minimum-scale=1.0,user-scalable=0,width=device-width,initial-scale=1.0"/>
	<meta name="format-detection" content="telephone=no,email=no,date=no,address=no">
	<title>详情</title>
	<style type="text/css">
		html,body,#app{ width: 100%; height: 100%; margin: 0; padding: 0;background:#FFF; }
	</style>
</head>
<body>
	<div id="app">
		<iframe id="otherPage" style="display: none;" src=""width="100%" height="100%" frameborder="0"></iframe>
	</div>
</body>
<script type="text/javascript">
	//接收页面参数
	var datas={};var param=location.href.split('?');if(param.length>1){param=decodeURIComponent(param[1]);var params=param.split("&");if(params){for(var i=0;i<params.length;i++){if(params[i]){var data_key=params[i].substring(0,params[i].indexOf("="));var data_value=params[i].substring(params[i].indexOf("=")+1);if(data_value.indexOf("{")==0||data_value.indexOf("[")==0){data_value=JSON.parse(data_value)}datas[data_key]=data_value}}}};
	
	window.onload = function() {
		//alert(JSON.stringify(datas));
		document.getElementById("otherPage").src = datas.url;
		document.getElementById("otherPage").style.display = "block";
	};
</script>
</html>