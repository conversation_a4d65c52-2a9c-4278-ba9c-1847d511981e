(function() {
	var LOAD_ING = "加载中，请稍候...";
	var LOAD_MORE = "点击加载更多";
	var LOAD_ALL = "已加载完";
	var NET_ERR = "用户您好，系统正在更新，请稍后再试。";

	//参数是否为空
	function isParameters(_obj) {
		return _obj != null && _obj != undefined;
	}
	//是否数组
	function isArray(_obj) {
		return isParameters(_obj) && toString.apply(_obj) === "[object Array]";
	}
	//是否对象
	function isObject(_obj) {
		return isParameters(_obj) && typeof _obj === "object";
	}
	//是否方法
	function isFunction(_obj) {
		return isParameters(_obj) && typeof _obj === "function";
	}

	//获取随机数
	function getNum() {
		return Math.floor(Math.random() * 100000000);
	}

	//合并json
	function setNewJSON(_obj, _newobj, _dotReplace) {
		_obj = _obj || {};
		_newobj = _newobj || {};
		var returnObj = {};
		for (var key in _obj) {
			returnObj[key] = _obj[key];
		}
		for (var key in _newobj) {
			if (
				(_dotReplace && isParameters(returnObj[key])) ||
				isFunction(isParameters(returnObj[key]))
			)
				continue;
			returnObj[key] =
				isArray(returnObj[key]) || !isObject(returnObj[key])
					? _newobj[key]
					: setNewJSON(returnObj[key], _newobj[key]);
		}
		return returnObj;
	}

	//移除字符串所有标签
	function removeTag(str) {
		if (!str) return str;
		return decodeCharacter(
			str
				.replace(/<!--[\w\W\r\n]*?-->/gim, "")
				.replace(/(<[^\s\/>]+)\b[^>]*>/gi, "$1>")
				.replace(/<[^>]+>/g, "")
				.replace(/\s*/g, "")
		);
	}

	//转义字符串
	function decodeCharacter(str) {
		if (!str) return str;
		return str
			.replace(/&amp;/g, "&")
			.replace(/(&nbsp;|&ensp;|&emsp;)/g, " ")
			.replace(/&mdash;/g, "—")
			.replace(/&ldquo;/g, "“")
			.replace(/&rsquo;/g, "’")
			.replace(/&lsquo;/g, "‘")
			.replace(/&rdquo;/g, "”")
			.replace(/&middot;/g, "·")
			.replace(/&hellip;/g, "…")
			.replace(/&quot;/g, '"')
			.replace(/&lt;/g, "<")
			.replace(/&gt;/g, ">");
	}

	//获取平台类型
	function platform() {
		return api.platform;
	}

	//app设置状态栏
	function setStatusBarStyle(_param) {
		try {
			api.setStatusBarStyle(_param);
		} catch (e) {}
	}

	//区域参数
	function safeArea() {
		try {
			return api.safeArea;
		} catch (e) {
			return {top: 0, left: 0, bottom: 0, right: 0};
		}
	}

	//页面参数对象
	function pageParam(_this) {
		try {
			var pageParam =
				(_this && _this.props
					? _this.props.pageParam || (_this.props.dataMore || {}).pageParam
					: null) ||
				api.pageParam ||
				{};
			if (pageParam.paramSaveKey) {
				pageParam = JSON.parse(getPrefs(pageParam.paramSaveKey) || "{}");
			}
			if (platform() == "web" && JSON.stringify(pageParam) == "{}") {
				pageParam = getOtherParam(window.location.href);
			}
			return pageParam;
		} catch (e) {
			return {};
		}
	}

	//获取缓存
	function getPrefs(key) {
		try {
			return api.getPrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				return window.parent[key];
			} else {
				return window[key];
			}
		}
	}

	//设置缓存
	function setPrefs(key, value) {
		if (!isParameters(value)) {
			removePrefs(key);
			return;
		}
		try {
			api.setPrefs({sync: true, key: key, value: value});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				window.parent[key] = value;
			} else {
				window[key] = value;
			}
		}
	}

	//删除缓存
	function removePrefs(key) {
		try {
			api.removePrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				delete window.parent[key];
			} else {
				delete window[key];
			}
		}
	}

	//添加监听
	function addEventListener(name, callback) {
		var keyback = function keyback(ret, err) {
			isFunction(callback) && callback(ret, err);
		};
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				if (!window.baseEventList) window.baseEventList = [];
				if (getItemForKey(name, window.baseEventList)) {
					delItemForKey(name, window.baseEventList);
				}
				window.baseEventList.push({key: name, value: keyback});
			} else {
				api.addEventListener({name: name}, keyback);
			}
		} catch (e) {}
	}
	//移除监听
	function removeEventListener(name) {
		if (
			platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			delItemForKey(name, window.baseEventList);
		} else {
			try {
				api.removeEventListener({name: name});
			} catch (e) {}
		}
	}
	//发送监听
	function sendEvent(name, extra) {
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				var pageframes = window.parent.document.getElementsByTagName("iframe");
				for (var i = 0; i < pageframes.length; i++) {
					if (isArray(pageframes[i].contentWindow.baseEventList)) {
						var sendItem = getItemForKey(
							isObject(name) ? name.name : name,
							pageframes[i].contentWindow.baseEventList
						);
						if (sendItem)
							sendItem.value({value: isObject(name) ? name.extra : extra});
					}
				}
			} else {
				api.sendEvent(isObject(name) ? name : {name: name, extra: extra});
			}
		} catch (e) {}
	}
	//加载框
	function showProgress(_param, modal) {
		var o = {
			style: "default",
			animationType: "fade",
			title: "加载中",
			text: "请稍候...",
			modal: true //是否模态，模态时整个页面将不可交互
		};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.title = isParameters(_param) ? _param : "";
			o.modal = !modal; //是否可以交互 反过来了
		}
		o.title = o.title.toString();
		api.showProgress(o);
	}

	//隐藏加载框
	function hideProgress() {
		api.hideProgress();
	}

	//处理app链接 可以不带app会拼接上 或者需要带上特定参数
	function handleSYSLink(_link) {
		if (!_link) return;
		_link = _link.replace("{{tomcatAddress}}", tomcatAddress());
		_link = _link.replace("{{shareAddress}}", shareAddress());
		_link = _link.replace("{{token}}", encodeURIComponent(getPrefs("sys_token")));
		_link = _link.replace("{{sysUrl}}", appUrl());
		_link = _link.replace("{{areaId}}", areaId()); //当前跳转页面的地区id，例如：430000
		_link = _link.replace("{{userId}}", G.userId); //当前用户id，例如：1
		_link = _link.replace("{{iszx}}", G.sysSign == "zx"); //当前系统类型，例如：true (true为政协，flase为人大)
		_link = _link.replace("{{appTheme}}", G.appTheme); //当前app主题颜色，例如：#3657C0
		_link = _link.replace("{{careMode}}", G.careMode); //当前是否为关怀模式：例如：true (关怀模式下字体大4px)
		if (_link.indexOf("?ndata=") != -1) {
			if (_link.indexOf("sysUrl-zy-") == -1) _link += "-zyz-sysUrl-zy-" + appUrl();
			if (_link.indexOf("sysAreaId-zy-") == -1)
				_link += "-zyz-sysAreaId-zy-" + areaId();
			if (_link.indexOf("iszx-zy-") == -1)
				_link += "-zyz-iszx-zy-" + (G.sysSign == "zx");
			if (_link.indexOf("appTheme-zy-") == -1)
				_link += "-zyz-appTheme-zy-" + G.appTheme;
			if (_link.indexOf("careMode-zy-") == -1)
				_link += "-zyz-careMode-zy-" + G.careMode;
		}
		return _link;
	}

	//打开新页面
	function openWin(name, url, pageParam, _more) {
		url = handleSYSLink(url); //先处理跳转链接
		if (url.indexOf("http") != 0) {
			url =
				platform() == "web"
					? url.substring(url.lastIndexOf("/") + 1)
					: url.indexOf("..") != 0
					? "../" + url.split(".")[0] + "/" + url
					: url;
		}
		var o = {
			name: name,
			url: url,
			pageParam: pageParam || {},
			bounces: false,
			bgColor: "#FFF",
			slidBackEnabled: false, //ios滑动返回
			vScrollBarEnabled: true,
			hScrollBarEnabled: true,
			scaleEnabled: true,
			animation: {
				type: "push",
				subType: "from_right",
				duration: 300
			},

			reload: true, // 去除设置
			allowEdit: true, //去除设置 默认都可以复制粘贴
			delay: 0,
			overScrollMode: "scrolls",
			defaultRefreshHeader: "swipe"
		};

		if (isObject(_more)) {
			o = setNewJSON(o, _more);
		}
		if (G.headTheme != getPrefs("headTheme") && G.headTheme != "transparent") {
			o.pageParam.headTheme = G.headTheme;
		}
		if (
			G.appTheme != getPrefs("appTheme" + G.sysSign) &&
			G.appTheme != (G.sysSign == "rd" ? "#C61414" : "#3088FE")
		) {
			o.pageParam.appTheme = G.appTheme;
		}
		if (
			o.pageParam.areaId != areaId() ||
			(areaId() != getPrefs("sys_aresId") && areaId() != getPrefs("sys_platform"))
		) {
			o.pageParam.areaId = o.pageParam.areaId || areaId();
		}
		if (o.pageParam.paramSaveKey) {
			setPrefs(o.pageParam.paramSaveKey, JSON.stringify(o.pageParam));
			o.pageParam = {paramSaveKey: o.pageParam.paramSaveKey};
		}
		videoPlayRemoves();
		api.openWin(o);
	}

	function closeWin(_param) {
		var o = {};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.name = _param;
		}
		try {
			if (api.pageParam.paramSaveKey) {
				removePrefs(api.pageParam.paramSaveKey);
			}
			videoPlayRemoves();
			api.closeWin(o);
		} catch (e) {}
	}

	function toast(_param, location, global) {
		var o = {
			msg: "",
			duration: 2000,
			location: location || "middle",
			global: global ? true : false
		};

		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.msg = isParameters(_param) ? _param : "";
		}
		o.msg = o.msg.toString();
		api.toast(o);
	}

	//弹出alert
	function alert(_param, _callback) {
		var o = {title: "", msg: "", buttons: ["确定"]};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.msg = (isParameters(_param) ? _param : "").toString();
		}
		var alertBox = {
			title: o.title,
			content: (o.msg || o.content || "").toString(),
			placeholder: o.placeholder,
			closeType: o.closeType || "1",
			type: o.type || "text",
			timeout: o.timeout || 0,
			autoClose: o.autoClose,
			cancel: {
				show: o.buttons.length > 1 || o.closeType == "2" || o.closeType == "3",
				text: o.buttons[1],
				color: "#333333"
			},

			sure: {
				show: o.buttons.length > 0,
				text: o.buttons[0],
				color: "appTheme"
			},

			show: true,
			callback: function callback(ret) {
				_callback && _callback(ret);
			}
		};

		alertBox = setNewJSON(alertBox, _param.otherParam);
		G.alertPop = alertBox;
	}

	//弹出地区选择
	function openAreas(_param, _callback) {
		var areasBox = {
			show: true,
			callback: function callback(ret) {
				_callback && _callback(ret);
			}
		};

		G.areasPop = setNewJSON(areasBox, _param);
	}

	//网络请求
	function ajax(url, tag, callback, logText, method, data, header) {
		var getUrl = url,
			dataType = "json",
			cacheType = "",
			paramData = {},
			aId = areaId(),
			isWeb = "";
		if (isObject(url)) {
			getUrl = url.u;
			dataType = url.dt || "json";
			cacheType = url.t || "";
			paramData = url.paramData || {};
			aId = url.areaId || areaId();
			isWeb = url.web || "";
		}
		var o = {
			url: getUrl,
			tag: tag,
			method: method || "get",
			cache: false,
			timeout: 120,
			dataType: dataType,
			data: isObject(data) ? data : {},
			headers: setNewJSON(
				{
					"u-login-areaId": aId,
					Authorization: getPrefs("sys_token") || "",
					"content-type": "application/json",
					"u-terminal": G.terminal || "APP"
				},
				header || {}
			)
		};

		o = setNewJSON(o, paramData);
		if (o.url.indexOf("push/rongCloud") != -1) {
			if (o.method == "get") {
				o.url +=
					(o.url.indexOf("?") != -1 ? "&" : "?") +
					"environment=" +
					chatEnvironment();
			} else if (o.data.values) {
				o.data.values.environment = chatEnvironment();
			}
		}
		if (isWeb) {
			(o.headers.Authorization =
				(header || {}).Authorization || getPrefs("public_token") || ""),
				(o.headers["u-terminal"] = "PUBLIC");
		}
		var oldContentType = o.headers["content-type"];
		if (oldContentType == "file") {
			delete o.headers["content-type"];
		}
		if (platform() == "app" && logText) {
			console.log(logText + o.method + "：" + JSON.stringify(o));
		}
		var cbFun = function cbFun(ret, err) {
			if (isFunction(callback)) {
				// if(isObject(err)){
				// 	try{
				// 		ret = JSON.parse(err.msg);
				// 	}catch(e){
				// 		ret = JSON.parse(JSON.stringify(err));
				// 	}
				// 	err = null;
				// }
				if (platform() == "app" && logText) {
					console.log(
						"得到" + logText + "返回结果：" + JSON.stringify(ret ? ret : err)
					);
				}
				if (isObject(ret)) {
					ret.message = ret.message || ret.msg || "";
					var errcode = ret.code || "";
					if ((errcode == 302 || errcode == 2) && cacheType != "login") {
						cleanAllMsg();
						sendEvent({
							name: "index",
							extra: {type: "verificationToken", errmsg: ret.message}
						});
					}
					// if(!isObject(ret.data)){
					// 	ret.data = "";
					// }
				}
				callback(ret, err);
			}
		};
		if (platform() == "web") {
			var xhr = new XMLHttpRequest();
			xhr.open(o.method, o.url);
			for (var header in o.headers) {
				xhr.setRequestHeader(header, o.headers[header]);
			}
			var sendValue = "";
			if (oldContentType.indexOf("x-www-form-urlencoded") != -1) {
				var dValue = o.data.values || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue +=
						(!sendValue ? "" : "&") + vItem + "=" + encodeURIComponent(dValue[vItem]);
				}
			} else if (oldContentType.indexOf("file") != -1) {
				sendValue = new FormData();
				var dValue = o.data.values || {};
				var fileValue = o.data.files || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue.append(vItem, encodeURIComponent(dValue[vItem]));
				}
				for (var vItem in fileValue) {
					sendValue.append(vItem, fileValue[vItem]);
				}
			} else {
				sendValue = o.data.body || JSON.stringify(o.data.values);
			}
			xhr.onreadystatechange = function() {
				if (xhr.readyState === XMLHttpRequest.DONE) {
					var ret, err;
					if (!xhr.responseText) {
						err = {};
					} else {
						var response = this.responseText;
						if (o.dataType == "json") {
							try {
								ret = JSON.parse(response);
							} catch (e) {
								err = {msg: response};
							}
						} else {
							ret = response;
						}
					}
					cbFun(ret, err);
				}
			};
			xhr.send(sendValue);
		} else {
			api.cancelAjax({tag: tag});
			api.ajax(o, cbFun);
		}
	}

	function getPicture(_param, callback) {
		var o = {
			sourceType: "camera",
			encodingType: "jpg",
			mediaValue: "pic",
			targetWidth: 720,
			count: 1,
			max: 0
		};

		o = setNewJSON(o, _param);
		try {
			if (platform() == "web") {
				var h5Input = document.createElement("input");
				h5Input.style = "display: none;";
				h5Input.type = "file";
				h5Input.accept = "image/*";
				var ua = navigator.userAgent.toLowerCase();
				var version = "";
				if (ua.indexOf("android") > 0) {
					var reg = /android [\d._]+/gi;
					var v_info = ua.match(reg);
					version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, ".");
					version = parseInt(version.split(".")[0]);
				}
				if (!version || Number(version) <= 13) {
					h5Input.multiple = "multiple";
				}
				h5Input.onchange = function() {
					var listLength =
						o.max != 0 && h5Input.files.length > o.max ? o.max : h5Input.files.length;
					for (var i = 0; i < listLength; i++) {
						callback({data: h5Input.files[i]}, null);
					}
				};
				document.body.appendChild(h5Input);
				h5Input.click();
			} else if (platform() == "mp") {
				wx.chooseImage({
					count: o.max != 0 ? o.max : 9,
					sourceType: [o.sourceType == "camera" ? "camera" : "album"],
					success: function success(res) {
						for (var i = 0; i < res.tempFiles.length; i++) {
							callback({data: res.tempFiles[i]}, null);
						}
					}
				});
			} else if (platform() == "app") {
				var preName = o.sourceType == "camera" ? "camera" : "photos";
				if (
					!confirmPer(
						preName,
						"getPicture",
						o.reason || "用于上传并使用图片的功能，若取消将无法使用图片功能"
					)
				) {
					addEventListener(preName + "Per_" + "getPicture", function(ret, err) {
						if (ret.value.granted) {
							getPicture(_param, callback);
						}
						removeEventListener(preName + "Per_" + "getPicture");
					});
					return;
				}
				if (o.sourceType == "camera") {
					api.getPicture(o, function(ret, err) {
						isFunction(callback) && callback(ret, err);
					});
				} else {
					api.require("WXPhotoPicker").open(
						{
							max: o.max != 0 ? o.max : 9,
							styles: {
								mark: {checked: G.appTheme},
								bottomTabBar: {sendText: "确定", sendBgColor: G.appTheme}
							},
							type: "image"
						},
						function(ret) {
							var eventType = ret ? ret.eventType : "cancel";
							if (eventType == "cancel") return;
							if (eventType == "confirm" && ret.list.length != 0) {
								for (var i = 0; i < ret.list.length; i++) {
									callback({data: ret.list[i].path}, null);
								}
							}
						}
					);
				}
			}
		} catch (e) {}
	}

	function hasPermission(one_per) {
		if (platform() == "app") {
			if (!one_per) return;
			var rets = api.hasPermission({list: one_per.split(",")});
			if (one_per.indexOf(",") != -1) {
				alert("判断结果：" + JSON.stringify(rets));
				return;
			} else {
				return rets;
			}
		}
	}

	function requestPermission(one_per, callback, _fName) {
		if (platform() == "app") {
			if (!one_per) return;
			api.requestPermission({list: one_per.split(",")}, function(ret) {
				console.log(JSON.stringify(ret));
				ret.list.forEach(function(_eItem, _eIndex, _eArr) {
					sendEvent(_eItem.name + "Per_" + _fName, {granted: _eItem.granted});
				});
				isFunction(callback) && callback(ret, err);
			});
		}
	}

	function confirmPer(perm, _fName, _reason) {
		if (platform() == "app") {
			var has = hasPermission(perm);
			if (!has || !has[0] || !has[0].granted) {
				var hintWord = {
					camera: "相机",
					storage: "存储",
					photos: "照片",
					microphone: "麦克风",
					location: "位置",
					phone: "电话",
					"phone-r": "通话状态",
					"phone-r-log": "通话记录"
				};

				var iosHint =
					"请在" +
					(api.uiMode == "phone" ? "iPhone" : "iPad") +
					"的“设置-隐私-" +
					hintWord[perm] +
					"”中请允许访问" +
					hintWord[perm] +
					(_reason ? "，" + _reason : "。");
				var androidHint =
					"使用该功能需要" +
					hintWord[perm] +
					"权限，" +
					(_reason || "请前往系统设置开启权限。");
				alert(
					{
						title: "无法使用" + hintWord[perm],
						msg: api.systemType == "ios" ? iosHint : androidHint,
						buttons: ["下一步", "取消"]
					},
					function(ret) {
						if (1 == ret.buttonIndex) {
							requestPermission(perm, null, _fName);
						} else {
							sendEvent(perm + "Per_" + _fName, {granted: false});
						}
					}
				);
				return false;
			}
			return true;
		}
		return true;
	}

	//全局页面引用变量
	var G = {
		sysSign: sysSign(), //人大政协标识
		pageWidth: "", //页面总宽度
		onShowNum: -1, //当前页面展示次数 1为首次
		appName: "", //app名字
		appFont: "", //app全局字体
		appFontSize: 0, //app全局字体大小
		appTheme: "", //app全局主题色
		headTheme: "", //head全局主题色
		headColor: "", //标题前景色
		headTitle: "", //标题栏文字
		loginInfo: "",

		careMode: false, //是否启用了关怀模式
		systemtTypeIsPlatform: false, //系统类型是否是平台版
		isAppReview: false, //app是否上架期间 隐藏和显示部分功能
		v: "", //缓存版本号

		uId: "", //普通用户id
		userId: "", //当前用户id 账号id
		userName: "", //当前用户名字
		userImg: "", //当前用户头像
		areaId: "", //当前地区id
		specialRoleKeys: [], //当前用户角色集合
		isAdmin: false, //是否管理员 拥有所有权限
		grayscale: "", //全局置灰
		watermark: "", //全局水印

		// chatInfos:[],
		// chatInfoTask:[],

		alertPop: {
			// alert提示框
			show: false
		},

		actionSheetPop: {
			//actionSheet弹出框
			show: false
		},

		imgPreviewerPop: {
			//图片预览
			show: false
		},

		areasPop: {
			// 全局地区弹窗
			show: false
		},

		numInputPop: {
			// 全局数字弹窗
			show: false
		},

		favoritePop: {
			//收藏弹窗
			show: false
		},

		sharePosterPop: {
			//分享海报
			show: false
		},

		sharePop: {
			//分享
			show: false
		},

		identifyAudioPop: {
			//语音输入
			show: false
		},

		selectPop: {
			//单多选
			show: false
		},

		qrcodePop: {
			//h5扫码
			show: false
		},

		addressBookPop: {
			//通讯录
			show: false
		},

		fileListPop: {
			//选择文件
			show: false
		}
	};

	//默认情况下是否展示标题栏
	function showHeader() {
		return platform() == "app";
	}

	//计算头部离顶上距离
	function headerTop(_type) {
		if (platform() == "mp" && _type) {
			var wxArea = wx.getMenuButtonBoundingClientRect();
			return wxArea.top + (_type == 2 ? wxArea.height : 0);
		}
		return showHeader() ? safeArea().top : 0;
	}

	//动态加载字体和大小
	function loadConfiguration(size) {
		return (
			"font-size:" +
			((G.appFontSize || 0) + (size || 0)) +
			"px;" +
			(G.appFont != "heitiSimplified" ? "font-family:" + G.appFont + ";" : "")
		);
	}

	//动态宽度配置
	function loadConfigurationSize(size, _who) {
		var changeSize = size || 0,
			cssWidth,
			cssHeight;
		if (isArray(size)) {
			cssWidth = "width:" + (G.appFontSize + (size[0] || 0)) + "px;";
			cssHeight = "height:" + (G.appFontSize + (size[1] || 0)) + "px;";
		} else {
			cssWidth = "width:" + (G.appFontSize + changeSize) + "px;";
			cssHeight = "height:" + (G.appFontSize + changeSize) + "px;";
		}
		if (!_who) {
			return cssWidth + cssHeight;
		} else {
			return _who == "w" ? cssWidth : cssHeight;
		}
	}

	//动态计算
	function dataForNum(_num) {
		return Array.from({length: _num || 0}).fill("");
	}

	//获取item参数
	function getItemForKey(_value, _list, _key, _child) {
		if (!isParameters(_list)) return;
		var hasChild = false,
			listLength = _list.length;
		for (var i = 0; i < listLength; i++) {
			var listItem = _list[i];
			if (isArray(listItem)) {
				hasChild = true;
				var result = getItemForKey(_value, listItem, _key, true);
				if (result) return result;
			} else {
				if (!isObject(listItem)) {
					if (listItem === _value) return listItem;
				} else {
					var listItemKey = listItem[_key || "key"];
					if (isArray(listItemKey)) {
						hasChild = true;
						var result = getItemForKey(_value, listItemKey, _key, true);
						if (result) {
							return listItem;
						}
					} else if (!isObject(listItemKey) && listItemKey === _value) {
						listItem["_i"] = i;
						return listItem;
					}
				}
				if (isObject(listItem) && isArray(listItem.children)) {
					hasChild = true;
					var result = getItemForKey(_value, listItem.children, _key, true);
					if (result) return result;
				}
			}
		}
		if (!_child && !hasChild) return false;
	}

	//删除item中的元素
	function delItemForKey(_obj, _list, _key) {
		var contrastObj = !isObject(_obj) ? _obj : _obj[_key || "key"];
		for (var i = 0; i < _list.length; i++) {
			if (
				(!isObject(_list[i]) ? _list[i] : _list[i][_key || "key"]) === contrastObj
			) {
				_list.splice(i, 1);
				delItemForKey(_obj, _list, _key);
				break;
			}
		}
	}

	//web和小程序阻止底部事件
	function stopBubble(e) {
		if (!e) return;
		if (platform() == "web") {
			e.preventDefault();
			e.stopPropagation();
		} else if (platform() == "mp") {
			e.$_canBubble = false;
		}
	}

	//移动事件 不左滑关闭屏幕
	function touchmove() {
		G.nTouchmove = true;
		clearTimeout(G.touchmoveTask);
		G.touchmoveTask = setTimeout(function() {
			G.nTouchmove = false;
		}, 1000);
	}

	//适配图片
	function showImg(_item, _add) {
		if (_add === void 0) {
			_add = "";
		}
		var baseUrl = isObject(_item) ? _item.url || "" : _item || "";
		if (!baseUrl) return;
		if (/^(?!h|(\.)|(\/)).*/.test(baseUrl)) {
			baseUrl =
				appUrl() +
				"image/" +
				_add +
				(baseUrl.indexOf("-compress-") > -1
					? baseUrl.split("-compress-")[1]
					: baseUrl);
		}
		return baseUrl + (baseUrl.indexOf("?") != -1 ? "&" : "?") + "v=" + G.v;
	}

	//获取元素位置宽高
	function getBoundingClientRect(_id, _callback) {
		if (!document.getElementById(_id)) return;
		if (platform() == "mp") {
			document
				.getElementById(_id)
				.$$getBoundingClientRect()
				.then(function(res) {
					return _callback(res);
				});
		} else {
			return _callback(document.getElementById(_id).getBoundingClientRect());
		}
	}

	//颜色转成rgba
	function colorRgba(_color, _alpha) {
		if (!_color) return;
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		var color = _color.toLowerCase();
		if (reg.test(color)) {
			if (color.length === 4) {
				var colorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
				}
				color = colorNew;
			}
			var colorChange = [];
			for (var i = 1; i < 7; i += 2) {
				colorChange.push(parseInt("0x" + color.slice(i, i + 2)));
			}
			return (
				"rgba(" +
				colorChange.join(",") +
				"," +
				(isParameters(_alpha) ? _alpha : "1") +
				")"
			);
		} else {
			return color;
		}
	}

	//删除所有缓存
	function cleanAllMsg() {
		var exitPrefs = [
			"isAutoLogin",
			"loginPassword",
			"sys_token",
			"sys_Id",
			"sys_UserID",
			"sys_UserName",
			"sys_AppPhoto",
			"sys_Mobile",
			"sys_Position",
			"sys_aresId",
			"sys_OfficeId",
			"sys_SpecialRoleKeys",
			"sdt_signin_phone",
			"public_token",
			"last14Msg",
			"sys_unread"
		];
		exitPrefs.forEach(function(_eItem) {
			removePrefs(_eItem);
		});
	}

	//列表无数据处理
	function dealData(_type, _this, ret) {
		if (!_type) {
			_this.data.listData = [];
			_this.data.emptyBox.type = ret ? "1" : "2";
			_this.data.emptyBox.text =
				ret && ret.code != 200 ? ret.message || ret.data : "";
		} else {
			_this.data.emptyBox.text = ret
				? ret.code == 200
					? LOAD_ALL
					: ret.message || ret.data
				: NET_ERR;
		}
	}

	//保存登录信息
	function saveLogin(_info) {
		var infolist = _info || {};
		setPrefs("sys_Id", infolist.id);
		setPrefs("sys_UserID", infolist.accountId);
		setPrefs("sys_UserName", infolist.userName);
		setPrefs("sys_AppPhoto", infolist.headImg || infolist.photo); //headImg用户头像 photo代表头像
		setPrefs("sys_Mobile", infolist.mobile);
		setPrefs("sys_Position", infolist.position);
		if (!getPrefs("sys_aresId")) {
			setPrefs("sys_aresId", infolist.areaId);
		}
		setPrefs("sys_OfficeId", infolist.officeId);
		setPrefs(
			"sys_SpecialRoleKeys",
			JSON.stringify(infolist.specialRoleKeys || [])
		);
		if (infolist.id) {
			// //修改头像后保存下缓存数据
			setAllChatInfo({
				id: infolist.accountId,
				name: infolist.userName,
				url: infolist.headImg || infolist.photo
			});
		}
		sendEvent({name: "sys_refresh"});
	}

	//获取链接中参数
	function getOtherParam(_url) {
		if (_url.indexOf("?") != -1) {
			_url = _url.substring(_url.indexOf("?") + 1);
		}
		var params = _url.split("&"),
			rp = {};
		for (var j = 0; j < params.length; j++) {
			if (params[j]) {
				var data_key = params[j].substring(0, params[j].indexOf("="));
				if (!data_key) {
					continue;
				}
				var data_value = decodeURIComponent(
					params[j].substring(params[j].indexOf("=") + 1)
				);
				if (data_value.indexOf("{") == 0 || data_value.indexOf("[") == 0)
					data_value = JSON.parse(data_value);
				rp[data_key] = data_value;
			}
		}
		return rp;
	}

	//获取地区参数
	function getAreaForKey(_key, _all) {
		var rItem = null;
		var areas = JSON.parse(getPrefs("sys_areas") || "[]");
		if (_all || !areas.length) {
			areas = JSON.parse(getPrefs("sys_allAreas") || "[]");
		}
		if (areas.length) {
			rItem = getItemForKey(_key, areas, "id");
			if (rItem) {
				rItem.name =
					rItem.name.length > 4 ? rItem.name.substring(0, 4) + "..." : rItem.name;
			}
		}
		return rItem || {};
	}

	//通用上传附件 item格式  url本地地址路径 uploadId上传后的id	state状态【1上传中2完成3失败】
	function uploadFile(_item, callback) {
		if (_item._fileAjax || _item.module == "-noUpload") return;
		_item._fileAjax = true;
		_item.state = 1;
		if (_item.showToast) {
			showProgress("上传中");
		}
		var nCallack = function nCallack(ret, err) {
			hideProgress();
			var code = ret ? ret.code : "";
			if (code == 200) {
				var data = ret.data || {};
				_item.state = 2;
				_item.uploadId = data.id;
				_item.otherInfo = data;
			} else {
				_item.state = 3;
				_item.error = ret ? ret.message || ret.data : err.data || err.body || "";
			}
			callback && callback(_item);
		};
		if (platform() == "mp") {
			wx.uploadFile({
				url: appUrl() + "file/upload",
				filePath: _item.url.path,
				name: "file",
				header: {
					"Content-Type": "multipart/form-data",
					"u-login-areaId": areaId(),
					Authorization: getPrefs("sys_token") || ""
				},

				success: function success(res) {
					nCallack(JSON.parse(res.data), null);
				},
				fail: function fail(err) {
					nCallack(null, JSON.parse(err.data));
				}
			});
		} else {
			ajax(
				{u: appUrl() + "file/upload", web: _item.web},
				"file/upload" + _item.url,
				nCallack,
				"上传附件",
				"post",
				{
					files: {file: _item.url}
				},
				{"content-type": "file"}
			);
		}
	}

	//人大政协标识  rd人大 zx政协
	function sysSign() {
		return getPrefs("sys_sign") || "rd";
	}

	//配置地址
	function appUrl() {
		var prot = sysSign() == "rd" ? "20169" : "20170";
		return (
			getPrefs("sys_appUrl") || "https://productpc.cszysoft.com:" + prot + "/lzt/"
		);
	}
	//tomcat配置地址
	function tomcatAddress() {
		return (
			getPrefs("sys_tomcatAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http://cszysoft.com:9090/"
				: "https://cszysoft.com:9091/")
		);
	}
	//分享地址
	function shareAddress(_type) {
		if (_type == 1 && platform() != "mp") return "../../";
		return (
			getPrefs("sys_shareAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http:"
				: "https:") +
				"//cszysoft.com/appShare/" +
				(sysSign() == "rd" ? "platform5rd/" : "platform5zx/")
		);
	}
	//融云唯一前缀
	function chatHeader() {
		return getPrefs("sys_chatHeader") || "platform5" + sysSign();
	}
	//融云正测
	function chatEnvironment() {
		return getPrefs("sys_chatEnvironment") || "1";
	}
	//当前页面地区id
	function areaId() {
		return (
			(G._this && G._this.data.area ? G._this.data.area.key : "") ||
			pageParam().areaId ||
			getPrefs("sys_aresId") ||
			getPrefs("sys_platform") ||
			""
		);
	}

	var SECONDS_A_MINUTE = 60;
	var SECONDS_A_HOUR = SECONDS_A_MINUTE * 60;
	var SECONDS_A_DAY = SECONDS_A_HOUR * 24;
	var SECONDS_A_WEEK = SECONDS_A_DAY * 7;
	var MILLISECONDS_A_SECOND = 1e3;
	var MILLISECONDS_A_MINUTE = SECONDS_A_MINUTE * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_HOUR = SECONDS_A_HOUR * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_DAY = SECONDS_A_DAY * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_WEEK = SECONDS_A_WEEK * MILLISECONDS_A_SECOND; // English locales

	var MS = "millisecond";
	var S = "second";
	var MIN = "minute";
	var H = "hour";
	var D = "day";
	var W = "week";
	var M = "month";
	var Q = "quarter";
	var Y = "year";
	var DATE = "date";
	var FORMAT_DEFAULT = "YYYY-MM-DDTHH:mm:ssZ";
	var INVALID_DATE_STRING = "Invalid Date"; // regex

	var REGEX_PARSE = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/;
	var REGEX_FORMAT = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;

	var en = {
		name: "en",
		weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split(
			"_"
		),
		months: "January_February_March_April_May_June_July_August_September_October_November_December".split(
			"_"
		)
	};

	var padStart = function padStart(string, length, pad) {
		var s = String(string);
		if (!s || s.length >= length) return string;
		return "" + Array(length + 1 - s.length).join(pad) + string;
	};
	var padZoneStr = function padZoneStr(instance) {
		var negMinutes = -instance.utcOffset();
		var minutes = Math.abs(negMinutes);
		var hourOffset = Math.floor(minutes / 60);
		var minuteOffset = minutes % 60;
		return (
			"" +
			(negMinutes <= 0 ? "+" : "-") +
			padStart(hourOffset, 2, "0") +
			":" +
			padStart(minuteOffset, 2, "0")
		);
	};
	var monthDiff = function monthDiff(a, b) {
		if (a.date() < b.date()) return -monthDiff(b, a);
		var wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month());
		var anchor = a.clone().add(wholeMonthDiff, M);
		var c = b - anchor < 0;
		var anchor2 = a.clone().add(wholeMonthDiff + (c ? -1 : 1), M);
		return +(
			-(
				wholeMonthDiff +
				(b - anchor) / (c ? anchor - anchor2 : anchor2 - anchor)
			) || 0
		);
	};
	var absFloor = function absFloor(n) {
		return n < 0 ? Math.ceil(n) || 0 : Math.floor(n);
	};
	var prettyUnit = function prettyUnit(u) {
		var special = {
			M: M,
			y: Y,
			w: W,
			d: D,
			D: DATE,
			h: H,
			m: MIN,
			s: S,
			ms: MS,
			Q: Q
		};

		return (
			special[u] ||
			String(u || "")
				.toLowerCase()
				.replace(/s$/, "")
		);
	};
	var isUndefined = function isUndefined(s) {
		return s === undefined;
	};
	var U = {
		s: padStart,
		z: padZoneStr,
		m: monthDiff,
		a: absFloor,
		p: prettyUnit,
		u: isUndefined
	};

	var L = "en";
	var Ls = {};
	Ls[L] = en;
	var isDayjs = function isDayjs(d) {
		return d instanceof Dayjs;
	};
	var parseLocale = function parseLocale(preset, object, isLocal) {
		var l;
		if (!preset) return L;
		if (typeof preset === "string") {
			var presetLower = preset.toLowerCase();
			if (Ls[presetLower]) {
				l = presetLower;
			}
			if (object) {
				Ls[presetLower] = object;
				l = presetLower;
			}
			var presetSplit = preset.split("-");
			if (!l && presetSplit.length > 1) {
				return parseLocale(presetSplit[0]);
			}
		} else {
			var name = preset.name;
			Ls[name] = preset;
			l = name;
		}
		if (!isLocal && l) L = l;
		return l || (!isLocal && L);
	};
	var dayjs = function dayjs(date, c) {
		if (isDayjs(date)) {
			return date.clone();
		}
		var cfg = typeof c === "object" ? c : {};
		cfg.date = date;
		cfg.args = arguments;
		return new Dayjs(cfg);
	};
	var wrapper = function wrapper(date, instance) {
		return dayjs(date, {
			locale: instance.$L,
			utc: instance.$u,
			x: instance.$x,
			$offset: instance.$offset
		});
	};
	var Utils = U;
	Utils.l = parseLocale;
	Utils.i = isDayjs;
	Utils.w = wrapper;
	var parseDate = function parseDate(cfg) {
		var date = cfg.date,
			utc = cfg.utc;
		if (date === null) return new Date(NaN);
		if (Utils.u(date)) return new Date();
		if (date instanceof Date) return new Date(date);
		if (typeof date === "string" && !/Z$/i.test(date)) {
			var d = date.match(REGEX_PARSE);
			if (d) {
				var m = d[2] - 1 || 0;
				var ms = (d[7] || "0").substring(0, 3);
				if (utc) {
					return new Date(
						Date.UTC(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms)
					);
				}
				return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);
			}
		}
		return new Date(date);
	};
	var Dayjs = (function() {
		function Dayjs(cfg) {
			this.$L = parseLocale(cfg.locale, null, true);
			this.parse(cfg);
		}
		var _proto = Dayjs.prototype;
		_proto.parse = function parse(cfg) {
			this.$d = parseDate(cfg);
			this.$x = cfg.x || {};
			this.init();
		};
		_proto.init = function init() {
			var $d = this.$d;
			this.$y = $d.getFullYear();
			this.$M = $d.getMonth();
			this.$D = $d.getDate();
			this.$W = $d.getDay();
			this.$H = $d.getHours();
			this.$m = $d.getMinutes();
			this.$s = $d.getSeconds();
			this.$ms = $d.getMilliseconds();
		};
		_proto.$utils = function $utils() {
			return Utils;
		};
		_proto.isValid = function isValid() {
			return !(this.$d.toString() === INVALID_DATE_STRING);
		};
		_proto.isSame = function isSame(that, units) {
			var other = dayjs(that);
			return this.startOf(units) <= other && other <= this.endOf(units);
		};
		_proto.isAfter = function isAfter(that, units) {
			return dayjs(that) < this.startOf(units);
		};
		_proto.isBefore = function isBefore(that, units) {
			return this.endOf(units) < dayjs(that);
		};
		_proto.$g = function $g(input, get, set) {
			if (Utils.u(input)) return this[get];
			return this.set(set, input);
		};
		_proto.unix = function unix() {
			return Math.floor(this.valueOf() / 1000);
		};
		_proto.valueOf = function valueOf() {
			return this.$d.getTime();
		};
		_proto.startOf = function startOf(units, _startOf) {
			var _this = this;
			var isStartOf = !Utils.u(_startOf) ? _startOf : true;
			var unit = Utils.p(units);
			var instanceFactory = function instanceFactory(d, m) {
				var ins = Utils.w(
					_this.$u ? Date.UTC(_this.$y, m, d) : new Date(_this.$y, m, d),
					_this
				);
				return isStartOf ? ins : ins.endOf(D);
			};
			var instanceFactorySet = function instanceFactorySet(method, slice) {
				var argumentStart = [0, 0, 0, 0];
				var argumentEnd = [23, 59, 59, 999];
				return Utils.w(
					_this
						.toDate()
						[method].apply(
							_this.toDate("s"),
							(isStartOf ? argumentStart : argumentEnd).slice(slice)
						),
					_this
				);
			};
			var $W = this.$W,
				$M = this.$M,
				$D = this.$D;
			var utcPad = "set" + (this.$u ? "UTC" : "");
			switch (unit) {
				case Y:
					return isStartOf ? instanceFactory(1, 0) : instanceFactory(31, 11);
				case M:
					return isStartOf ? instanceFactory(1, $M) : instanceFactory(0, $M + 1);
				case W: {
					var weekStart = this.$locale().weekStart || 0;
					var gap = ($W < weekStart ? $W + 7 : $W) - weekStart;
					return instanceFactory(isStartOf ? $D - gap : $D + (6 - gap), $M);
				}
				case D:
				case DATE:
					return instanceFactorySet(utcPad + "Hours", 0);
				case H:
					return instanceFactorySet(utcPad + "Minutes", 1);
				case MIN:
					return instanceFactorySet(utcPad + "Seconds", 2);
				case S:
					return instanceFactorySet(utcPad + "Milliseconds", 3);
				default:
					return this.clone();
			}
		};
		_proto.endOf = function endOf(arg) {
			return this.startOf(arg, false);
		};
		_proto.$set = function $set(units, _int) {
			var _C$D$C$DATE$C$M$C$Y$C;
			var unit = Utils.p(units);
			var utcPad = "set" + (this.$u ? "UTC" : "");
			var name = ((_C$D$C$DATE$C$M$C$Y$C = {}),
			(_C$D$C$DATE$C$M$C$Y$C[D] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[DATE] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[M] = utcPad + "Month"),
			(_C$D$C$DATE$C$M$C$Y$C[Y] = utcPad + "FullYear"),
			(_C$D$C$DATE$C$M$C$Y$C[H] = utcPad + "Hours"),
			(_C$D$C$DATE$C$M$C$Y$C[MIN] = utcPad + "Minutes"),
			(_C$D$C$DATE$C$M$C$Y$C[S] = utcPad + "Seconds"),
			(_C$D$C$DATE$C$M$C$Y$C[MS] = utcPad + "Milliseconds"),
			_C$D$C$DATE$C$M$C$Y$C)[unit];
			var arg = unit === D ? this.$D + (_int - this.$W) : _int;
			if (unit === M || unit === Y) {
				var date = this.clone().set(DATE, 1);
				date.$d[name](arg);
				date.init();
				this.$d = date.set(DATE, Math.min(this.$D, date.daysInMonth())).$d;
			} else if (name) this.$d[name](arg);
			this.init();
			return this;
		};
		_proto.set = function set(string, _int2) {
			return this.clone().$set(string, _int2);
		};
		_proto.get = function get(unit) {
			return this[Utils.p(unit)]();
		};
		_proto.add = function add(number, units) {
			var _this2 = this,
				_C$MIN$C$H$C$S$unit;
			number = Number(number);
			var unit = Utils.p(units);
			var instanceFactorySet = function instanceFactorySet(n) {
				var d = dayjs(_this2);
				return Utils.w(d.date(d.date() + Math.round(n * number)), _this2);
			};
			if (unit === M) {
				return this.set(M, this.$M + number);
			}
			if (unit === Y) {
				return this.set(Y, this.$y + number);
			}
			if (unit === D) {
				return instanceFactorySet(1);
			}
			if (unit === W) {
				return instanceFactorySet(7);
			}
			var step =
				((_C$MIN$C$H$C$S$unit = {}),
				(_C$MIN$C$H$C$S$unit[MIN] = MILLISECONDS_A_MINUTE),
				(_C$MIN$C$H$C$S$unit[H] = MILLISECONDS_A_HOUR),
				(_C$MIN$C$H$C$S$unit[S] = MILLISECONDS_A_SECOND),
				_C$MIN$C$H$C$S$unit)[unit] || 1;
			var nextTimeStamp = this.$d.getTime() + number * step;
			return Utils.w(nextTimeStamp, this);
		};
		_proto.subtract = function subtract(number, string) {
			return this.add(number * -1, string);
		};
		_proto.format = function format(formatStr) {
			var _this3 = this;
			var locale = this.$locale();
			if (!this.isValid()) return locale.invalidDate || INVALID_DATE_STRING;
			var str = formatStr || FORMAT_DEFAULT;
			var zoneStr = Utils.z(this);
			var $H = this.$H,
				$m = this.$m,
				$M = this.$M;
			var weekdays = locale.weekdays,
				months = locale.months,
				meridiem = locale.meridiem;
			var getShort = function getShort(arr, index, full, length) {
				return (
					(arr && (arr[index] || arr(_this3, str))) || full[index].slice(0, length)
				);
			};
			var get$H = function get$H(num) {
				return Utils.s($H % 12 || 12, num, "0");
			};
			var meridiemFunc =
				meridiem ||
				function(hour, minute, isLowercase) {
					var m = hour < 12 ? "AM" : "PM";
					return isLowercase ? m.toLowerCase() : m;
				};
			var matches = {
				YY: String(this.$y).slice(-2),
				YYYY: this.$y,
				M: $M + 1,
				MM: Utils.s($M + 1, 2, "0"),
				MMM: getShort(locale.monthsShort, $M, months, 3),
				MMMM: getShort(months, $M),
				D: this.$D,
				DD: Utils.s(this.$D, 2, "0"),
				d: String(this.$W),
				dd: getShort(locale.weekdaysMin, this.$W, weekdays, 2),
				ddd: getShort(locale.weekdaysShort, this.$W, weekdays, 3),
				dddd: weekdays[this.$W],
				H: String($H),
				HH: Utils.s($H, 2, "0"),
				h: get$H(1),
				hh: get$H(2),
				a: meridiemFunc($H, $m, true),
				A: meridiemFunc($H, $m, false),
				m: String($m),
				mm: Utils.s($m, 2, "0"),
				s: String(this.$s),
				ss: Utils.s(this.$s, 2, "0"),
				SSS: Utils.s(this.$ms, 3, "0"),
				Z: zoneStr
			};

			return str.replace(REGEX_FORMAT, function(match, $1) {
				return $1 || matches[match] || zoneStr.replace(":", "");
			});
		};
		_proto.utcOffset = function utcOffset() {
			return -Math.round(this.$d.getTimezoneOffset() / 15) * 15;
		};
		_proto.diff = function diff(input, units, _float) {
			var _C$Y$C$M$C$Q$C$W$C$D$;
			var unit = Utils.p(units);
			var that = dayjs(input);
			var zoneDelta =
				(that.utcOffset() - this.utcOffset()) * MILLISECONDS_A_MINUTE;
			var diff = this - that;
			var result = Utils.m(this, that);
			result =
				((_C$Y$C$M$C$Q$C$W$C$D$ = {}),
				(_C$Y$C$M$C$Q$C$W$C$D$[Y] = result / 12),
				(_C$Y$C$M$C$Q$C$W$C$D$[M] = result),
				(_C$Y$C$M$C$Q$C$W$C$D$[Q] = result / 3),
				(_C$Y$C$M$C$Q$C$W$C$D$[W] = (diff - zoneDelta) / MILLISECONDS_A_WEEK),
				(_C$Y$C$M$C$Q$C$W$C$D$[D] = (diff - zoneDelta) / MILLISECONDS_A_DAY),
				(_C$Y$C$M$C$Q$C$W$C$D$[H] = diff / MILLISECONDS_A_HOUR),
				(_C$Y$C$M$C$Q$C$W$C$D$[MIN] = diff / MILLISECONDS_A_MINUTE),
				(_C$Y$C$M$C$Q$C$W$C$D$[S] = diff / MILLISECONDS_A_SECOND),
				_C$Y$C$M$C$Q$C$W$C$D$)[unit] || diff;
			return _float ? result : Utils.a(result);
		};
		_proto.daysInMonth = function daysInMonth() {
			return this.endOf(M).$D;
		};
		_proto.$locale = function $locale() {
			return Ls[this.$L];
		};
		_proto.locale = function locale(preset, object) {
			if (!preset) return this.$L;
			var that = this.clone();
			var nextLocaleName = parseLocale(preset, object, true);
			if (nextLocaleName) that.$L = nextLocaleName;
			return that;
		};
		_proto.clone = function clone() {
			return Utils.w(this.$d, this);
		};
		_proto.toDate = function toDate() {
			return new Date(this.valueOf());
		};
		_proto.toJSON = function toJSON() {
			return this.isValid() ? this.toISOString() : null;
		};
		_proto.toISOString = function toISOString() {
			return this.$d.toISOString();
		};
		_proto.toString = function toString() {
			return this.$d.toUTCString();
		};
		return Dayjs;
	})();
	var proto = Dayjs.prototype;
	dayjs.prototype = proto;
	[
		["$ms", MS],
		["$s", S],
		["$m", MIN],
		["$H", H],
		["$W", D],
		["$M", M],
		["$y", Y],
		["$D", DATE]
	].forEach(function(g) {
		proto[g[1]] = function(input) {
			return this.$g(input, g[0], g[1]);
		};
	});
	dayjs.extend = function(plugin, option) {
		if (!plugin.$i) {
			plugin(option, Dayjs, dayjs);
			plugin.$i = true;
		}
		return dayjs;
	};
	dayjs.locale = parseLocale;
	dayjs.isDayjs = isDayjs;
	dayjs.unix = function(timestamp) {
		return dayjs(timestamp * 1e3);
	};
	dayjs.en = Ls[L];
	dayjs.Ls = Ls;
	dayjs.p = {};

	//获取资讯栏目
	function getColumn5(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "newsColumn/app/list",
				param: setNewJSON({pageNo: "1", pageSize: "999"}, _param.param),
				areaId: _param.areaId,
				tag: "newsColumn",
				name: "栏目"
			},
			function(ret, err) {
				_callback && _callback(ret, err);
			}
		);
	}

	//获取资讯列表
	function getList5(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "newsContent/app/batchList",
				param: setNewJSON({query: {isNeedContent: false}}, _param.param),
				areaId: _param.areaId,
				tag: "batchList",
				name: "列表"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.code = _eItem.moduleType == "1" ? module5.code : module7.code;
						item.sourceModuleName = _eItem.sourceModuleName || "";
						item.showCode = _eItem.showCodeName || "";
						item.title = _eItem.title || "";
						item.content = removeTag(_eItem.content || "");
						item.id = _eItem.sourceId || _eItem.id || "";
						var infoPic = _eItem.infoPic || "";
						item.url = infoPic
							? infoPic.indexOf(",") != -1
								? infoPic.split(",").map(function(obj) {
										return {url: obj};
								  })
								: infoPic
							: "";
						item.videoHrefs = _eItem.infoVideo || "";
						var fileLink = _eItem.fileLink || [];
						if (isParameters(fileLink) && fileLink.length) {
							var useType1 = [],
								useType2 = [];
							fileLink.forEach(function(_mItem) {
								if (
									_mItem.fileId ||
									(isArray(_mItem.attachments) && _mItem.attachments.length)
								) {
									if (_mItem.useType == "1" && useType1.length < 3) {
										useType1.push({url: _mItem.attachments[0].newFileName});
									} else if (_mItem.useType == "2") {
										useType2.push(
											_mItem.fileId ||
												(_mItem.attachments.length ? _mItem.attachments[0].id : "")
										);
									}
								}
							});
							if (useType1.length) {
								item.url = useType1.length > 1 ? useType1 : useType1[0].url;
							}
							if (useType2.length && useType2[0]) {
								item.videoHrefs = useType2[0];
							}
						}
						item.source = _eItem.source || "";
						item.isTop = _eItem.isTop || "";
						item.link = _eItem.contentType == 2 ? _eItem.linkUrl || "" : "";
						item.time = _eItem.pubTime || "";
						if (item.videoHrefs) {
							item.poster = item.url || "../../image/bg_launch.png";
							item.autoplay = false;
						}
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取通知公告
	function getList22(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "notification/list",
				param: setNewJSON(
					{query: {isDraft: 0}, tableId: "id_message_notification"},
					_param.param
				),
				tag: "notification/list",
				name: "通知公告"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.code = module22.code;
						item.businessCode = module22.businessCode;
						item.id = _eItem.id || "";
						item.title = _eItem.theme || "";
						item.content = removeTag(_eItem.content || "");
						item.time = _eItem.publishTime || _eItem.createDate || "";
						item.source = _eItem.publishOfficeId || "";
						item.channelId = _eItem.channelId || ""; //类型
						item.isTop = _eItem.isTop || ""; //置顶
						item.attachmentIds = _eItem.attachmentIds || ""; //附件
						item.isReceipt = _eItem.isReceipt || ""; //回执1
						item.isRedDot = _eItem.isRedDot || ""; //是否未读
						var urgentLevel = _eItem.urgentLevel || {};
						if (urgentLevel.value) {
							item.urgentLevel = {
								color: {"1": "#67C23A", "2": "#E6A23C", "3": "#F56C6C"}[
									urgentLevel.value
								],
								text: urgentLevel.name
							};
						}
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	var module5 = {
		name: "资讯",
		code: "5",
		businessCode: "informationContent",
		behaviorCode: "information_content"
	};
	var module6 = {
		name: sysSign() == "rd" ? "意见征集" : "网络议政",
		code: "6",
		businessCode: "opinioncollect"
	};
	var module7 = {name: "专题", code: "7", businessCode: "infoSubject"};
	var module9 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息",
		code: "9",
		businessCode: "cppcc_member"
	};
	var module9_1 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息变更申请",
		code: "9_1",
		businessCode: "cppccMemberCheckPrepare"
	};
	var module12 = {
		name: sysSign() == "rd" ? "圈子" : "委员说",
		code: "12",
		businessCode: "styleCircle"
	};
	var module22 = {name: "通知公告", code: "22", businessCode: "notification"};

	//打开链接
	function openWin_url(_param) {
		_param.url = handleSYSLink(_param.url);
		if (
			(_param.wopen || _param.url.indexOf("wopen") != -1) &&
			platform() == "web"
		) {
			window.open(_param.url);
		} else {
			openWin("mo_details_url", "../mo_details_url/mo_details_url.stml", _param);
		}
	}

	//打开搜索新
	function openWin_search_n(_item) {
		var openPage = "mo_search_n";
		var param = {};
		param = setNewJSON(param, _item);
		openWin(
			openPage + _item.code,
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
	}

	//打开聊天
	function openWin_chat(_param) {
		var openPage = "mo_chat";
		var myParam = {
			conversationType: _param.conversationType,
			targetId: _param.targetId || _param.id
		};

		if (_param.paramMore) {
			myParam = setNewJSON(myParam, _param.paramMore);
		}
		openWin(
			openPage + myParam.targetId,
			"../" + openPage + "/" + openPage + ".stml",
			myParam
		);
	}

	//打开详情------------------------------------------------------------------------------------

	//打开资讯
	function openWin_news(_item) {
		var openPage =
			_item.code == "7" ? "mo_news_topic" : _item.id ? "mo_details_n" : "mo_news";
		var param = {};
		param.id = _item.id;
		param.code = module5.code;
		if (_item.link) {
			openWin_url({url: _item.link});
		} else {
			openWin(
				openPage + (_item.id || _item.code),
				"../" + openPage + "/" + openPage + ".stml",
				param
			);
		}
		addBehaviorRecord({id: param.id, behaviorCode: module5.behaviorCode});
	}

	//打开通知公告
	function openWin_notice(_item) {
		var openPage = _item.id ? "mo_notice_detail" : "mo_business_list_n";
		var param = {};
		param.id = _item.id;
		param.areaId = _item.areaId;
		param.code = _item.code || module22.code;
		param.title = _item.id ? "" : "通知公告";
		openWin(
			openPage + (_item.id || module22.code),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
	}

	// 打开后台配置的应用
	function openWin_module(_item) {
		console.log(JSON.stringify(_item));
		var pageParam = {};
		pageParam.key = _item.key;
		pageParam.title = _item.value;
		pageParam.code = _item.code;
		if (_item.remarks) {
			var remarkList = _item.remarks.split("&");
			remarkList.forEach(function(_eItem) {
				pageParam[_eItem.split("=")[0]] = _eItem.split("=")[1];
			});
		}
		var moduleUrl = handleSYSLink(_item.moduleUrl);
		if (moduleUrl && moduleUrl != "/") {
			if (pageParam.code == "15" || moduleUrl == "scan") {
				openScan({}, function(ret) {});
				return;
			}
			if (moduleUrl == "takeshot") {
				getPicture({sourceType: "camera", destinationType: "url", max: 1}, function(
					ret,
					err
				) {
					var dataUrl = ret ? ret.data || ret.base64Data : "";
					if (dataUrl) {
						var _item = {showToast: true, url: dataUrl};
						uploadFile(_item, function(ret) {
							if (ret.otherInfo) {
								moduleUrl = "mo_" + moduleUrl;
								pageParam.picid = ret.otherInfo.id;
								pageParam.picname = ret.otherInfo.newFileName;
								openWin(
									moduleUrl + pageParam.code,
									"../" + moduleUrl + "/" + moduleUrl + ".stml",
									pageParam
								);
							} else {
								toast(ret.error);
							}
						});
					}
				});
				return;
			}
			if (moduleUrl.indexOf("mo_") == 0) {
				if (
					moduleUrl == "mo_performance_file_list" &&
					!(
						G.isAdmin ||
						getItemForKey("worker", G.specialRoleKeys) ||
						getItemForKey(
							(G.sysSign == "rd" ? "npc" : "cppcc") + "_contact_committee",
							G.specialRoleKeys
						) ||
						getItemForKey("delegation_manager", G.specialRoleKeys)
					)
				) {
					moduleUrl = "mo_performance_file";
				}
				openWin(
					moduleUrl + pageParam.code,
					"../" + moduleUrl + "/" + moduleUrl + ".stml",
					pageParam
				);
			} else if (moduleUrl.indexOf("http") == 0) {
				pageParam.url = moduleUrl;
				openWin_url(pageParam);
			} else {
				toast("加急开发中");
			}
		} else {
			toast("加急开发中");
		}
	}

	//获取登录信息
	function getLoginInfo(_param, _callback) {
		ajax(
			{u: appUrl() + "login/user"},
			"login/user",
			function(ret, err) {
				if (ret && ret.code == 200 && ret.data.id != "anonymous") {
					saveLogin(ret.data);
				}
				_callback && _callback(ret, err);
			},
			"用户信息",
			"post",
			{
				values: _param.param
			},
			_param.header
		);
	}

	// 增加阅读量
	function addBehaviorRecord(_param) {
		ajax(
			{u: appUrl() + ("behavior/record/" + _param.behaviorCode + "/" + _param.id)},
			"behavior/record",
			function(ret, err) {},
			"阅读",
			"post",
			{
				body: JSON.stringify({})
			}
		);
	}

	//请求
	function ajaxProcess(_param, _callback) {
		if (_param.toast) showProgress(_param.toast);
		ajax(
			{u: _param.url, areaId: _param.areaId, web: _param.web},
			_param.tag || "ajaxProcess",
			function(ret, err) {
				hideProgress();
				if (_param.toast) {
					toast(ret ? ret.message || ret.data : NET_ERR);
				}
				_callback && _callback(ret, err);
			},
			_param.name || "\u64CD\u4F5C",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	//获取系统图片管理
	function getSysBackground(_callback) {
		ajax(
			{u: appUrl() + "wind/runner/openList"},
			"openList",
			function(ret) {
				var data = ret && ret.code == 200 ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					data.forEach(function(item) {
						item.dictCode = item.location.value;
					});
					setPrefs("appBackgroupImg", JSON.stringify(data));
					_callback(data);
				}
			},
			"图片配置",
			"post",
			{
				body: JSON.stringify({
					tableId: "sys_app_backgroup_img",
					wheres: [
						{columnId: "sys_app_backgroup_img_is_using", queryType: "EQ", value: "1"}
					]
				})
			},
			{Authorization: ""}
		);
	}

	//设置菜单列表数据
	function setModuleList(item, itemData) {
		item.url = itemData.iconUrl || "";
		item.key = itemData.id || "";
		item.value = itemData.title || "";
		item.code = (itemData.appModuleCode || {}).value;
		item.moduleUrl = itemData.moduleUrl || "";
		item.remarks = itemData.remarks || "";
		if (item.remarks) {
			var remarkList = item.remarks.split("&");
			remarkList.forEach(function(_eItem) {
				item[_eItem.split("=")[0]] = _eItem.split("=")[1];
			});
		}
		item.children = [];
		var children = itemData.children || [];
		if (isArray(children) && children.length) {
			for (var i = 0; i < children.length; i++) {
				var nItem = {},
					nItemData = children[i];
				setModuleList(nItem, nItemData);
				item.children.push(nItem);
			}
		}
		item.pointNumber = getItemUnread(item);
	}

	//设置菜单未读数
	function setModuleReadNum(_list, _key) {
		_list.forEach(function(_eItem) {
			_eItem[_key || "pointNumber"] = getItemUnread(_eItem);
		});
	}

	//获取菜单未读数
	function getItemUnread(_item) {
		var sys_unread = JSON.parse(getPrefs("sys_unread") || "{}");
		sys_unread.chatMsg = Number(getPrefs("chatMsg") || "0");
		var businessCode = _item.businessCode || "",
			unreadNum = 0;
		if (businessCode) {
			businessCode.split(",").forEach(function(_bItem) {
				unreadNum += sys_unread[_bItem] || 0;
			});
		}
		if (isArray(_item.children) && _item.children.length && !_item.businessOnly) {
			var nowPointType = getItemForKey("big", _item.children, "pointType")
					? "big"
					: "small",
				bigNum = 0,
				smallNum = 0;
			for (var i = 0; i < _item.children.length; i++) {
				var nItem = _item.children[i];
				var nowMunber = getItemUnread(nItem);
				if (nItem.pointType == "big") {
					bigNum += nowMunber;
				} else {
					smallNum += nowMunber;
				}
			}
			if (nowPointType == "big") {
				unreadNum += bigNum;
			} else {
				unreadNum += bigNum + smallNum;
			}
			if (nowPointType == "big" && unreadNum == 0 && smallNum != 0) {
				nowPointType = "small";
				unreadNum = smallNum;
			}
			_item.pointType = nowPointType;
		}
		return unreadNum;
	}

	// 获取所有地区 //area/tree
	function getSysAreas(_param, _callback) {
		var otherUrl = (_param || {}).url;
		ajax(
			{u: otherUrl || appUrl() + "login/areas", areaId: getPrefs("sys_platform")},
			otherUrl || "areas",
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					setPrefs(otherUrl ? "sys_allAreas" : "sys_areas", JSON.stringify(data));
					areaNotice({key: ""});
				}
				_callback(ret, err);
			},
			"所有地区",
			"post",
			{
				body: JSON.stringify({query: {isUsing: 1}})
			}
		);
	}

	//地区切换通知
	function areaNotice(extra) {
		[
			"module",
			"news1",
			"news2",
			"my",
			"negotiable",
			"addressBook",
			"workstation"
		].forEach(function(_eItem) {
			sendEvent({name: "areaChange_" + _eItem, extra: extra});
		});
	}

	//获取app底部菜单
	function getAppMenus(_param, _callback) {
		ajax(
			{
				u:
					appUrl() + "login/appMenus" + (G.terminal == "PUBLIC" ? "/anonymous" : "")
			},
			"appMenus" + _param.code,
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					data = data.filter(function(item) {
						return item.isShow == 1;
					});
					setPrefs("appMenuTrue", JSON.stringify(data));
					_callback(data, null);
				} else {
					_callback(ret, err);
				}
			},
			"app模块",
			"post",
			{
				body: JSON.stringify({})
			}
		);
	}

	function getAllChatInfo() {
		var chatInfos = JSON.parse(getPrefs("chatInfos") || "[]"),
			addInfo = [];
		chatInfos.forEach(function(_eItem) {
			if (_eItem.id) {
				if (/^(?!h|(\.)|(\/)).*/.test(_eItem.url)) {
					_eItem.url = showImg(_eItem.url);
				}
				addInfo.push(_eItem);
			}
		});
		G.chatInfos = addInfo;
		setPrefs("chatInfos", JSON.stringify(G.chatInfos));
		return G.chatInfos;
	}

	function setAllChatInfo(_item) {
		getAllChatInfo();
		var nowInfo = JSON.parse(JSON.stringify(G.chatInfos));
		if (isArray(_item)) {
			_item.forEach(function(_eItem) {
				if (_eItem.id) {
					delItemForKey(_eItem.id, nowInfo, "id");
					nowInfo.push(_eItem);
				}
			});
		} else {
			if (!_item.id) {
				return;
			}
			delItemForKey(_item.id, nowInfo, "id");
			nowInfo.push(_item);
		}
		setPrefs("chatInfos", JSON.stringify(nowInfo));
		setRongChatInfo();
	}

	function setRongChatInfo() {
		if (platform() == "app" && api.require("zyRongCloudRTC")) {
			getAllChatInfo();
			var setUser = {
				apply: 1,
				users: G.chatInfos.map(function(obj) {
					return {id: chatHeader() + obj.id, name: obj.name, url: obj.url};
				}),
				groupUser: JSON.parse(getPrefs("rongGroupUser") || "[]"), //当前通话的群组成员列表id集合
				totalNumber: 10 //语音或视频最大人数
			};
			// console.log("融云设置信息："+JSON.stringify(setUser));
			api.require("zyRongCloudRTC").setUsers(setUser, function(ret) {
				removePrefs("rongGroupUser");
				setRongChatInfo();
				setTimeout(
					function() {
						sendEvent({name: "chat_refresh"});
					},
					api.systemType == "ios" ? 600 : 1500
				);
			});
		}
	}

	//打开扫码
	function openScan(_param, _callback) {
		function scanResult(_content) {
			if (_content.indexOf(chatHeader() + "|") != -1) {
				var params = _content.split("|");
				if (params.length < 3) {
					alert({title: "识别结果为：", msg: _content, buttons: ["关闭"]}, function(
						ret,
						err
					) {});
					return;
				}
				switch (params[1]) {
					case "login": //登录
						showProgress("登录中");
						ajax(
							{u: appUrl() + "scanCodeLogin/receipt/appToken?qrCodeId=" + params[2]},
							"qrCodeId",
							function(ret, err) {
								hideProgress();
								toast(ret ? ret.message || ret.data : NET_ERR);
								if (ret && ret.code == 200) {
									_callback && _callback();
								}
							},
							params[1]
						);
						break;
					case "activityCode":
						activityOption(
							{
								id: params[2],
								state: "签到中",
								signInCommand: params[3],
								toast: "签到中"
							},
							_callback
						);
						break;
					case "groupQr": //扫群二维码
						var _id = params[2];
						showProgress();
						ajax(
							{u: appUrl() + "chatGroup/info"},
							"chatGroup/info" + _id,
							function(ret, err) {
								hideProgress();
								var data = ret ? ret.data || {} : {};
								if (!ret || ret.code != "200" || !data.id) {
									toast("获取群信息失败，请稍候重试");
									return;
								}
								var memberUserIds = data.memberUserIds || [];
								if (getItemForKey(G.userId, memberUserIds)) {
									openWin_chat({
										conversationType: "GROUP",
										targetId: chatHeader() + data.id
									});
									return;
								}
								showProgress("加入中");
								ajax(
									{u: appUrl() + "chatGroup/edit"},
									"chatGroup/edit" + _id,
									function(ret, err) {
										hideProgress();
										if (!ret || ret.code != "200") {
											toast("加入群组失败，请稍候重试");
											return;
										}
										openWin_chat({
											conversationType: "GROUP",
											targetId: chatHeader() + data.id,
											paramMore: {joinType: "groupQr"}
										});
									},
									"\u52A0\u5165\u7FA4\u7EC4" + _id,
									"post",
									{
										body: JSON.stringify({
											form: {groupName: data.groupName, id: data.id},
											memberUserIds: memberUserIds.concat([G.userId]),
											ownerUserId: data.ownerUserId
										})
									}
								);
							},
							"\u83B7\u53D6\u7FA4\u4FE1\u606F" + _id,
							"post",
							{
								body: JSON.stringify({detailId: _id})
							}
						);

						break;
					case "toWygzsSignApp":
						var url = "";
						if (params.length > 4) {
							url = params[2] + "app/wygzsApp/operationActivityUserByUser?";
						} else {
							alert(
								{title: "提示", msg: "后台未配置有误,请联系管理员", buttons: ["关闭"]},
								function(ret, err) {}
							);
							return;
						}
						var paramObj = {activityId: params[4], type: 2, status: 1}; //type:2 签到操作 , status: 1签到
						showProgress("签到中");
						ajax(
							{u: url},
							"serByUser",
							function(ret, err) {
								hideProgress();
								toast(ret ? ret.message || ret.data : NET_ERR);
								if (ret && ret.code == 200) {
									var workstationsUrl =
										params[3] +
										"/#/activitiesDetail?id=" +
										params[4] +
										"&token={{token}}&areaId={{areaId}}";
									setTimeout(function() {
										// openDetails({code:"8897",link:workstationsUrl,title:'详情'},null,_this);
									}, 1500);
								}
							},
							"联络站扫码签到接口",
							"post",
							{
								values: paramObj
							},
							{
								"content-type": "application/x-www-form-urlencoded"
							}
						);

						break;
					case "zhtmeetingSignIn": //会议签到--智会通
						var url = "";
						if (params.length > 3) {
							url = params[2] + "appMeetSign/tosignin?";
						} else {
							alert(
								{title: "提示", msg: "后台未配置有误,请联系管理员", buttons: ["关闭"]},
								function(ret, err) {}
							);
							return;
						}
						var paramObj = {
							meetId: params[3],
							activityId: params[3],
							conferenceId: params[3],
							dataId: params[3],
							signInType: "qrCode",
							userId: G.userId,
							type: "signIn"
						};

						showProgress("签到中");
						ajax(
							{u: url},
							"tosignin",
							function(ret, err) {
								hideProgress();
								toast(ret ? ret.message || ret.data : NET_ERR);
							},
							"会议系统签到接口",
							"post",
							{
								values: paramObj
							},
							{
								"content-type": "application/x-www-form-urlencoded"
							}
						);

						break;
					case "meettingCode": //会务系统签到
						meettingOption(
							{
								id: params[2],
								state: "签到中",
								signInCommand: params[3],
								toast: "签到中"
							},
							_callback
						);
						break;
					case "noticeCode": //公告详情
						openWin_notice({id: params[2]});
						break;
					default:
						alert({title: "识别结果为：", msg: _content, buttons: ["关闭"]}, function(
							ret,
							err
						) {});
						break;
				}
			} else if (_content.indexOf("http") == 0) {
				if (
					(_content.indexOf("pages/index/index.html?") != -1 ||
						_content.indexOf("pages/index/?") != -1) &&
					_content.indexOf(".stml") != -1
				) {
					var d = JSON.parse(
						decodeURI(_content.substring(_content.indexOf("?") + 1))
					);
					d.p.qr = 1;
					openWin(d.n, d.u, d.p);
				} else if (_content.indexOf(appUrl() + "viewing/") != -1) {
					showProgress("打开中");
					ajax(
						{u: appUrl() + "longLink/" + _content.replace(appUrl() + "viewing/", "")},
						"qrCodeId",
						function(ret, err) {
							hideProgress();
							if (ret && ret.code == 200 && ret.data.indexOf("http") == 0) {
								var d = JSON.parse(
									decodeURI(ret.data.substring(ret.data.indexOf("?") + 1))
								);
								d.p.qr = 1;
								openWin(d.n, d.u, d.p);
							} else {
								toast(ret ? ret.message || ret.data : NET_ERR);
							}
						},
						"获取长链接"
					);
				} else {
					openWin_url({url: _content});
				}
			} else {
				alert({title: "识别结果为：", msg: _content, buttons: ["关闭"]}, function(
					ret,
					err
				) {});
			}
		}
		if (platform() == "app") {
			var preName = "camera";
			if (
				!confirmPer(
					preName,
					"getPicture",
					"用于打开摄像头并扫码，若取消将无法使用扫一扫功能"
				)
			) {
				addEventListener(preName + "Per_" + "getPicture", function(ret, err) {
					if (ret.value.granted) {
						openScan(_param, _callback);
					}
					removeEventListener(preName + "Per_" + "getPicture");
				});
				return;
			}
			api.require("zyHmsScan").openDefaultView({}, function(ret) {
				if (ret.status) {
					setTimeout(function() {
						scanResult(ret.result);
					}, 300);
				}
			});
		} else if (platform() == "mp") {
			wx.scanCode({
				success: function success(ret) {
					setTimeout(function() {
						scanResult(ret.result);
					}, 300);
				}
			});
		} else {
			G.qrcodePop = {
				show: true,
				callback: function callback(ret) {
					scanResult(ret.result);
				}
			};
		}
	}

	// 活动签到报名等操作
	function activityOption(_param, _callback) {
		var url = "",
			param = {
				form: {
					activityId: _param.id
				}
			};

		if (_param.state == "报名中") {
			if (_param.joinStatus == "nosign") {
				url = appUrl() + "activityperson/sign";
			} else {
				url = appUrl() + "activityperson/cancelsign";
			}
		} else if (_param.state == "签到中") {
			url = appUrl() + "activityperson/join";
			param.signInCommand = _param.signInCommand;
		}
		ajaxProcess(
			{
				toast: _param.toast || "操作中",
				url: url,
				param: param,
				name: _param.state
			},
			function(ret, err) {
				_callback && _callback(ret, err);
			}
		);
	}

	// 会务系统会议签到操作
	function meettingOption(_param, _callback) {
		if (!_param.id || !_param.signInCommand) {
			toast("此会议二维码有误！请联系管理员");
			return;
		}
		var url = appUrl() + "zyMeetUser/qrSignIn";
		var param = {
			signInCommand: _param.signInCommand,
			form: {
				meetId: _param.id
			}
		};

		ajaxProcess(
			{
				toast: _param.toast || "操作中",
				url: url,
				param: param,
				name: _param.state
			},
			function(ret, err) {
				_callback && _callback(ret, err);
			}
		);
	}

	function dealVideoId(_id) {
		return _id.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, "");
	}

	//当前页面播放视频集合
	function videoPlayPush(_id) {
		if (!G.playVideos) {
			G.playVideos = [];
		}
		if (getItemForKey(_id, G.playVideos)) {
			return;
		}
		videoPlayRemoves();
		G.playVideos.push(_id);
		console.log("当前播放：" + JSON.stringify(G.playVideos));
	}

	//去除某个视频播放
	function videoPlayRemove(_id) {
		delItemForKey(_id, G.playVideos);
	}

	//去除所有播放
	function videoPlayRemoves() {
		(G.playVideos || []).forEach(function(_id) {
			document.getElementById(_id) && document.getElementById(_id).pause();
			videoPlayRemove(_id);
		});
	}

	var ZTag = /*@__PURE__*/ (function(Component) {
		function ZTag(props) {
			Component.call(this, props);
		}

		if (Component) ZTag.__proto__ = Component;
		ZTag.prototype = Object.create(Component && Component.prototype);
		ZTag.prototype.constructor = ZTag;
		ZTag.prototype.getTagStyle = function() {
			var color = this.props.color || G.appTheme;
			var rc = "",
				rbg = "",
				rbor = "";
			switch (this.props.type) {
				case "2":
					rc = color;
					rbg = "#FFF";
					rbor = colorRgba(color);
					break;
				case "3":
					rc = "#FFF";
					rbg = color;
					rbor = color;
					break;
				default:
					rc = color;
					rbg = colorRgba(color, 0.1);
					rbor = "transparent";
					break;
			}

			return (
				"color:" +
				rc +
				";background:" +
				rbg +
				";border: 1px solid " +
				rbor +
				";border-radius: " +
				(this.props.roundSize || 2) +
				"px;"
			);
		};
		ZTag.prototype.render = function() {
			return apivm.h(
				"view",
				{style: "flex-direction:row;"},
				apivm.h(
					"text",
					{
						id: "" + (this.props.id || ""),
						class: "tag_box " + (this.props.class || ""),
						style:
							"" +
							loadConfiguration((this.props.size || 0) - 2) +
							this.getTagStyle() +
							(this.props.style || "")
					},
					this.props.text || this.props.children
				)
			);
		};

		return ZTag;
	})(Component);
	ZTag.css = {".tag_box": {padding: "1px 10px"}};
	apivm.define("z-tag", ZTag);

	var ZVideo = /*@__PURE__*/ (function(Component) {
		function ZVideo(props) {
			Component.call(this, props);
			this.data = {
				controls: true
			};
			this.compute = {
				getSrc: function() {
					var src = this.props.src;
					if (src.indexOf("http") != 0) {
						src = appUrl() + "file/preview/" + src;
					}
					return src;
				}
			};
		}

		if (Component) ZVideo.__proto__ = Component;
		ZVideo.prototype = Object.create(Component && Component.prototype);
		ZVideo.prototype.constructor = ZVideo;
		ZVideo.prototype.firstPause = function() {
			var this$1 = this;

			if (!this.isPause) {
				this.isPause = true;
				if (platform() == "app" && !this.props.poster) {
					this.data.controls = false;
					setTimeout(function() {
						if (!this$1.props.autoplay) {
							document.getElementById(dealVideoId(this$1.props.src)) &&
								document.getElementById(dealVideoId(this$1.props.src)).pause();
						}
						this$1.data.controls = true;
					}, 50);
				} else {
					this.data.controls = true;
				}
			}
		};
		ZVideo.prototype.loadedmetadata = function(e) {
			if (api.systemType != "ios") {
				this.isPause = false;
				this.firstPause();
			}
		};
		ZVideo.prototype.play = function() {
			videoPlayPush(dealVideoId(this.props.src));
		};
		ZVideo.prototype.pause = function() {
			videoPlayRemove(dealVideoId(this.props.src));
		};
		ZVideo.prototype.render = function() {
			return apivm.h("video", {
				id: dealVideoId(this.props.src),
				style: "width:100%;height:" + G.pageWidth * 0.56 + "px;",
				controls: this.data.controls,
				autoplay:
					(platform() == "app" && !this.props.poster) || this.props.autoplay,
				onLoadedmetadata: this.loadedmetadata,
				onWaiting: this.firstPause,
				onPlay: this.play,
				onPause: this.pause,
				onEnded: this.pause,
				src: this.getSrc,
				poster: showImg(this.props.poster)
			});
		};

		return ZVideo;
	})(Component);
	apivm.define("z-video", ZVideo);

	var Item5 = /*@__PURE__*/ (function(Component) {
		function Item5(props) {
			Component.call(this, props);
		}

		if (Component) Item5.__proto__ = Component;
		Item5.prototype = Object.create(Component && Component.prototype);
		Item5.prototype.constructor = Item5;
		Item5.prototype.handClick = function(e) {
			stopBubble(e);
		};
		Item5.prototype.openVideos = function(e, _item, _index) {
			this.handClick(e);
			_item.autoplay = true;
			_item.showCode = "QUANLANSP";
		};
		Item5.prototype.showAxis = function() {
			if (!this.props.index) {
				return true;
			}
			if (
				this.props.item.isTop == "1" &&
				this.props.list[this.props.index - 1].isTop == 1
			) {
				return false;
			}
			if (
				dayjs(this.props.item.time).format("YYYY-MM-DD") !=
				dayjs(this.props.list[this.props.index - 1].time).format("YYYY-MM-DD")
			) {
				return true;
			}
			return false;
		};
		Item5.prototype.getPlayState = function() {
			var playItem = {src: "shengyin1", text: "播放"};
			if (this.props.item.id == this.props.floatModule.id) {
				switch (this.props.floatModule.state + "") {
					case "0":
						playItem.src = "shengyin1";
						playItem.text = "播放";
						break;
					case "1":
						playItem.src = "shengyin";
						playItem.text = "暂停";
						break;
					case "2":
						playItem.src = "shengyinjingyin";
						playItem.text = "继续播放";
						break;
					case "3":
						playItem.src = "shengyin1";
						playItem.text = "重新播放";
						break;
				}
			} else {
				playItem.src = "shengyin1";
				playItem.text = "播放";
			}
			return playItem;
		};
		Item5.prototype.playing = function(e) {
			this.handClick(e);
			if (this.props.item.id == this.props.floatModule.id) {
				if (this.props.floatModule.state == "1") {
					sendEvent({name: "index", extra: {type: "pauseStartPlay"}});
				} else {
					sendEvent({name: "index", extra: {type: "reStartPlay"}});
				}
			} else {
				sendEvent({
					name: "index",
					extra: {
						type: "startPlay",
						floatType: this.props.item.code,
						id: this.props.item.id,
						src: this.props.item.content
					}
				});
			}
		};
		Item5.prototype.openDetail = function(e) {
			openWin_news(this.props.item);
			this.handClick(e);
		};
		Item5.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					class: "news_item",
					style:
						"padding-left:" + (this.props.layoutId == "SJCXS" ? "24" : "16") + "px;",
					onClick: this.openDetail
				},
				apivm.h("view", {
					class: "news_point",
					style:
						"display:" +
						(this.props.layoutId == "SJCXS" && this.showAxis() ? "block" : "none") +
						";background:" +
						G.appTheme +
						";"
				}),
				apivm.h("view", {
					class: "news_line_top",
					style:
						"display:" +
						(this.props.layoutId == "SJCXS" ? "block" : "none") +
						";background:" +
						colorRgba(G.appTheme, this.props.index ? 0.15 : 0) +
						";"
				}),
				apivm.h("view", {
					class: "news_line_bottom",
					style:
						"display:" +
						(this.props.layoutId == "SJCXS" ? "block" : "none") +
						";background:" +
						colorRgba(
							G.appTheme,
							isArray(this.props.list) &&
								this.props.index == this.props.list.length - 1
								? 0
								: 0.15
						) +
						";"
				}),
				apivm.h(
					"view",
					null,
					this.props.layoutId == "SJCXS" &&
						apivm.h(
							"text",
							{
								style:
									"display:" +
									(this.showAxis() ? "block" : "none") +
									";" +
									loadConfiguration(-2) +
									"font-weight: 600;color:#333;margin-bottom:10px;"
							},
							this.props.item.isTop == 1
								? "置顶"
								: dayjs(this.props.item.time).format("YYYY-MM-DD")
						)
				),
				apivm.h(
					"view",
					null,
					((this.props.item.showCode == "QUANLANSP" && this.props.item.videoHrefs) ||
						isArray(this.props.item.url) ||
						this.props.item.code == "7") && [
						apivm.h(
							"view",
							{class: "flex_row"},
							apivm.h(
								"view",
								null,
								this.props.item.code == "7" &&
									apivm.h("view", {
										style:
											loadConfigurationSize(-2, "h") +
											"width:3px;background:" +
											G.appTheme +
											";margin-right:4px;"
									})
							),
							apivm.h(
								"view",
								{class: "flex_row", style: "flex:1;flex-wrap: wrap;"},
								this.props.item.title.split("").map(function(nItem, nIndex) {
									return (
										nIndex <
											Math.floor((G.pageWidth - 40) / (G.appFontSize + 2)) -
												(this$1.props.item.code == "7" ? 3 : 1) &&
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"margin:2px 0;font-weight: 600;color: " +
													(this$1.props.search &&
													this$1.props.search.result &&
													this$1.props.search.result.indexOf(nItem) > -1
														? G.appTheme
														: "#333") +
													";"
											},
											nIndex ==
												Math.floor((G.pageWidth - 40) / (G.appFontSize + 2)) -
													(this$1.props.item.code == "7" ? 3 : 1) -
													1
												? "..."
												: nItem
										)
									);
								})
							),
							apivm.h(
								"view",
								{class: "flex_row", style: "margin-left:5px;"},
								this.props.item.code == "7" &&
									apivm.h(
										"text",
										{
											style:
												loadConfiguration(-1) +
												"color:#999;flex-shrink: 0;margin-right:2px;"
										},
										"查看"
									),
								apivm.h(
									"view",
									null,
									apivm.h("a-iconfont", {
										name: "xiangxia1",
										style: "transform: rotate(-90deg);",
										color: "#666",
										size: "" + G.appFontSize
									})
								)
							)
						),
						apivm.h(
							"view",
							{style: "margin-top:10px;"},
							this.props.item.videoHrefs && this.props.item.showCode == "QUANLANSP"
								? apivm.h(
										"view",
										{onClick: this.handClick},
										apivm.h("z-video", {
											autoplay: this.props.item.autoplay,
											poster: this.props.item.poster,
											src: this.props.item.videoHrefs
										})
								  )
								: isArray(this.props.item.url)
								? apivm.h(
										"view",
										{class: "flex_row"},
										(Array.isArray(this.props.item.url)
											? this.props.item.url
											: Object.values(this.props.item.url)
										).map(function(nItem, nIndex) {
											return apivm.h(
												"view",
												{style: "width:33.33%;height:84px;margin: 0 2px;"},
												apivm.h("image", {
													class: "xy_100",
													src: showImg(nItem),
													mode: "aspectFill",
													thumbnail: "false"
												})
											);
										})
								  )
								: apivm.h("image", {
										style: "width:100%;height: " + G.pageWidth * 0.52 + "px;",
										src: showImg(this.props.item),
										mode: "aspectFill",
										thumbnail: "false"
								  })
						),
						apivm.h(
							"view",
							null,
							this.props.item.code != "7" &&
								apivm.h(
									"view",
									{style: "margin-top:10px;", class: "flex_row"},
									apivm.h(
										"view",
										{class: "flex_w flex_row"},
										this.props.layoutId != "SJCXS" &&
											apivm.h(
												"text",
												{
													class: "c_999 " + (this.props.item.url ? "text_one" : ""),
													style: "margin-right:10px;" + loadConfiguration(-4) + ";"
												},
												dayjs(this.props.item.time).format("YYYY-MM-DD")
											),
										apivm.h(
											"text",
											{class: "c_999 text_one", style: loadConfiguration(-4) + ";"},
											this.props.item.source
										)
									),
									apivm.h(
										"view",
										{style: "flex-shrink: 0;"},
										isObject(this.props.floatModule) &&
											this.props.item.content &&
											platform() == "app" &&
											api.require("voiceRecognizer") &&
											apivm.h(
												"view",
												{onClick: this.playing, class: "flex_row"},
												apivm.h("a-iconfont", {
													name: this.getPlayState().src,
													color: G.appTheme,
													size: G.appFontSize
												}),
												apivm.h(
													"text",
													{
														style:
															"margin-left:2px;" +
															loadConfiguration(-3) +
															";color:" +
															G.appTheme +
															";"
													},
													this.getPlayState().text
												)
											)
									),
									apivm.h(
										"view",
										{style: "flex-shrink: 0;"},
										this.props.item.sourceModuleName &&
											apivm.h(
												"z-tag",
												{style: "margin-left:10px;", type: "2", color: G.appTheme},
												this.props.item.sourceModuleName
											)
									)
								)
						)
					]
				),
				apivm.h(
					"view",
					null,
					(this.props.item.showCode != "QUANLANSP" || !this.props.item.videoHrefs) &&
						!isArray(this.props.item.url) &&
						this.props.item.code != "7" &&
						apivm.h(
							"view",
							{class: "flex_row"},
							apivm.h(
								"view",
								null,
								this.props.item.url &&
									apivm.h(
										"view",
										{class: "news_item_img"},
										apivm.h("image", {
											class: "xy_100",
											src: showImg(this.props.item),
											mode: "aspectFill",
											thumbnail: "false"
										}),
										this.props.item.videoHrefs &&
											this.props.item.showCode == "ZUOCESP" &&
											apivm.h(
												"view",
												{
													style:
														"position:absolute;z-index:1;left:0;top:0;right:0;bottom:0;align-items: center;justify-content: center;",
													onClick: function(e) {
														return this$1.openVideos(
															e,
															this$1.props.item,
															this$1.props.index
														);
													}
												},
												apivm.h("image", {
													mode: "aspectFill",
													style: loadConfigurationSize(22),
													thumbnail: "false",
													src: shareAddress(1) + "image/icon_play.png"
												})
											)
									)
							),
							apivm.h(
								"view",
								{class: "flex_w"},
								apivm.h(
									"view",
									{class: "flex_row", style: "flex-wrap: wrap;"},
									this.props.item.title.split("").map(function(nItem, nIndex) {
										return (
											nIndex <
												Math.floor(
													(G.pageWidth - 40 - (this$1.props.item.url ? 122 : 0)) /
														(G.appFontSize + 2)
												) *
													2 &&
											apivm.h(
												"text",
												{
													style:
														loadConfiguration(1) +
														"margin:2px 0;font-weight: 600;color: " +
														(this$1.props.search &&
														this$1.props.search.result &&
														this$1.props.search.result.indexOf(nItem) > -1
															? G.appTheme
															: "#333") +
														";"
												},
												nIndex ==
													Math.floor(
														(G.pageWidth - 40 - (this$1.props.item.url ? 122 : 0)) /
															(G.appFontSize + 2)
													) *
														2 -
														1
													? "..."
													: nItem
											)
										);
									})
								),
								apivm.h(
									"view",
									null,
									this.props.item.url &&
										apivm.h(
											"text",
											{
												class: "c_999 text_one",
												style: "margin-top:2px;" + loadConfiguration(-4) + ";"
											},
											this.props.item.source
										)
								),
								apivm.h(
									"view",
									{class: "flex_row", style: "margin-top:3px;"},
									apivm.h(
										"view",
										{class: "flex_w flex_row"},
										this.props.layoutId != "SJCXS" &&
											apivm.h(
												"text",
												{
													class: "c_999 " + (this.props.item.url ? "text_one" : ""),
													style: "margin-right:10px;" + loadConfiguration(-4) + ";"
												},
												dayjs(this.props.item.time).format("YYYY-MM-DD")
											),
										!this.props.item.url &&
											apivm.h(
												"text",
												{class: "c_999 text_one", style: loadConfiguration(-4) + ";"},
												this.props.item.source
											)
									),
									apivm.h(
										"view",
										{style: "flex-shrink: 0;"},
										isObject(this.props.floatModule) &&
											this.props.item.content &&
											platform() == "app" &&
											api.require("voiceRecognizer") &&
											apivm.h(
												"view",
												{onClick: this.playing, class: "flex_row"},
												apivm.h("a-iconfont", {
													name: this.getPlayState().src,
													color: G.appTheme,
													size: G.appFontSize
												}),
												apivm.h(
													"text",
													{
														style:
															"margin-left:2px;" +
															loadConfiguration(-4) +
															";color:" +
															G.appTheme +
															";"
													},
													this.getPlayState().text
												)
											)
									),
									apivm.h(
										"view",
										{style: "flex-shrink: 0;"},
										this.props.item.sourceModuleName &&
											apivm.h(
												"z-tag",
												{style: "margin-left:10px;", type: "2", color: G.appTheme},
												this.props.item.sourceModuleName
											)
									)
								)
							)
						)
				)
			);
		};

		return Item5;
	})(Component);
	Item5.css = {
		".c_999": {color: "#999"},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden",
			display: "inline"
		},
		".news_item": {padding: "15px 16px"},
		".news_item_img": {
			width: "112px",
			height: "84px",
			marginRight: "10px",
			borderRadius: "2px"
		},
		".news_point": {
			position: "absolute",
			zIndex: "999",
			left: "10px",
			top: "21px",
			borderRadius: "50%",
			width: "7px",
			height: "7px"
		},
		".news_line_top": {
			position: "absolute",
			zIndex: "999",
			left: "13px",
			top: "0",
			height: "24px",
			width: "1px"
		},
		".news_line_bottom": {
			position: "absolute",
			zIndex: "999",
			left: "13px",
			top: "24px",
			bottom: "0",
			width: "1px"
		}
	};
	apivm.define("item5", Item5);

	var ZAlert = /*@__PURE__*/ (function(Component) {
		function ZAlert(props) {
			Component.call(this, props);
			this.data = {
				show: false,

				inputBox: {
					dotIcon: true,
					value: "",
					placeholder: "",
					autoFocus: true,
					cleanFocus: true,
					height: 150,
					confirmType: ""
				},

				inputBoxPass: {
					dotIcon: true,
					value: "",
					placeholder: "",
					inputType: "password",
					autoFocus: false,
					cleanFocus: true,
					height: 150,
					confirmType: ""
				},

				marginBottom: 0,
				timeout: 0
			};
			this.compute = {
				monitor: function() {
					var dm = this.props.dataMore;
					if (dm.show != this.data.show) {
						this.data.inputBox.autoFocus = true;
						this.data.show = dm.show;
						this.data.marginBottom = 0;
						if (this.data.show) {
							this.data.timeout = dm.timeout || 0;
							if (this.data.timeout > 0) {
								this.startTime();
							}
							if (
								this.props.dataMore.type == "input" ||
								this.props.dataMore.type == "textarea"
							) {
								this.data.inputBox.value = dm.content;
								this.data.inputBox.placeholder = dm.placeholder;
								this.data.inputBox.confirmType = dm.confirmType || "done";

								this.data.inputBoxPass.value = dm.content2;
								this.data.inputBoxPass.placeholder = dm.placeholder2;
								this.data.inputBoxPass.confirmType = dm.confirmType2 || "done";
							}
						}
					}
				}
			};
		}

		if (Component) ZAlert.__proto__ = Component;
		ZAlert.prototype = Object.create(Component && Component.prototype);
		ZAlert.prototype.constructor = ZAlert;
		ZAlert.prototype.closePage = function(_type) {
			this.props.dataMore.show = false;
			if (!_type) {
				G.alertPop.callback({buttonIndex: 2});
			}
		};
		ZAlert.prototype.closeStop = function(e) {
			stopBubble(e);
		};
		ZAlert.prototype.inputFocus = function(e) {
			if (platform() == "app" && api.systemType == "ios") {
				this.data.marginBottom = e.detail.height;
			}
		};
		ZAlert.prototype.inputBlur = function(e) {
			this.data.marginBottom = 0;
		};
		ZAlert.prototype.itemClick = function() {
			if (this.data.timeout > 0) {
				return;
			}
			if (
				this.props.dataMore.type == "input" ||
				this.props.dataMore.type == "textarea"
			) {
				this.props.dataMore.content = this.data.inputBox.value;
				this.props.dataMore.content2 = this.data.inputBoxPass.value;
			}
			this.props.dataMore.buttonIndex = 1;
			G.alertPop.callback(this.props.dataMore);
			this.closePage(1);
		};
		ZAlert.prototype.startTime = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.data.timeout--;
				if (this$1.data.timeout >= 0) {
					this$1.startTime();
				} else if (this$1.props.dataMore.autoClose) {
					this$1.itemClick();
				}
			}, 1000);
		};
		ZAlert.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box xy_center",
					onClick: this.closeStop,
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);z-index:1001;"
				},
				this.data.show && [
					apivm.h(
						"view",
						{
							class: "alert_warp",
							style: "margin-bottom:" + this.data.marginBottom + "px;",
							onClick: this.closeStop
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							null,
							(this.props.dataMore.title || this.props.dataMore.closeType == "2") &&
								apivm.h(
									"view",
									{style: "padding: 20px 20px 0;"},
									this.props.dataMore.title &&
										apivm.h(
											"text",
											{class: "alert_title", style: "" + loadConfiguration(4)},
											this.props.dataMore.title
										),
									apivm.h(
										"view",
										{
											style:
												"display:" +
												(this.props.dataMore.closeType == "2" ? "flex" : "none") +
												";position:absolute;right:0;top:0;"
										},
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.closePage();
												},
												style: "padding:20px 20px 10px 10px;"
											},
											apivm.h("a-iconfont", {
												name: "cuohao",
												color: "#666",
												size: G.appFontSize + 4
											})
										)
									)
								)
						),
						apivm.h(
							"scroll-view",
							{class: "alert_content_box", "scroll-y": true},
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "input" &&
									apivm.h("z-input", {
										id: "input",
										dataMore: this.data.inputBox,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.inputPassword &&
									apivm.h("z-input", {
										id: "password",
										style: "margin-top:15px;",
										dataMore: this.data.inputBoxPass,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "textarea" &&
									apivm.h("z-textarea", {
										class: "alert_textarea",
										dataMore: this.data.inputBox,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "richText" &&
									apivm.h("z-rich-text", {
										detail: true,
										nodes: this.props.dataMore.content
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "text" &&
									apivm.h(
										"text",
										{class: "alert_content", style: "" + loadConfiguration(1)},
										this.props.dataMore.content
									)
							)
						),
						apivm.h(
							"view",
							null,
							this.props.dataMore.closeType == "1" && [
								apivm.h(
									"view",
									{style: "width:100%;height:1px;padding:0 15px;flex-shrink: 0;"},
									apivm.h("view", {style: "height:1px;background: #F6F6F6;"})
								),
								apivm.h(
									"view",
									{class: "alert_btn_box"},
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.closePage();
											},
											class: "alert_btn_item",
											style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}
										},
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color:" +
													(this.props.dataMore.cancel.color == "appTheme"
														? G.appTheme
														: this.props.dataMore.cancel.color) +
													";"
											},
											this.props.dataMore.cancel.text
										)
									),
									apivm.h(
										"view",
										{style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}},
										apivm.h("view", {style: "width:1px;height:30px;background:#F6F6F6;"})
									),
									apivm.h(
										"view",
										{
											onClick: this.itemClick,
											class: "alert_btn_item",
											style: {display: this.props.dataMore.sure.show ? "flex" : "none"}
										},
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color:" +
													(this.props.dataMore.sure.color == "appTheme"
														? G.appTheme
														: this.props.dataMore.sure.color) +
													";opacity:" +
													(this.data.timeout > 0 ? "0.5" : "1") +
													";"
											},
											this.props.dataMore.sure.text,
											this.data.timeout > 0 ? "(" + this.data.timeout + ")" : ""
										)
									)
								)
							]
						),
						apivm.h(
							"view",
							null,
							this.props.dataMore.closeType != "1" &&
								this.props.dataMore.sure.show &&
								apivm.h(
									"view",
									{style: "padding-bottom:15px;", class: "alert_btn_box"},
									apivm.h(
										"z-button",
										{
											style: "width:210px;",
											disabled: this.data.timeout > 0,
											color:
												this.props.dataMore.sure.color == "appTheme"
													? G.appTheme
													: this.props.dataMore.sure.color,
											round: true,
											onClick: this.itemClick
										},
										this.props.dataMore.sure.text,
										this.data.timeout > 0 ? "(" + this.data.timeout + ")" : ""
									)
								)
						)
					),
					apivm.h(
						"view",
						null,
						this.props.dataMore.closeType == "3" &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.closePage();
									},
									style: "margin-top:15px;"
								},
								apivm.h("a-iconfont", {
									style: "transform: rotate(45deg);",
									name: "gengduo1",
									color: "#FFF",
									size: G.appFontSize + 26
								})
							)
					)
				]
			);
		};

		return ZAlert;
	})(Component);
	ZAlert.css = {
		".alert_warp": {background: "#FFF", borderRadius: "10px", width: "320px"},
		".alert_content_box": {
			margin: "20px 15px",
			maxHeight: "385px",
			width: "auto"
		},
		".alert_title": {color: "#333333", fontWeight: "bold", textAlign: "center"},
		".alert_content": {
			width: "100%",
			textAlign: "center",
			color: "#333333",
			wordWrap: "break-word"
		},
		".alert_btn_box": {
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_btn_item": {
			flex: "1",
			padding: "10px",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_textarea": {
			borderColor: "#ccc !important",
			borderRadius: "10px",
			padding: "8px",
			width: "100%"
		}
	};
	apivm.define("z-alert", ZAlert);

	var YScrollView = /*@__PURE__*/ (function(Component) {
		function YScrollView(props) {
			Component.call(this, props);
			this.data = {
				mId: "",
				scroll_view: "", //滚动到id
				scroll_animation: true, //滚动动画
				refreshEd: false
			};
			this.compute = {
				monitor: function() {
					if (!this.data.mId) {
						var nowId = this.props.id || "scroll_view" + getNum();
						this.data.mId = !document.getElementById(nowId)
							? nowId
							: "scroll_view" + getNum();
						(this.props.dataMore || {}).id = this.data.mId;
					}
					var dataMore = this.props.dataMore || {};
					if (this.data.scroll_view != dataMore.scroll_view) {
						this.data.scroll_animation = isParameters(dataMore.scroll_animation)
							? dataMore.scroll_animation
							: true;
						this.data.scroll_view = dataMore.scroll_view || "";
						if (this.data.scroll_view) {
							this.scrollTo(this.data.scroll_view);
						}
					}
				}
			};
		}

		if (Component) YScrollView.__proto__ = Component;
		YScrollView.prototype = Object.create(Component && Component.prototype);
		YScrollView.prototype.constructor = YScrollView;
		YScrollView.prototype.onrefresherrefresh = function(e) {
			var this$1 = this;

			if (this.props._this && isFunction(this.props._this.pageRefresh)) {
				this.props._this.pageRefresh({detail: {}});
			} else {
				this.fire("lower", {});
			}
			this.data.refreshEd = true;
			setTimeout(function() {
				this$1.data.refreshEd = false;
			}, 800);
		};
		YScrollView.prototype.onscrolltolower = function(e) {
			if (this.props._this && isFunction(this.props._this.loadMore)) {
				this.props._this.loadMore({detail: {}});
			} else {
				this.fire("up", {});
			}
		};
		YScrollView.prototype.onscroll = function(ref) {
			var detail = ref.detail;

			if (this.props._this && isFunction(this.props._this.pageScroll)) {
				this.props._this.pageScroll({detail: detail});
			} else {
				this.fire("scroll", detail);
			}
		};
		YScrollView.prototype.scrollTo = function(nowView) {
			var this$1 = this;

			var _animated = this.data.scroll_animation;
			if (document.getElementById(this.data.mId) && platform() != "mp") {
				var scrollTo =
					platform() == "app"
						? {view: nowView, animated: _animated}
						: this.props.dataMore.direction == "horizontal"
						? {
								left: this.getOffestValue(document.getElementById(nowView)).left,
								behavior: _animated ? "smooth" : "instant"
						  }
						: {
								top: this.getOffestValue(document.getElementById(nowView)).top,
								behavior: _animated ? "smooth" : "instant"
						  };
				document.getElementById(this.data.mId).scrollTo(scrollTo);
			}
			setTimeout(function() {
				this$1.props.dataMore.scroll_view = "";
			}, 500);
		};
		YScrollView.prototype.getOffestValue = function(elem) {
			var Far = null;
			var topValue = elem && elem.offsetTop;
			var leftValue = elem && elem.offsetLeft;
			var offsetFar = elem && elem.offsetParent;
			while (offsetFar) {
				if (offsetFar.id == this.data.mId) {
					break;
				}
				topValue += offsetFar.offsetTop;
				leftValue += offsetFar.offsetLeft;
				Far = offsetFar;
				offsetFar = offsetFar.offsetParent;
				if (offsetFar.id == this.data.mId) {
					break;
				}
			}
			return {top: topValue, left: leftValue, Far: Far};
		};
		YScrollView.prototype.render = function() {
			return apivm.h(
				"scroll-view",
				{
					s: this.monitor,
					id: "" + this.data.mId,
					style: "flex:1;" + (this.props.style || ""),
					class: "" + (this.props.class || ""),
					"refresher-background": "rgba(0,0,0,0)",
					"scroll-x": isParameters(this.props["scroll-x"])
						? this.props["scroll-x"]
						: false,
					"scroll-y": isParameters(this.props["scroll-y"])
						? this.props["scroll-y"]
						: true,
					bounces: isParameters(this.props["bounces"])
						? this.props["bounces"]
						: false,
					"scroll-into-view": platform() == "mp" ? this.data.scroll_view : null,
					"scroll-with-animation":
						platform() == "mp" ? this.data.scroll_animation : null,
					"refresher-enabled": isParameters(this.props["refresh"])
						? this.props["refresh"] &&
						  (platform() != "app" ? !this.props._this.props.dataMore : true)
						: false,
					"refresher-triggered": this.data.refreshEd,
					onScrolltolower: this.onscrolltolower,
					onRefresherrefresh: this.onrefresherrefresh,
					onScroll: this.onscroll
				},
				this.props.children
			);
		};

		return YScrollView;
	})(Component);
	apivm.define("y-scroll-view", YScrollView);

	var ZTabsTwo = /*@__PURE__*/ (function(Component) {
		function ZTabsTwo(props) {
			Component.call(this, props);
			this.data = {
				scrollView: {
					scroll_view: "",
					direction: "horizontal"
				}
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (
						!this.show &&
						this.props.dataMore.data.length &&
						api.systemType == "ios"
					) {
						this.show = true;
						setTimeout(function() {
							document.getElementById(this$1.data.scrollView.id) &&
								document
									.getElementById(this$1.data.scrollView.id)
									.scrollTo({position: "upper", animated: false});
						}, 390);
					}
				},
				position: function() {},
				animated: function() {}
			};
		}

		if (Component) ZTabsTwo.__proto__ = Component;
		ZTabsTwo.prototype = Object.create(Component && Component.prototype);
		ZTabsTwo.prototype.constructor = ZTabsTwo;
		ZTabsTwo.prototype.tabClick = function(_item) {
			if (_item.readonly) {
				this.fire("readonly", _item);
				return;
			}
			if (this.props.dotDefault && this.props.dataMore.key == _item.key) {
				this.props.dataMore.key = "";
				this.fire("change", {key: this.props.dataMore.key});
				return;
			}
			if (this.props.dataMore.key == _item.key) {
				return;
			}
			this.props.dataMore.key = _item.key;
			this.fire("change", {key: this.props.dataMore.key});
			this.data.scrollView.scroll_view =
				(this.props.id || "") + "_two_" + _item.key;
		};
		ZTabsTwo.prototype.nTouchmove = function() {
			touchmove();
		};
		ZTabsTwo.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					style: "flex-direction:row;" + (this.props.style || ""),
					class: "" + (this.props.class || "")
				},
				apivm.h(
					"y-scroll-view",
					{
						style: "display: block;white-space: nowrap;",
						_this: this,
						dataMore: this.data.scrollView,
						"scroll-x": true,
						"scroll-y": false
					},
					(Array.isArray(this.props.dataMore.data || [])
						? this.props.dataMore.data || []
						: Object.values(this.props.dataMore.data || [])
					).map(function(item$1, index$1) {
						return apivm.h(
							"view",
							{
								id: (this$1.props.id || "") + "_two_" + item$1.key,
								class: "tabs_two_item",
								onClick: function() {
									return this$1.tabClick(item$1);
								},
								style:
									"width:" +
									(this$1.props.average
										? 100 / this$1.props.dataMore.data.length + "%"
										: "auto") +
									";",
								onTouchStart: this$1.nTouchmove,
								onTouchMove: this$1.nTouchmove,
								onTouchEnd: this$1.nTouchmove
							},
							apivm.h(
								"view",
								{
									class: "tabs_two_ibox",
									style:
										"background:" +
										(this$1.props.dataMore.key == item$1.key
											? this$1.props.color || G.appTheme
											: this$1.props.dColor || "#F8F8F8") +
										";"
								},
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(this$1.props.size) +
											";color:#" +
											(this$1.props.dataMore.key == item$1.key ? "fff" : "666") +
											";"
									},
									item$1.value
								)
							)
						);
					})
				)
			);
		};

		return ZTabsTwo;
	})(Component);
	ZTabsTwo.css = {
		".tabs_two_item": {
			flexDirection: "row",
			justifyContent: "center",
			display: "inline-block",
			padding: "8px 5px",
			textAlign: "center"
		},
		".tabs_two_ibox": {
			flexDirection: "row",
			justifyContent: "center",
			display: "inline-block",
			padding: "2px 8px",
			borderRadius: "2px"
		}
	};
	apivm.define("z-tabs-two", ZTabsTwo);

	var ZTabsOne = /*@__PURE__*/ (function(Component) {
		function ZTabsOne(props) {
			Component.call(this, props);
			this.data = {
				scrollView: {
					scroll_view: "",
					direction: "horizontal"
				}
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (
						!this.show &&
						this.props.dataMore.data.length &&
						api.systemType == "ios"
					) {
						this.show = true;
						setTimeout(function() {
							document.getElementById(this$1.data.scrollView.id) &&
								document
									.getElementById(this$1.data.scrollView.id)
									.scrollTo({position: "upper", animated: false});
						}, 390);
					}
				},
				position: function() {},
				animated: function() {}
			};
		}

		if (Component) ZTabsOne.__proto__ = Component;
		ZTabsOne.prototype = Object.create(Component && Component.prototype);
		ZTabsOne.prototype.constructor = ZTabsOne;
		ZTabsOne.prototype.tabClick = function(_item) {
			if (_item.readonly) {
				this.fire("readonly", _item);
				return;
			}
			if (this.props.dotDefault && this.props.dataMore.key == _item.key) {
				this.props.dataMore.key = "";
				this.fire("change", {key: this.props.dataMore.key});
				return;
			}
			if (this.props.dataMore.key == _item.key) {
				this.fire("dotchange", {key: this.props.dataMore.key});
				return;
			}
			this.props.dataMore.key = _item.key;
			this.fire("change", {key: this.props.dataMore.key});
			this.data.scrollView.scroll_view =
				(this.props.id || "") + "tabs_" + _item.key;
		};
		ZTabsOne.prototype.nTouchmove = function() {
			touchmove();
		};
		ZTabsOne.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					style: "flex-direction:row;" + (this.props.style || ""),
					class: "" + (this.props.class || "")
				},
				apivm.h(
					"y-scroll-view",
					{
						style: "display: block;white-space: nowrap;",
						_this: this,
						dataMore: this.data.scrollView,
						"scroll-x": true,
						"scroll-y": false
					},
					(Array.isArray(this.props.dataMore.data || [])
						? this.props.dataMore.data || []
						: Object.values(this.props.dataMore.data || [])
					).map(function(item$1, index$1) {
						return apivm.h(
							"view",
							{
								id: (this$1.props.id || "") + "tabs_" + item$1.key,
								class: "tabs_item",
								onClick: function() {
									return this$1.tabClick(item$1);
								},
								style:
									"width:" +
									(this$1.props.average
										? 100 / this$1.props.dataMore.data.length + "%"
										: "auto") +
									";",
								onTouchStart: this$1.nTouchmove,
								onTouchMove: this$1.nTouchmove,
								onTouchEnd: this$1.nTouchmove
							},
							apivm.h(
								"text",
								{
									class: "tabs_text",
									style:
										loadConfiguration(
											this$1.props.dataMore.key == item$1.key
												? this$1.props.activeSize
												: this$1.props.size
										) +
										";color:#" +
										(this$1.props.dataMore.key == item$1.key ? "333" : "666") +
										";font-weight: " +
										(this$1.props.dataMore.key == item$1.key ? "8" : "4") +
										"00;"
								},
								item$1.value
							),
							apivm.h("view", {
								class: "tabs_line",
								style:
									"background:" +
									(this$1.props.dataMore.key != item$1.key
										? "transparent"
										: this$1.props.color || G.appTheme) +
									";" +
									(this$1.props.lineS || "")
							})
						);
					})
				)
			);
		};

		return ZTabsOne;
	})(Component);
	ZTabsOne.css = {
		".tabs_item": {
			display: "inline-block",
			padding: "6px 9px",
			textAlign: "center"
		},
		".tabs_text": {textAlign: "center"},
		".tabs_line": {
			width: "80%",
			maxWidth: "38px",
			height: "2px",
			margin: "2px auto 1px auto"
		}
	};
	apivm.define("z-tabs-one", ZTabsOne);

	var ZInput = /*@__PURE__*/ (function(Component) {
		function ZInput(props) {
			Component.call(this, props);
			this.data = {
				inputId: this.props.id || "z_input" + getNum()
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.autoFocus) {
						this.props.dataMore.autoFocus = false;
						this.props.dataMore.inputId = this.data.inputId;
						setTimeout(function() {
							document.getElementById(this$1.data.inputId).focus();
						}, 500);
					}
					if (this.props.dataMore.inputId != this.data.inputId) {
						this.props.dataMore.inputId = this.data.inputId;
					}
				}
			};
		}

		if (Component) ZInput.__proto__ = Component;
		ZInput.prototype = Object.create(Component && Component.prototype);
		ZInput.prototype.constructor = ZInput;
		ZInput.prototype.inputConfirm = function(e) {
			document.getElementById(this.data.inputId).blur();
			this.fire("confirm", e.detail);
		};
		ZInput.prototype.inputIng = function(e) {
			var nValue = (e.detail || {}).value;
			if (!nValue) {
				//解决安卓上清空输入无效的问题
				if (!this.i) {
					this.props.dataMore.value = "";
					this.i = 1;
				} else {
					this.props.dataMore.value = " ".repeat(this.i++ % 2);
					this.props.dataMore.value = " ".repeat(this.i++ % 2);
				}
			} else {
				this.props.dataMore.value = nValue;
			}
			if (this.props.dataMore.number) {
				if (!this.props.dataMore.value) {
					return;
				}
				if (/[^-?\d+(\.\d+)?$]/.test(this.props.dataMore.value)) {
					this.props.dataMore.value = this.props.dataMore.value.replace(
						/[^-?\d+(\.\d+)?$]/g,
						""
					);
					toast("请输入数字！");
					return;
				}
			}
			if (this.props.dataMore.expression) {
				//有正则表达示
				this.props.dataMore.value = this.props.dataMore.value.replace(
					new RegExp(this.props.dataMore.expression, "g"),
					""
				);
			}
			this.fire("input", e.detail);
		};
		ZInput.prototype.inputBlur = function(e) {
			this.fire("blur", e.detail);
		};
		ZInput.prototype.inputFocus = function(e) {
			document.getElementById(this.data.inputId).focus();
			this.fire("focus", e.detail);
		};
		ZInput.prototype.clean = function() {
			var this$1 = this;

			this.inputIng({detail: {value: ""}});
			this.fire("clean");
			if (this.props.dataMore.cleanFocus) {
				setTimeout(function() {
					document.getElementById(this$1.data.inputId).focus();
				}, 150);
			}
		};
		ZInput.prototype.switchLook = function() {
			this.props.dataMore.isLook = !this.props.dataMore.isLook;
			this.props.dataMore.inputType = this.props.dataMore.isLook
				? "text"
				: "password";
		};
		ZInput.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "z_input_box " + (this.props.class || ""),
					style:
						"border-radius:" +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;background:" +
						(this.props.bg || "rgba(0,0,0,0.05)") +
						";justify-content: " +
						(this.props.justify || "flex-start") +
						";" +
						(this.props.style || "")
				},
				apivm.h(
					"view",
					null,
					this.props.children.length >= 1 && this.props.children[0]
				),
				apivm.h(
					"view",
					null,
					!this.props.dataMore.dotIcon &&
						!this.props.dotIcon &&
						apivm.h(
							"view",
							{onClick: this.inputConfirm, style: "padding: 5px;marign-right:5px;"},
							apivm.h("a-iconfont", {
								name: this.props.dataMore.icon || "sousuo",
								color: "#999",
								size: G.appFontSize + (this.props.dataMore.iconSize || 0)
							})
						)
				),
				this.props.type == 2
					? apivm.h(
							"text",
							{
								style:
									"line-height:" +
									(G.appFontSize + 14) +
									"px;color:#999;" +
									loadConfiguration()
							},
							this.props.dataMore.placeholder || this.props.placeholder
					  )
					: apivm.h("input", {
							id: this.data.inputId,
							style:
								loadConfiguration() +
								"height:" +
								(G.appFontSize + 14) +
								"px;" +
								(this.props.dataMore.inputStyle || ""),
							"placeholder-style": "color:#ccc;",
							class: "z_input_input flex_w",
							type: this.props.dataMore.inputType || "text",
							placeholder:
								this.props.dataMore.placeholder ||
								this.props.dataMore.hint ||
								this.props.placeholder ||
								"请输入" + (this.props.dataMore.title || ""),
							onInput: function(e) {
								if (typeof this$1 != "undefined") {
									this$1.props.dataMore.value = e.target.value;
								} else {
									this$1.data.this.props.dataMore.value = e.target.value;
								}
								this$1.inputIng(e);
							},
							maxlength: this.props.dataMore.maxlength || this.props.dataMore.max,
							disabled:
								(isParameters(this.props.disabled) ? this.props.disabled : false) ||
								isParameters(this.props.readonly)
									? this.props.readonly
									: false,
							"confirm-type":
								this.props.confirmType || this.props.dataMore.confirmType || "search",
							"keyboard-type": this.props.dataMore.keyboardType || "default",
							onConfirm: this.inputConfirm,
							onBlur: this.inputBlur,
							onFocus: this.inputFocus,
							value:
								typeof this == "undefined"
									? this.data.this.props.dataMore.value
									: this.props.dataMore.value
					  }),
				apivm.h(
					"view",
					null,
					this.props.dataMore.value &&
						!this.props.dataMore.dotCleanIcon &&
						apivm.h(
							"view",
							{onClick: this.clean, style: "padding: 5px;"},
							apivm.h("a-iconfont", {
								name: "qingkong",
								color: "#666",
								size: G.appFontSize
							})
						)
				),
				apivm.h(
					"view",
					null,
					isParameters(this.props.dataMore.isLook) &&
						this.props.dataMore.value &&
						apivm.h(
							"view",
							{
								onClick: function() {
									return this$1.switchLook();
								},
								style: "padding:5px 10px 5px 3px;"
							},
							apivm.h("a-iconfont", {
								name: this.props.dataMore.isLook ? "kejian" : "bukejian",
								color: "#919191",
								size: G.appFontSize + 4
							})
						)
				),
				apivm.h(
					"view",
					null,
					this.props.children.length >= 2 && this.props.children[1]
				)
			);
		};

		return ZInput;
	})(Component);
	ZInput.css = {
		".z_input_box": {
			width: "100%",
			flexDirection: "row",
			padding: "2px 5px 2px 8px",
			alignItems: "center"
		},
		".z_input_input": {
			background: "transparent",
			borderColor: "transparent",
			color: "#333",
			paddingRight: "5px"
		},
		".z_input_input::placeholder": {color: "#ccc"}
	};
	apivm.define("z-input", ZInput);

	var ZDivider = /*@__PURE__*/ (function(Component) {
		function ZDivider(props) {
			Component.call(this, props);
		}

		if (Component) ZDivider.__proto__ = Component;
		ZDivider.prototype = Object.create(Component && Component.prototype);
		ZDivider.prototype.constructor = ZDivider;
		ZDivider.prototype.render = function() {
			return apivm.h(
				"view",
				{style: "width:100%;height:1px;padding:0 16px;" + this.props.style || null},
				apivm.h("view", {
					style:
						"border-bottom:1px solid " +
						(this.props.color || "rgba(0,0,0,0.03)") +
						";"
				})
			);
		};

		return ZDivider;
	})(Component);
	apivm.define("z-divider", ZDivider);

	var Areas = /*@__PURE__*/ (function(Component) {
		function Areas(props) {
			Component.call(this, props);
			this.data = {
				show: false,

				area: {key: "", value: ""},
				areas: [],

				areaFirst: {index: 0, data: []},
				areaSecond: {index: -1, data: []},
				areaThird: {index: -1, data: []},
				sourceData: [],

				search: {value: "", placeholder: "搜索关键词"},
				searchShow: false,
				searchData: []
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							setTimeout(
								function() {
									this$1.baseInit();
								},
								platform() == "app" ? 50 : 0
							);
						}
					}
				}
			};
		}

		if (Component) Areas.__proto__ = Component;
		Areas.prototype = Object.create(Component && Component.prototype);
		Areas.prototype.constructor = Areas;
		Areas.prototype.baseInit = function() {
			var this$1 = this;

			var dm = this.props.dataMore;
			var areas = JSON.parse(
				(dm.all ? getPrefs("sys_allAreas") : "") || getPrefs("sys_areas") || "[]"
			);
			if (areas.length) {
				var sourceData = areas.concat(areas[0].children);
				sourceData[0].children = [];
				if (JSON.stringify(this.data.sourceData) != JSON.stringify(sourceData)) {
					this.data.sourceData = sourceData;
					this.data.areaFirst.index = 0;
					this.data.areaSecond.index = -1;
					this.data.areaSecond.data = [];
					this.data.areaThird.index = [];
				}
			}
			this.data.areaFirst.data = this.data.sourceData;

			getSysAreas(
				{
					url: dm.all ? appUrl() + "area/tree" : "",
					areaId: dm.all ? getPrefs("sys_platform") : ""
				},
				function(ret) {
					if (ret && ret.code == 200 && isArray(ret.data) && ret.data.length > 0) {
						var _data = ret.data;
						this$1.data.sourceData = _data.concat(_data[0].children);
						this$1.data.sourceData[0].children = [];
						this$1.data.areaFirst.data = this$1.data.sourceData;
					}
				}
			);

			this.data.area.key = dm.key || (!dm.dt ? areaId() : "");
			if (this.data.area.key) {
				this.data.area.value = getAreaForKey(
					this.data.area.key,
					this.props.dataMore.all
				).name;
			}
		};
		Areas.prototype.penetrate = function() {};
		Areas.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		Areas.prototype.caSearchInput = function(e) {
			if (this.data.search.value) {
				this.searchStart(this.data.sourceData, 0);
				this.data.searchData.sort(function(a, b) {
					return b.weights - a.weights;
				});
			}
		};
		Areas.prototype.searchStart = function(_list, _start) {
			if (!isParameters(_list)) {
				return;
			}
			if (_start == 0) {
				this.data.searchData = [];
			}
			_start++;
			var searchArr = this.data.search.value.split("");
			for (var i = 0; i < _list.length; i++) {
				var weights = 0;
				for (var j = 0; j < searchArr.length; j++) {
					if (_list[i].name.indexOf(searchArr[j]) != -1) {
						weights++;
					}
				}
				if (weights > 0) {
					_list[i].weights = weights;
					this.data.searchData.push(_list[i]);
				}
				if (_list[i].children && _list[i].children.length) {
					this.searchStart(_list[i].children, _start);
				}
			}
		};
		Areas.prototype.cckAreaItem = function(_item, _index, _level) {
			if (_level != 1 && (!isArray(_item.children) || !_item.children.length)) {
				_level = -1;
			}
			switch (_level) {
				case 1:
					this.data.areaFirst.index = _index;
					this.data.areaSecond.index = -1;
					this.data.areaSecond.data = this.data.areaFirst.data[
						this.data.areaFirst.index
					].children;
					break;
				case 2:
					this.data.areaSecond.index = _index;
					this.data.areaThird.index = -1;
					this.data.areaThird.data = this.data.areaSecond.data[
						this.data.areaSecond.index
					].children;
					break;
				case 3:
					break;
				default:
					if (this.data.area.key == _item.id) {
						toast("当前地区已是【" + _item.name + "】");
						return;
					}
					this.switchArea(_item);
					break;
			}
		};
		Areas.prototype.switchArea = function(_item) {
			var this$1 = this;

			var nowInfo = getAreaForKey(_item.id);
			var extra = {
				id: _item.id,
				key: _item.id,
				name: _item.name,
				value: _item.name
			};
			if (!nowInfo.id) {
				sendEvent({name: "areaChange_" + this.props.dataMore.all, extra: extra});
			} else if (!this.props.dataMore.ds) {
				setPrefs("sys_aresId", _item.id);
				areaNotice(extra);
			}
			G.areasPop.callback(extra);
			if (!this.props.dataMore.dt) {
				toast("切换成功");
			}
			setTimeout(function() {
				this$1.closePage();
			}, 500);
		};
		Areas.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style: "height:15%;flex-shrink: 0;"
				}),
				this.props.dataMore.show &&
					apivm.h(
						"view",
						{
							class: "flex_h pages_warp",
							onClick: function() {
								return this$1.penetrate();
							}
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							{class: "header_warp flex_row"},
							apivm.h(
								"view",
								{class: "header_main xy_center"},
								apivm.h(
									"text",
									{
										style: loadConfiguration(1) + "font-weight: 600;color:" + G.headColor
									},
									this.props.dataMore.title || "地区切换"
								)
							),
							apivm.h(
								"view",
								{class: "header_left_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{style: loadConfiguration(1) + "color:#666;margin:0 4px;"},
										"关闭"
									)
								)
							)
						),
						apivm.h("z-divider", null),
						apivm.h(
							"view",
							{class: "flex_h"},
							apivm.h(
								"view",
								{class: "now_box"},
								this.data.area.key
									? apivm.h(
											"view",
											{class: "flex_row"},
											apivm.h(
												"text",
												{style: loadConfiguration(2) + "color:#333;"},
												"当前选择："
											),
											apivm.h(
												"text",
												{style: loadConfiguration(2) + "color:#333;font-weight: 600;"},
												this.data.area.value
											)
									  )
									: null
							),
							apivm.h(
								"view",
								{style: "padding:4px 16px;"},
								apivm.h("z-input", {
									dataMore: this.data.search,
									onInput: this.caSearchInput
								})
							),
							apivm.h(
								"view",
								{style: "height:1px;flex:1;flex-direction:row;"},
								this.data.areaFirst.data.length > 0 &&
									apivm.h(
										"scroll-view",
										{
											class: "flex_w",
											style:
												"background:#F4F5F7;max-width: " + (G.appFontSize + 17) * 3 + "px;",
											"scroll-y": true
										},
										(Array.isArray(this.data.areaFirst.data)
											? this.data.areaFirst.data
											: Object.values(this.data.areaFirst.data)
										).map(function(item$1, index$1) {
											return apivm.h(
												"view",
												{
													onClick: function() {
														return this$1.cckAreaItem(item$1, index$1, 1);
													},
													class: "areas_item flex_row",
													style:
														"background:" +
														(index$1 == this$1.data.areaFirst.index
															? "#FFFFFF"
															: "transparent") +
														";"
												},
												apivm.h(
													"text",
													{
														class: "areas_item_text",
														style:
															loadConfiguration(2) +
															"font-weight:" +
															(index$1 == this$1.data.areaFirst.index ? "6" : "4") +
															"00;"
													},
													item$1.name
												),
												apivm.h("a-iconfont", {
													name: "xiangzuo",
													color:
														index$1 == this$1.data.areaFirst.index ? G.appTheme : "#ccc",
													style: "transform: rotate(180deg);",
													size: G.appFontSize - 2
												})
											);
										})
									),
								this.data.areaFirst.data.length > 0 &&
									apivm.h(
										"scroll-view",
										{class: "flex_w", "scroll-y": true},
										apivm.h(
											"view",
											{
												class: "areas_item",
												onClick: function() {
													return this$1.cckAreaItem(
														this$1.data.areaFirst.data[this$1.data.areaFirst.index],
														-1,
														-1
													);
												}
											},
											apivm.h(
												"text",
												{class: "areas_item_text", style: "" + loadConfiguration(2)},
												this.data.areaFirst.data[this.data.areaFirst.index].name
											)
										),
										(Array.isArray(this.data.areaSecond.data)
											? this.data.areaSecond.data
											: Object.values(this.data.areaSecond.data)
										).map(function(item$1, index$1) {
											return apivm.h(
												"view",
												{
													onClick: function() {
														return this$1.cckAreaItem(item$1, index$1, 2);
													},
													class: "areas_item flex_row",
													style:
														"background:" +
														(index$1 == this$1.data.areaSecond.index
															? "#F4F5F7"
															: "transparent") +
														";"
												},
												apivm.h(
													"text",
													{
														class: "areas_item_text",
														style:
															loadConfiguration(2) +
															"font-weight: " +
															(index$1 == this$1.data.areaSecond.index ? "6" : "4") +
															"00;"
													},
													item$1.name
												),
												isArray(item$1.children) &&
													item$1.children.length > 0 &&
													apivm.h("a-iconfont", {
														name: "xiangzuo",
														color:
															index$1 == this$1.data.areaSecond.index ? G.appTheme : "#ccc",
														style: "transform: rotate(180deg);",
														size: G.appFontSize - 2
													})
											);
										})
									),
								this.data.areaSecond.index > -1 &&
									apivm.h(
										"scroll-view",
										{class: "flex_w", "scroll-y": true},
										apivm.h(
											"view",
											{
												class: "areas_item",
												onClick: function() {
													return this$1.cckAreaItem(
														this$1.data.areaSecond.data[this$1.data.areaSecond.index],
														-1,
														-1
													);
												}
											},
											apivm.h(
												"text",
												{class: "areas_item_text", style: "" + loadConfiguration(2)},
												this.data.areaSecond.data[this.data.areaSecond.index].name,
												"(本级)"
											)
										),
										(Array.isArray(this.data.areaThird.data)
											? this.data.areaThird.data
											: Object.values(this.data.areaThird.data)
										).map(function(item$1, index$1) {
											return apivm.h(
												"view",
												{
													onClick: function() {
														return this$1.cckAreaItem(item$1, index$1, -1);
													},
													class: "areas_item flex_row",
													style:
														"background:" +
														(index$1 == this$1.data.areaThird.index
															? "#FFFFFF"
															: "transparent") +
														";"
												},
												apivm.h(
													"text",
													{
														class: "areas_item_text",
														style:
															loadConfiguration(2) +
															"font-weight: " +
															(index$1 == this$1.data.areaThird.index ? "6" : "4") +
															"00;"
													},
													item$1.name
												)
											);
										})
									),
								apivm.h(
									"view",
									{
										class: "page_box",
										style: "display:" + (this.data.search.value ? "flex" : "none") + ";"
									},
									apivm.h(
										"scroll-view",
										{class: "xy_100 search_box", "scroll-y": true},
										(Array.isArray(this.data.searchData)
											? this.data.searchData
											: Object.values(this.data.searchData)
										).map(function(item$1, index$1) {
											return apivm.h(
												"view",
												{
													onClick: function() {
														return this$1.cckAreaItem(item$1, index$1, -1);
													},
													class: "search_item"
												},
												item$1.name.split("").map(function(nItem, nIndex) {
													return [
														apivm.h(
															"text",
															{
																style:
																	loadConfiguration(2) +
																	"color: " +
																	(this$1.data.search.value.indexOf(nItem) != -1
																		? G.appTheme
																		: "#333") +
																	";"
															},
															nItem
														)
													];
												})
											);
										})
									)
								)
							)
						)
					)
			);
		};

		return Areas;
	})(Component);
	Areas.css = {
		".pages_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		},
		".now_box": {padding: "10px 16px"},
		".areas_item": {width: "100%", padding: "15px 6px 15px 16px"},
		".areas_item_text": {color: "#333"},
		".search_box": {
			padding: "10px 0",
			width: "100%",
			height: "100%",
			backgroundColor: "#FFF"
		},
		".search_item": {padding: "15px 16px", flexDirection: "row", flexWrap: "wrap"}
	};
	apivm.define("areas", Areas);

	var ZButton = /*@__PURE__*/ (function(Component) {
		function ZButton(props) {
			Component.call(this, props);
		}

		if (Component) ZButton.__proto__ = Component;
		ZButton.prototype = Object.create(Component && Component.prototype);
		ZButton.prototype.constructor = ZButton;
		ZButton.prototype.componentsClick = function(e) {
			stopBubble(e);
			if (!this.props.disabled && !this.props.readonly) {
				this.fire("click", {});
			}
		};
		ZButton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					id: "" + (this.props.id || ""),
					class: "z_button " + (this.props.class || ""),
					style:
						"border-radius:" +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;border-color:" +
						(this.props.color || G.appTheme) +
						";background:" +
						(this.props.plain ? "#FFF" : this.props.color || G.appTheme) +
						";opacity:" +
						(this.props.disabled ? "0.5" : "1") +
						";" +
						(this.props.style || ""),
					onClick: function(e) {
						return this$1.componentsClick(e);
					}
				},
				this.props.icon &&
					apivm.h("a-iconfont", {
						name: this.props.icon,
						style: "margin-right:5px;",
						color: this.props.plain ? this.props.color || G.appTheme : "#FFF",
						size: G.appFontSize - 1 + (this.props.size || 0)
					}),
				apivm.h(
					"text",
					{
						style:
							"color:" +
							(this.props.plain ? this.props.color || G.appTheme : "#FFF") +
							";" +
							loadConfiguration(this.props.size || 0)
					},
					this.props.text || this.props.children
				)
			);
		};

		return ZButton;
	})(Component);
	ZButton.css = {
		".z_button": {
			padding: "7px 12px",
			borderWidth: "1px",
			borderStyle: "solid",
			boxSizing: "border-box",
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("z-button", ZButton);

	var ZEmpty = /*@__PURE__*/ (function(Component) {
		function ZEmpty(props) {
			Component.call(this, props);
		}

		if (Component) ZEmpty.__proto__ = Component;
		ZEmpty.prototype = Object.create(Component && Component.prototype);
		ZEmpty.prototype.constructor = ZEmpty;
		ZEmpty.prototype.getShowImg = function() {
			if (this.props.dataMore.type == 1 || this.props.dataMore.type == 2) {
				return showImg(
					shareAddress(1) + "image/icon_empty_" + this.props.dataMore.type + ".png"
				);
			}
			return showImg(this.props.dataMore.type);
		};
		ZEmpty.prototype.getShowText = function() {
			return (
				this.props.dataMore.text ||
				(this.props.dataMore.type == 1
					? "暂无数据"
					: this.props.dataMore.type == 2
					? "网络异常，请点击重试"
					: "")
			);
		};
		ZEmpty.prototype.refresh = function() {
			showProgress("刷新中");
			this.fire("refresh");
			setTimeout(function() {
				hideProgress();
			}, 2500);
		};
		ZEmpty.prototype.up = function() {
			if (this.props._this && isFunction(this.props._this.loadMore)) {
				this.props._this.loadMore({detail: {}});
			} else {
				this.fire("up", {});
			}
		};
		ZEmpty.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"view",
					null,
					this.props.dataMore.type &&
						this.props.dataMore.type != "load" &&
						apivm.h(
							"view",
							{
								id: "" + (this.props.id || ""),
								style: "margin:100px 0;" + (this.props.style || ""),
								class: "xy_center " + (this.props.class || "")
							},
							apivm.h("image", {
								style: "width:200px;height:200px;",
								src: this.getShowImg(),
								mode: "aspectFill"
							}),
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(this.props.size || 0) +
										"color:#666;padding:10px 20px;text-align: center;"
								},
								this.getShowText()
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == 2 &&
									apivm.h(
										"z-button",
										{
											style: "width:132px;margin-top:20px;",
											color: G.appTheme,
											round: true,
											onClick: this.refresh
										},
										"刷新"
									)
							),
							this.props.children
						)
				),
				apivm.h(
					"view",
					null,
					!this.props.dataMore.type &&
						this.props.dataMore.text &&
						apivm.h(
							"view",
							{class: "xy_center", onClick: this.up},
							apivm.h(
								"text",
								{style: loadConfiguration(-4) + "color:#bbb;padding:15px;"},
								this.props.dataMore.text
							)
						)
				)
			);
		};

		return ZEmpty;
	})(Component);
	apivm.define("z-empty", ZEmpty);

	var ZSkeleton = /*@__PURE__*/ (function(Component) {
		function ZSkeleton(props) {
			Component.call(this, props);
		}

		if (Component) ZSkeleton.__proto__ = Component;
		ZSkeleton.prototype = Object.create(Component && Component.prototype);
		ZSkeleton.prototype.constructor = ZSkeleton;
		ZSkeleton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{style: "width:100%;"},
				(Array.isArray(dataForNum(this.props.cell || 3))
					? dataForNum(this.props.cell || 3)
					: Object.values(dataForNum(this.props.cell || 3))
				).map(function(item$1, index$1, list) {
					return apivm.h(
						"view",
						{class: "z_skeleton"},
						apivm.h(
							"view",
							null,
							(Array.isArray(dataForNum(this$1.props.row || 4))
								? dataForNum(this$1.props.row || 4)
								: Object.values(dataForNum(this$1.props.row || 4))
							).map(function(item$1, index$1, list) {
								return apivm.h("view", {
									class: "z_skeleton_row",
									style:
										"width:" +
										(!index$1 ? 40 : index$1 == list.length - 1 ? 60 : 100) +
										"%;"
								});
							})
						)
					);
				})
			);
		};

		return ZSkeleton;
	})(Component);
	ZSkeleton.css = {
		".z_skeleton": {width: "100%", padding: "10px 16px"},
		".z_skeleton_row": {
			marginTop: "12px",
			height: "16px",
			backgroundColor: "#f2f3f5"
		}
	};
	apivm.define("z-skeleton", ZSkeleton);

	var baoguo_hezi_o = "";
	var fuzhilianjiexian = "";
	var fuzhilianjiemian = "";
	var wenjian = "";
	var tianjia = "";
	var tianjiawenjian = "";
	var sousuowenjian = "";
	var wenjianshangchuan = "";
	var dalou = "";
	var wucan = "";
	var zengjia1 = "";
	var xinsui = "";
	var aixin = "";
	var jianshao2 = "";
	var yishoucang = "";
	var yuyinshibie = "";
	var shuruxiangce = "";
	var tupianshibie = "";
	var shuruwenjian = "";
	var shurupaizhao = "";
	var tongji4 = "";
	var pinglun1 = "";
	var gerentongxunlu = "";
	var fenxiang2 = "";
	var dianzan = "";
	var shoucang1 = "";
	var sousuo2 = "";
	var jiancexinbanben = "";
	var shezhi1 = "";
	var guanhuaimoshi = "";
	var xiugaimima = "";
	var anquantuichu = "";
	var dianhua = "";
	var shipintianchong = "";
	var xiangxiagengduo = "";
	var mubiao = "";
	var jianshao1 = "";
	var biaoqian = "";
	var duigou = "";
	var jinengbiaoqian = "";
	var jiantou_xiangyouliangci = "";
	var toupiao = "";
	var riqi = "";
	var gonggao = "";
	var tongji3 = "";
	var paihang = "";
	var mn_paiming_fill = "";
	var shaixuan = "";
	var envelope = "";
	var nvshangjia = "";
	var nan = "";
	var tongji1 = "";
	var tuceng = "";
	var tongji2 = "";
	var biaoqianlan_shouye = "";
	var shengyinjingyin = "";
	var shengyin = "";
	var shengyin1 = "";
	var tianxie = "";
	var erweima = "";
	var huanyuan = "";
	var lianjie = "";
	var changyonglogo28 = "";
	var pengyouquan = "";
	var zhixiangxia = "";
	var QQ = "";
	var quanxian = "";
	var baocun = "";
	var suoyouwenjian = "";
	var xiangxia1 = "";
	var xinzeng = "";
	var ziyuanxhdpi = "";
	var xiazai = "";
	var zhongmingming = "";
	var yunpan = "";
	var tongji = "";
	var kefu = "";
	var weibiaoti1 = "";
	var tongxunlu = "";
	var xinyongqia = "";
	var tuichu = "";
	var aixinxiantiao = "";
	var shezhi = "";
	var jianchaxinbanben = "";
	var yuyin = "";
	var shanchu = "";
	var remen = "";
	var tupian = "";
	var xiangji = "";
	var zan = "";
	var zan1 = "";
	var like = "";
	var unlike = "";
	var pinglun = "";
	var jushoucang = "";
	var jushoucanggift = "";
	var share = "";
	var shoucangfill = "";
	var fenxiang1 = "";
	var shoucang = "";
	var bofang = "";
	var daojishi = "";
	var cuohao = "";
	var duihao = "";
	var chakan = "";
	var shoujihaoma = "";
	var mima1 = "";
	var zhanghao1 = "";
	var yanzhengma1 = "";
	var kejian = "";
	var bukejian = "";
	var gengduo1 = "";
	var gengduogongneng = "";
	var gengduo2 = "";
	var gengduo3 = "";
	var fanhui1 = "";
	var fanhui2 = "";
	var sousuo = "";
	var sousuo1 = "";
	var saoyisao1 = "";
	var xiangxia = "";
	var xiangzuo = "";
	var fangxingweixuanzhong = "";
	var fangxingxuanzhongfill = "";
	var fangxingxuanzhong = "";
	var yuanxingweixuanzhong = "";
	var yuanxingxuanzhong = "";
	var yuanxingxuanzhongfill = "";
	var danxuan_xuanzhong = "";
	var danxuan_weixuanzhong = "";
	var gengduo = "";
	var fenxiang = "";
	var zhuanfa00 = "";
	var dingwei = "";
	var dingwei1 = "";
	var jianshao = "";
	var zengjia = "";
	var qingkong = "";
	var mima = "";
	var zhanghao = "";
	var yanzhengma = "";
	var icons = {
		baoguo_hezi_o: baoguo_hezi_o,
		fuzhilianjiexian: fuzhilianjiexian,
		fuzhilianjiemian: fuzhilianjiemian,
		wenjian: wenjian,
		tianjia: tianjia,
		tianjiawenjian: tianjiawenjian,
		sousuowenjian: sousuowenjian,
		wenjianshangchuan: wenjianshangchuan,
		"zuanshi-L": "",
		dalou: dalou,
		wucan: wucan,
		zengjia1: zengjia1,
		xinsui: xinsui,
		aixin: aixin,
		jianshao2: jianshao2,
		yishoucang: yishoucang,
		yuyinshibie: yuyinshibie,
		shuruxiangce: shuruxiangce,
		tupianshibie: tupianshibie,
		shuruwenjian: shuruwenjian,
		shurupaizhao: shurupaizhao,
		tongji4: tongji4,
		pinglun1: pinglun1,
		gerentongxunlu: gerentongxunlu,
		fenxiang2: fenxiang2,
		dianzan: dianzan,
		shoucang1: shoucang1,
		sousuo2: sousuo2,
		jiancexinbanben: jiancexinbanben,
		shezhi1: shezhi1,
		guanhuaimoshi: guanhuaimoshi,
		xiugaimima: xiugaimima,
		anquantuichu: anquantuichu,
		dianhua: dianhua,
		shipintianchong: shipintianchong,
		"31dianhua": "",
		xiangxiagengduo: xiangxiagengduo,
		mubiao: mubiao,
		jianshao1: jianshao1,
		biaoqian: biaoqian,
		"gantanhao-xianxingyuankuang": "",
		duigou: duigou,
		jinengbiaoqian: jinengbiaoqian,
		jiantou_xiangyouliangci: jiantou_xiangyouliangci,
		toupiao: toupiao,
		riqi: riqi,
		gonggao: gonggao,
		tongji3: tongji3,
		paihang: paihang,
		mn_paiming_fill: mn_paiming_fill,
		"line-084": "",
		"line-085": "",
		shaixuan: shaixuan,
		envelope: envelope,
		"envelope-open": "",
		"a-4_huaban1": "",
		nvshangjia: nvshangjia,
		nan: nan,
		tongji1: tongji1,
		"weibiaoti--": "",
		tuceng: tuceng,
		tongji2: tongji2,
		biaoqianlan_shouye: biaoqianlan_shouye,
		shengyinjingyin: shengyinjingyin,
		shengyin: shengyin,
		shengyin1: shengyin1,
		tianxie: tianxie,
		"file-download-fill": "",
		"file-damage-fill": "",
		"file-excel-fill": "",
		"file-copy-fill": "",
		"file-edit-fill": "",
		"file-music-fill": "",
		"file-gif-fill": "",
		"file-history-fill": "",
		"file-lock-fill": "",
		"file-info-fill": "",
		"file-pdf-fill": "",
		"file-text-fill": "",
		"file-ppt-fill": "",
		"file-upload-fill": "",
		"file-unknow-fill": "",
		"file-word-fill": "",
		"file-search-fill": "",
		"file-transfer-fill": "",
		"file-zip-fill": "",
		"folder-2-fill": "",
		"file-warning-fill": "",
		"folder-add-fill": "",
		erweima: erweima,
		"file-reduce-fill": "",
		huanyuan: huanyuan,
		lianjie: lianjie,
		changyonglogo28: changyonglogo28,
		pengyouquan: pengyouquan,
		zhixiangxia: zhixiangxia,
		QQ: QQ,
		quanxian: quanxian,
		baocun: baocun,
		suoyouwenjian: suoyouwenjian,
		xiangxia1: xiangxia1,
		"wj-gxwj": "",
		xinzeng: xinzeng,
		ziyuanxhdpi: ziyuanxhdpi,
		xiazai: xiazai,
		zhongmingming: zhongmingming,
		yunpan: yunpan,
		"file-code-fill": "",
		"file-add-fill": "",
		tongji: tongji,
		kefu: kefu,
		weibiaoti1: weibiaoti1,
		tongxunlu: tongxunlu,
		xinyongqia: xinyongqia,
		tuichu: tuichu,
		aixinxiantiao: aixinxiantiao,
		shezhi: shezhi,
		jianchaxinbanben: jianchaxinbanben,
		yuyin: yuyin,
		shanchu: shanchu,
		remen: remen,
		tupian: tupian,
		xiangji: xiangji,
		zan: zan,
		zan1: zan1,
		like: like,
		unlike: unlike,
		"like-fill": "",
		"unlike-fill": "",
		pinglun: pinglun,
		jushoucang: jushoucang,
		jushoucanggift: jushoucanggift,
		share: share,
		shoucangfill: shoucangfill,
		fenxiang1: fenxiang1,
		shoucang: shoucang,
		bofang: bofang,
		"bell-off": "",
		daojishi: daojishi,
		cuohao: cuohao,
		duihao: duihao,
		chakan: chakan,
		shoujihaoma: shoujihaoma,
		mima1: mima1,
		zhanghao1: zhanghao1,
		yanzhengma1: yanzhengma1,
		kejian: kejian,
		bukejian: bukejian,
		gengduo1: gengduo1,
		gengduogongneng: gengduogongneng,
		gengduo2: gengduo2,
		gengduo3: gengduo3,
		"a-14Bshanchu": "",
		fanhui1: fanhui1,
		fanhui2: fanhui2,
		sousuo: sousuo,
		sousuo1: sousuo1,
		saoyisao1: saoyisao1,
		xiangxia: xiangxia,
		xiangzuo: xiangzuo,
		fangxingweixuanzhong: fangxingweixuanzhong,
		fangxingxuanzhongfill: fangxingxuanzhongfill,
		fangxingxuanzhong: fangxingxuanzhong,
		yuanxingweixuanzhong: yuanxingweixuanzhong,
		yuanxingxuanzhong: yuanxingxuanzhong,
		yuanxingxuanzhongfill: yuanxingxuanzhongfill,
		danxuan_xuanzhong: danxuan_xuanzhong,
		danxuan_weixuanzhong: danxuan_weixuanzhong,
		gengduo: gengduo,
		fenxiang: fenxiang,
		zhuanfa00: zhuanfa00,
		dingwei: dingwei,
		dingwei1: dingwei1,
		jianshao: jianshao,
		zengjia: zengjia,
		qingkong: qingkong,
		mima: mima,
		zhanghao: zhanghao,
		yanzhengma: yanzhengma
	};

	var AMpcss = /*@__PURE__*/ (function(Component) {
		function AMpcss(props) {
			Component.call(this, props);
		}

		if (Component) AMpcss.__proto__ = Component;
		AMpcss.prototype = Object.create(Component && Component.prototype);
		AMpcss.prototype.constructor = AMpcss;
		AMpcss.prototype.render = function() {
			return;
		};

		return AMpcss;
	})(Component);
	AMpcss.css = {
		"@font-face": {
			fontFamily: '"iconfont_mp"',
			src:
				"url('https://at.alicdn.com/t/c/font_3560231_mff0m4knr.ttf') format('truetype')"
		}
	};

	apivm.define("a-mpcss", AMpcss);

	var AIconfont = /*@__PURE__*/ (function(Component) {
		function AIconfont(props) {
			Component.call(this, props);
		}

		if (Component) AIconfont.__proto__ = Component;
		AIconfont.prototype = Object.create(Component && Component.prototype);
		AIconfont.prototype.constructor = AIconfont;
		AIconfont.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"text",
					{
						style:
							"font-size:" +
							(this.props.size || 16) +
							"px;color:" +
							(this.props.color || "#999") +
							";\n\t\tfont-family: " +
							(api.platform == "mp" ? "iconfont_mp" : "iconfont;") +
							";\n\t\t" +
							(this.props.style || "") +
							";font-weight:400;",
						class: "" + (this.props.class || "")
					},
					icons[this.props.name + ""]
				),
				api.platform == "mp" && apivm.h("a-mpcss", null)
			);
		};

		return AIconfont;
	})(Component);
	AIconfont.css = {
		"@font-face": {
			fontFamily: '"iconfont"',
			src:
				"url('../../components/act/a-iconfont/fonts/iconfont.ttf') format('truetype')"
		}
	};
	apivm.define("a-iconfont", AIconfont);

	var YBasePage = /*@__PURE__*/ (function(Component) {
		function YBasePage(props) {
			Component.call(this, props);
			this.data = {
				show: false, //为组件时显示隐藏
				initFrist: true, //首次初始化
				title: "" //标题栏
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					if (!this.props.dataMore) {
						if (this.headTitle != G.headTitle) {
							this.headTitle = G.headTitle;
							this.setHeader();
						}
						if (this.headTheme != (G.showHeadTheme || G.headTheme)) {
							this.headTheme = G.showHeadTheme || G.headTheme;
							this.setHeadTheme();
						}
					} else {
						if (this.props.dataMore.show != this.data.show) {
							this.data.show = this.props.dataMore.show;
							videoPlayRemoves();
							if (this.data.show) {
								if (this.data.initFrist) {
									setTimeout(function() {
										this$1.baseInit();
									}, 110);
								} else {
									this.pageRefresh();
								}
							} else {
								if (this.props._this && isFunction(this.props._this.baseClose)) {
									this.props._this.baseClose({detail: {}});
								} else {
									this.fire("baseClose");
								}
							}
						}
					}
					if (
						(!this.props.dataMore || this.data.show) &&
						G.onShowNum != this.onShowNum
					) {
						this.onShowNum = G.onShowNum;
						if (this.isShow && this.onShowNum > 0) {
							this.pageRefresh();
							if (!this.props.dataMore) {
								console.log(
									(api.winName || "") +
										"第" +
										G.onShowNum +
										"次返回：" +
										JSON.stringify(this.props._this.data.pageParam)
								);
							}
						}
						this.isShow = true;
					}
				},
				detail: function() {}
			};
		}

		if (Component) YBasePage.__proto__ = Component;
		YBasePage.prototype = Object.create(Component && Component.prototype);
		YBasePage.prototype.constructor = YBasePage;
		YBasePage.prototype.installed = function() {
			var this$1 = this;
			var that = this.props._this;
			that.data.pageParam = pageParam(that);
			that.data.pageType = that.data.pageParam.pageType || "page";
			clearTimeout(that.data.oneTask);
			that.data.oneTask = setTimeout(function() {
				console.log("当前页面参数：" + JSON.stringify(that.data.pageParam));
				G.chatInfos = [];
				G.chatInfoTask = [];
				setTimeout(function() {
					if (!this$1.props.dataMore) {
						if (!G.headTitle) {
							G.headTitle = that.data.pageParam.title || that.data.dTitle || "";
						}
					} else {
						this$1.data.title = that.data.pageParam.title || that.data.dTitle || "";
					}
				}, 400);
				if (!this$1.props.dataMore) {
					G._this = that;
					if (that.data.pageParam.token) {
						setPrefs("sys_token", decodeURIComponent(that.data.pageParam.token));
						if (!getPrefs("sys_Mobile")) {
							getLoginInfo({header: {"u-login-areaId": ""}}, function(ret, err) {});
						}
					}
					if (that.data.pageParam.areaId) {
						setPrefs("sys_aresId", that.data.pageParam.areaId);
					}
					addEventListener("sys_refresh", function(ret) {
						this$1.initConfiguration();
					});
					var traverseList = function(_obj, _option) {
						for (var key in _obj) {
							var item = _obj[key];
							if (
								isObject(item) &&
								!isArray(item) &&
								key.endsWith("Pop") &&
								isParameters(item.show) &&
								item.show
							) {
								if (_option) {
									item.show = false;
								}
								return false;
							}
						}
						return true;
					};
					addEventListener("keyback", function(ret, err) {
						if (G.alertPop.show) {
							if (G.alertPop.cancel.show) {
								G.alertPop.show = false;
							}
							return;
						}
						if (!traverseList(G, true) || !traverseList(that.data, true)) {
							return;
						}
						this$1.close();
					});
					addEventListener("swiperight", function(ret) {
						if (G.nTouchmove) {
							return;
						}
						if (!traverseList(G, false) || !traverseList(that.data, false)) {
							return;
						}
						this$1.close(1);
					});
					this$1.initConfiguration();
					this$1.baseInit();
				}
			}, 50);
		};
		YBasePage.prototype.baseInit = function() {
			var this$1 = this;

			setTimeout(
				function() {
					var detail = {first: this$1.data.initFrist};
					if (this$1.props._this && isFunction(this$1.props._this.baseInit)) {
						this$1.props._this.baseInit({detail: detail});
					} else {
						this$1.fire("init", detail);
					}
					this$1.data.initFrist = false;
				},
				!this.props.dataMore ? 300 : 0
			);
		};
		YBasePage.prototype.close = function(_type) {
			if (this.props._this && isFunction(this.props._this.close)) {
				this.props._this.close({detail: {type: _type}});
			} else {
				this.fire("close", {type: _type});
			}
		};
		YBasePage.prototype.cTitle = function() {
			this.fire("cTitle");
		};
		YBasePage.prototype.pageRefresh = function(_frist) {
			if (!this.props.dataMore) {
				this.setHeadTheme();
			}
			if (!_frist) {
				if (this.props._this && isFunction(this.props._this.pageRefresh)) {
					this.props._this.pageRefresh({detail: {back: true}});
				} else {
					this.fire("pageRefresh", {back: true});
				}
			}
		};
		YBasePage.prototype.initConfiguration = function() {
			G.pageWidth =
				platform() == "web"
					? api.winWidth > api.winHeight
						? 600
						: api.winWidth
					: api.winWidth;
			G.showCaptcha = getPrefs("enableShortAuth") == "true";
			G.appName = getPrefs("sys_systemName") || "";
			G.terminal = getPrefs("terminal") || "";
			G.appFont = getPrefs("appFont") || "heitiSimplified";
			G.appFontSize = Number(getPrefs("appFontSize") || "16");
			G.sysSign = sysSign();
			G.appTheme =
				this.props._this.data.pageParam.appTheme ||
				getPrefs("appTheme" + G.sysSign) ||
				(G.sysSign == "rd" ? "#C61414" : "#3088FE");
			var headTheme =
				this.props._this.data.headTheme ||
				this.props._this.data.pageParam.headTheme ||
				getPrefs("headTheme") ||
				"transparent";
			G.headTheme = headTheme == "appTheme" ? G.appTheme : headTheme;
			G.careMode = parseInt(G.appFontSize) > 16;
			G.systemtTypeIsPlatform = getPrefs("sys_systemType") == "platform"; //系统类型是否是平台版
			G.loginInfo = getPrefs("sys_systemLoginContact") || "";
			if (platform() == "app") {
				G.isAppReview =
					JSON.parse(getPrefs("sys_appReviewVersion") || "{}")[api.systemType] ==
					api.appVersion;
			}
			G.v = getPrefs("sys_appVersion") || "";

			G.uId = getPrefs("sys_Id") || "";
			G.userId = getPrefs("sys_UserID") || "";
			G.userName = getPrefs("sys_UserName") || "";
			G.userImg = getPrefs("sys_AppPhoto") || "";
			G.areaId = getPrefs("sys_aresId") || "";
			G.specialRoleKeys = JSON.parse(getPrefs("sys_SpecialRoleKeys") || "[]");
			G.isAdmin =
				G.userId == "1" ||
				getItemForKey("dc_admin", G.specialRoleKeys) ||
				getItemForKey("admin", G.specialRoleKeys);
			G.grayscale = getPrefs("sys_grayscale") || "";
			var watermark = getPrefs("sys_watermark") || "";
			if (platform() == "app") {
				api.screenLayerFilter({
					region: "window",
					sat: G.grayscale == "true" ? 0 : 1
				});
			}
			if (watermark && watermark != "false") {
				var t = watermark
					.replace("user", G.userName)
					.replace("phone", getPrefs("sys_Mobile") || "")
					.replace("true", G.appName);
				if (t) {
					G.watermark =
						tomcatAddress() +
						"utils2/watermark?s=" +
						G.appFontSize +
						"&t=" +
						t +
						"&w=" +
						G.pageWidth +
						"&h=" +
						api.winHeight;
				}
			} else {
				G.watermark = false;
			}

			this.pageRefresh(true);
			if (platform() == "web") {
				var fontStyleId = "fontStyle";
				if (document.getElementById(fontStyleId)) {
					//存在的时候先删除
					document
						.getElementById(fontStyleId)
						.parentNode.removeChild(document.getElementById(fontStyleId));
				}
				var fontStyle = document.createElement("style");
				fontStyle.id = fontStyleId;
				switch (G.appFont) {
					case "shusongSimplified":
						fontStyle.innerText =
							"@font-face{font-family: shusongSimplified;src: url('../../res/fz_shusong_simplified.ttf')}";
						break;
					case "kaitiSimplified":
						fontStyle.innerText =
							"@font-face{font-family: kaitiSimplified;src: url('../../res/fz_kaiti_simplified.ttf')}";
						break;
					case "heitiTraditional":
						fontStyle.innerText =
							"@font-face{font-family: heitiTraditional;src: url('../../res/fz_heiti_traditional.ttf')}";
						break;
				}

				document.getElementsByTagName("head")[0].appendChild(fontStyle);
				this.fitWidth();
			}
		};
		YBasePage.prototype.fitWidth = function() {
			if (platform() == "web") {
				$("body").style.width = "100%";
				$("body").style.maxWidth = G.pageWidth + "px";
				$("body").style.minWidth = "300px";
				$("body").style.margin = "auto";
				$("body").style.position = "relative";
			}
		};
		YBasePage.prototype.isColorDarkOrLight = function(hexcolor) {
			try {
				var colors = colorRgba(hexcolor).match(/\d+\.?\d*/g);
				var red = colors[1],
					green = colors[2],
					blue = colors[3],
					brightness;
				brightness = (red * 299 + green * 587 + blue * 114) / 255000;
				return brightness >= 0.5 ? "light" : "dark";
			} catch (e) {
				return "";
			}
		};
		YBasePage.prototype.setHeadTheme = function() {
			var colorDarkOrLight = this.isColorDarkOrLight(
				this.headTheme == "transparent" ? "#FFF" : this.headTheme
			);
			G.headColor = colorDarkOrLight == "dark" ? "#FFF" : "#333";
			setStatusBarStyle({
				style: colorDarkOrLight == "light" ? "dark" : "light",
				color: "rgba(0,0,0,0)"
			});
		};
		YBasePage.prototype.setHeader = function() {
			if (this.props.dataMore) {
				return;
			}
			this.data.title = G.headTitle;
			if (platform() == "web") {
				if (window.parent) {
					window.parent.document.title = this.data.title;
				} else {
					document.title = this.data.title;
				}
			} else if (platform() == "mp") {
				wx.setNavigationBarTitle({title: this.data.title});
			}
		};
		YBasePage.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "page_box",
					class:
						"page_box page_bg " +
						(G.grayscale == "true" ? "filterGray" : "filterNone"),
					style: {display: !this.props.dataMore || this.data.show ? "flex" : "none"}
				},
				apivm.h(
					"view",
					{class: "watermark_box"},
					G.watermark &&
						apivm.h("image", {
							class: "xy_100",
							src: G.watermark,
							mode: "aspectFill",
							thumbnail: "false"
						})
				),
				G.appTheme && [
					apivm.h(
						"view",
						{class: "xy_100"},

						apivm.h(
							"view",
							{
								class: "flex_shrink",
								style: {
									display:
										!this.props.dataMore || !this.props.dataMore.closeH ? "flex" : "none"
								}
							},
							!this.props.closeH &&
								(this.props.titleBox ||
									this.props.back ||
									this.props.more ||
									showHeader()) &&
								apivm.h(
									"view",
									{
										style:
											"height:auto;padding-top:" +
											headerTop() +
											"px;background:" +
											(this.props._this.data.headTheme || G.headTheme)
									},
									apivm.h(
										"view",
										{class: "header_warp flex_row"},
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.cTitle();
												},
												class: "header_main xy_center",
												style: this.props.titleStyle || null
											},
											this.props.titleBox && this.props.children.length >= 3
												? [this.props.children[2]]
												: [
														apivm.h(
															"text",
															{
																style:
																	loadConfiguration(4) + "font-weight: 600;color:" + G.headColor
															},
															this.data.title
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "header_left_box"},
											this.props.back && this.props.children.length >= 1
												? [this.props.children[0]]
												: [
														apivm.h(
															"view",
															{
																onClick: function() {
																	return this$1.close();
																},
																class: "header_btn",
																style: {
																	display:
																		showHeader() && this.props._this.data.pageType == "page"
																			? "flex"
																			: "none"
																}
															},
															apivm.h("a-iconfont", {
																name: "fanhui1",
																color: G.headColor,
																size: G.appFontSize + 1
															})
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "header_right_box"},
											this.props.more &&
												this.props.children.length >= 2 &&
												this.props.children[1]
										)
									)
								)
						),
						this.props.children.length >= 4 && this.props.children[3]
					),
					this.props.children.length >= 5
						? this.props.children.filter(function(item, index) {
								return index >= 4;
						  })
						: null,
					!this.props.dataMore && [
						apivm.h("previewer-img", {dataMore: G.imgPreviewerPop}),
						apivm.h("areas", {dataMore: G.areasPop}),
						apivm.h("select-item", {dataMore: G.selectPop}),
						apivm.h("z-actionSheet", {dataMore: G.actionSheetPop}),
						apivm.h("z-alert", {dataMore: G.alertPop})
					]
				]
			);
		};

		return YBasePage;
	})(Component);
	YBasePage.css = {
		".avm-toast,.avm-confirm-mask": {zIndex: "999"},
		element: {width: "auto", height: "auto"},
		".flex_shrink,div": {flexShrink: "0", WebkitOverflowScrolling: "touch"},
		".flex_w": {flex: "1", width: "1px"},
		".flex_h": {flex: "1", height: "1px"},
		".flex_row": {flexDirection: "row", alignItems: "center"},
		".xy_center": {alignItems: "center", justifyContent: "center"},
		".xy_100": {width: "100%", height: "100%"},
		".page_box": {
			position: "absolute",
			zIndex: "999",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		},
		".page_bg": {background: "#FFF"},
		".header_warp": {height: "44px"},
		".header_main": {
			height: "100%",
			flex: "1",
			flexDirection: "row",
			padding: "0 44px"
		},
		".header_left_box": {
			position: "absolute",
			zIndex: "999",
			left: "0",
			width: "auto",
			height: "44px"
		},
		".header_right_box": {
			position: "absolute",
			zIndex: "999",
			right: "0",
			width: "auto",
			height: "44px"
		},
		".header_btn": {
			width: "auto",
			height: "100%",
			minWidth: "36px",
			padding: "0 12px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".header_btn:active": {background: "rgba(0,0,0,0.05)"},
		".filterNone": {filter: "none"},
		".filterGray": {filter: "grayscale(1)"},
		".watermark_box": {
			position: "absolute",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		}
	};
	apivm.define("y-base-page", YBasePage);

	var MoModule = /*@__PURE__*/ (function(Component) {
		function MoModule(props) {
			Component.call(this, props);
			this.data = {
				pageParam: {},
				pageType: "",
				MG: !this.props.dataMore ? G : null,
				scrollTop: 0,
				defaultBg: {url: ""},
				area: {key: "", value: ""},

				listTop: [],
				listBtns: [],
				notices: {
					index: 0,
					data: [],
					task: null,
					unRead: 0
				},

				floatModule: {state: "", id: ""},

				pageNo: 1,
				pageSize: 15,
				refreshPageSize: 0,
				listData: [],

				emptyBox: {
					type: "load",
					text: ""
				},

				newModule: 2,
				tabBox: {key: "", data: []},
				tabChildBox: {key: "", data: []},

				layoutId: "ZHXS",
				layoutCid: "ZHXSTT1",
				showTopColumn: false,

				newsHint: "",
				newsHide: true
			};
		}

		if (Component) MoModule.__proto__ = Component;
		MoModule.prototype = Object.create(Component && Component.prototype);
		MoModule.prototype.constructor = MoModule;
		MoModule.prototype.onShow = function() {
			G.onShowNum++;
		};
		MoModule.prototype.baseInit = function(ref) {
			var this$1 = this;
			var detail = ref.detail;

			this.data.newsHint = this.data.pageParam.newsHint || "资料文件";
			this.data.newsHide = this.data.pageParam.newsHide == "true";
			this.data.newModule = this.data.pageParam.newModule || 2;
			addEventListener("playState_module", function(ret, err) {
				this$1.data.floatModule = ret.value;
			});
			addEventListener("areaChange_module", function(ret, err) {
				if (ret.value.key) {
					this$1.data.area.key = ret.value.key;
					this$1.pageRefresh();
				}
				this$1.data.area.value = getAreaForKey(this$1.data.area.key).name;
			});
			addEventListener("unreadChange_module", function(ret, err) {
				this$1.showUnRead();
			});
			this.data.area.key = areaId();
			this.data.area.value = getAreaForKey(this.data.area.key).name;
			this.pageRefresh();
			this.showData(JSON.parse(getPrefs("appMenuTrue") || "[]"));
			this.showBG(JSON.parse(getPrefs("appBackgroupImg") || "[]"));
		};
		MoModule.prototype.pageRefresh = function(e) {
			var this$1 = this;
			if (e === void 0) e = {};

			if (!this.props.dataMore) {
				this.initData = true;
			}
			if (this.initData) {
				getAppMenus({code: "module"}, function(ret, err) {
					if (isArray(ret) && ret.length) {
						this$1.showData(ret);
						if (!e.detail || !e.detail.back) {
							sendEvent({name: "index", extra: {type: "getUnRead"}});
						}
					} else if (ret && ret.code == 200) {
						this$1.data.listTop = [];
						this$1.data.listBtns = [];
					}
				});
				getSysBackground(function(data) {
					this$1.showBG(data);
				});
			}
			this.initData = true;
			getList22({param: {pageNo: "1", pageSize: "3"}}, function(ret) {
				var data = ret ? ret.dealWith || [] : [];
				if (this$1.data.notices.index >= data.length) {
					this$1.data.notices.index = 0;
				}
				this$1.data.notices.data = data;
				this$1.startNoticeTask();
			});
			if (!this.data.newsHide) {
				this.getColumn(1);
				this.getData(0);
			}
		};
		MoModule.prototype.close = function() {
			if (this.props.dataMore) {
				this.props.dataMore.show = false;
			} else {
				closeWin();
			}
		};
		MoModule.prototype.showBG = function(data) {
			var startFlash = getItemForKey("3", data, "dictCode");
			this.data.defaultBg.url = startFlash ? startFlash.icon : "";
		};
		MoModule.prototype.showData = function(data) {
			var nowList = [];
			var iData = getItemForKey(
				this.data.pageParam.key || "1580173282710196226",
				data,
				"id"
			);
			if (iData && isArray(iData.children) && iData.children.length) {
				data = iData.children;
				data = data.filter(function(item, index) {
					return item.isShow == 1;
				});
				var collation = "1";
				for (var i = 0; i < data.length; i++) {
					if (data[i].menuFunction.value) {
						collation = "2";
						break;
					}
				}
				for (var i = 0; i < data.length; i++) {
					var item = {},
						itemData = data[i];
					setModuleList(item, itemData);
					var nBaseItem;
					if (collation == "1") {
						nBaseItem = getItemForKey(
							itemData.menuIsPublic.value == "1" ? "1" : "2",
							nowList,
							"key"
						);
						if (!nBaseItem) {
							//还没有栏目
							nowList.push({
								key: itemData.menuIsPublic.value == "1" ? "1" : "2",
								value: itemData.menuIsPublic.label,
								data: []
							});
							nBaseItem = getItemForKey(
								itemData.menuIsPublic.value == "1" ? "1" : "2",
								nowList,
								"key"
							);
						}
					} else if (collation == "2") {
						if (!itemData.menuFunction.value) {
							//没有选择项目
							itemData.menuFunction.value = "999999";
							itemData.menuFunction.label = "其它";
						}
						nBaseItem = getItemForKey(itemData.menuFunction.value, nowList, "key");
						if (!nBaseItem) {
							//还没有栏目
							nowList.push({
								key: itemData.menuFunction.value,
								value: itemData.menuFunction.label,
								isFast: itemData.menuFunction.value == "0",
								data: []
							});
							nBaseItem = getItemForKey(itemData.menuFunction.value, nowList, "key");
						}
					}
					nBaseItem && nBaseItem.data.push(item);
				}
				var listTop = nowList.filter(function(item, index) {
					return item.key == "-1";
				});
				this.data.listTop = listTop.length ? listTop[0].data : [];
				nowList = nowList.filter(function(item, index) {
					return item.key != "-1";
				});
				nowList.sort(function(a, b) {
					return Number(a.key) - Number(b.key);
				});
				this.data.listBtns = nowList;
				this.showUnRead();
			}
		};
		MoModule.prototype.showUnRead = function() {
			var sys_unread = JSON.parse(getPrefs("sys_unread") || "{}");
			this.data.notices.unRead = sys_unread["notification"] || 0;
			setModuleReadNum(this.data.listTop || []);
			this.data.listBtns.forEach(function(_eItem) {
				setModuleReadNum(_eItem.data || []);
			});
		};
		MoModule.prototype.pageScroll = function(ref) {
			var detail = ref.detail;

			this.data.scrollTop = detail.scrollTop;
			this.calculationTop();
			var videos = this.data.listData.filter(function(item) {
				return item.videoHrefs;
			});
			if (videos.length && isArray(G.playVideos) && G.playVideos.length) {
				videos.forEach(function(_eItem) {
					var vId = dealVideoId(_eItem.videoHrefs);
					if (G.playVideos[0] != vId) {
						return;
					}
					if (document.getElementById(vId)) {
						getBoundingClientRect(vId, function(ret) {
							if (ret.top < headerTop() || ret.bottom > Number(api.winHeight)) {
								videoPlayRemoves();
							}
						});
					}
				});
			}
		};
		MoModule.prototype.calculationTop = function() {
			var this$1 = this;

			if (this.data.newsHide) {
				return;
			}
			setTimeout(function() {
				getBoundingClientRect("moduleTop", function(ret) {
					getBoundingClientRect("columnId", function(ret2) {
						this$1.data.showTopColumn = ret2.top - ret.height <= 0;
					});
				});
			}, 0);
		};
		MoModule.prototype.getData = function(_type) {
			var this$1 = this;

			if (!this.data.tabBox.key) {
				return;
			}
			var postParam = {
				pageNo: !_type ? 1 : this.data.pageNo,
				pageSize:
					!_type && this.data.refreshPageSize > this.data.pageSize
						? this.data.refreshPageSize
						: this.data.pageSize,
				keyword: "",
				query: {
					moduleId: this.data.newModule,
					columnId: this.data.tabChildBox.key || this.data.tabBox.key || null,
					passFlag: 1
				}
			};

			getList5({param: postParam, areaId: this.data.area.key}, function(ret) {
				var data = ret ? ret.dealWith || [] : [];
				if (!isArray(data) || !data.length) {
					dealData(_type, this$1, ret);
					return;
				}
				if (!_type) {
					this$1.data.listData = data;
				} else {
					this$1.data.listData = this$1.data.listData.concat(data);
				}
				this$1.data.emptyBox.type = "";
				this$1.data.emptyBox.text =
					data.length >= postParam.pageSize ? LOAD_MORE : LOAD_ALL;
				this$1.data.pageNo =
					Math.ceil(this$1.data.listData.length / this$1.data.pageSize) + 1;
				this$1.data.refreshPageSize =
					Math.ceil(this$1.data.listData.length / this$1.data.pageSize) *
					this$1.data.pageSize;
				this$1.calculationTop();
			});
		};
		MoModule.prototype.loadMore = function() {
			if (
				(this.data.emptyBox.text == LOAD_MORE ||
					this.data.emptyBox.text == NET_ERR) &&
				this.data.pageNo != 1
			) {
				this.data.emptyBox.text = LOAD_ING;
				this.getData(this.data.listData.length ? 1 : 0);
			}
		};
		MoModule.prototype.startNoticeTask = function() {
			var this$1 = this;

			if (!this.data.notices.task) {
				this.data.notices.task = setInterval(function() {
					var nowIndex = this$1.data.notices.index + 1;
					if (nowIndex >= this$1.data.notices.data.length) {
						nowIndex = 0;
					}
					this$1.data.notices.index = nowIndex;
				}, 3000);
			}
		};
		MoModule.prototype.openAreas = function() {
			openAreas({}, function(ret) {});
		};
		MoModule.prototype.getColumn = function(_type) {
			var this$1 = this;

			getColumn5(
				{
					param: {
						query: {
							moduleId: this.data.newModule,
							parentId: _type == 1 ? "0" : this.data.tabBox.key
						}
					},

					areaId: this.data.area.key
				},
				function(ret, err) {
					hideProgress();
					var data = ret ? ret.data || [] : [];
					var nowList = [];
					var oldKey = this$1.data.tabBox.key;
					if (isArray(data) && data.length > 0) {
						for (var i = 0; i < data.length; i++) {
							var id = data[i].id;
							var name = data[i].name;
							var layoutId = data[i].layoutCode;
							var layoutCid = data[i].layoutChildrenCode;
							var item = {
								value: name,
								key: id,
								layoutId: layoutId,
								layoutCid: layoutCid
							};
							nowList.push(item);
						}
						if (_type == 1) {
							if (
								!this$1.data.tabBox.key ||
								!getItemForKey(this$1.data.tabBox.key, nowList)
							) {
								this$1.data.tabBox.key = nowList.length ? nowList[0].key : "";
							}
							this$1.tabChange(1, oldKey);
						} else if (_type == 2) {
							if (
								this$1.data.tabChildBox.key &&
								!getItemForKey(this$1.data.tabChildBox.key, nowList)
							) {
								this$1.data.tabChildBox.key = "";
								this$1.getData(0);
							}
						}
						_type == 1
							? (this$1.data.tabBox.data = nowList)
							: (this$1.data.tabChildBox.data = nowList);
					} else {
						if (_type == 1) {
							this$1.data.tabBox.key = "";
							this$1.data.tabChildBox.key = "";
							this$1.data.tabBox.data = [];
							this$1.data.tabChildBox.data = [];
							this$1.data.listData = [];
							this$1.data.emptyBox.type = ret ? "1" : "2";
							this$1.data.emptyBox.text =
								ret && ret.code != 200 ? ret.message || ret.data : "";
							this$1.calculationTop();
						} else {
							if (this$1.data.tabChildBox.key) {
								this$1.data.tabChildBox.key = "";
								this$1.getData(0);
							}
							this$1.data.tabChildBox.data = [];
						}
					}
				}
			);
		};
		MoModule.prototype.tabChange = function(_type, dot) {
			if (_type == 1) {
				this.data.layoutId = this.data.tabBox.key
					? getItemForKey(this.data.tabBox.key, this.data.tabBox.data).layoutId
					: "ZHXS";
				this.data.layoutCid = this.data.tabBox.key
					? getItemForKey(this.data.tabBox.key, this.data.tabBox.data).layoutCid
					: "ZHXSTT1";
				if (!dot) {
					this.data.tabChildBox.key = "";
					this.data.tabChildBox.data = [];
				}
				if (this.data.tabBox.key) {
					this.getColumn(2);
				}
			} else {
				this.data.layoutId = this.data.tabChildBox.key
					? getItemForKey(this.data.tabChildBox.key, this.data.tabChildBox.data)
							.layoutId
					: getItemForKey(this.data.tabBox.key, this.data.tabBox.data).layoutId;
				this.data.layoutCid = this.data.tabChildBox.key
					? getItemForKey(this.data.tabChildBox.key, this.data.tabChildBox.data)
							.layoutCid
					: getItemForKey(this.data.tabBox.key, this.data.tabBox.data).layoutCid;
			}
			if (!dot) {
				this.data.emptyBox.type = "load";
				this.data.listData = [];
				this.calculationTop();
			}
			if (dot != this.data.tabBox.key) {
				this.getData(0);
			}
		};
		MoModule.prototype.tabdotChange = function() {
			if (this.data.tabChildBox.key) {
				this.data.tabChildBox.key = "";
				this.getData(0);
			}
		};
		MoModule.prototype.openSearch = function() {
			openWin_search_n({code: "all"});
		};
		MoModule.prototype.openNotice = function() {
			openWin_notice({});
		};
		MoModule.prototype.openModule = function(_item) {
			openWin_module(_item);
		};
		MoModule.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"y-base-page",
				{_this: this, dataMore: this.props.dataMore, closeH: true},
				apivm.h("view", null),
				apivm.h("view", null),
				apivm.h("view", null),
				apivm.h(
					"view",
					{class: "flex_h", style: "background:rgba(0,0,0,0.03)"},
					apivm.h(
						"view",
						{
							style:
								"opacity:" +
								(this.data.scrollTop < -115
									? 0
									: this.data.scrollTop < -15
									? 1 - (Math.abs(this.data.scrollTop) - 15) / 100
									: 1) +
								";",
							class: "top_box"
						},
						apivm.h(
							"view",
							{
								id: "moduleTop",
								style:
									"width:100%;padding-top:" +
									headerTop() +
									"px;background:" +
									(this.data.scrollTop > 0
										? colorRgba(
												G.appTheme,
												this.data.scrollTop < 100 ? this.data.scrollTop / 100 : 1
										  )
										: "transparent") +
									";"
							},
							apivm.h(
								"view",
								{class: "header_warp flex_row", style: "padding:0 16px;"},
								apivm.h(
									"view",
									null,
									showHeader() &&
										this.data.pageType != "home" &&
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.close();
												},
												class: "header_btn",
												style: "margin:0 5px 0 -10px;"
											},
											apivm.h("a-iconfont", {
												name: "fanhui1",
												color: "#FFF",
												size: G.appFontSize + 1
											})
										)
								),
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.openSearch();
										},
										class: "flex_w"
									},
									apivm.h("z-input", {
										style: "padding:0;",
										round: true,
										bg: "#FFF",
										type: "2",
										justify: "center",
										placeholder: "关键词搜索",
										dataMore: {}
									})
								),
								apivm.h(
									"view",
									null,
									!G.isAppReview &&
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.openAreas();
												},
												style: "margin-left: 22px;flex-direction:row; align-items: center;"
											},
											apivm.h(
												"text",
												{style: loadConfiguration(4) + "font-weight: 600; color: #FFF;"},
												this.data.area.value
											),
											apivm.h("a-iconfont", {
												style: "margin-left:12px;",
												name: "gengduogongneng",
												color: "#FFF",
												size: G.appFontSize + 4
											})
										)
								)
							)
						),
						apivm.h(
							"view",
							{style: "padding:0 7px;background:#FFF;"},
							this.data.showTopColumn &&
								this.data.tabBox.data.length > 0 &&
								apivm.h("z-tabs-one", {
									dataMore: this.data.tabBox,
									onChange: function() {
										return this$1.tabChange(1);
									},
									onDotchange: this.tabdotChange
								})
						),
						apivm.h(
							"view",
							{style: "padding:0 11px;background:#FFF;"},
							this.data.showTopColumn &&
								this.data.tabChildBox.data.length > 0 &&
								apivm.h("z-tabs-two", {
									dotDefault: true,
									dataMore: this.data.tabChildBox,
									onChange: function() {
										return this$1.tabChange(2);
									}
								})
						)
					),
					apivm.h(
						"y-scroll-view",
						{_this: this, refresh: true, onScroll: this.pageScroll},
						apivm.h(
							"view",
							{
								class: "flex_shrink",
								style: "width:100%;height:" + (176 + headerTop()) + "px;"
							},
							this.data.defaultBg.url &&
								apivm.h("image", {
									class: "xy_100",
									mode: "aspectFill",
									thumbnail: "false",
									src: showImg(this.data.defaultBg)
								})
						),
						apivm.h("view", {style: "margin-top: -30px;"}),
						apivm.h(
							"view",
							null,
							this.data.listTop.length > 0 &&
								apivm.h(
									"view",
									{class: "module_box"},
									apivm.h(
										"view",
										{
											class: "module_top_warp",
											style:
												"background-color: rgba(255, 255, 255, " +
												(G.watermark ? 0.95 : 1) +
												");"
										},
										(Array.isArray(this.data.listTop)
											? this.data.listTop
											: Object.values(this.data.listTop)
										).map(function(item$1, index$1) {
											return apivm.h(
												"view",
												{
													onClick: function() {
														return this$1.openModule(item$1);
													},
													class: "module_top_item flex_w"
												},
												apivm.h(
													"view",
													null,
													apivm.h("image", {
														style: "" + loadConfigurationSize(14),
														mode: "aspectFill",
														thumbnail: "false",
														src: showImg(item$1)
													}),
													item$1.pointNumber > 0 &&
														apivm.h(
															"view",
															{
																class:
																	"redDot_box " +
																	(item$1.pointType == "big" ? "redDot_big" : "redDot_small"),
																style:
																	"" +
																	loadConfigurationSize(item$1.pointType == "big" ? 4 : -6) +
																	(G.careMode
																		? item$1.pointType == "big"
																			? ";top:-10px;right: -12px;"
																			: "top:-4px;right: -7px;"
																		: "")
															},
															item$1.pointType == "big" &&
																apivm.h(
																	"text",
																	{style: "color: #FFF;" + loadConfiguration(-4)},
																	item$1.pointNumber > 99 ? "99" : item$1.pointNumber
																)
														)
												),
												apivm.h(
													"text",
													{style: "" + loadConfiguration(-2), class: "module_top_item_text"},
													item$1.value
												)
											);
										})
									)
								)
						),
						apivm.h(
							"view",
							{
								class: "module_box",
								onClick: function() {
									return this$1.openNotice();
								}
							},
							apivm.h(
								"view",
								{
									class: "module_notice_warp",
									style:
										"background-color: rgba(255, 255, 255, " +
										(G.watermark ? 0.85 : 1) +
										");"
								},
								apivm.h(
									"view",
									null,
									apivm.h(
										"text",
										{style: loadConfiguration(1) + "color:#333;font-weight: bold;"},
										"通知"
									),
									apivm.h(
										"text",
										{
											style:
												loadConfiguration(1) +
												"color:" +
												G.appTheme +
												";font-weight: bold;margin-top:2px;"
										},
										"公告"
									)
								),
								apivm.h("view", {
									class: "module_notice_line",
									style: "height:" + (G.appFontSize + 5) * 2 + "px;"
								}),
								apivm.h(
									"view",
									{class: "flex_w"},
									apivm.h(
										"view",
										null,
										this.data.notices.data.length > 0
											? [
													apivm.h(
														"text",
														{
															class: "text_one",
															style:
																loadConfiguration(1) +
																"color:#333;font-weight: 600;text-align: left;"
														},
														this.data.notices.data[this.data.notices.index].title
													),
													apivm.h(
														"text",
														{
															class: "text_one",
															style: loadConfiguration(-4) + "color:#666;margin-top:5px;"
														},
														this.data.notices.data[this.data.notices.index].content
													)
											  ]
											: apivm.h(
													"text",
													{
														class: "text_one",
														style:
															loadConfiguration(-2) +
															"text-align: center;color:#ccc;font-weight: 400;"
													},
													"暂无通知公告，先看看别的吧~"
											  )
									),
									this.data.notices.unRead > 0 &&
										apivm.h("view", {
											style: "" + loadConfigurationSize(-6),
											class: "redDot_box redDot_small"
										})
								)
							)
						),
						apivm.h(
							"view",
							null,
							this.data.listBtns.length > 0 &&
								apivm.h(
									"view",
									{class: "module_box"},
									(Array.isArray(this.data.listBtns)
										? this.data.listBtns
										: Object.values(this.data.listBtns)
									).map(function(item$1, index$1) {
										return apivm.h(
											"view",
											{
												class: "module_btns_warp",
												style:
													"background-color: rgba(255, 255, 255, " +
													(G.watermark ? 0.6 : 1) +
													");margin-top:" +
													(index$1 ? "1" : "") +
													"0px;"
											},
											apivm.h(
												"view",
												{class: "module_btns_hint_box"},
												apivm.h(
													"view",
													null,
													item$1.isFast &&
														apivm.h("view", {
															style:
																loadConfigurationSize(-9) +
																"border: 1px solid " +
																G.appTheme +
																";border-radius:50%;margin-right:4px;"
														})
												),
												apivm.h(
													"text",
													{
														class: "module_btns_hint_text",
														style: "" + loadConfiguration(item$1.isFast ? -2 : 1)
													},
													item$1.value
												),
												apivm.h(
													"view",
													null,
													item$1.isFast &&
														apivm.h(
															"view",
															{style: "margin-left:9px;flex-direction:row;margin-top:-1px;"},
															(Array.isArray("///////////////////".split(""))
																? "///////////////////".split("")
																: Object.values("///////////////////".split(""))
															).map(function(nItem, nIndex) {
																return apivm.h(
																	"text",
																	{
																		style:
																			loadConfiguration(-4) +
																			"color:" +
																			colorRgba(G.appTheme, 0.7) +
																			";margin:0 1.5px;"
																	},
																	"/"
																);
															})
														)
												)
											),
											apivm.h(
												"view",
												{style: "flex-direction:row;flex-wrap: wrap;"},
												(Array.isArray(item$1.data)
													? item$1.data
													: Object.values(item$1.data)
												).map(function(nItem, nIndex) {
													return apivm.h(
														"view",
														{
															onClick: function() {
																return this$1.openModule(nItem);
															},
															class: "module_btns_item"
														},
														apivm.h(
															"view",
															null,
															apivm.h("image", {
																style: "" + loadConfigurationSize(20),
																mode: "aspectFill",
																thumbnail: "false",
																src: showImg(nItem)
															}),
															nItem.pointNumber > 0 &&
																apivm.h(
																	"view",
																	{
																		class:
																			"redDot_box " +
																			(nItem.pointType == "big" ? "redDot_big" : "redDot_small"),
																		style:
																			"" +
																			(nItem.pointType == "big"
																				? loadConfigurationSize(4)
																				: loadConfigurationSize(-6)) +
																			(G.careMode
																				? nItem.pointType == "big"
																					? ";top:-10px;right: -12px;"
																					: "top:-4px;right: -7px;"
																				: "")
																	},
																	nItem.pointType == "big" &&
																		apivm.h(
																			"text",
																			{style: "color: #FFF;" + loadConfiguration(-4)},
																			nItem.pointNumber > 99 ? "99" : nItem.pointNumber
																		)
																)
														),
														apivm.h(
															"text",
															{
																style: "" + loadConfiguration(-2),
																class: "module_btns_item_text"
															},
															nItem.value
														)
													);
												})
											)
										);
									})
								)
						),
						apivm.h(
							"view",
							null,
							!this.data.newsHide &&
								apivm.h(
									"view",
									null,
									apivm.h(
										"text",
										{
											style:
												loadConfiguration(4) +
												"color:#000;font-weight: 600;padding:10px 16px;"
										},
										this.data.newsHint
									),
									apivm.h(
										"view",
										{id: "columnId", style: "padding:0 7px;"},
										(api.systemType != "android" || this.data.tabBox.data.length > 0) &&
											apivm.h("z-tabs-one", {
												dataMore: this.data.tabBox,
												onChange: function() {
													return this$1.tabChange(1);
												},
												onDotchange: this.tabdotChange
											})
									),
									apivm.h(
										"view",
										{style: "padding:0 11px;"},
										(api.systemType != "android" ||
											this.data.tabChildBox.data.length > 0) &&
											apivm.h("z-tabs-two", {
												dotDefault: true,
												dataMore: this.data.tabChildBox,
												onChange: function() {
													return this$1.tabChange(2);
												}
											})
									),
									apivm.h(
										"view",
										null,
										!this.data.emptyBox.type &&
											this.data.listData.map(function(item, index) {
												return [
													apivm.h("item5", {
														floatModule: this$1.data.floatModule,
														layoutId: this$1.data.layoutId,
														_this: this$1,
														list: this$1.data.listData,
														item: item,
														index: index
													})
												];
											})
									),
									apivm.h(
										"view",
										null,
										this.data.emptyBox.type == "load" && apivm.h("z-skeleton", null)
									),
									apivm.h("z-empty", {
										_this: this,
										dataMore: this.data.emptyBox,
										onRefresh: this.pageRefresh
									})
								)
						)
					)
				)
			);
		};

		return MoModule;
	})(Component);
	MoModule.css = {
		".top_box": {
			position: "absolute",
			width: "100%",
			height: "auto",
			zIndex: "3"
		},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden",
			display: "inline"
		},
		".module_box": {padding: "0 12px", marginBottom: "12px"},
		".module_top_warp": {
			boxShadow: "0px 2px 10px 1px rgba(24,64,118,0.08)",
			borderTopLeftRadius: "4px",
			borderTopRightRadius: "4px",
			borderBottomLeftRadius: "4px",
			borderBottomRightRadius: "4px",
			width: "100%",
			minHeight: "90px",
			flexDirection: "row",
			alignItems: "center"
		},
		".module_top_item": {
			padding: "15px 0px",
			alignItems: "center",
			justifyContent: "flex-start"
		},
		".module_top_item_text": {
			marginTop: "6px",
			color: "#333",
			textAlign: "center"
		},
		".module_notice_warp": {
			width: "100%",
			minHeight: "66px",
			boxShadow: "0px 2px 10px 1px rgba(24,64,118,0.08)",
			borderTopLeftRadius: "4px",
			borderTopRightRadius: "4px",
			borderBottomLeftRadius: "4px",
			borderBottomRightRadius: "4px",
			flexDirection: "row",
			alignItems: "center",
			padding: "0 10px"
		},
		".module_notice_line": {
			width: "1px",
			backgroundColor: "#999",
			opacity: "0.16",
			margin: "0 6px"
		},
		".module_btns_warp": {
			boxShadow: "0px 2px 10px 1px rgba(24,64,118,0.08)",
			borderTopLeftRadius: "4px",
			borderTopRightRadius: "4px",
			borderBottomLeftRadius: "4px",
			borderBottomRightRadius: "4px",
			width: "100%",
			minHeight: "100px"
		},
		".module_btns_hint_box": {
			flexDirection: "row",
			alignItems: "center",
			padding: "10px"
		},
		".module_btns_hint_text": {fontWeight: "600", color: "#000000"},
		".module_btns_item": {
			width: "25%",
			padding: "12px 0px",
			alignItems: "center",
			justifyContent: "flex-start"
		},
		".module_btns_item_text": {
			marginTop: "6px",
			color: "#333",
			textAlign: "center"
		},
		".redDot_box": {
			position: "absolute",
			zIndex: "2",
			background: "#f92323",
			borderRadius: "50%",
			whiteSpace: "nowrap",
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		},
		".redDot_small": {top: "-2px", right: "-5px"},
		".redDot_big": {top: "-8px", right: "-10px"}
	};
	apivm.define("mo-module", MoModule);
	apivm.render(apivm.h("mo-module", null), "body");
})();
