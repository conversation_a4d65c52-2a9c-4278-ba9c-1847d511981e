(function() {
	var G = {
		pageWidth:
			api.platform == "web"
				? api.winWidth > api.winHeight
					? 600
					: api.winWidth
				: api.winWidth,
		refreshPageSize: 0, //返回当前页刷新列表的条数
		dotRefsresh: false, // 返回当前页是否不刷新
		showSkeleton: true, //是否展示骨架屏
		seachText: "", //搜索词
		seachPlaceholder: "请输入搜索内容", //搜索提示
		firstAjax: false, //首次网络请求是否成功
		dotCloseListener: false, //当前页面不要划动返回
		hasCloseListener: false, //不管其它页面 直接添加关闭监听

		appName: "",
		appFont: "", //app全局字体
		appFontSize: 0, //app全局字体大小
		appTheme: "", //app全局主题色
		headTheme: "", //head全局主题色
		careMode: false, //是否启用了关怀模式
		htmlStyle: "", //html级别设置style 置灰等操作
		htmlClass: "", //html级别设置class
		systemtTypeIsPlatform: false, //系统类型是否是平台版
		v: "", //缓存版本号

		uId: "", //普通用户id
		userId: "", //当前用户id 账号id
		userName: "", //当前用户名字
		userImg: "", //当前用户头像
		areaId: "", //当前地区id
		specialRoleKeys: [], //当前用户角色集合
		isAdmin: false, //是否管理员 拥有所有权限
		viewappearFrist: true, //是否首次进入页面

		touchmoveTask: null, //划动元素时 禁用页面划动返回事件
		nTouchmove: false,

		isAppReview: false, //app是否上架期间 隐藏和显示部分功能

		touchmove: function touchmove() {
			G.nTouchmove = true;
			G.touchmoveTask && clearTimeout(G.touchmoveTask);
			G.touchmoveTask = setTimeout(function() {
				G.nTouchmove = false;
			}, 1000);
		},
		//通用组件 start=====================================================

		imagePreviewer: {
			//全局图片预览组件
			show: false,
			imgs: [],
			activeIndex: 0,
			type: 1
		},

		openImgPreviewer: function openImgPreviewer(_param) {
			if (_param === void 0) {
				_param = {};
			}
			if ((_param.imgs || []).length <= 0) {
				return;
			}
			G.imagePreviewer.activeIndex = _param.index || 0;
			G.imagePreviewer.imgs = _param.imgs;
			G.imagePreviewer.show = true;
			T.sendEvent("updatePage");
		},

		areasBox: {
			//地区切换弹窗组件
			show: false,
			type: "half", //full全屏打开  half半屏打开
			pageParam: null
		},

		openAreas: function openAreas(_param, _callback) {
			if (_param === void 0) {
				_param = {};
			}
			G.areasBox.pageParam = _param;
			G.areasBox.show = true;
			T.addEventListener("base_areas_callback", function(ret) {
				_callback && _callback(ret.value);
				T.removeEventListener("base_areas_callback");
			});
			T.sendEvent("updatePage");
		},

		alertBox: {
			// 确认提示框
			show: false,
			title: "",
			content: "",
			richText: false,
			input: false,
			textarea: false,
			placeholder: "",
			cancel: {show: false, text: "取消", color: "#333333"},
			sure: {show: true, text: "确定", color: "appTheme"}
		},

		alert: function alert(_param, _callback) {
			var o = {title: "", msg: "", buttons: ["确定"]};
			if (T.isObject(_param)) {
				o = T.setNewJSON(o, _param);
			} else {
				o.msg = T.isParameters(_param) ? _param : "";
			}
			G.alertBox.title = o.title;
			G.alertBox.content = (o.msg || o.content || "").toString();
			G.alertBox.input = o.input;
			G.alertBox.textarea = o.textarea;
			G.alertBox.placeholder = o.placeholder;
			G.alertBox.richText = o.richText;
			G.alertBox.cancel.show = o.buttons.length > 1;
			G.alertBox.cancel.text = o.buttons[1];
			G.alertBox.sure.text = o.buttons[0];
			G.alertBox.show = true;
			T.addEventListener("base_alert_callback", function(ret) {
				_callback && _callback(ret.value);
				T.removeEventListener("base_alert_callback");
			});
			T.sendEvent("updatePage");
		},

		actionSheetBox: {
			show: false,
			cancel: false,
			title: "",
			active: null,
			data: []
		},

		actionSheet: function actionSheet(_param, _callback) {
			var o = {title: "", cancelTitle: "取消", destructiveTitle: ""};
			o = T.setNewJSON(o, _param);
			G.actionSheetBox.title = o.title;
			G.actionSheetBox.cancel = o.cancelTitle;
			var oldButton = o.buttons || [],
				newButton = [];
			oldButton.forEach(function(item) {
				newButton.push(T.isObject(item) ? item : {value: item});
			});
			G.actionSheetBox.data = newButton;
			G.actionSheetBox.active = _param.active;
			G.actionSheetBox.dotClose = _param.dotClose;
			G.actionSheetBox.show = true;
			T.addEventListener("base_actionSheet_callback", function(ret) {
				_callback && _callback(ret.value);
				T.removeEventListener("base_actionSheet_callback");
			});
			T.sendEvent("updatePage");
		},
		//通用组件 end=====================================================

		installed: function installed(_this) {
			var _this2 = this;
			if (_this.props && _this.props.dataMore);
			else {
				G.fitWidth();
				G.changeConfiguration(_this);
				G.appGrayscale();
				G.initOther();
				T.addEventListener("index_login_ok", function(ret, err) {
					G.initOther();
				});
				//字体刷新
				T.addEventListener("changeConfiguration", function(ret, err) {
					G.changeConfiguration(_this);
				});
				//地区刷新监听
				T.addEventListener("areaChange", function(ret, err) {
					var notifas = ["module", "news", "my", "negotiable", "area"];
					notifas.forEach(function(_eItem) {
						T.sendEvent({name: "areaChange_" + _eItem, extra: ret.value});
					});
				});
				//红点刷新
				T.addEventListener("unreadChange", function(ret, err) {
					var notifas = ["module", "my"];
					notifas.forEach(function(_eItem) {
						T.sendEvent({name: "unreadChange_" + _eItem, extra: ret.value});
					});
				});
				if (
					T.isFunction(_this.close) &&
					!T.isParameters(_this.props.pageParam) &&
					!T.isParameters(_this.props.dataMore)
				) {
					T.addEventListener("keyback", function(ret, err) {
						if (G.imagePreviewer.show) {
							G.imagePreviewer.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G.areasBox.show) {
							G.areasBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G.alertBox.show) {
							if (G.alertBox.cancel.show) {
								G.alertBox.show = false;
								T.sendEvent("updatePage");
							}
							return;
						}
						if (G.actionSheetBox.show) {
							if (G.actionSheetBox.cancel) {
								G.actionSheetBox.show = false;
								T.sendEvent("updatePage");
							}
							return;
						}
						_this2.close(_this);
					});
					T.addEventListener("swiperight", function(ret, err) {
						if (G.nTouchmove) {
							return;
						}
						if (G.imagePreviewer.show) {
							return;
						}
						if (G.areasBox.show) {
							G.areasBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G.alertBox.show && G.alertBox.cancel.show) {
							G.alertBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G.actionSheetBox.show) {
							G.actionSheetBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						_this2.close(_this);
					});
				}
				setTimeout(function() {
					_this2.setHeader(_this);
				}, 10);
			}
			try {
				_this.init();
			} catch (e) {
				console.log(e);
			}
		},
		close: function close(_this) {
			_this.close();
		},
		setHeader: function setHeader(_this) {
			var title = _this.data.title;
			// console.log("=================="+title);
			// console.log(_this.props);
			// console.log(_this.data);
			if (!title) {
				return;
			}
			if (T.platform() == "web") {
				if (window.parent) {
					window.parent.document.title = title;
				} else {
					document.title = title;
				}
			} else if (T.platform() == "mp") {
				wx.setNavigationBarTitle({
					title: title
				});
			}
		},
		//多端页面显示回调 app、h5、小程序
		onShow: function onShow(_this) {
			var _this3 = this;
			if (_this.props.dataMore) {
				return;
			}
			if (G.viewappearFrist) {
				G.viewappearFrist = false;
				return;
			}
			console.log("返回了当前页面：");
			T.sendEvent({name: "changeConfiguration"});
			if (G.areaId != T.getPrefs("sys_aresId")) {
				G.areaId = T.getPrefs("sys_aresId") || "";
				T.sendEvent({name: "areaChange", extra: {key: G.areaId}});
			}
			setTimeout(function() {
				_this3.setHeader(_this);
			}, 10);
			if (_this.getData) {
				//返回刷新一下
				_this.getData(false, {refresh: 1});
			}
		},
		//初始化后其它配置
		initOther: function initOther() {
			G.areaId = T.getPrefs("sys_aresId") || "";
			G.systemtTypeIsPlatform = T.getPrefs("sys_systemType") == "platform"; //系统类型是否是平台版
			G.appName = T.getPrefs("sys_systemName") || "";
			G.uId = T.getPrefs("sys_Id") || "";
			G.userId = T.getPrefs("sys_UserID") || "";
			G.userName = T.getPrefs("sys_UserName") || "";
			G.userImg = T.getPrefs("sys_AppPhoto") || "";
			G.specialRoleKeys = JSON.parse(T.getPrefs("sys_SpecialRoleKeys") || "[]");
			G.isAdmin =
				G.userId == "1" ||
				G.getItemForKey("dc_admin", G.specialRoleKeys) ||
				G.getItemForKey("admin", G.specialRoleKeys);
			G.v = T.getPrefs("sys_appVersion") || "";
			if (T.platform() == "app") {
				G.isAppReview =
					JSON.parse(T.getPrefs("sys_appReviewVersion") || "{}")[api.systemType] ==
					api.appVersion;
			}
		},
		//全局配置
		changeConfiguration: function changeConfiguration(_this) {
			G.appFont =
				T.getPrefs("appFont") && T.getPrefs("appFont") != "0"
					? T.getPrefs("appFont")
					: "heitiSimplified";
			G.appFontSize = Number(
				T.getPrefs("appFontSize") && T.getPrefs("appFontSize") != "0"
					? T.getPrefs("appFontSize")
					: "16"
			);
			G.appTheme =
				T.pageParam(_this).appTheme ||
				T.getPrefs("appTheme" + (myjs.iszx ? "zx" : "rd")) ||
				(myjs.iszx ? "#3088FE" : "#C61414");
			var headTheme =
				_this.data.headTheme ||
				T.pageParam(_this).headTheme ||
				T.getPrefs("headTheme") ||
				"#FFF";
			G.headTheme = headTheme == "appTheme" ? G.appTheme : headTheme;
			G.careMode = parseInt(G.appFontSize) > 16;
			if (T.platform() == "web") {
				var fontStyleId = "fontStyle";
				if (document.getElementById(fontStyleId)) {
					//存在的时候先删除
					document
						.getElementById(fontStyleId)
						.parentNode.removeChild(document.getElementById(fontStyleId));
				}
				var fontStyle = document.createElement("style");
				fontStyle.id = fontStyleId;
				switch (G.appFont) {
					case "shusongSimplified":
						fontStyle.innerText =
							"@font-face{font-family: shusongSimplified;src: url('../../res/fz_shusong_simplified.ttf')}";
						break;
					case "kaitiSimplified":
						fontStyle.innerText =
							"@font-face{font-family: kaitiSimplified;src: url('../../res/fz_kaiti_simplified.ttf')}";
						break;
					case "heitiTraditional":
						fontStyle.innerText =
							"@font-face{font-family: heitiTraditional;src: url('../../res/fz_heiti_traditional.ttf')}";
						break;
				}

				document.getElementsByTagName("head")[0].appendChild(fontStyle);
			}
			_this.update();
			T.sendEvent("updatePage");
		},
		//是否全局置灰
		appGrayscale: function appGrayscale() {
			var appGrayscale = T.getPrefs("appGrayscale") || "0";
			if (T.platform() == "app");
			else {
				// G.htmlStyle = "filter:"+(appGrayscale == 1?'grayscale(1)':'none')+";";//小程序不知道为啥style没用
				G.htmlClass = appGrayscale == 1 ? "filterGray" : "filterNone";
			}
		},
		//展示图片
		showImg: function showImg(_item) {
			var baseUrl = T.isObject(_item) ? _item.url || "" : _item || "";
			baseUrl = G.showAllSystemImg(baseUrl); //先显示系统图片
			if (
				baseUrl.indexOf("http") == 0 &&
				baseUrl.indexOf("http://127.0.0.1") != 0 &&
				baseUrl.indexOf(myjs.tomcatAddress()) != 0
			) {
				//是链接 不是小程序本地链接 不是处理过的链接
				if (myjs.proxy && T.platform() != "app" && baseUrl.indexOf("https") != 0) {
					baseUrl = myjs.tomcatAddress() + "utils/proxyPic?" + baseUrl;
				}
			}
			return baseUrl + (baseUrl.indexOf("?") != -1 ? "&" : "?") + "v=" + G.v;
		},
		showAllSystemImg: function showAllSystemImg(_url) {
			return !_url ||
				_url.indexOf("http") == 0 ||
				_url.indexOf("/") == 0 ||
				_url.indexOf("../") == 0
				? _url
				: myjs.appUrl() + "image/" + _url;
		},
		//图片处理
		cacheImg: function cacheImg(_item, _thumbnail, _url, _priority) {
			if (!T.isObject(_item) || !T.isParameters(_item.url)) return; //没有传对象 或者没有url的时候不处理
			var baseUrl = _item.webImg || _url || _item.url || ""; //存储当前缓存地址
		},
		//字体配置
		loadConfiguration: function loadConfiguration(_changeSize) {
			return (
				"font-size:" +
				((G.appFontSize || 0) + (_changeSize || 0)) +
				"px;font-family:" +
				G.appFont +
				";"
			);
		},
		//宽度配置
		loadConfigurationSize: function loadConfigurationSize(_changeSize, _who) {
			var changeSize = _changeSize || 0,
				returnCss = "",
				cssWidth,
				cssHeight;
			if (T.isArray(_changeSize)) {
				cssWidth = "width:" + (G.appFontSize + (_changeSize[0] || 0)) + "px;";
				cssHeight = "height:" + (G.appFontSize + (_changeSize[1] || 0)) + "px;";
			} else {
				cssWidth = "width:" + (G.appFontSize + changeSize) + "px;";
				cssHeight = "height:" + (G.appFontSize + changeSize) + "px;";
			}
			if (!_who) {
				returnCss = cssWidth + cssHeight;
			} else {
				returnCss = _who == "w" ? cssWidth : cssHeight;
			}
			return returnCss;
		},
		//获取item	只有一层级的时候 会返回 当前index	_i
		getItemForKey: function getItemForKey(_value, _list, _key, _child) {
			var hasChild = false;
			if (!T.isParameters(_list)) return;
			var listLength = _list.length;
			for (var i = 0; i < listLength; i++) {
				var listItem = _list[i];
				if (T.isArray(listItem)) {
					hasChild = true;
					var result = G.getItemForKey(_value, listItem, _key, true);
					if (result) return result;
				} else {
					if (!T.isObject(listItem)) {
						if (listItem === _value) {
							return listItem;
						}
					} else {
						if (T.isArray(listItem[_key || "key"])) {
							hasChild = true;
							var result = G.getItemForKey(
								_value,
								listItem[_key || "key"],
								_key,
								true
							);
							if (result) {
								listItem["_i"] = i;
								return listItem;
							}
						} else if (listItem[_key || "key"] === _value) {
							listItem["_i"] = i;
							return listItem;
						}
					}
					if (
						T.isObject(listItem) &&
						listItem.children &&
						T.isArray(listItem.children)
					) {
						hasChild = true;
						var result = G.getItemForKey(_value, listItem.children, _key, true);
						if (result) return result;
					}
				}
			}
			if (!_child && !hasChild) return false;
		},
		//在集合中删除第一个参数obj和index都可以或对比字符串	第二个传入集合	第三个为对比key
		delItemForKey: function delItemForKey(_obj, _list, _key) {
			if (T.isTargetType(_obj, "number") && _obj < _list.length) {
				_list.splice(_obj, 1);
			} else {
				var contrastObj = !T.isObject(_obj) ? _obj : _obj[_key || "key"];
				for (var i = 0; i < _list.length; i++) {
					if (
						(!T.isObject(_list[i]) ? _list[i] : _list[i][_key || "key"]) ==
						contrastObj
					) {
						_list.splice(i, 1);
						G.delItemForKey(_obj, _list, _key);
						break;
					}
				}
			}
		},
		//是否显示顶部
		showHeader: function showHeader(_this) {
			return T.platform() == "app" || T.pageParam(_this).showHeader;
		},
		//适配多端状态栏
		headerTop: function headerTop() {
			return T.platform() == "app" ? T.safeArea().top : 0;
		},
		//底部可视区域
		footerBottom: function footerBottom(_bottom) {
			return _bottom ? T.safeArea().bottom : 0;
		},
		//适配pc页打开宽度
		fitWidth: function fitWidth() {
			if (
				T.platform() == "web" &&
				document.documentElement.clientWidth > document.documentElement.clientHeight
			) {
				$("body").style.width = "100%";
				$("body").style.maxWidth = "600px";
				$("body").style.minWidth = "300px";
				$("body").style.margin = "auto";
				$("body").style.position = "relative";
			}
		},
		//获取当前主题色相对应前景色
		getHeadThemeRelatively: function getHeadThemeRelatively(_this) {
			var theme =
				(_this && _this.data && _this.data.headTheme
					? _this.data.headTheme || ""
					: "") || G.headTheme;
			return theme && T.isColorDarkOrLight(theme) == "dark" ? "#FFF" : "#333";
		},
		//转换成html格式
		convertRichText: function convertRichText(value) {
			value = (T.isParameters(value) ? value : "") + "";
			if (T.isObject(value) || T.isArray(value)) {
				value = JSON.stringify(value);
			}
			var textList = value.split("\n");
			var str = "";
			for (var i = 0; i < textList.length; i++) {
				var addText = textList[i].replace(/&amp;/g, "&").replace(/ /g, "&nbsp;");
				if (addText) {
					str += "<p>" + addText + "</p>";
				}
			}
			return str;
		},
		//清空html格式
		clearRichText: function clearRichText(value) {
			value = (T.isParameters(value) ? value : "") + "";
			if (T.isObject(value) || T.isArray(value)) {
				value = JSON.stringify(value);
			}
			//坑爹的后台管理了 & 符号
			value = value.replace(/&amp;/g, "&");
			// 空格处理
			value = value.replace(/(&nbsp;)/g, " ");
			// 换行处理
			value = value.replace(/<br\/?[^>]*>/g, "\n");

			value = value.replace(/<\/[p|div|h1|h2|h3|h4|h5|h6]>/g, "\n");
			value = value.replace(/<\/?[^>]*>/g, "");
			return value;
		},
		//阻止冒泡事件
		stopBubble: function stopBubble(e) {
			if (!e) return;
			if (T.platform() == "web") {
				e.preventDefault();
				e.stopPropagation();
			} else if (T.platform() == "mp") {
				e.$_canBubble = false;
			}
		},
		getTagColor: function getTagColor(_key) {
			if (_key === void 0) {
				_key = "";
			}
			var tagColors = [
				{key: "未处理", value: "#F6631C"},
				{key: "未开始", value: "#F6631C"},

				{key: "签到中", value: "#F6931C"},
				{key: "报名中", value: "#F6931C"},
				{key: "进行中", value: "#F6931C"},

				{key: "请假通过", value: "#50C614"},
				{key: "请假待审批", value: "#F6931C"},
				{key: "请假中", value: "#F6931C"},
				{key: "已参与", value: G.appTheme},
				{key: "待审核", value: "#F6931C"},
				{key: "已通过", value: "#50C614"},
				{key: "有效", value: G.appTheme},
				{key: "待审查", value: "#F6931C"},
				{key: "人大交办中", value: "#F6931C"},
				{key: "政协交办中", value: "#F6931C"},
				{key: "政府交办中", value: "#F6931C"},
				{key: "党委交办中", value: "#F6931C"},
				{key: "两院交办中", value: "#F6931C"},
				{key: "法院交办中", value: "#F6931C"},
				{key: "检察院交办中", value: "#F6931C"},
				{key: "转参阅件", value: "#C61414"},
				{key: "办理中", value: "#F6931C"},
				{key: "重新办理", value: "#F6931C"},
				{key: "已答复", value: "#50C614"},
				{key: "已办结", value: "#559FFF"},
				{key: "A类", value: "#F6931C"},
				{key: "B类", value: "#1A74DA"},
				{key: "待受理", value: "#F6931C"},
				{key: "已受理", value: "#50C614"},
				{key: "已回复", value: "#50C614"},
				{key: "待交付审议", value: "#F6931C"},
				{key: "专委会审议中", value: "#F6931C"},
				{key: "已上传相关资料", value: "#50C614"},
				{key: "留存", value: "#F6931C"},
				{key: "采用", value: "#50C614"}
			];

			var tagColor = G.getItemForKey(_key, tagColors);
			return tagColor ? tagColor.value : "#666666";
		},
		//获取文件类型 并返回数据
		getFileInfo: function getFileInfo(_name) {
			if (_name === void 0) {
				_name = "";
			}
			var name = _name.toLocaleLowerCase(),
				fileInfo = {name: "file-unknow-fill", color: "#bccbd7", type: "unknown"};
			try {
				if (name.indexOf(".") != -1)
					name = name.split(".")[name.split(".").length - 1];
				switch (name) {
					case "xlsx":
					case "xlsm":
					case "xlsb":
					case "xltx":
					case "xltm":
					case "xls":
					case "xlt":
					case "et":
					case "csv":
					case "uos": //excel格式
						fileInfo.name = "file-excel-fill";
						fileInfo.color = "#00bd76";
						fileInfo.type = "excel";
						fileInfo.convertType = "0";
						break;
					case "doc":
					case "docx":
					case "docm":
					case "dotx":
					case "dotm":
					case "dot":
					case "xps":
					case "rtf":
					case "wps":
					case "wpt":
					case "uot": //word格式
						fileInfo.name = "file-word-fill";
						fileInfo.color = "#387efa";
						fileInfo.type = "word";
						fileInfo.convertType = "0";
						break;
					case "pdf": //pdf格式
						fileInfo.name = "file-pdf-fill";
						fileInfo.color = "#e9494a";
						fileInfo.type = "pdf";
						fileInfo.convertType = "20";
						break;
					case "ppt":
					case "pptx":
					case "pps":
					case "pot":
					case "pptm":
					case "potx":
					case "potm":
					case "ppsx":
					case "ppsm":
					case "ppa":
					case "ppam":
					case "dps":
					case "dpt":
					case "uop": //ppt
						fileInfo.name = "file-ppt-fill";
						fileInfo.color = "#ff7440";
						fileInfo.type = "ppt";
						fileInfo.convertType = "0";
						break;
					case "bmp":
					case "gif":
					case "jpg":
					case "pic":
					case "png":
					case "tif":
					case "jpeg":
					case "jpe":
					case "icon":
					case "jfif":
					case "dib": //图片格式 case 'webp':
						fileInfo.name = "file-text-fill";
						fileInfo.color = "#ff7440";
						fileInfo.type = "image";
						fileInfo.convertType = "440";
						break;
					case "txt": //文本
						fileInfo.name = "file-text-fill";
						fileInfo.color = "#2696ff";
						fileInfo.type = "txt";
						fileInfo.convertType = "0";
						break;
					case "rar":
					case "zip":
					case "7z":
					case "tar":
					case "gz":
					case "jar":
					case "ios": //压缩格式
						fileInfo.name = "file-zip-fill";
						fileInfo.color = "#a5b0c0";
						fileInfo.type = "compression";
						fileInfo.convertType = "19";
						break;
					case "mp4":
					case "avi":
					case "flv":
					case "f4v":
					case "webm":
					case "m4v":
					case "mov":
					case "3gp":
					case "rm":
					case "rmvb":
					case "mkv":
					case "mpeg":
					case "wmv": //视频格式
						fileInfo.name = "file-music-fill";
						fileInfo.color = "#e14a4a";
						fileInfo.type = "video";
						fileInfo.convertType = "450";
						break;
					case "mp3":
					case "m4a":
					case "amr":
					case "pcm":
					case "wav":
					case "aiff":
					case "aac":
					case "ogg":
					case "wma":
					case "flac":
					case "alac":
					case "wma":
					case "cda": //音频格式
						fileInfo.name = "file-music-fill";
						fileInfo.color = "#8043ff";
						fileInfo.type = "voice";
						fileInfo.convertType = "660";
						break;
					case "folder": //文件夹
						fileInfo.name = "folder-2-fill";
						fileInfo.color = "#ffd977";
						fileInfo.type = "folder";
						break;
				}
			} catch (e) {
				console.log(e.message);
			}
			return fileInfo;
		},
		//获取文件大小
		getFileSize: function getFileSize(_fileSize) {
			if (!_fileSize && _fileSize != 0) return "";
			try {
				var size1 = parseFloat((_fileSize / 1024 / 1024).toFixed(1));
				var size2 = parseFloat((_fileSize / 1024).toFixed(1));
				if (size1 >= 1) {
					return size1 + "MB";
				} else if (size2 >= 1) {
					return size2 + "KB";
				} else {
					return parseInt(_fileSize) + "B";
				}
			} catch (e) {
				return _fileSize;
			}
		},
		//选择文件并上传
		chooseFile: function chooseFile(_this, _item, callback) {
			var max = T.isNumber(_item.max) ? _item.max : 0;
			if (T.platform() == "app") {
				if (T.systemType() == "ios") {
					if (!T.confirmPer("storage", "chooseFile")) {
						//存储权限
						T.addEventListener("storage" + "Per_" + "chooseFile", function(ret, err) {
							T.removeEventListener("storage" + "Per_" + "chooseFile");
							if (ret.value.granted) {
								G.chooseFile(_this, _item, callback);
							}
						});
						return;
					}
				} else {
					if (!api.require("zyRongCloud").hasAllFilesPermission()) {
						T.alert(
							{
								title: "提示",
								msg: "选择本机文件需要您授权访问所有文件权限，是否继续?",
								buttons: ["确定", "取消"]
							},
							function(ret) {
								if (ret.buttonIndex == "1") {
									api.require("zyRongCloud").requestAllFilesPermission(function(ret) {
										if (ret.status) {
											G.chooseFile(_this, _item, callback);
										}
									});
								}
							}
						);
						return;
					}
				}
				var fileBrowser = api.require("fileBrowser");
				fileBrowser.open({}, function(ret, err) {
					fileBrowser.close();
					setTimeout(function() {
						_item.url = ret.url;
						G.uploadFile(_this, _item, function(ret) {
							callback && callback(ret);
						});
					}, 500);
				});
			} else if (T.platform() == "web") {
				var h5Input = document.createElement("input");
				h5Input.type = "file";
				h5Input.accept = "";
				var ua = navigator.userAgent.toLowerCase();
				var version = "";
				if (ua.indexOf("android") > 0) {
					var reg = /android [\d._]+/gi;
					var v_info = ua.match(reg);
					version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, ".");
					version = parseInt(version.split(".")[0]);
				}
				if (!version || Number(version) <= 13) {
					h5Input.multiple = "multiple";
				}
				h5Input.click();
				h5Input.onchange = function() {
					var listLength =
						max != 0 && h5Input.files.length > max ? max : h5Input.files.length;
					for (var i = 0; i < listLength; i++) {
						(function(j) {
							var nItem = JSON.parse(JSON.stringify(_item));
							nItem.url = h5Input.files[j];
							G.uploadFile(_this, nItem, function(ret) {
								callback && callback(ret);
							});
						})(i);
					}
				};
			} else if (T.platform() == "mp") {
				wx.chooseMessageFile({
					count: max != 0 ? max : 9,
					type: "file",
					success: function success(res) {
						for (var i = 0; i < res.tempFiles.length; i++) {
							(function(j) {
								var nItem = JSON.parse(JSON.stringify(_item));
								nItem.url = res.tempFiles[j];
								G.uploadFile(_this, nItem, function(ret) {
									callback && callback(ret);
								});
							})(i);
						}
					}
				});
			}
		},
		//通用上传附件 item格式  url本地地址路径 uploadId上传后的id	state状态【1上传中2完成3失败】
		uploadFile: function uploadFile(_this, _item, callback) {
			if (_item._fileAjax || _item.module == "-noUpload")
				//有传过 或者明确不传
				return;
			_item._fileAjax = true; //是否请求过	有就不再请求
			_item.state = 1;
			if (_item.showToast) {
				T.showProgress("上传中");
			}
			var nCallack = function nCallack(ret, err) {
				T.hideProgress();
				var code = ret ? ret.code : "";
				if (code == 200) {
					var data = ret.data || {};
					_item.state = 2;
					_item.uploadId = data.id;
					_item.otherInfo = data;
				} else {
					_item.state = 3;
					_item.error = ret ? ret.message || ret.data : err.data || "";
				}
				callback && callback(_item);
			};
			if (T.platform() == "mp") {
				wx.uploadFile({
					url: myjs.tomcatAddress() + "utils/proxy",
					filePath: _item.url.path,
					name: "file",
					header: {
						"Content-Type": "multipart/form-data",
						"u-login-areaId": myjs.areaId(_this),
						Authorization: T.getPrefs("sys_token") || ""
					},

					formData: {
						BASE_URL: myjs.appUrl() + "file/upload",
						BASE_TYPE: "file",
						fileName:
							_item.url.name ||
							_item.url.path.substring(_item.url.path.lastIndexOf("/") + 1)
					},

					success: function success(res) {
						nCallack(JSON.parse(res.data), null);
					},
					fail: function fail(err) {
						nCallack(null, JSON.parse(err.data));
					}
				});
			} else {
				T.ajax(
					{u: myjs.appUrl() + "file/upload", _this: _this, web: _item.web},
					"file/upload" + _item.url,
					nCallack,
					"上传附件",
					"post",
					{
						files: {file: _item.url},
						values: {a: 1}
					},
					{
						"content-type": "file" //安卓附件不能传 得用默认的
					}
				);
			}
		},
		getAreaForKey: function getAreaForKey(_key, _dotAll) {
			var rItem = null;
			var areas = JSON.parse(T.getPrefs("sys_areas") || "[]");
			if (!_dotAll || !areas.length) {
				areas = JSON.parse(T.getPrefs("sys_allAreas") || "[]");
			}
			if (areas.length) {
				rItem = G.getItemForKey(_key, areas, "id");
				if (rItem) {
					rItem.name =
						rItem.name.length > 4 ? rItem.name.substring(0, 4) + "..." : rItem.name;
				}
			}
			return rItem || {};
		},
		showTextSize: function showTextSize(_text, _size, _middle) {
			if (_size && _text) {
				_text =
					_text.length > _size
						? _middle
							? _text.substring(0, _size / 2) +
							  "..." +
							  _text.substring(_text.length - _size / 2)
							: _text.substring(0, _size) + "..."
						: _text;
			}
			return _text;
		},
		ajaxAlert: function ajaxAlert(_param, _this, _callback) {
			var _this4 = this;
			var param = {
				title: "提示",
				msg: _param.msg || "",
				buttons: _param.buttons || ["确定", "取消"]
			};

			if (_param.alertParam) {
				param = T.setNewJSON(param, _param.alertParam);
			}
			T.alert(param, function(ret) {
				if (ret.buttonIndex == "1") {
					_this4.ajaxProcess(_param, _this, _callback);
				}
			});
		},
		ajaxProcess: function ajaxProcess(_param, _this, _callback) {
			if (!_param.dotProgress) T.showProgress(_param.toast);
			T.ajax(
				{u: _param.url, _this: _this},
				"ajaxProcess",
				function(ret) {
					if (!_param.dotProgress) T.hideProgress();
					if (!_param.dotToast) T.toast(ret ? ret.message || ret.data : T.NET_ERR);
					if (ret && ret.code == "200") {
						_callback && _callback(ret);
					}
				},
				"\u64CD\u4F5C",
				"post",
				{
					body: JSON.stringify(_param.param)
				}
			);
		}
	};

	//参数是否为空
	function isParameters(_obj) {
		return _obj != null && _obj != undefined;
	}
	//是否数组
	function isArray(_obj) {
		return isParameters(_obj) && toString.apply(_obj) === "[object Array]";
	}
	//是否对象
	function isObject(_obj) {
		return isParameters(_obj) && typeof _obj === "object";
	}
	//是否方法
	function isFunction(_obj) {
		return isParameters(_obj) && typeof _obj === "function";
	}

	//获取随机数
	function getNum() {
		return Math.floor(Math.random() * 100000000);
	}

	//合并json
	function setNewJSON(_obj, _newobj, _dotReplace) {
		_obj = _obj || {};
		_newobj = _newobj || {};
		var returnObj = {};
		for (var key in _obj) {
			returnObj[key] = _obj[key];
		}
		for (var key in _newobj) {
			if (
				(_dotReplace && isParameters(returnObj[key])) ||
				isFunction(isParameters(returnObj[key]))
			)
				continue;
			returnObj[key] =
				isArray(returnObj[key]) || !isObject(returnObj[key])
					? _newobj[key]
					: setNewJSON(returnObj[key], _newobj[key]);
		}
		return returnObj;
	}

	//获取平台类型
	function platform() {
		return api.platform;
	}

	//区域参数
	function safeArea() {
		try {
			return api.safeArea;
		} catch (e) {
			return {top: 0, left: 0, bottom: 0, right: 0};
		}
	}

	//页面参数对象
	function pageParam(_this) {
		try {
			var pageParam =
				(_this && _this.props
					? _this.props.pageParam || (_this.props.dataMore || {}).pageParam
					: null) ||
				api.pageParam ||
				{};
			if (pageParam.paramSaveKey) {
				pageParam = JSON.parse(getPrefs(pageParam.paramSaveKey) || "{}");
			}
			if (platform() == "web" && JSON.stringify(pageParam) == "{}") {
				pageParam = getOtherParam(window.location.href);
			}
			return pageParam;
		} catch (e) {
			return {};
		}
	}

	//获取缓存
	function getPrefs(key) {
		try {
			return api.getPrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				return window.parent[key];
			} else {
				return window[key];
			}
		}
	}

	//设置缓存
	function setPrefs(key, value) {
		if (!isParameters(value)) {
			removePrefs(key);
			return;
		}
		try {
			api.setPrefs({sync: true, key: key, value: value});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				window.parent[key] = value;
			} else {
				window[key] = value;
			}
		}
	}

	//删除缓存
	function removePrefs(key) {
		try {
			api.removePrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				delete window.parent[key];
			} else {
				delete window[key];
			}
		}
	}

	//发送监听
	function sendEvent(name, extra) {
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				var pageframes = window.parent.document.getElementsByTagName("iframe");
				for (var i = 0; i < pageframes.length; i++) {
					if (isArray(pageframes[i].contentWindow.baseEventList)) {
						var sendItem = getItemForKey(
							isObject(name) ? name.name : name,
							pageframes[i].contentWindow.baseEventList
						);
						if (sendItem)
							sendItem.value({value: isObject(name) ? name.extra : extra});
					}
				}
			} else {
				api.sendEvent(isObject(name) ? name : {name: name, extra: extra});
			}
		} catch (e) {}
	}
	//处理app链接 可以不带app会拼接上 或者需要带上特定参数
	function handleSYSLink(_link) {
		if (!_link) return;
		_link = _link.replace("{{tomcatAddress}}", tomcatAddress());
		_link = _link.replace("{{shareAddress}}", shareAddress());
		_link = _link.replace("{{token}}", encodeURIComponent(getPrefs("sys_token")));
		_link = _link.replace("{{sysUrl}}", appUrl());
		_link = _link.replace("{{areaId}}", areaId()); //当前跳转页面的地区id，例如：430000
		_link = _link.replace("{{userId}}", G$1.userId); //当前用户id，例如：1
		_link = _link.replace("{{iszx}}", G$1.sysSign == "zx"); //当前系统类型，例如：true (true为政协，flase为人大)
		_link = _link.replace("{{appTheme}}", G$1.appTheme); //当前app主题颜色，例如：#3657C0
		_link = _link.replace("{{careMode}}", G$1.careMode); //当前是否为关怀模式：例如：true (关怀模式下字体大4px)
		if (_link.indexOf("?ndata=") != -1) {
			if (_link.indexOf("sysUrl-zy-") == -1) _link += "-zyz-sysUrl-zy-" + appUrl();
			if (_link.indexOf("sysAreaId-zy-") == -1)
				_link += "-zyz-sysAreaId-zy-" + areaId();
			if (_link.indexOf("iszx-zy-") == -1)
				_link += "-zyz-iszx-zy-" + (G$1.sysSign == "zx");
			if (_link.indexOf("appTheme-zy-") == -1)
				_link += "-zyz-appTheme-zy-" + G$1.appTheme;
			if (_link.indexOf("careMode-zy-") == -1)
				_link += "-zyz-careMode-zy-" + G$1.careMode;
		}
		return _link;
	}

	//打开新页面
	function openWin(name, url, pageParam, _more) {
		url = handleSYSLink(url); //先处理跳转链接
		if (url.indexOf("http") != 0) {
			url =
				platform() == "web"
					? url.substring(url.lastIndexOf("/") + 1)
					: url.indexOf("..") != 0
					? "../" + url.split(".")[0] + "/" + url
					: url;
		}
		var o = {
			name: name,
			url: url,
			pageParam: pageParam || {},
			bounces: false,
			bgColor: "#FFF",
			slidBackEnabled: false, //ios滑动返回
			vScrollBarEnabled: true,
			hScrollBarEnabled: true,
			scaleEnabled: true,
			animation: {
				type: "push",
				subType: "from_right",
				duration: 300
			},

			reload: true, // 去除设置
			allowEdit: true, //去除设置 默认都可以复制粘贴
			delay: 0,
			overScrollMode: "scrolls",
			defaultRefreshHeader: "swipe"
		};

		if (isObject(_more)) {
			o = setNewJSON(o, _more);
		}
		if (
			G$1.headTheme != getPrefs("headTheme") &&
			G$1.headTheme != "transparent"
		) {
			o.pageParam.headTheme = G$1.headTheme;
		}
		if (
			G$1.appTheme != getPrefs("appTheme" + G$1.sysSign) &&
			G$1.appTheme != (G$1.sysSign == "rd" ? "#C61414" : "#3088FE")
		) {
			o.pageParam.appTheme = G$1.appTheme;
		}
		if (
			o.pageParam.areaId != areaId() ||
			(areaId() != getPrefs("sys_aresId") && areaId() != getPrefs("sys_platform"))
		) {
			o.pageParam.areaId = o.pageParam.areaId || areaId();
		}
		if (o.pageParam.paramSaveKey) {
			setPrefs(o.pageParam.paramSaveKey, JSON.stringify(o.pageParam));
			o.pageParam = {paramSaveKey: o.pageParam.paramSaveKey};
		}
		videoPlayRemoves();
		api.openWin(o);
	}

	//弹出alert
	function alert(_param, _callback) {
		var o = {title: "", msg: "", buttons: ["确定"]};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.msg = (isParameters(_param) ? _param : "").toString();
		}
		var alertBox = {
			title: o.title,
			content: (o.msg || o.content || "").toString(),
			placeholder: o.placeholder,
			closeType: o.closeType || "1",
			type: o.type || "text",
			timeout: o.timeout || 0,
			autoClose: o.autoClose,
			cancel: {
				show: o.buttons.length > 1 || o.closeType == "2" || o.closeType == "3",
				text: o.buttons[1],
				color: "#333333"
			},

			sure: {
				show: o.buttons.length > 0,
				text: o.buttons[0],
				color: "appTheme"
			},

			show: true,
			callback: function callback(ret) {
				_callback && _callback(ret);
			}
		};

		alertBox = setNewJSON(alertBox, _param.otherParam);
		G$1.alertPop = alertBox;
	}

	//网络请求
	function ajax(url, tag, callback, logText, method, data, header) {
		var getUrl = url,
			dataType = "json",
			cacheType = "",
			paramData = {},
			aId = areaId(),
			isWeb = "";
		if (isObject(url)) {
			getUrl = url.u;
			dataType = url.dt || "json";
			cacheType = url.t || "";
			paramData = url.paramData || {};
			aId = url.areaId || areaId();
			isWeb = url.web || "";
		}
		var o = {
			url: getUrl,
			tag: tag,
			method: method || "get",
			cache: false,
			timeout: 120,
			dataType: dataType,
			data: isObject(data) ? data : {},
			headers: setNewJSON(
				{
					"u-login-areaId": aId,
					Authorization: getPrefs("sys_token") || "",
					"content-type": "application/json",
					"u-terminal": "APP"
				},
				header || {}
			)
		};

		o = setNewJSON(o, paramData);
		if (o.url.indexOf("push/rongCloud") != -1) {
			if (o.method == "get") {
				o.url +=
					(o.url.indexOf("?") != -1 ? "&" : "?") +
					"environment=" +
					chatEnvironment();
			} else if (o.data.values) {
				o.data.values.environment = chatEnvironment();
			}
		}
		if (isWeb) {
			(o.headers.Authorization =
				(header || {}).Authorization || getPrefs("public_token") || ""),
				(o.headers["u-terminal"] = "PUBLIC");
		}
		var oldContentType = o.headers["content-type"];
		if (oldContentType == "file") {
			delete o.headers["content-type"];
		}
		if (platform() == "app" && logText) {
			console.log(logText + o.method + "：" + JSON.stringify(o));
		}
		var cbFun = function cbFun(ret, err) {
			if (isFunction(callback)) {
				// if(isObject(err)){
				// 	try{
				// 		ret = JSON.parse(err.msg);
				// 	}catch(e){
				// 		ret = JSON.parse(JSON.stringify(err));
				// 	}
				// 	err = null;
				// }
				if (platform() == "app" && logText) {
					console.log(
						"得到" + logText + "返回结果：" + JSON.stringify(ret ? ret : err)
					);
				}
				if (isObject(ret)) {
					ret.message = ret.message || ret.msg || "";
					var errcode = ret.code || "";
					if ((errcode == 302 || errcode == 2) && cacheType != "login") {
						cleanAllMsg();
						sendEvent({
							name: "index",
							extra: {type: "verificationToken", errmsg: ret.message}
						});
					}
					// if(!isObject(ret.data)){
					// 	ret.data = "";
					// }
				}
				callback(ret, err);
			}
		};
		if (platform() == "web") {
			var xhr = new XMLHttpRequest();
			xhr.open(o.method, o.url);
			for (var header in o.headers) {
				xhr.setRequestHeader(header, o.headers[header]);
			}
			var sendValue = "";
			if (oldContentType.indexOf("x-www-form-urlencoded") != -1) {
				var dValue = o.data.values || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue +=
						(!sendValue ? "" : "&") + vItem + "=" + encodeURIComponent(dValue[vItem]);
				}
			} else if (oldContentType.indexOf("file") != -1) {
				sendValue = new FormData();
				var dValue = o.data.values || {};
				var fileValue = o.data.files || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue.append(vItem, encodeURIComponent(dValue[vItem]));
				}
				for (var vItem in fileValue) {
					sendValue.append(vItem, fileValue[vItem]);
				}
			} else {
				sendValue = o.data.body || JSON.stringify(o.data.values);
			}
			xhr.onreadystatechange = function() {
				if (xhr.readyState === XMLHttpRequest.DONE) {
					var ret, err;
					if (!xhr.responseText) {
						err = {};
					} else {
						var response = this.responseText;
						if (o.dataType == "json") {
							try {
								ret = JSON.parse(response);
							} catch (e) {
								err = {msg: response};
							}
						} else {
							ret = response;
						}
					}
					cbFun(ret, err);
				}
			};
			xhr.send(sendValue);
		} else {
			api.cancelAjax({tag: tag});
			api.ajax(o, cbFun);
		}
	}

	function hasPermission(one_per) {
		if (platform() == "app") {
			if (!one_per) return;
			var rets = api.hasPermission({list: one_per.split(",")});
			if (one_per.indexOf(",") != -1) {
				alert("判断结果：" + JSON.stringify(rets));
				return;
			} else {
				return rets;
			}
		}
	}

	function requestPermission(one_per, callback, _fName) {
		if (platform() == "app") {
			if (!one_per) return;
			api.requestPermission({list: one_per.split(",")}, function(ret) {
				console.log(JSON.stringify(ret));
				ret.list.forEach(function(_eItem, _eIndex, _eArr) {
					sendEvent(_eItem.name + "Per_" + _fName, {granted: _eItem.granted});
				});
				isFunction(callback) && callback(ret, err);
			});
		}
	}

	function confirmPer(perm, _fName, _reason) {
		if (platform() == "app") {
			var has = hasPermission(perm);
			if (!has || !has[0] || !has[0].granted) {
				var hintWord = {
					camera: "相机",
					storage: "存储",
					photos: "照片",
					microphone: "麦克风",
					location: "位置",
					phone: "电话",
					"phone-r": "通话状态",
					"phone-r-log": "通话记录"
				};

				var iosHint =
					"请在" +
					(api.uiMode == "phone" ? "iPhone" : "iPad") +
					"的“设置-隐私-" +
					hintWord[perm] +
					"”中请允许访问" +
					hintWord[perm] +
					(_reason ? "，" + _reason : "。");
				var androidHint =
					"使用该功能需要" +
					hintWord[perm] +
					"权限，" +
					(_reason || "请前往系统设置开启权限。");
				alert(
					{
						title: "无法使用" + hintWord[perm],
						msg: api.systemType == "ios" ? iosHint : androidHint,
						buttons: ["下一步", "取消"]
					},
					function(ret) {
						if (1 == ret.buttonIndex) {
							requestPermission(perm, null, _fName);
						} else {
							sendEvent(perm + "Per_" + _fName, {granted: false});
						}
					}
				);
				return false;
			}
			return true;
		}
		return true;
	}

	//人大政协标识  rd人大 zx政协
	function sysSign() {
		return getPrefs("sys_sign") || "rd";
	}

	//配置地址
	function appUrl() {
		var prot = sysSign() == "rd" ? "20169" : "20170";
		return (
			getPrefs("sys_appUrl") || "https://productpc.cszysoft.com:" + prot + "/lzt/"
		);
	}
	//tomcat配置地址
	function tomcatAddress() {
		return (
			getPrefs("sys_tomcatAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http://cszysoft.com:9090/"
				: "https://cszysoft.com:9091/")
		);
	}
	//分享地址
	function shareAddress(_type) {
		if (_type == 1 && platform() != "mp") return "../../";
		return (
			getPrefs("sys_shareAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http:"
				: "https:") +
				"//cszysoft.com/appShare/" +
				(sysSign() == "rd" ? "platform5rd/" : "platform5zx/")
		);
	}
	//融云唯一前缀
	function chatHeader() {
		return getPrefs("sys_chatHeader") || "platform5" + sysSign();
	}
	//融云正测
	function chatEnvironment() {
		return getPrefs("sys_chatEnvironment") || "1";
	}
	//当前页面地区id
	function areaId() {
		return (
			pageParam().areaId ||
			getPrefs("sys_aresId") ||
			getPrefs("sys_platform") ||
			""
		);
	}

	var SECONDS_A_MINUTE = 60;
	var SECONDS_A_HOUR = SECONDS_A_MINUTE * 60;
	var SECONDS_A_DAY = SECONDS_A_HOUR * 24;
	var SECONDS_A_WEEK = SECONDS_A_DAY * 7;
	var MILLISECONDS_A_SECOND = 1e3;
	var MILLISECONDS_A_MINUTE = SECONDS_A_MINUTE * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_HOUR = SECONDS_A_HOUR * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_DAY = SECONDS_A_DAY * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_WEEK = SECONDS_A_WEEK * MILLISECONDS_A_SECOND; // English locales

	var MS = "millisecond";
	var S = "second";
	var MIN = "minute";
	var H = "hour";
	var D = "day";
	var W = "week";
	var M = "month";
	var Q = "quarter";
	var Y = "year";
	var DATE = "date";
	var FORMAT_DEFAULT = "YYYY-MM-DDTHH:mm:ssZ";
	var INVALID_DATE_STRING = "Invalid Date"; // regex

	var REGEX_PARSE = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/;
	var REGEX_FORMAT = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;

	var en = {
		name: "en",
		weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split(
			"_"
		),
		months: "January_February_March_April_May_June_July_August_September_October_November_December".split(
			"_"
		)
	};

	var padStart = function padStart(string, length, pad) {
		var s = String(string);
		if (!s || s.length >= length) return string;
		return "" + Array(length + 1 - s.length).join(pad) + string;
	};
	var padZoneStr = function padZoneStr(instance) {
		var negMinutes = -instance.utcOffset();
		var minutes = Math.abs(negMinutes);
		var hourOffset = Math.floor(minutes / 60);
		var minuteOffset = minutes % 60;
		return (
			"" +
			(negMinutes <= 0 ? "+" : "-") +
			padStart(hourOffset, 2, "0") +
			":" +
			padStart(minuteOffset, 2, "0")
		);
	};
	var monthDiff = function monthDiff(a, b) {
		if (a.date() < b.date()) return -monthDiff(b, a);
		var wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month());
		var anchor = a.clone().add(wholeMonthDiff, M);
		var c = b - anchor < 0;
		var anchor2 = a.clone().add(wholeMonthDiff + (c ? -1 : 1), M);
		return +(
			-(
				wholeMonthDiff +
				(b - anchor) / (c ? anchor - anchor2 : anchor2 - anchor)
			) || 0
		);
	};
	var absFloor = function absFloor(n) {
		return n < 0 ? Math.ceil(n) || 0 : Math.floor(n);
	};
	var prettyUnit = function prettyUnit(u) {
		var special = {
			M: M,
			y: Y,
			w: W,
			d: D,
			D: DATE,
			h: H,
			m: MIN,
			s: S,
			ms: MS,
			Q: Q
		};

		return (
			special[u] ||
			String(u || "")
				.toLowerCase()
				.replace(/s$/, "")
		);
	};
	var isUndefined = function isUndefined(s) {
		return s === undefined;
	};
	var U = {
		s: padStart,
		z: padZoneStr,
		m: monthDiff,
		a: absFloor,
		p: prettyUnit,
		u: isUndefined
	};

	var L = "en";
	var Ls = {};
	Ls[L] = en;
	var isDayjs = function isDayjs(d) {
		return d instanceof Dayjs;
	};
	var parseLocale = function parseLocale(preset, object, isLocal) {
		var l;
		if (!preset) return L;
		if (typeof preset === "string") {
			var presetLower = preset.toLowerCase();
			if (Ls[presetLower]) {
				l = presetLower;
			}
			if (object) {
				Ls[presetLower] = object;
				l = presetLower;
			}
			var presetSplit = preset.split("-");
			if (!l && presetSplit.length > 1) {
				return parseLocale(presetSplit[0]);
			}
		} else {
			var name = preset.name;
			Ls[name] = preset;
			l = name;
		}
		if (!isLocal && l) L = l;
		return l || (!isLocal && L);
	};
	var dayjs = function dayjs(date, c) {
		if (isDayjs(date)) {
			return date.clone();
		}
		var cfg = typeof c === "object" ? c : {};
		cfg.date = date;
		cfg.args = arguments;
		return new Dayjs(cfg);
	};
	var wrapper = function wrapper(date, instance) {
		return dayjs(date, {
			locale: instance.$L,
			utc: instance.$u,
			x: instance.$x,
			$offset: instance.$offset
		});
	};
	var Utils = U;
	Utils.l = parseLocale;
	Utils.i = isDayjs;
	Utils.w = wrapper;
	var parseDate = function parseDate(cfg) {
		var date = cfg.date,
			utc = cfg.utc;
		if (date === null) return new Date(NaN);
		if (Utils.u(date)) return new Date();
		if (date instanceof Date) return new Date(date);
		if (typeof date === "string" && !/Z$/i.test(date)) {
			var d = date.match(REGEX_PARSE);
			if (d) {
				var m = d[2] - 1 || 0;
				var ms = (d[7] || "0").substring(0, 3);
				if (utc) {
					return new Date(
						Date.UTC(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms)
					);
				}
				return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);
			}
		}
		return new Date(date);
	};
	var Dayjs = (function() {
		function Dayjs(cfg) {
			this.$L = parseLocale(cfg.locale, null, true);
			this.parse(cfg);
		}
		var _proto = Dayjs.prototype;
		_proto.parse = function parse(cfg) {
			this.$d = parseDate(cfg);
			this.$x = cfg.x || {};
			this.init();
		};
		_proto.init = function init() {
			var $d = this.$d;
			this.$y = $d.getFullYear();
			this.$M = $d.getMonth();
			this.$D = $d.getDate();
			this.$W = $d.getDay();
			this.$H = $d.getHours();
			this.$m = $d.getMinutes();
			this.$s = $d.getSeconds();
			this.$ms = $d.getMilliseconds();
		};
		_proto.$utils = function $utils() {
			return Utils;
		};
		_proto.isValid = function isValid() {
			return !(this.$d.toString() === INVALID_DATE_STRING);
		};
		_proto.isSame = function isSame(that, units) {
			var other = dayjs(that);
			return this.startOf(units) <= other && other <= this.endOf(units);
		};
		_proto.isAfter = function isAfter(that, units) {
			return dayjs(that) < this.startOf(units);
		};
		_proto.isBefore = function isBefore(that, units) {
			return this.endOf(units) < dayjs(that);
		};
		_proto.$g = function $g(input, get, set) {
			if (Utils.u(input)) return this[get];
			return this.set(set, input);
		};
		_proto.unix = function unix() {
			return Math.floor(this.valueOf() / 1000);
		};
		_proto.valueOf = function valueOf() {
			return this.$d.getTime();
		};
		_proto.startOf = function startOf(units, _startOf) {
			var _this = this;
			var isStartOf = !Utils.u(_startOf) ? _startOf : true;
			var unit = Utils.p(units);
			var instanceFactory = function instanceFactory(d, m) {
				var ins = Utils.w(
					_this.$u ? Date.UTC(_this.$y, m, d) : new Date(_this.$y, m, d),
					_this
				);
				return isStartOf ? ins : ins.endOf(D);
			};
			var instanceFactorySet = function instanceFactorySet(method, slice) {
				var argumentStart = [0, 0, 0, 0];
				var argumentEnd = [23, 59, 59, 999];
				return Utils.w(
					_this
						.toDate()
						[method].apply(
							_this.toDate("s"),
							(isStartOf ? argumentStart : argumentEnd).slice(slice)
						),
					_this
				);
			};
			var $W = this.$W,
				$M = this.$M,
				$D = this.$D;
			var utcPad = "set" + (this.$u ? "UTC" : "");
			switch (unit) {
				case Y:
					return isStartOf ? instanceFactory(1, 0) : instanceFactory(31, 11);
				case M:
					return isStartOf ? instanceFactory(1, $M) : instanceFactory(0, $M + 1);
				case W: {
					var weekStart = this.$locale().weekStart || 0;
					var gap = ($W < weekStart ? $W + 7 : $W) - weekStart;
					return instanceFactory(isStartOf ? $D - gap : $D + (6 - gap), $M);
				}
				case D:
				case DATE:
					return instanceFactorySet(utcPad + "Hours", 0);
				case H:
					return instanceFactorySet(utcPad + "Minutes", 1);
				case MIN:
					return instanceFactorySet(utcPad + "Seconds", 2);
				case S:
					return instanceFactorySet(utcPad + "Milliseconds", 3);
				default:
					return this.clone();
			}
		};
		_proto.endOf = function endOf(arg) {
			return this.startOf(arg, false);
		};
		_proto.$set = function $set(units, _int) {
			var _C$D$C$DATE$C$M$C$Y$C;
			var unit = Utils.p(units);
			var utcPad = "set" + (this.$u ? "UTC" : "");
			var name = ((_C$D$C$DATE$C$M$C$Y$C = {}),
			(_C$D$C$DATE$C$M$C$Y$C[D] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[DATE] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[M] = utcPad + "Month"),
			(_C$D$C$DATE$C$M$C$Y$C[Y] = utcPad + "FullYear"),
			(_C$D$C$DATE$C$M$C$Y$C[H] = utcPad + "Hours"),
			(_C$D$C$DATE$C$M$C$Y$C[MIN] = utcPad + "Minutes"),
			(_C$D$C$DATE$C$M$C$Y$C[S] = utcPad + "Seconds"),
			(_C$D$C$DATE$C$M$C$Y$C[MS] = utcPad + "Milliseconds"),
			_C$D$C$DATE$C$M$C$Y$C)[unit];
			var arg = unit === D ? this.$D + (_int - this.$W) : _int;
			if (unit === M || unit === Y) {
				var date = this.clone().set(DATE, 1);
				date.$d[name](arg);
				date.init();
				this.$d = date.set(DATE, Math.min(this.$D, date.daysInMonth())).$d;
			} else if (name) this.$d[name](arg);
			this.init();
			return this;
		};
		_proto.set = function set(string, _int2) {
			return this.clone().$set(string, _int2);
		};
		_proto.get = function get(unit) {
			return this[Utils.p(unit)]();
		};
		_proto.add = function add(number, units) {
			var _this2 = this,
				_C$MIN$C$H$C$S$unit;
			number = Number(number);
			var unit = Utils.p(units);
			var instanceFactorySet = function instanceFactorySet(n) {
				var d = dayjs(_this2);
				return Utils.w(d.date(d.date() + Math.round(n * number)), _this2);
			};
			if (unit === M) {
				return this.set(M, this.$M + number);
			}
			if (unit === Y) {
				return this.set(Y, this.$y + number);
			}
			if (unit === D) {
				return instanceFactorySet(1);
			}
			if (unit === W) {
				return instanceFactorySet(7);
			}
			var step =
				((_C$MIN$C$H$C$S$unit = {}),
				(_C$MIN$C$H$C$S$unit[MIN] = MILLISECONDS_A_MINUTE),
				(_C$MIN$C$H$C$S$unit[H] = MILLISECONDS_A_HOUR),
				(_C$MIN$C$H$C$S$unit[S] = MILLISECONDS_A_SECOND),
				_C$MIN$C$H$C$S$unit)[unit] || 1;
			var nextTimeStamp = this.$d.getTime() + number * step;
			return Utils.w(nextTimeStamp, this);
		};
		_proto.subtract = function subtract(number, string) {
			return this.add(number * -1, string);
		};
		_proto.format = function format(formatStr) {
			var _this3 = this;
			var locale = this.$locale();
			if (!this.isValid()) return locale.invalidDate || INVALID_DATE_STRING;
			var str = formatStr || FORMAT_DEFAULT;
			var zoneStr = Utils.z(this);
			var $H = this.$H,
				$m = this.$m,
				$M = this.$M;
			var weekdays = locale.weekdays,
				months = locale.months,
				meridiem = locale.meridiem;
			var getShort = function getShort(arr, index, full, length) {
				return (
					(arr && (arr[index] || arr(_this3, str))) || full[index].slice(0, length)
				);
			};
			var get$H = function get$H(num) {
				return Utils.s($H % 12 || 12, num, "0");
			};
			var meridiemFunc =
				meridiem ||
				function(hour, minute, isLowercase) {
					var m = hour < 12 ? "AM" : "PM";
					return isLowercase ? m.toLowerCase() : m;
				};
			var matches = {
				YY: String(this.$y).slice(-2),
				YYYY: this.$y,
				M: $M + 1,
				MM: Utils.s($M + 1, 2, "0"),
				MMM: getShort(locale.monthsShort, $M, months, 3),
				MMMM: getShort(months, $M),
				D: this.$D,
				DD: Utils.s(this.$D, 2, "0"),
				d: String(this.$W),
				dd: getShort(locale.weekdaysMin, this.$W, weekdays, 2),
				ddd: getShort(locale.weekdaysShort, this.$W, weekdays, 3),
				dddd: weekdays[this.$W],
				H: String($H),
				HH: Utils.s($H, 2, "0"),
				h: get$H(1),
				hh: get$H(2),
				a: meridiemFunc($H, $m, true),
				A: meridiemFunc($H, $m, false),
				m: String($m),
				mm: Utils.s($m, 2, "0"),
				s: String(this.$s),
				ss: Utils.s(this.$s, 2, "0"),
				SSS: Utils.s(this.$ms, 3, "0"),
				Z: zoneStr
			};

			return str.replace(REGEX_FORMAT, function(match, $1) {
				return $1 || matches[match] || zoneStr.replace(":", "");
			});
		};
		_proto.utcOffset = function utcOffset() {
			return -Math.round(this.$d.getTimezoneOffset() / 15) * 15;
		};
		_proto.diff = function diff(input, units, _float) {
			var _C$Y$C$M$C$Q$C$W$C$D$;
			var unit = Utils.p(units);
			var that = dayjs(input);
			var zoneDelta =
				(that.utcOffset() - this.utcOffset()) * MILLISECONDS_A_MINUTE;
			var diff = this - that;
			var result = Utils.m(this, that);
			result =
				((_C$Y$C$M$C$Q$C$W$C$D$ = {}),
				(_C$Y$C$M$C$Q$C$W$C$D$[Y] = result / 12),
				(_C$Y$C$M$C$Q$C$W$C$D$[M] = result),
				(_C$Y$C$M$C$Q$C$W$C$D$[Q] = result / 3),
				(_C$Y$C$M$C$Q$C$W$C$D$[W] = (diff - zoneDelta) / MILLISECONDS_A_WEEK),
				(_C$Y$C$M$C$Q$C$W$C$D$[D] = (diff - zoneDelta) / MILLISECONDS_A_DAY),
				(_C$Y$C$M$C$Q$C$W$C$D$[H] = diff / MILLISECONDS_A_HOUR),
				(_C$Y$C$M$C$Q$C$W$C$D$[MIN] = diff / MILLISECONDS_A_MINUTE),
				(_C$Y$C$M$C$Q$C$W$C$D$[S] = diff / MILLISECONDS_A_SECOND),
				_C$Y$C$M$C$Q$C$W$C$D$)[unit] || diff;
			return _float ? result : Utils.a(result);
		};
		_proto.daysInMonth = function daysInMonth() {
			return this.endOf(M).$D;
		};
		_proto.$locale = function $locale() {
			return Ls[this.$L];
		};
		_proto.locale = function locale(preset, object) {
			if (!preset) return this.$L;
			var that = this.clone();
			var nextLocaleName = parseLocale(preset, object, true);
			if (nextLocaleName) that.$L = nextLocaleName;
			return that;
		};
		_proto.clone = function clone() {
			return Utils.w(this.$d, this);
		};
		_proto.toDate = function toDate() {
			return new Date(this.valueOf());
		};
		_proto.toJSON = function toJSON() {
			return this.isValid() ? this.toISOString() : null;
		};
		_proto.toISOString = function toISOString() {
			return this.$d.toISOString();
		};
		_proto.toString = function toString() {
			return this.$d.toUTCString();
		};
		return Dayjs;
	})();
	var proto = Dayjs.prototype;
	dayjs.prototype = proto;
	[
		["$ms", MS],
		["$s", S],
		["$m", MIN],
		["$H", H],
		["$W", D],
		["$M", M],
		["$y", Y],
		["$D", DATE]
	].forEach(function(g) {
		proto[g[1]] = function(input) {
			return this.$g(input, g[0], g[1]);
		};
	});
	dayjs.extend = function(plugin, option) {
		if (!plugin.$i) {
			plugin(option, Dayjs, dayjs);
			plugin.$i = true;
		}
		return dayjs;
	};
	dayjs.locale = parseLocale;
	dayjs.isDayjs = isDayjs;
	dayjs.unix = function(timestamp) {
		return dayjs(timestamp * 1e3);
	};
	dayjs.en = Ls[L];
	dayjs.Ls = Ls;
	dayjs.p = {};

	var module6 = {
		name: sysSign() == "rd" ? "意见征集" : "网络议政",
		code: "6",
		businessCode: "opinioncollect"
	};
	var module9 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息",
		code: "9",
		businessCode: "cppcc_member"
	};
	var module9_1 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息变更申请",
		code: "9_1",
		businessCode: "cppccMemberCheckPrepare"
	};
	var module12 = {
		name: sysSign() == "rd" ? "圈子" : "委员说",
		code: "12",
		businessCode: "styleCircle"
	};

	//打开附件预览
	function openWin_filePreviewer(_param) {
		openWin("mo_details_url", "../mo_details_url/mo_details_url.stml", _param);
	}

	//获取登录信息
	function getLoginInfo(_param, _callback) {
		ajax(
			{u: appUrl() + "login/user"},
			"login/user",
			function(ret, err) {
				if (ret && ret.code == 200 && ret.data.id != "anonymous") {
					saveLogin(ret.data);
				}
				_callback && _callback(ret, err);
			},
			"用户信息",
			"post",
			{
				values: _param.param
			},
			_param.header
		);
	}

	function getAllChatInfo() {
		var chatInfos = JSON.parse(getPrefs("chatInfos") || "[]"),
			addInfo = [];
		chatInfos.forEach(function(_eItem) {
			if (_eItem.id) {
				if (/^(?!h|(\.)|(\/)).*/.test(_eItem.url)) {
					_eItem.url = showImg(_eItem.url);
				}
				addInfo.push(_eItem);
			}
		});
		G$1.chatInfos = addInfo;
		setPrefs("chatInfos", JSON.stringify(G$1.chatInfos));
		return G$1.chatInfos;
	}

	function setAllChatInfo(_item) {
		getAllChatInfo();
		var nowInfo = JSON.parse(JSON.stringify(G$1.chatInfos));
		if (isArray(_item)) {
			_item.forEach(function(_eItem) {
				if (_eItem.id) {
					delItemForKey(_eItem.id, nowInfo, "id");
					nowInfo.push(_eItem);
				}
			});
		} else {
			if (!_item.id) {
				return;
			}
			delItemForKey(_item.id, nowInfo, "id");
			nowInfo.push(_item);
		}
		setPrefs("chatInfos", JSON.stringify(nowInfo));
		setRongChatInfo();
	}

	function setRongChatInfo() {
		if (platform() == "app" && api.require("zyRongCloudRTC")) {
			getAllChatInfo();
			var setUser = {
				apply: 1,
				users: G$1.chatInfos.map(function(obj) {
					return {id: chatHeader() + obj.id, name: obj.name, url: obj.url};
				}),
				groupUser: JSON.parse(getPrefs("rongGroupUser") || "[]"), //当前通话的群组成员列表id集合
				totalNumber: 10 //语音或视频最大人数
			};
			// console.log("融云设置信息："+JSON.stringify(setUser));
			api.require("zyRongCloudRTC").setUsers(setUser, function(ret) {
				removePrefs("rongGroupUser");
				setRongChatInfo();
				setTimeout(
					function() {
						sendEvent({name: "chat_refresh"});
					},
					api.systemType == "ios" ? 600 : 1500
				);
			});
		}
	}

	//去除某个视频播放
	function videoPlayRemove(_id) {
		delItemForKey(_id, G$1.playVideos);
	}

	//去除所有播放
	function videoPlayRemoves() {
		[].forEach(function(_id) {
			document.getElementById(_id) && document.getElementById(_id).pause();
			videoPlayRemove(_id);
		});
	}

	//全局页面引用变量
	var G$1 = {
		sysSign: sysSign(), //人大政协标识
		pageWidth: "", //页面总宽度
		onShowNum: -1, //当前页面展示次数 1为首次
		appName: "", //app名字
		appFont: "", //app全局字体
		appFontSize: 0, //app全局字体大小
		appTheme: "", //app全局主题色
		headTheme: "", //head全局主题色
		headColor: "", //标题前景色
		headTitle: "", //标题栏文字
		loginInfo: "",

		careMode: false, //是否启用了关怀模式
		systemtTypeIsPlatform: false, //系统类型是否是平台版
		isAppReview: false, //app是否上架期间 隐藏和显示部分功能
		v: "", //缓存版本号

		uId: "", //普通用户id
		userId: "", //当前用户id 账号id
		userName: "", //当前用户名字
		userImg: "", //当前用户头像
		areaId: "", //当前地区id
		specialRoleKeys: [], //当前用户角色集合
		isAdmin: false, //是否管理员 拥有所有权限
		grayscale: "", //全局置灰
		watermark: "", //全局水印

		// chatInfos:[],
		// chatInfoTask:[],

		alertPop: {
			// alert提示框
			show: false
		},

		actionSheetPop: {
			//actionSheet弹出框
			show: false
		},

		imgPreviewerPop: {
			//图片预览
			show: false
		},

		areasPop: {
			// 全局地区弹窗
			show: false
		},

		numInputPop: {
			// 全局数字弹窗
			show: false
		},

		favoritePop: {
			//收藏弹窗
			show: false
		},

		sharePosterPop: {
			//分享海报
			show: false
		},

		sharePop: {
			//分享
			show: false
		},

		identifyAudioPop: {
			//语音输入
			show: false
		},

		selectPop: {
			//单多选
			show: false
		},

		qrcodePop: {
			//h5扫码
			show: false
		},

		addressBookPop: {
			//通讯录
			show: false
		},

		fileListPop: {
			//选择文件
			show: false
		}
	};

	//动态加载字体和大小
	function loadConfiguration(size) {
		return (
			"font-size:" +
			((G$1.appFontSize || 0) + (size || 0)) +
			"px;" +
			("font-family:" + G$1.appFont + ";")
		);
	}

	//动态宽度配置
	function loadConfigurationSize(size, _who) {
		var changeSize = size || 0,
			cssWidth,
			cssHeight;
		if (isArray(size)) {
			cssWidth = "width:" + (G$1.appFontSize + (size[0] || 0)) + "px;";
			cssHeight = "height:" + (G$1.appFontSize + (size[1] || 0)) + "px;";
		} else {
			cssWidth = "width:" + (G$1.appFontSize + changeSize) + "px;";
			cssHeight = "height:" + (G$1.appFontSize + changeSize) + "px;";
		}
		if (!_who) {
			return cssWidth + cssHeight;
		} else {
			return _who == "w" ? cssWidth : cssHeight;
		}
	}

	//获取item参数
	function getItemForKey(_value, _list, _key, _child) {
		if (!isParameters(_list)) return;
		var hasChild = false,
			listLength = _list.length;
		for (var i = 0; i < listLength; i++) {
			var listItem = _list[i];
			if (isArray(listItem)) {
				hasChild = true;
				var result = getItemForKey(_value, listItem, _key, true);
				if (result) return result;
			} else {
				if (!isObject(listItem)) {
					if (listItem === _value) return listItem;
				} else {
					var listItemKey = listItem[_key || "key"];
					if (isArray(listItemKey)) {
						hasChild = true;
						var result = getItemForKey(_value, listItemKey, _key, true);
						if (result) {
							return listItem;
						}
					} else if (!isObject(listItemKey) && listItemKey === _value) {
						listItem["_i"] = i;
						return listItem;
					}
				}
				if (isObject(listItem) && isArray(listItem.children)) {
					hasChild = true;
					var result = getItemForKey(_value, listItem.children, _key, true);
					if (result) return result;
				}
			}
		}
		if (!_child && !hasChild) return false;
	}

	//删除item中的元素
	function delItemForKey(_obj, _list, _key) {
		var contrastObj = !isObject(_obj) ? _obj : _obj[_key || "key"];
		for (var i = 0; i < _list.length; i++) {
			if (
				(!isObject(_list[i]) ? _list[i] : _list[i][_key || "key"]) === contrastObj
			) {
				_list.splice(i, 1);
				delItemForKey(_obj, _list, _key);
				break;
			}
		}
	}

	//web和小程序阻止底部事件
	function stopBubble(e) {
		if (!e) return;
		if (platform() == "web") {
			e.preventDefault();
			e.stopPropagation();
		} else if (platform() == "mp") {
			e.$_canBubble = false;
		}
	}

	//适配图片
	function showImg(_item, _add) {
		if (_add === void 0) {
			_add = "";
		}
		var baseUrl = isObject(_item) ? _item.url || "" : _item || "";
		if (!baseUrl) return;
		if (/^(?!h|(\.)|(\/)).*/.test(baseUrl)) {
			baseUrl =
				appUrl() +
				"image/" +
				_add +
				(baseUrl.indexOf("-compress-") > -1
					? baseUrl.split("-compress-")[1]
					: baseUrl);
		}
		return baseUrl + (baseUrl.indexOf("?") != -1 ? "&" : "?") + "v=" + G$1.v;
	}

	//获取元素位置宽高
	function getBoundingClientRect(_id, _callback) {
		if (!document.getElementById(_id)) return;
		if (platform() == "mp") {
			document
				.getElementById(_id)
				.$$getBoundingClientRect()
				.then(function(res) {
					return _callback(res);
				});
		} else {
			return _callback(document.getElementById(_id).getBoundingClientRect());
		}
	}

	//删除所有缓存
	function cleanAllMsg() {
		var exitPrefs = [
			"isAutoLogin",
			"loginPassword",
			"sys_token",
			"sys_Id",
			"sys_UserID",
			"sys_UserName",
			"sys_AppPhoto",
			"sys_Mobile",
			"sys_Position",
			"sys_aresId",
			"sys_OfficeId",
			"sys_SpecialRoleKeys",
			"sdt_signin_phone",
			"public_token",
			"last14Msg",
			"sys_unread"
		];
		exitPrefs.forEach(function(_eItem) {
			removePrefs(_eItem);
		});
	}

	//保存登录信息
	function saveLogin(_info) {
		var infolist = _info || {};
		setPrefs("sys_Id", infolist.id);
		setPrefs("sys_UserID", infolist.accountId);
		setPrefs("sys_UserName", infolist.userName);
		setPrefs("sys_AppPhoto", infolist.headImg || infolist.photo); //headImg用户头像 photo代表头像
		setPrefs("sys_Mobile", infolist.mobile);
		setPrefs("sys_Position", infolist.position);
		if (!getPrefs("sys_aresId")) {
			setPrefs("sys_aresId", infolist.areaId);
		}
		setPrefs("sys_OfficeId", infolist.officeId);
		setPrefs(
			"sys_SpecialRoleKeys",
			JSON.stringify(infolist.specialRoleKeys || [])
		);
		if (infolist.id) {
			// //修改头像后保存下缓存数据
			setAllChatInfo({
				id: infolist.accountId,
				name: infolist.userName,
				url: infolist.headImg || infolist.photo
			});
		}
		sendEvent({name: "sys_refresh"});
	}

	//获取链接中参数
	function getOtherParam(_url) {
		if (_url.indexOf("?") != -1) {
			_url = _url.substring(_url.indexOf("?") + 1);
		}
		var params = _url.split("&"),
			rp = {};
		for (var j = 0; j < params.length; j++) {
			if (params[j]) {
				var data_key = params[j].substring(0, params[j].indexOf("="));
				if (!data_key) {
					continue;
				}
				var data_value = decodeURIComponent(
					params[j].substring(params[j].indexOf("=") + 1)
				);
				if (data_value.indexOf("{") == 0 || data_value.indexOf("[") == 0)
					data_value = JSON.parse(data_value);
				rp[data_key] = data_value;
			}
		}
		return rp;
	}

	// import dayjs from "./dayjs";
	// import { MD5 } from './crypto-ts.js';
	/**
	 * 封装和适配 api相关所有接口 和一些别的
	 */
	var T = {};
	T.NET_ERR = "网络不小心断开了";
	T.NET_OK = "操作成功";
	T.NET_NO = "操作失败，请重试";
	T.JK_ERR = "接口异常，请联系技术";
	T.LOAD_ING = "加载中，请稍候...";
	T.LOAD_MORE = "点击加载更多";
	T.LOAD_ALL = "已加载完";
	T.LOAD_NO = "暂无数据";
	T.LOAD_NOT = "页面尚未加载完成，请刷新重试";

	T.isParameters = function(obj) {
		return obj != null && obj != undefined;
	};
	T.isTargetType = function(obj, typeString) {
		return typeof obj === typeString;
	};
	T.isNumber = function(obj) {
		return T.isParameters(obj) && T.isTargetType(obj, "number");
	};
	T.isObject = function(obj) {
		return T.isParameters(obj) && T.isTargetType(obj, "object");
	};
	T.isArray = function(obj) {
		return T.isParameters(obj) && toString.apply(obj) === "[object Array]";
	};
	T.isFunction = function(obj) {
		return T.isParameters(obj) && T.isTargetType(obj, "function");
	};

	T.setNewJSON = function(obj, newobj, _ifReplace) {
		obj = obj || {};
		newobj = newobj || {};
		var returnObj = {};
		for (var key in obj) {
			returnObj[key] = obj[key];
		}
		for (var key in newobj) {
			if (_ifReplace && returnObj.hasOwnProperty(key)) {
				//是否不替换前者 默认替换
				continue;
			}
			returnObj[key] = newobj[key];
		}
		return returnObj;
	};

	T.getNum = function() {
		return Math.round(Math.random() * 1000000000);
	};

	T.trim = function(str) {
		if (String.prototype.trim) {
			return str == null ? "" : String.prototype.trim.call(str);
		} else {
			return str.replace(/(^\s*)|(\s*$)/g, "");
		}
	};
	T.trimAll = function(str) {
		return str.replace(/\s*/g, "");
	};
	T.removeTag = function(str) {
		if (!str) return str;
		return T.decodeCharacter(
			str
				.replace(/<!--[\w\W\r\n]*?-->/gim, "")
				.replace(/(<[^\s\/>]+)\b[^>]*>/gi, "$1>")
				.replace(/<[^>]+>/g, "")
				.replace(/\s*/g, "")
		);
	};
	T.decodeCharacter = function(str) {
		if (!str) return str;
		return str
			.replace(/&amp;/g, "&")
			.replace(/(&nbsp;|&ensp;)/g, " ")
			.replace(/&mdash;/g, "—")
			.replace(/&ldquo;/g, "“")
			.replace(/&rsquo;/g, "’")
			.replace(/&lsquo;/g, "‘")
			.replace(/&rdquo;/g, "”")
			.replace(/&middot;/g, "·")
			.replace(/&hellip;/g, "…")
			.replace(/&quot;/g, '"')
			.replace(/&lt;/g, "<")
			.replace(/&gt;/g, ">");
	};

	T.platform = function() {
		try {
			return api.platform || "";
		} catch (e) {
			return "";
		}
	};

	T.rebootApp = function() {
		if (T.platform() == "web") {
			window.location.reload();
		} else if (T.platform() == "mp") {
			wx.reLaunch({url: "/pages/index/index"});
		} else {
			api.rebootApp();
		}
	};

	T.appName = function() {
		try {
			return myjs.appName || "";
		} catch (e) {
			return "";
		}
	};

	T.systemType = function() {
		try {
			return api.systemType;
		} catch (e) {
			return "";
		}
	};

	T.pageParam = function(_this) {
		try {
			var pageParam =
				(_this && _this.props ? _this.props.pageParam : null) ||
				api.pageParam ||
				{};
			if (pageParam.paramSaveKey) {
				pageParam = JSON.parse(T.getPrefs(pageParam.paramSaveKey) || "{}");
			}
			if (T.platform() == "web" && JSON.stringify(pageParam) == "{}") {
				pageParam = getOtherParam(window.location.href);
			}
			return pageParam;
		} catch (e) {
			return {};
		}
	};

	T.safeArea = function() {
		try {
			return api.safeArea;
		} catch (e) {
			return {top: 0, left: 0, bottom: 0, right: 0};
		}
	};

	T.setPrefs = function(key, value) {
		if (!T.isParameters(value)) {
			T.removePrefs(key);
			return;
		}
		try {
			api.setPrefs({sync: true, key: key, value: value});
		} catch (e) {}
	};

	T.getPrefs = function(key) {
		try {
			return api.getPrefs({sync: true, key: key});
		} catch (e) {
			return "";
		}
	};

	T.removePrefs = function(key) {
		try {
			api.removePrefs({sync: true, key: key});
		} catch (e) {}
	};

	T.addEventListener = function(name, callback) {
		var keyback = function keyback(ret, err) {
			T.isFunction(callback) && callback(ret, err);
		};
		if (
			T.platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			if (!window.baseEventList) {
				window.baseEventList = [];
			}
			if (G.getItemForKey(name, window.baseEventList)) {
				G.delItemForKey(name, window.baseEventList);
			}
			window.baseEventList.push({
				key: name,
				value: keyback
			});
		} else {
			api.addEventListener({name: name}, keyback);
		}
	};

	T.removeEventListener = function(name) {
		if (
			T.platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			G.delItemForKey(name, window.baseEventList);
		} else {
			api.removeEventListener({name: name});
		}
	};

	T.sendEvent = function(name, extra) {
		if (
			T.platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			var pageframes = window.parent.document.getElementsByTagName("iframe");
			for (var i = 0; i < pageframes.length; i++) {
				if (T.isArray(pageframes[i].contentWindow.baseEventList)) {
					var sendItem = G.getItemForKey(
						T.isObject(name) ? name.name : name,
						pageframes[i].contentWindow.baseEventList
					);
					if (sendItem) {
						sendItem.value({value: T.isObject(name) ? name.extra : extra});
					}
				}
			}
		} else {
			try {
				api.sendEvent(T.isObject(name) ? name : {name: name, extra: extra});
			} catch (e) {}
		}
	};

	T.removeLaunchView = function() {
		try {
			api.removeLaunchView();
		} catch (e) {}
	};

	T.setScreenOrientation = function(orientation) {
		try {
			api.setScreenOrientation({orientation: orientation});
		} catch (e) {}
	};

	T.cancelAjax = function(name) {
		try {
			api.cancelAjax({tag: name});
		} catch (e) {}
	};

	T.ajax = function(url, tag, callback, logText, method, data, header) {
		if (header === void 0) {
			header = {};
		}
		T.cancelAjax(tag);
		var getUrl = url; //请求链接
		var frequency = 0; //网络异常 重复请求次数
		var dataType = "json"; //返回数据类型
		var cacheType = ""; //请求类型
		var paramData = {};
		var areaId = "";
		var timeout = 0;
		var isWeb = "";
		if (T.isObject(url)) {
			getUrl = url.u; //请求链接
			dataType = url.dt || "json"; //
			cacheType = url.t || ""; //
			frequency = url.frequency || 0;
			paramData = url.paramData || {};
			areaId = url.areaId || myjs.areaId(url._this);
			timeout = url.timeout || 0;
			isWeb = url.web || "";
		}
		var o = {
			url: getUrl,
			tag: tag,
			method: method ? method : "get",
			cache: false,
			timeout: 50,
			dataType: dataType,
			data: T.isObject(data) ? data : {},
			headers: T.setNewJSON(
				{
					"u-login-areaId": areaId,
					Authorization: T.getPrefs("sys_token") || "",
					"content-type": "application/json",
					"u-terminal": "APP"
				},
				header
			)
		};

		o = T.setNewJSON(o, paramData);
		if (T.platform() == "web") {
			delete o.tag;
		}
		if (o.url.indexOf("push/rongCloud") != -1) {
			//融云接口
			if (o.method == "get") {
				o.url +=
					(o.url.indexOf("?") != -1 ? "&" : "?") +
					"environment=" +
					myjs.chatEnvironment();
			} else if (o.data.values) {
				o.data.values.environment = myjs.chatEnvironment();
			}
		}
		if (
			isWeb &&
			JSON.stringify(o.data) != "{}" &&
			(o.data.body || JSON.stringify(o.data.values) != "{}")
		) {
			//公众端通用 过期时间不传token且置空public_token
			var webToken = "";
			if (
				T.getPrefs("tokenEndTime") &&
				new Date().getTime() < T.getPrefs("tokenEndTime")
			) {
				webToken = T.getPrefs("public_token") || "";
			} else {
				T.removePrefs("public_token");
			}
			(o.headers.Authorization = header.Authorization || webToken || ""),
				(o.headers["u-terminal"] = "PUBLIC");
			// var isBody = o.data.body?true:false;
			// var postParam = isBody?JSON.parse(o.data.body):o.data.values;
			// var signParam = {};
			// function getParam(_obj){
			// 	// console.log(JSON.stringify(_obj));
			// 	for (var key in _obj) {
			// 		var kValue = _obj[key];
			// 		if(T.isObject(kValue) && !T.isArray(kValue)){
			// 			getParam(kValue);
			// 		}else{
			// 			kValue = T.isArray(kValue)?kValue.join(","):kValue;
			// 			if (signParam.hasOwnProperty(key)) {
			// 				signParam[key] += (signParam[key]?',':'') + kValue;
			// 			}else{
			// 				signParam[key] = kValue;
			// 			}
			// 		}
			// 	}
			// }
			// getParam(postParam);
			// var signStr = T.sort_ascii(signParam,"#");
			// postParam.clientId = M.clientId;
			// postParam.token = isWeb;
			// postParam.timestamp = dayjs().valueOf();
			// postParam.nonce = "zyrd";
			// postParam.sign = MD5(signStr + "#" + M.clientId + isWeb + postParam.timestamp + postParam.nonce).toString().toUpperCase()
			// if(isBody){
			// 	o.data.body = JSON.stringify(postParam);
			// }
		}
		var oldContentType = o.headers["content-type"];
		if (myjs.proxy && T.platform() != "app" && o.url.indexOf("https") != 0) {
			//小程序 使用代理 T.platform() == "mp" &&
			var oldUrl = o.url;
			var proxyUrl = myjs.tomcatAddress() + "utils/proxy";
			if (oldUrl.indexOf("?") != -1) {
				o.url = proxyUrl + oldUrl.substring(oldUrl.indexOf("?"));
				oldUrl = oldUrl.substring(0, oldUrl.indexOf("?"));
			} else {
				o.url = proxyUrl;
			}
			o.url +=
				(o.url.indexOf("?") != -1
					? o.url.substring(o.url.indexOf("?")) == "?"
						? ""
						: "&"
					: "?") +
				"BASE_URL=" +
				oldUrl;
			o.url += "&BASE_TYPE=" + oldContentType;
			o.headers["content-type"] = "application/x-www-form-urlencoded";
		}
		if (oldContentType == "file") {
			delete o.headers["content-type"];
		}
		if (T.platform() == "app" && logText) {
			if (o.method == "post") {
				console.log(logText + "post【" + frequency + "】：" + JSON.stringify(o));
			} else {
				console.log(logText + "get【" + frequency + "】：" + JSON.stringify(o));
			}
		}
		try {
			var cbFun = function cbFun(ret, err) {
				// if(T.isObject(url) && T.isParameters(url._this.ajax)){
				// 	url._this.ajax = true;
				// }
				if (T.isFunction(callback)) {
					if (err) {
						try {
							ret = JSON.parse(err.msg);
							err = null;
						} catch (e) {
							ret = JSON.parse(JSON.stringify(err));
							err = null;
						}
					}
					if (err) {
						// if (frequency > 0) {
						// 	var frequencyUrl = url;
						// 	frequencyUrl.frequency--;
						// 	T.ajax(frequencyUrl, tag, callback, logText, method, data, header);
						// 	return;
						// }
					}
					if (T.platform() == "app" && logText) {
						if (ret)
							console.log("得到" + logText + "返回结果ret：" + JSON.stringify(ret));
						if (err)
							console.log("得到" + logText + "返回结果err：" + JSON.stringify(err));
					}
					if (ret) {
						ret.message = ret.message || ret.msg || "";
						var errcode = ret.code || "";
						if ((errcode == 302 || errcode == 2) && cacheType != "login") {
							//令牌失效
							T.hideProgress();
							T.sendEvent({
								name: "index",
								extra: {type: "verificationToken", errmsg: ret.message}
							});
							// return;
						}
					}
					callback(ret, err, true);
				}
			};
			setTimeout(function() {
				if (T.platform() == "web") {
					var xhr = new XMLHttpRequest();
					xhr.open(o.method, o.url);
					for (var header in o.headers) {
						xhr.setRequestHeader(header, o.headers[header]);
					}
					var sendValue = "";
					if (oldContentType.indexOf("x-www-form-urlencoded") != -1) {
						var dValue = o.data.values || {};
						var sendBody = o.data.body || "";
						if (sendBody) {
							dValue = JSON.parse(sendBody);
						}
						for (var vItem in dValue) {
							sendValue +=
								(!sendValue ? "" : "&") +
								vItem +
								"=" +
								encodeURIComponent(dValue[vItem]);
						}
					} else if (oldContentType.indexOf("file") != -1) {
						sendValue = new FormData();
						var dValue = o.data.values || {};
						var fileValue = o.data.files || {};
						var sendBody = o.data.body || "";
						if (sendBody) {
							dValue = JSON.parse(sendBody);
						}
						for (var vItem in dValue) {
							sendValue.append(vItem, encodeURIComponent(dValue[vItem]));
						}
						for (var vItem in fileValue) {
							sendValue.append(vItem, fileValue[vItem]);
						}
					} else {
						sendValue = o.data.body || JSON.stringify(o.data.values); //encodeURIComponent web加了之后 不能传递json了
					}
					xhr.send(sendValue);
					xhr.onreadystatechange = function() {
						if (xhr.readyState === XMLHttpRequest.DONE) {
							if (xhr.responseText) {
								var response = this.responseText;
								if (o.dataType == "json") {
									var isJSON = false;
									try {
										response = JSON.parse(response);
										isJSON = true;
									} catch (e) {
										isJSON = false;
									}
									if (isJSON) {
										cbFun(response, null);
									} else {
										cbFun(null, response);
									}
								} else {
									cbFun(response, null);
								}
							} else {
								cbFun(null, {});
							}
						}
					};
				} else {
					api.ajax(o, cbFun);
				}
			}, timeout);
		} catch (e) {
			console.log(e);
		}
	};

	T.openWin = function(name, url, pageParam, _this, allowEdit, _more) {
		var delay = 0;
		url = T.handleSYSLink(url, _this); //先处理跳转链接
		var o = {
			name: name,
			url: T.platform() == "web" ? url.substring(url.lastIndexOf("/") + 1) : url,
			pageParam: pageParam ? pageParam : {},
			bounces: false,
			bgColor: "#FFF",
			slidBackEnabled: false, //ios滑动返回
			vScrollBarEnabled: true,
			hScrollBarEnabled: true,
			scaleEnabled: true,
			animation: {
				type: "push",
				subType: "from_right",
				duration: 300
			},

			reload: true, // 去除设置
			allowEdit: true, //去除设置 默认都可以复制粘贴
			delay: delay,
			overScrollMode: "scrolls",
			defaultRefreshHeader: "swipe"
		};

		if (T.isObject(_more)) {
			o = T.setNewJSON(o, _more);
		}
		o.pageParam.headTheme =
			(_this && _this.data && _this.data.headTheme
				? _this.data.headTheme || ""
				: "") ||
			G.headTheme ||
			"";
		o.pageParam.appTheme = G.appTheme || "";
		o.pageParam.areaId = o.pageParam.areaId || myjs.areaId(_this);
		o.pageParam.v = G.v;
		if (o.pageParam.paramSaveKey) {
			T.setPrefs(o.pageParam.paramSaveKey, JSON.stringify(o.pageParam));
			o.pageParam = {paramSaveKey: o.pageParam.paramSaveKey};
		}
		api.openWin(o);
	};

	T.closeWin = function(_param) {
		var o = {};
		if (T.isObject(_param)) {
			o = T.setNewJSON(o, _param);
		} else {
			o.name = _param;
		}
		try {
			if (api.pageParam.paramSaveKey) {
				T.removePrefs(api.pageParam.paramSaveKey);
			}
			api.closeWin(o);
		} catch (e) {}
	};

	T.clearCache = function(callback) {
		var o = {};
		try {
			api.clearCache(o, function(ret, err) {
				T.isFunction(callback) && callback(ret, err);
			});
		} catch (e) {}
	};

	T.toast = function(_param, location, global) {
		var o = {
			msg: "",
			duration: 2000,
			location: "middle",
			global: false
		};

		if (T.isObject(_param)) {
			o = T.setNewJSON(o, _param);
		} else {
			o.msg = T.isParameters(_param) ? _param : "";
			o.location = location || "middle";
			o.global = global;
		}
		o.msg = o.msg.toString();
		try {
			api.toast(o);
		} catch (e) {}
	};

	T.showProgress = function(_param, modal) {
		var o = {
			style: "default",
			animationType: "fade",
			title: "加载中",
			text: "请稍候...",
			modal: true //是否模态，模态时整个页面将不可交互
		};
		if (T.isObject(_param)) {
			o = T.setNewJSON(o, _param);
		} else {
			o.title = T.isParameters(_param) ? _param : "";
			o.modal = !modal; //是否可以交互 反过来了
		}
		o.title = o.title.toString();
		try {
			api.showProgress(o);
		} catch (e) {}
	};

	T.hideProgress = function() {
		try {
			api.hideProgress();
		} catch (e) {}
	};

	T.alert = function(_param, callback) {
		G.alert(_param, callback);
		// var o = {
		// 	title: '提示',
		// 	msg: "",
		// 	buttons: ["确定"]
		// };
		// if (T.isObject(_param)) {
		// 	o = T.setNewJSON(o, _param);
		// } else {
		// 	o.msg = T.isParameters(_param)?_param:"";
		// }
		// o.msg = o.msg.toString();
		// try{
		// 	api.alert(o, (ret, err)=> {
		// 		T.isFunction(callback) && callback(ret, err);
		// 	});
		// }catch(e){}
	};

	T.confirm = function(_param, callback) {
		G.alert(_param, callback);
		// var o = {
		// 	title: '提示',
		// 	msg: "",
		// 	buttons: ['确定', '取消']
		// };
		// if (T.isObject(_param)) {
		// 	o = T.setNewJSON(o, _param);
		// } else {
		// 	o.msg = _param;
		// }
		// o = T.setNewJSON(o, _param);
		// try{
		// 	api.confirm(o, (ret, err)=> {
		// 		T.isFunction(callback) && callback(ret, err);
		// 	});
		// }catch(e){}
	};

	T.actionSheet = function(_param, _callback) {
		G.actionSheet(_param, _callback);
		// var o = {
		// 	title: '请选择',
		// 	cancelTitle: '取消',
		// 	destructiveTitle: "",
		// };
		// o = T.setNewJSON(o, _param);
		// try{
		// 	api.actionSheet(o, (ret, err)=> {
		// 		T.isFunction(callback) && callback(ret, err);
		// 	});
		// }catch(e){}
	};

	T.getPicture = function(_param, callback) {
		if (!callback) {
			T.toast("请先设置callback");
			return;
		}
		var o = {
			sourceType: "camera",
			encodingType: "jpg",
			mediaValue: "pic",
			targetWidth: 720,
			count: 1,
			max: 0
		};

		o = T.setNewJSON(o, _param);
		try {
			if (T.platform() == "web") {
				var h5Input = document.createElement("input");
				h5Input.style = "display: none;";
				h5Input.type = "file";
				h5Input.accept = "image/*";
				var ua = navigator.userAgent.toLowerCase();
				var version = "";
				if (ua.indexOf("android") > 0) {
					var reg = /android [\d._]+/gi;
					var v_info = ua.match(reg);
					version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, ".");
					version = parseInt(version.split(".")[0]);
				}
				if (!version || Number(version) <= 13) {
					h5Input.multiple = "multiple";
				}
				h5Input.onchange = function() {
					var listLength =
						o.max != 0 && h5Input.files.length > o.max ? o.max : h5Input.files.length;
					for (var i = 0; i < listLength; i++) {
						callback({data: h5Input.files[i]}, null);
					}
				};
				//ios拍照需要加到真实dom能才进onchange
				document.body.appendChild(h5Input);
				h5Input.click();
			} else if (T.platform() == "mp") {
				wx.chooseImage({
					count: o.max != 0 ? o.max : 9,
					sourceType: [o.sourceType == "camera" ? "camera" : "album"],
					success: function success(res) {
						for (var i = 0; i < res.tempFiles.length; i++) {
							callback({data: res.tempFiles[i]}, null);
						}
					}
				});
			} else if (T.platform() == "app") {
				var preName = o.sourceType == "camera" ? "camera" : "photos";
				if (
					!T.confirmPer(
						preName,
						"getPicture",
						o.reason || "用于上传并使用图片的功能，若取消将无法使用图片功能"
					)
				) {
					//相机相册权限
					T.addEventListener(preName + "Per_" + "getPicture", function(ret, err) {
						T.removeEventListener(preName + "Per_" + "getPicture");
						if (ret.value.granted) {
							T.getPicture(_param, callback);
						}
					});
					return;
				}
				if (o.sourceType == "camera" || o.userOne) {
					api.getPicture(o, function(ret, err) {
						T.isFunction(callback) && callback(ret, err);
					});
				} else {
					api.require("WXPhotoPicker").open(
						{
							max: o.max != 0 ? o.max : 9,
							styles: {
								mark: {checked: G.appTheme},
								bottomTabBar: {sendText: "确定", sendBgColor: G.appTheme}
							},
							type: "image"
						},
						function(ret) {
							var eventType = ret ? ret.eventType : "cancel";
							if (eventType == "cancel") return;
							if (eventType == "confirm" && ret.list.length != 0) {
								for (var i = 0; i < ret.list.length; i++) {
									callback({data: ret.list[i].path}, null);
								}
							}
						}
					);
				}
			}
		} catch (e) {}
	};

	T.hasPermission = function(one_per) {
		if (T.platform() == "app") {
			if (!one_per) return;
			var rets = api.hasPermission({list: one_per.split(",")});
			if (one_per.indexOf(",") != -1) {
				//判断一堆时 就自己看	一般是一个一个判断
				T.alert("判断结果：" + JSON.stringify(rets));
				return;
			} else {
				return rets;
			}
		}
	};

	T.requestPermission = function(one_per, callback, _fName) {
		if (T.platform() == "app") {
			if (!one_per) return;
			api.requestPermission({list: one_per.split(",")}, function(ret) {
				console.log(JSON.stringify(ret));
				//把结果 发监听过去
				ret.list.forEach(function(_eItem, _eIndex, _eArr) {
					T.sendEvent(_eItem.name + "Per_" + _fName, {granted: _eItem.granted});
				});
				T.isFunction(callback) && callback(ret, err);
			});
		}
	};

	T.confirmPer = function(perm, _fName, _reason) {
		if (T.platform() == "app") {
			var has = T.hasPermission(perm);
			if (!has || !has[0] || !has[0].granted) {
				var hintWord = {
					camera: "相机",
					storage: "存储",
					photos: "照片",
					microphone: "麦克风",
					location: "位置",
					phone: "电话",
					"phone-r": "通话状态",
					"phone-r-log": "通话记录"
				};

				var iosHint =
					"请在" +
					(api.uiMode == "phone" ? "iPhone" : "iPad") +
					"的“设置-隐私-" +
					hintWord[perm] +
					"”中请允许访问" +
					hintWord[perm] +
					(_reason ? "，" + _reason : "。");
				var androidHint =
					"使用该功能需要" +
					hintWord[perm] +
					"权限，" +
					(_reason || "请前往系统设置开启权限。");
				T.confirm(
					{
						title: "无法使用" + hintWord[perm],
						msg: T.systemType() == "ios" ? iosHint : androidHint,
						buttons: ["下一步", "取消"]
					},
					function(ret, err) {
						if (1 == ret.buttonIndex) {
							T.requestPermission(perm, null, _fName);
						} else {
							T.sendEvent(perm + "Per_" + _fName, {granted: false});
						}
					}
				);
				return false;
			}
			return true;
		}
		return true;
	};

	//处理app链接 可以不带app会拼接上 或者需要带上特定参数
	T.handleSYSLink = function(_link, _this, myParam) {
		if (_link === void 0) {
			_link = "";
		}
		// if(_link.indexOf("http") != 0){
		// 	return _link;
		// }
		myParam = myParam || {};
		//index.html?token={{token}}&userId={{userId}}
		_link = _link.replace("{{tomcatAddress}}", myjs.tomcatAddress());
		_link = _link.replace("{{shareAddress}}", myjs.shareAddress());
		_link = _link.replace(
			"{{token}}",
			encodeURIComponent(T.getPrefs("sys_token"))
		); //当前app登录用户的token，例如：bearer eyJhbGciOiJ...
		_link = _link.replace("{{sysUrl}}", myjs.appUrl()); //当前app请求系统地址，例如：http://**************:54386/lzt/
		_link = _link.replace("{{areaId}}", myParam.areaId || myjs.areaId(_this)); //当前跳转页面的地区id，例如：430000
		_link = _link.replace("{{userId}}", G.userId); //当前用户id，例如：1
		_link = _link.replace("{{iszx}}", myjs.iszx); //当前系统类型，例如：true (true为政协，flase为人大)
		_link = _link.replace("{{appTheme}}", G.appTheme); //当前app主题颜色，例如：#3657C0
		_link = _link.replace("{{careMode}}", G.careMode); //当前是否为关怀模式：例如：true (关怀模式下字体大4px)
		if (_link.indexOf("?ndata=") != -1) {
			//是app内页面 带上特有参数如果没有
			if (_link.indexOf("sysUrl-zy-") == -1) {
				//没有带地址
				_link += "-zyz-sysUrl-zy-" + myjs.appUrl();
			}
			if (_link.indexOf("sysAreaId-zy-") == -1) {
				//没有带地区
				_link += "-zyz-sysAreaId-zy-" + (myParam.areaId || myjs.areaId(_this));
			}
			if (_link.indexOf("iszx-zy-") == -1) {
				//没有带人大政协判断
				_link += "-zyz-iszx-zy-" + myjs.iszx;
			}
			if (_link.indexOf("appTheme-zy-") == -1) {
				//没有带主题
				_link += "-zyz-appTheme-zy-" + G.appTheme;
			}
			if (_link.indexOf("careMode-zy-") == -1) {
				//没有唯一标识
				_link += "-zyz-careMode-zy-" + G.careMode;
			}
		}
		return _link;
	};

	//按照ascii 排序
	T.sort_ascii = function(obj, _default) {
		var arr = [];
		var num = 0;
		for (var i in obj) {
			arr[num] = i;
			num++;
		}
		var sortArr = arr.sort();
		var str = "";
		for (var _i = 0; _i < sortArr.length; _i++) {
			var sValue = obj[sortArr[_i]];
			str +=
				sortArr[_i] +
				"=" +
				(T.isTargetType(sValue, "number")
					? sValue
					: T.isObject(sValue)
					? JSON.stringify(sValue)
					: sValue || _default) +
				"&";
		}
		var char = "&";
		str = str.replace(new RegExp("^\\" + char + "+|\\" + char + "+$", "g"), "");
		return str;
	};

	/** 判断颜色属于深色还是浅色*/
	T.isColorDarkOrLight = function(hexcolor) {
		try {
			var colorrgb = T.colorRgb(hexcolor);
			var colors = colorrgb.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
			var red = colors[1];
			var green = colors[2];
			var blue = colors[3];
			var brightness;
			brightness = red * 299 + green * 587 + blue * 114;
			brightness = brightness / 255000;
			if (brightness >= 0.5) {
				return "light";
			} else {
				return "dark";
			}
		} catch (e) {
			return "";
		}
	};

	//16进制颜色转化为RGB颜色
	T.colorRgb = function(str) {
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		var sColor = str.toLowerCase();
		if (sColor && reg.test(sColor)) {
			if (sColor.length === 4) {
				var sColorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1));
				}
				sColor = sColorNew;
			}
			var sColorChange = [];
			for (var i = 1; i < 7; i += 2) {
				sColorChange.push(parseInt("0x" + sColor.slice(i, i + 2)));
			}
			return "rgb(" + sColorChange.join(",") + ")";
		} else {
			return sColor;
		}
	};

	/** 16进制颜色 转换成rgba颜色	可设置透明 */
	T.colorRgba = function(_color, _alpha) {
		if (!_color) return;
		// 16进制颜色值的正则
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		// 把颜色值变成小写
		var color = _color.toLowerCase();
		if (reg.test(color)) {
			if (color.length === 4) {
				var colorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
				}
				color = colorNew;
			}
			var colorChange = [];
			for (var i = 1; i < 7; i += 2) {
				colorChange.push(parseInt("0x" + color.slice(i, i + 2)));
			}
			return (
				"rgba(" +
				colorChange.join(",") +
				"," +
				(T.isParameters(_alpha) ? _alpha : "1") +
				")"
			);
		} else {
			return color;
		}
	};

	var myjs = {};

	//修改时 需同步修改_zy/myjs.js
	myjs.iszx = false;

	myjs.proxy = false;

	myjs.appName = (myjs.iszx ? "政协" : "人大") + "平台版";

	myjs.appUrl = function() {
		// if(T.platform() == "web"){//适配正式测试 不用重复切换
		// 	switch(location.hostname){
		// 		case "*************":
		// 			return "http://*************:810/lzt/";
		// 		case "***************":
		// 			return "http://***************:54386/lzt/";
		// 	}
		// }
		return (
			T.getPrefs("sys_appUrl") ||
			(myjs.iszx
				? "https://productpc.cszysoft.com:20170/lzt/"
				: "https://productpc.cszysoft.com:20169/lzt/")
		);
	};

	myjs.chatHeader = function() {
		return (
			T.getPrefs("sys_chatHeader") || "platform5" + (myjs.iszx ? "zx" : "rd")
		);
	};

	myjs.chatEnvironment = function() {
		return T.getPrefs("sys_chatEnvironment") || "1";
	};

	myjs.tomcatAddress = function() {
		return (
			T.getPrefs("sys_tomcatAddress") ||
			(T.platform() == "web"
				? window.location.protocol == "https:"
					? "https://cszysoft.com:9091/"
					: "http://cszysoft.com:9090/"
				: "https://cszysoft.com:9091/")
		); //http://**********:8080/ http://cszysoft.com:9090/ https://cszysoft.com:9091/
	};

	myjs.shareAddress = function(_type) {
		if (_type == 1 && T.platform() != "mp") {
			return "../../";
		}
		return (
			T.getPrefs("sys_shareAddress") ||
			(T.platform() == "web"
				? window.location.protocol.indexOf("http") == 0
					? window.location.protocol
					: "http:"
				: "https:") +
				"//cszysoft.com/appShare/" +
				(myjs.iszx ? "platform5zx/" : "platform5rd/")
		);
	};

	//默认当前页面 area下的地区 无则传参地区
	(myjs.areaId = function(_this) {
		return (
			(_this && _this.data && _this.data.area ? _this.data.area.key || "" : "") ||
			T.pageParam(_this).areaId ||
			T.getPrefs("sys_aresId") ||
			T.getPrefs("sys_platform") ||
			""
		);
	}),
		//系统类型：（平台版：platform）（标准版：standard）
		(myjs.systemType = function(_this) {
			return (
				T.pageParam(_this).platform || T.getPrefs("sys_systemType") || "platform"
			);
		});

	myjs.clientId = "zyrdV5TestAccount";

	myjs.clientSecret = "zyrdV5TestPassword";

	myjs.hw_project_id = "0611d8333100251b2fc1c01937b8e6d9";

	myjs.hw_bucket = "zy-soft";

	myjs.hw_header = "ZY";

	var ZTextarea = /*@__PURE__*/ (function(Component) {
		function ZTextarea(props) {
			Component.call(this, props);
			this.data = {
				textareaId: this.props.id || "z_textarea" + getNum()
			};
		}

		if (Component) ZTextarea.__proto__ = Component;
		ZTextarea.prototype = Object.create(Component && Component.prototype);
		ZTextarea.prototype.constructor = ZTextarea;
		ZTextarea.prototype.installed = function() {
			var this$1 = this;

			this.props.dataMore.textareaId = this.data.textareaId;
			if (this.props.dataMore.autoFocus) {
				setTimeout(function() {
					document.getElementById(this$1.data.textareaId).focus();
				}, 500);
			}
		};
		ZTextarea.prototype.input = function(e) {
			var value = e.detail.value;
			if (this.props.dataMore.expression) {
				value = value.replace(new RegExp(this.props.dataMore.expression, "g"), "");
			}
			this.props.dataMore.value = value;
			this.fire("input", e.detail);
		};
		ZTextarea.prototype.blur = function(e) {
			this.fire("blur", e.detail);
		};
		ZTextarea.prototype.focus = function(e) {
			document.getElementById(this.data.textareaId).focus();
			this.fire("focus", e.detail);
		};
		ZTextarea.prototype.render = function() {
			return apivm.h("textarea", {
				id: this.data.textareaId,
				style:
					"" +
					loadConfiguration() +
					(api.systemType == "android" ? "min-" : "") +
					"height: " +
					(this.props.dataMore.height || "250") +
					"px;" +
					(this.props.style || ""),
				class: "z_textarea " + (this.props.class || ""),
				placeholder:
					this.props.dataMore.replyPlaceholder ||
					this.props.dataMore.placeholder ||
					this.props.dataMore.hint ||
					"请输入" + (this.props.dataMore.title || ""),
				"placeholder-style": "color:#ccc;",
				"auto-height": api.systemType == "android",
				value: this.props.dataMore.value,
				onInput: this.input,
				onBlur: this.blur,
				onFocus: this.focus,
				maxlength: this.props.dataMore.maxlength || this.props.dataMore.max
			});
		};

		return ZTextarea;
	})(Component);
	ZTextarea.css = {
		".z_textarea": {
			background: "transparent",
			borderColor: "transparent",
			color: "#333",
			padding: "0",
			width: "100%",
			fontFamily: "none"
		},
		".z_textarea::placeholder": {color: "#ccc"}
	};
	apivm.define("z-textarea", ZTextarea);

	var ZButton = /*@__PURE__*/ (function(Component) {
		function ZButton(props) {
			Component.call(this, props);
		}

		if (Component) ZButton.__proto__ = Component;
		ZButton.prototype = Object.create(Component && Component.prototype);
		ZButton.prototype.constructor = ZButton;
		ZButton.prototype.componentsClick = function(e) {
			stopBubble(e);
			if (!this.props.disabled && !this.props.readonly) {
				this.fire("click", {});
			}
		};
		ZButton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					id: "" + (this.props.id || ""),
					class: "z_button " + (this.props.class || ""),
					style:
						"border-radius:" +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;border-color:" +
						(this.props.color || G$1.appTheme) +
						";background:" +
						(this.props.plain ? "#FFF" : this.props.color || G$1.appTheme) +
						";opacity:" +
						(this.props.disabled ? "0.5" : "1") +
						";" +
						(this.props.style || ""),
					onClick: function(e) {
						return this$1.componentsClick(e);
					}
				},
				this.props.icon &&
					apivm.h("a-iconfont", {
						name: this.props.icon,
						style: "margin-right:5px;",
						color: this.props.plain ? this.props.color || G$1.appTheme : "#FFF",
						size: G$1.appFontSize - 1 + (this.props.size || 0)
					}),
				apivm.h(
					"text",
					{
						style:
							"color:" +
							(this.props.plain ? this.props.color || G$1.appTheme : "#FFF") +
							";" +
							loadConfiguration(this.props.size || 0)
					},
					this.props.text || this.props.children
				)
			);
		};

		return ZButton;
	})(Component);
	ZButton.css = {
		".z_button": {
			padding: "7px 12px",
			borderWidth: "1px",
			borderStyle: "solid",
			boxSizing: "border-box",
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("z-button", ZButton);

	var ZDivider = /*@__PURE__*/ (function(Component) {
		function ZDivider(props) {
			Component.call(this, props);
		}

		if (Component) ZDivider.__proto__ = Component;
		ZDivider.prototype = Object.create(Component && Component.prototype);
		ZDivider.prototype.constructor = ZDivider;
		ZDivider.prototype.render = function() {
			return apivm.h(
				"view",
				{style: "width:100%;height:1px;padding:0 16px;" + this.props.style || null},
				apivm.h("view", {
					style:
						"border-bottom:1px solid " +
						(this.props.color || "rgba(0,0,0,0.03)") +
						";"
				})
			);
		};

		return ZDivider;
	})(Component);
	apivm.define("z-divider", ZDivider);

	var IdentifyAudio = /*@__PURE__*/ (function(Component) {
		function IdentifyAudio(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				isStart: false,
				volume: 0,
				textarea: {
					value: ""
				}
			};
			this.compute = {
				monitor: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.baseInit();
						} else {
							if (this.data.isStart && this.voiceRecognizer) {
								this.voiceRecognizer.recognizeCancel();
							}
						}
					}
				}
			};
		}

		if (Component) IdentifyAudio.__proto__ = Component;
		IdentifyAudio.prototype = Object.create(Component && Component.prototype);
		IdentifyAudio.prototype.constructor = IdentifyAudio;
		IdentifyAudio.prototype.baseInit = function() {
			var this$1 = this;

			var dm = this.props.dataMore;
			this.data.isStart = false;
			G$1.appTheme = myjs.iszx ? "#3088FE" : "#C61414";
			G$1.appFontSize = 16;
			this.data.textarea.value = "";
			if (dm.type == 1) {
				this.voiceRecognizer = api.require("voiceRecognizer");
				this.voiceRecognizer.createUtility(
					{ios_appid: "5b581b4e", android_appid: "5b581b4e"},
					function(ret, err) {}
				);
				this.voiceRecognizer.recognizeConfig(
					{
						config: {
							vadbos: "5000",
							vadeos: "5000",
							timeout: "30000",
							netTimeout: "20000",
							rate: "16000",
							dot: true
						}
					},

					function(ret) {}
				);
				this.voiceRecognizer.addEventListener(
					{name: "recognizeResult", realTime: true},
					function(ret) {
						ret = ret.recognizeResult;
						this$1.data.textarea.value += ret.result;
					}
				);
				this.voiceRecognizer.addEventListener({name: "onError"}, function(ret) {
					console.log("onError" + JSON.stringify(ret));
				});
				this.voiceRecognizer.addEventListener({name: "onEndOfSpeech"}, function(
					ret
				) {
					if (this$1.data.isStart) {
						//还在识别 说明是超时了 但是还按着
						this$1.data.textarea.value += "。";
						this$1.voiceRecognizer.recognizeStart();
					}
				});
				this.voiceRecognizer.addEventListener({name: "volume"}, function(ret) {
					this$1.data.volume = ret.volume;
				});
			}
		};
		IdentifyAudio.prototype.textwarp = function() {
			$("#" + this.data.textarea.textareaId).focus();
		};
		IdentifyAudio.prototype.penetrate = function() {};
		IdentifyAudio.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		IdentifyAudio.prototype.touchstart = function() {
			var this$1 = this;

			var dm = this.props.dataMore;
			if (!confirmPer("microphone")) {
				//麦克风权限
				return;
			}
			this.data.isStart = true;
			this.realStart = false;
			clearTimeout(this.task);
			this.task = setTimeout(function() {
				this$1.realStart = true;
				if (dm.type == 1) {
					this$1.voiceRecognizer.recognizeStart();
				}
			}, 150);
		};
		IdentifyAudio.prototype.touchend = function() {
			var dm = this.props.dataMore;
			this.data.isStart = false;
			clearTimeout(this.task);
			if (this.realStart) {
				if (dm.type == 1) {
					this.voiceRecognizer.recognizeStop();
				}
			}
			this.realStart = false;
		};
		IdentifyAudio.prototype.confirmBtn = function() {
			if (this.data.isStart) {
				return;
			}
			this.props.dataMore.callback(this.data.textarea.value);
			this.closePage();
		};
		IdentifyAudio.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				apivm.h(
					"view",
					{
						onClick: function() {
							return this$1.closePage();
						},
						style: "height:20%;flex-shrink: 0;"
					},
					apivm.h("view", {class: "flex_h"}),
					apivm.h(
						"view",
						{class: "xy_center"},
						this.data.isStart &&
							apivm.h("image", {
								mode: "aspectFit",
								style: loadConfigurationSize([25, 5]) + "margin-bottom:5px;",
								thumbnail: "false",
								src: shareAddress(1) + "image/icon_voice_in.gif"
							})
					)
				),
				this.props.dataMore.show &&
					apivm.h(
						"view",
						{
							class: "flex_h pages_warp",
							onClick: function() {
								return this$1.penetrate();
							}
						},
						apivm.h("view", {class: "watermark_box"}, G$1.watermark),
						apivm.h(
							"view",
							{class: "header_warp flex_row"},
							apivm.h(
								"view",
								{class: "header_main xy_center"},
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(1) + "font-weight: 600;color:" + G$1.headColor
									},
									this.props.dataMore.title || "语音输入"
								)
							),
							apivm.h(
								"view",
								{class: "header_left_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{style: loadConfiguration(1) + "color:#666;margin:0 4px;"},
										"关闭"
									)
								)
							),
							apivm.h(
								"view",
								{class: "header_right_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.confirmBtn();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{
											style:
												loadConfiguration(1) +
												"color:" +
												(this.data.isStart ? "#B2B2B2" : G$1.appTheme) +
												";margin:0 4px;"
										},
										"确定"
									)
								)
							)
						),
						apivm.h("z-divider", null),
						apivm.h(
							"view",
							{class: "flex_h"},
							apivm.h(
								"view",
								{class: "flex_h"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.textwarp();
										},
										class: "commentBig_box"
									},
									apivm.h("z-textarea", {dataMore: this.data.textarea})
								)
							),
							apivm.h(
								"view",
								null,
								this.data.isStart &&
									this.data.volume > 0 &&
									apivm.h("view", {
										style:
											"width:" +
											(this.data.volume * 100) / 30 +
											"%;height:3px;background:" +
											G$1.appTheme +
											";"
									})
							),
							apivm.h(
								"view",
								{style: "padding:30px;", class: "xy_center"},
								apivm.h(
									"view",
									{
										onTouchStart: this.touchstart,
										onTouchEnd: this.touchend,
										class: "ida_btn xy_center"
									},
									apivm.h("a-iconfont", {
										name: "yuyin",
										color: this.data.isStart ? "#B2B2B2" : G$1.appTheme,
										size: G$1.appFontSize + 12
									})
								),
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(-4) +
											"color:#666;text-align: center;margin:15px 0;"
									},
									"长按识别录音，松手后请确定"
								)
							)
						),
						apivm.h("view", {style: "padding-bottom:" + safeArea().bottom + "px;"})
					)
			);
		};

		return IdentifyAudio;
	})(Component);
	IdentifyAudio.css = {
		".header_warp": {height: "44px"},
		".header_main": {
			height: "100%",
			flex: "1",
			flexDirection: "row",
			padding: "0 44px"
		},
		".header_left_box": {
			position: "absolute",
			zIndex: "999",
			left: "0",
			width: "auto",
			height: "44px"
		},
		".header_right_box": {
			position: "absolute",
			zIndex: "999",
			right: "0",
			width: "auto",
			height: "44px"
		},
		".page_box": {
			position: "absolute",
			zIndex: "999",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		},
		".flex_h": {flex: "1", height: "1px"},
		".xy_center": {alignItems: "center", justifyContent: "center"},
		".pages_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		},
		".commentBig_box": {
			minHeight: "110px",
			maxHeight: "250px",
			marginBottom: "10px",
			padding: "10px 16px"
		},
		".ida_btn": {
			width: "100px",
			height: "100px",
			borderTopLeftRadius: "100px",
			borderTopRightRadius: "100px",
			borderBottomRightRadius: "100px",
			borderBottomLeftRadius: "100px",
			background: "#F9F9F9",
			boxShadow: "0px 3px 15px rgba(7, 20, 45, 0.15)"
		},
		".header_btn": {
			width: "auto",
			height: "100%",
			minWidth: "36px",
			padding: "0 12px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		}
	};
	apivm.define("identify-audio", IdentifyAudio);

	var SECONDS_A_MINUTE$1 = 60;
	var SECONDS_A_HOUR$1 = SECONDS_A_MINUTE$1 * 60;
	var SECONDS_A_DAY$1 = SECONDS_A_HOUR$1 * 24;
	var SECONDS_A_WEEK$1 = SECONDS_A_DAY$1 * 7;
	var MILLISECONDS_A_SECOND$1 = 1e3;
	var MILLISECONDS_A_MINUTE$1 = SECONDS_A_MINUTE$1 * MILLISECONDS_A_SECOND$1;
	var MILLISECONDS_A_HOUR$1 = SECONDS_A_HOUR$1 * MILLISECONDS_A_SECOND$1;
	var MILLISECONDS_A_DAY$1 = SECONDS_A_DAY$1 * MILLISECONDS_A_SECOND$1;
	var MILLISECONDS_A_WEEK$1 = SECONDS_A_WEEK$1 * MILLISECONDS_A_SECOND$1; // English locales

	var MS$1 = "millisecond";
	var S$1 = "second";
	var MIN$1 = "minute";
	var H$1 = "hour";
	var D$1 = "day";
	var W$1 = "week";
	var M$1 = "month";
	var Q$1 = "quarter";
	var Y$1 = "year";
	var DATE$1 = "date";
	var FORMAT_DEFAULT$1 = "YYYY-MM-DDTHH:mm:ssZ";
	var INVALID_DATE_STRING$1 = "Invalid Date"; // regex

	var REGEX_PARSE$1 = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/;
	var REGEX_FORMAT$1 = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;

	var en$1 = {
		name: "en",
		weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split(
			"_"
		),
		months: "January_February_March_April_May_June_July_August_September_October_November_December".split(
			"_"
		)
	};

	var padStart$1 = function padStart(string, length, pad) {
		var s = String(string);
		if (!s || s.length >= length) return string;
		return "" + Array(length + 1 - s.length).join(pad) + string;
	};
	var padZoneStr$1 = function padZoneStr(instance) {
		var negMinutes = -instance.utcOffset();
		var minutes = Math.abs(negMinutes);
		var hourOffset = Math.floor(minutes / 60);
		var minuteOffset = minutes % 60;
		return (
			"" +
			(negMinutes <= 0 ? "+" : "-") +
			padStart$1(hourOffset, 2, "0") +
			":" +
			padStart$1(minuteOffset, 2, "0")
		);
	};
	var monthDiff$1 = function monthDiff(a, b) {
		if (a.date() < b.date()) return -monthDiff(b, a);
		var wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month());
		var anchor = a.clone().add(wholeMonthDiff, M$1);
		var c = b - anchor < 0;
		var anchor2 = a.clone().add(wholeMonthDiff + (c ? -1 : 1), M$1);
		return +(
			-(
				wholeMonthDiff +
				(b - anchor) / (c ? anchor - anchor2 : anchor2 - anchor)
			) || 0
		);
	};
	var absFloor$1 = function absFloor(n) {
		return n < 0 ? Math.ceil(n) || 0 : Math.floor(n);
	};
	var prettyUnit$1 = function prettyUnit(u) {
		var special = {
			M: M$1,
			y: Y$1,
			w: W$1,
			d: D$1,
			D: DATE$1,
			h: H$1,
			m: MIN$1,
			s: S$1,
			ms: MS$1,
			Q: Q$1
		};

		return (
			special[u] ||
			String(u || "")
				.toLowerCase()
				.replace(/s$/, "")
		);
	};
	var isUndefined$1 = function isUndefined(s) {
		return s === undefined;
	};
	var U$1 = {
		s: padStart$1,
		z: padZoneStr$1,
		m: monthDiff$1,
		a: absFloor$1,
		p: prettyUnit$1,
		u: isUndefined$1
	};

	var L$1 = "en";
	var Ls$1 = {};
	Ls$1[L$1] = en$1;
	var isDayjs$1 = function isDayjs(d) {
		return d instanceof Dayjs$1;
	};
	var parseLocale$1 = function parseLocale(preset, object, isLocal) {
		var l;
		if (!preset) return L$1;
		if (typeof preset === "string") {
			var presetLower = preset.toLowerCase();
			if (Ls$1[presetLower]) {
				l = presetLower;
			}
			if (object) {
				Ls$1[presetLower] = object;
				l = presetLower;
			}
			var presetSplit = preset.split("-");
			if (!l && presetSplit.length > 1) {
				return parseLocale(presetSplit[0]);
			}
		} else {
			var name = preset.name;
			Ls$1[name] = preset;
			l = name;
		}
		if (!isLocal && l) L$1 = l;
		return l || (!isLocal && L$1);
	};
	var dayjs$1 = function dayjs(date, c) {
		if (isDayjs$1(date)) {
			return date.clone();
		}
		var cfg = typeof c === "object" ? c : {};
		cfg.date = date;
		cfg.args = arguments;
		return new Dayjs$1(cfg);
	};
	var wrapper$1 = function wrapper(date, instance) {
		return dayjs$1(date, {
			locale: instance.$L,
			utc: instance.$u,
			x: instance.$x,
			$offset: instance.$offset
		});
	};
	var Utils$1 = U$1;
	Utils$1.l = parseLocale$1;
	Utils$1.i = isDayjs$1;
	Utils$1.w = wrapper$1;
	var parseDate$1 = function parseDate(cfg) {
		var date = cfg.date,
			utc = cfg.utc;
		if (date === null) return new Date(NaN);
		if (Utils$1.u(date)) return new Date();
		if (date instanceof Date) return new Date(date);
		if (typeof date === "string" && !/Z$/i.test(date)) {
			var d = date.match(REGEX_PARSE$1);
			if (d) {
				var m = d[2] - 1 || 0;
				var ms = (d[7] || "0").substring(0, 3);
				if (utc) {
					return new Date(
						Date.UTC(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms)
					);
				}
				return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);
			}
		}
		return new Date(date);
	};
	var Dayjs$1 = (function() {
		function Dayjs(cfg) {
			this.$L = parseLocale$1(cfg.locale, null, true);
			this.parse(cfg);
		}
		var _proto = Dayjs.prototype;
		_proto.parse = function parse(cfg) {
			this.$d = parseDate$1(cfg);
			this.$x = cfg.x || {};
			this.init();
		};
		_proto.init = function init() {
			var $d = this.$d;
			this.$y = $d.getFullYear();
			this.$M = $d.getMonth();
			this.$D = $d.getDate();
			this.$W = $d.getDay();
			this.$H = $d.getHours();
			this.$m = $d.getMinutes();
			this.$s = $d.getSeconds();
			this.$ms = $d.getMilliseconds();
		};
		_proto.$utils = function $utils() {
			return Utils$1;
		};
		_proto.isValid = function isValid() {
			return !(this.$d.toString() === INVALID_DATE_STRING$1);
		};
		_proto.isSame = function isSame(that, units) {
			var other = dayjs$1(that);
			return this.startOf(units) <= other && other <= this.endOf(units);
		};
		_proto.isAfter = function isAfter(that, units) {
			return dayjs$1(that) < this.startOf(units);
		};
		_proto.isBefore = function isBefore(that, units) {
			return this.endOf(units) < dayjs$1(that);
		};
		_proto.$g = function $g(input, get, set) {
			if (Utils$1.u(input)) return this[get];
			return this.set(set, input);
		};
		_proto.unix = function unix() {
			return Math.floor(this.valueOf() / 1000);
		};
		_proto.valueOf = function valueOf() {
			return this.$d.getTime();
		};
		_proto.startOf = function startOf(units, _startOf) {
			var _this = this;
			var isStartOf = !Utils$1.u(_startOf) ? _startOf : true;
			var unit = Utils$1.p(units);
			var instanceFactory = function instanceFactory(d, m) {
				var ins = Utils$1.w(
					_this.$u ? Date.UTC(_this.$y, m, d) : new Date(_this.$y, m, d),
					_this
				);
				return isStartOf ? ins : ins.endOf(D$1);
			};
			var instanceFactorySet = function instanceFactorySet(method, slice) {
				var argumentStart = [0, 0, 0, 0];
				var argumentEnd = [23, 59, 59, 999];
				return Utils$1.w(
					_this
						.toDate()
						[method].apply(
							_this.toDate("s"),
							(isStartOf ? argumentStart : argumentEnd).slice(slice)
						),
					_this
				);
			};
			var $W = this.$W,
				$M = this.$M,
				$D = this.$D;
			var utcPad = "set" + (this.$u ? "UTC" : "");
			switch (unit) {
				case Y$1:
					return isStartOf ? instanceFactory(1, 0) : instanceFactory(31, 11);
				case M$1:
					return isStartOf ? instanceFactory(1, $M) : instanceFactory(0, $M + 1);
				case W$1: {
					var weekStart = this.$locale().weekStart || 0;
					var gap = ($W < weekStart ? $W + 7 : $W) - weekStart;
					return instanceFactory(isStartOf ? $D - gap : $D + (6 - gap), $M);
				}
				case D$1:
				case DATE$1:
					return instanceFactorySet(utcPad + "Hours", 0);
				case H$1:
					return instanceFactorySet(utcPad + "Minutes", 1);
				case MIN$1:
					return instanceFactorySet(utcPad + "Seconds", 2);
				case S$1:
					return instanceFactorySet(utcPad + "Milliseconds", 3);
				default:
					return this.clone();
			}
		};
		_proto.endOf = function endOf(arg) {
			return this.startOf(arg, false);
		};
		_proto.$set = function $set(units, _int) {
			var _C$D$C$DATE$C$M$C$Y$C;
			var unit = Utils$1.p(units);
			var utcPad = "set" + (this.$u ? "UTC" : "");
			var name = ((_C$D$C$DATE$C$M$C$Y$C = {}),
			(_C$D$C$DATE$C$M$C$Y$C[D$1] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[DATE$1] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[M$1] = utcPad + "Month"),
			(_C$D$C$DATE$C$M$C$Y$C[Y$1] = utcPad + "FullYear"),
			(_C$D$C$DATE$C$M$C$Y$C[H$1] = utcPad + "Hours"),
			(_C$D$C$DATE$C$M$C$Y$C[MIN$1] = utcPad + "Minutes"),
			(_C$D$C$DATE$C$M$C$Y$C[S$1] = utcPad + "Seconds"),
			(_C$D$C$DATE$C$M$C$Y$C[MS$1] = utcPad + "Milliseconds"),
			_C$D$C$DATE$C$M$C$Y$C)[unit];
			var arg = unit === D$1 ? this.$D + (_int - this.$W) : _int;
			if (unit === M$1 || unit === Y$1) {
				var date = this.clone().set(DATE$1, 1);
				date.$d[name](arg);
				date.init();
				this.$d = date.set(DATE$1, Math.min(this.$D, date.daysInMonth())).$d;
			} else if (name) this.$d[name](arg);
			this.init();
			return this;
		};
		_proto.set = function set(string, _int2) {
			return this.clone().$set(string, _int2);
		};
		_proto.get = function get(unit) {
			return this[Utils$1.p(unit)]();
		};
		_proto.add = function add(number, units) {
			var _this2 = this,
				_C$MIN$C$H$C$S$unit;
			number = Number(number);
			var unit = Utils$1.p(units);
			var instanceFactorySet = function instanceFactorySet(n) {
				var d = dayjs$1(_this2);
				return Utils$1.w(d.date(d.date() + Math.round(n * number)), _this2);
			};
			if (unit === M$1) {
				return this.set(M$1, this.$M + number);
			}
			if (unit === Y$1) {
				return this.set(Y$1, this.$y + number);
			}
			if (unit === D$1) {
				return instanceFactorySet(1);
			}
			if (unit === W$1) {
				return instanceFactorySet(7);
			}
			var step =
				((_C$MIN$C$H$C$S$unit = {}),
				(_C$MIN$C$H$C$S$unit[MIN$1] = MILLISECONDS_A_MINUTE$1),
				(_C$MIN$C$H$C$S$unit[H$1] = MILLISECONDS_A_HOUR$1),
				(_C$MIN$C$H$C$S$unit[S$1] = MILLISECONDS_A_SECOND$1),
				_C$MIN$C$H$C$S$unit)[unit] || 1;
			var nextTimeStamp = this.$d.getTime() + number * step;
			return Utils$1.w(nextTimeStamp, this);
		};
		_proto.subtract = function subtract(number, string) {
			return this.add(number * -1, string);
		};
		_proto.format = function format(formatStr) {
			var _this3 = this;
			var locale = this.$locale();
			if (!this.isValid()) return locale.invalidDate || INVALID_DATE_STRING$1;
			var str = formatStr || FORMAT_DEFAULT$1;
			var zoneStr = Utils$1.z(this);
			var $H = this.$H,
				$m = this.$m,
				$M = this.$M;
			var weekdays = locale.weekdays,
				months = locale.months,
				meridiem = locale.meridiem;
			var getShort = function getShort(arr, index, full, length) {
				return (
					(arr && (arr[index] || arr(_this3, str))) || full[index].slice(0, length)
				);
			};
			var get$H = function get$H(num) {
				return Utils$1.s($H % 12 || 12, num, "0");
			};
			var meridiemFunc =
				meridiem ||
				function(hour, minute, isLowercase) {
					var m = hour < 12 ? "AM" : "PM";
					return isLowercase ? m.toLowerCase() : m;
				};
			var matches = {
				YY: String(this.$y).slice(-2),
				YYYY: this.$y,
				M: $M + 1,
				MM: Utils$1.s($M + 1, 2, "0"),
				MMM: getShort(locale.monthsShort, $M, months, 3),
				MMMM: getShort(months, $M),
				D: this.$D,
				DD: Utils$1.s(this.$D, 2, "0"),
				d: String(this.$W),
				dd: getShort(locale.weekdaysMin, this.$W, weekdays, 2),
				ddd: getShort(locale.weekdaysShort, this.$W, weekdays, 3),
				dddd: weekdays[this.$W],
				H: String($H),
				HH: Utils$1.s($H, 2, "0"),
				h: get$H(1),
				hh: get$H(2),
				a: meridiemFunc($H, $m, true),
				A: meridiemFunc($H, $m, false),
				m: String($m),
				mm: Utils$1.s($m, 2, "0"),
				s: String(this.$s),
				ss: Utils$1.s(this.$s, 2, "0"),
				SSS: Utils$1.s(this.$ms, 3, "0"),
				Z: zoneStr
			};

			return str.replace(REGEX_FORMAT$1, function(match, $1) {
				return $1 || matches[match] || zoneStr.replace(":", "");
			});
		};
		_proto.utcOffset = function utcOffset() {
			return -Math.round(this.$d.getTimezoneOffset() / 15) * 15;
		};
		_proto.diff = function diff(input, units, _float) {
			var _C$Y$C$M$C$Q$C$W$C$D$;
			var unit = Utils$1.p(units);
			var that = dayjs$1(input);
			var zoneDelta =
				(that.utcOffset() - this.utcOffset()) * MILLISECONDS_A_MINUTE$1;
			var diff = this - that;
			var result = Utils$1.m(this, that);
			result =
				((_C$Y$C$M$C$Q$C$W$C$D$ = {}),
				(_C$Y$C$M$C$Q$C$W$C$D$[Y$1] = result / 12),
				(_C$Y$C$M$C$Q$C$W$C$D$[M$1] = result),
				(_C$Y$C$M$C$Q$C$W$C$D$[Q$1] = result / 3),
				(_C$Y$C$M$C$Q$C$W$C$D$[W$1] = (diff - zoneDelta) / MILLISECONDS_A_WEEK$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[D$1] = (diff - zoneDelta) / MILLISECONDS_A_DAY$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[H$1] = diff / MILLISECONDS_A_HOUR$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[MIN$1] = diff / MILLISECONDS_A_MINUTE$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[S$1] = diff / MILLISECONDS_A_SECOND$1),
				_C$Y$C$M$C$Q$C$W$C$D$)[unit] || diff;
			return _float ? result : Utils$1.a(result);
		};
		_proto.daysInMonth = function daysInMonth() {
			return this.endOf(M$1).$D;
		};
		_proto.$locale = function $locale() {
			return Ls$1[this.$L];
		};
		_proto.locale = function locale(preset, object) {
			if (!preset) return this.$L;
			var that = this.clone();
			var nextLocaleName = parseLocale$1(preset, object, true);
			if (nextLocaleName) that.$L = nextLocaleName;
			return that;
		};
		_proto.clone = function clone() {
			return Utils$1.w(this.$d, this);
		};
		_proto.toDate = function toDate() {
			return new Date(this.valueOf());
		};
		_proto.toJSON = function toJSON() {
			return this.isValid() ? this.toISOString() : null;
		};
		_proto.toISOString = function toISOString() {
			return this.$d.toISOString();
		};
		_proto.toString = function toString() {
			return this.$d.toUTCString();
		};
		return Dayjs;
	})();
	var proto$1 = Dayjs$1.prototype;
	dayjs$1.prototype = proto$1;
	[
		["$ms", MS$1],
		["$s", S$1],
		["$m", MIN$1],
		["$H", H$1],
		["$W", D$1],
		["$M", M$1],
		["$y", Y$1],
		["$D", DATE$1]
	].forEach(function(g) {
		proto$1[g[1]] = function(input) {
			return this.$g(input, g[0], g[1]);
		};
	});
	dayjs$1.extend = function(plugin, option) {
		if (!plugin.$i) {
			plugin(option, Dayjs$1, dayjs$1);
			plugin.$i = true;
		}
		return dayjs$1;
	};
	dayjs$1.locale = parseLocale$1;
	dayjs$1.isDayjs = isDayjs$1;
	dayjs$1.unix = function(timestamp) {
		return dayjs$1(timestamp * 1e3);
	};
	dayjs$1.en = Ls$1[L$1];
	dayjs$1.Ls = Ls$1;
	dayjs$1.p = {};

	var ZButton$1 = /*@__PURE__*/ (function(Component) {
		function ZButton(props) {
			Component.call(this, props);
		}

		if (Component) ZButton.__proto__ = Component;
		ZButton.prototype = Object.create(Component && Component.prototype);
		ZButton.prototype.constructor = ZButton;
		ZButton.prototype.zButtonClick = function(e, _props) {
			var this$1 = this;

			if (!this.props.disabled) {
				setTimeout(function() {
					this$1.fire("click", {});
				}, 0);
			}
			if (!this.props.bubble) {
				G.stopBubble(e);
			}
		};
		ZButton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					class: "z-button",
					style:
						"\n\t\tborder-top-left-radius: " +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;\n\t\tborder-top-right-radius: " +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;\n\t\tborder-bottom-right-radius: " +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;\n\t\tborder-bottom-left-radius: " +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;\n\t\tborder-color:" +
						this.props.color +
						";\n\t\tbackground:" +
						(this.props.plain ? "#FFF" : this.props.color) +
						";\n\t\topacity:" +
						(this.props.disabled ? "0.5" : "1") +
						";\n\t\t" +
						this.props.style,
					onClick: function(e) {
						return this$1.zButtonClick(e, this$1.props);
					}
				},
				apivm.h(
					"text",
					{
						style:
							"\n\t\tcolor:" +
							(this.props.plain ? this.props.color : "#FFF") +
							";\n\t\t" +
							G.loadConfiguration((this.props.size || 16) - 16) +
							"\n\t"
					},
					this.props.text
				)
			);
		};

		return ZButton;
	})(Component);
	ZButton$1.css = {
		".z-button": {
			padding: "8.3px 12px",
			textAlign: "center",
			borderWidth: "1px",
			borderStyle: "solid",
			boxSizing: "border-box",
			display: "flex",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("z-button", ZButton$1);

	var baoguo_hezi_o = "";
	var fuzhilianjiexian = "";
	var fuzhilianjiemian = "";
	var wenjian = "";
	var tianjia = "";
	var tianjiawenjian = "";
	var sousuowenjian = "";
	var wenjianshangchuan = "";
	var dalou = "";
	var wucan = "";
	var zengjia1 = "";
	var xinsui = "";
	var aixin = "";
	var jianshao2 = "";
	var yishoucang = "";
	var yuyinshibie = "";
	var shuruxiangce = "";
	var tupianshibie = "";
	var shuruwenjian = "";
	var shurupaizhao = "";
	var tongji4 = "";
	var pinglun1 = "";
	var gerentongxunlu = "";
	var fenxiang2 = "";
	var dianzan = "";
	var shoucang1 = "";
	var sousuo2 = "";
	var jiancexinbanben = "";
	var shezhi1 = "";
	var guanhuaimoshi = "";
	var xiugaimima = "";
	var anquantuichu = "";
	var dianhua = "";
	var shipintianchong = "";
	var xiangxiagengduo = "";
	var mubiao = "";
	var jianshao1 = "";
	var biaoqian = "";
	var duigou = "";
	var jinengbiaoqian = "";
	var jiantou_xiangyouliangci = "";
	var toupiao = "";
	var riqi = "";
	var gonggao = "";
	var tongji3 = "";
	var paihang = "";
	var mn_paiming_fill = "";
	var shaixuan = "";
	var envelope = "";
	var nvshangjia = "";
	var nan = "";
	var tongji1 = "";
	var tuceng = "";
	var tongji2 = "";
	var biaoqianlan_shouye = "";
	var shengyinjingyin = "";
	var shengyin = "";
	var shengyin1 = "";
	var tianxie = "";
	var erweima = "";
	var huanyuan = "";
	var lianjie = "";
	var changyonglogo28 = "";
	var pengyouquan = "";
	var zhixiangxia = "";
	var QQ = "";
	var quanxian = "";
	var baocun = "";
	var suoyouwenjian = "";
	var xiangxia1 = "";
	var xinzeng = "";
	var ziyuanxhdpi = "";
	var xiazai = "";
	var zhongmingming = "";
	var yunpan = "";
	var tongji = "";
	var kefu = "";
	var weibiaoti1 = "";
	var tongxunlu = "";
	var xinyongqia = "";
	var tuichu = "";
	var aixinxiantiao = "";
	var shezhi = "";
	var jianchaxinbanben = "";
	var yuyin = "";
	var shanchu = "";
	var remen = "";
	var tupian = "";
	var xiangji = "";
	var zan = "";
	var zan1 = "";
	var like = "";
	var unlike = "";
	var pinglun = "";
	var jushoucang = "";
	var jushoucanggift = "";
	var share = "";
	var shoucangfill = "";
	var fenxiang1 = "";
	var shoucang = "";
	var bofang = "";
	var daojishi = "";
	var cuohao = "";
	var duihao = "";
	var chakan = "";
	var shoujihaoma = "";
	var mima1 = "";
	var zhanghao1 = "";
	var yanzhengma1 = "";
	var kejian = "";
	var bukejian = "";
	var gengduo1 = "";
	var gengduogongneng = "";
	var gengduo2 = "";
	var gengduo3 = "";
	var fanhui1 = "";
	var fanhui2 = "";
	var sousuo = "";
	var sousuo1 = "";
	var saoyisao1 = "";
	var xiangxia = "";
	var xiangzuo = "";
	var fangxingweixuanzhong = "";
	var fangxingxuanzhongfill = "";
	var fangxingxuanzhong = "";
	var yuanxingweixuanzhong = "";
	var yuanxingxuanzhong = "";
	var yuanxingxuanzhongfill = "";
	var danxuan_xuanzhong = "";
	var danxuan_weixuanzhong = "";
	var gengduo = "";
	var fenxiang = "";
	var zhuanfa00 = "";
	var dingwei = "";
	var dingwei1 = "";
	var jianshao = "";
	var zengjia = "";
	var qingkong = "";
	var mima = "";
	var zhanghao = "";
	var yanzhengma = "";
	var icons = {
		baoguo_hezi_o: baoguo_hezi_o,
		fuzhilianjiexian: fuzhilianjiexian,
		fuzhilianjiemian: fuzhilianjiemian,
		wenjian: wenjian,
		tianjia: tianjia,
		tianjiawenjian: tianjiawenjian,
		sousuowenjian: sousuowenjian,
		wenjianshangchuan: wenjianshangchuan,
		"zuanshi-L": "",
		dalou: dalou,
		wucan: wucan,
		zengjia1: zengjia1,
		xinsui: xinsui,
		aixin: aixin,
		jianshao2: jianshao2,
		yishoucang: yishoucang,
		yuyinshibie: yuyinshibie,
		shuruxiangce: shuruxiangce,
		tupianshibie: tupianshibie,
		shuruwenjian: shuruwenjian,
		shurupaizhao: shurupaizhao,
		tongji4: tongji4,
		pinglun1: pinglun1,
		gerentongxunlu: gerentongxunlu,
		fenxiang2: fenxiang2,
		dianzan: dianzan,
		shoucang1: shoucang1,
		sousuo2: sousuo2,
		jiancexinbanben: jiancexinbanben,
		shezhi1: shezhi1,
		guanhuaimoshi: guanhuaimoshi,
		xiugaimima: xiugaimima,
		anquantuichu: anquantuichu,
		dianhua: dianhua,
		shipintianchong: shipintianchong,
		"31dianhua": "",
		xiangxiagengduo: xiangxiagengduo,
		mubiao: mubiao,
		jianshao1: jianshao1,
		biaoqian: biaoqian,
		"gantanhao-xianxingyuankuang": "",
		duigou: duigou,
		jinengbiaoqian: jinengbiaoqian,
		jiantou_xiangyouliangci: jiantou_xiangyouliangci,
		toupiao: toupiao,
		riqi: riqi,
		gonggao: gonggao,
		tongji3: tongji3,
		paihang: paihang,
		mn_paiming_fill: mn_paiming_fill,
		"line-084": "",
		"line-085": "",
		shaixuan: shaixuan,
		envelope: envelope,
		"envelope-open": "",
		"a-4_huaban1": "",
		nvshangjia: nvshangjia,
		nan: nan,
		tongji1: tongji1,
		"weibiaoti--": "",
		tuceng: tuceng,
		tongji2: tongji2,
		biaoqianlan_shouye: biaoqianlan_shouye,
		shengyinjingyin: shengyinjingyin,
		shengyin: shengyin,
		shengyin1: shengyin1,
		tianxie: tianxie,
		"file-download-fill": "",
		"file-damage-fill": "",
		"file-excel-fill": "",
		"file-copy-fill": "",
		"file-edit-fill": "",
		"file-music-fill": "",
		"file-gif-fill": "",
		"file-history-fill": "",
		"file-lock-fill": "",
		"file-info-fill": "",
		"file-pdf-fill": "",
		"file-text-fill": "",
		"file-ppt-fill": "",
		"file-upload-fill": "",
		"file-unknow-fill": "",
		"file-word-fill": "",
		"file-search-fill": "",
		"file-transfer-fill": "",
		"file-zip-fill": "",
		"folder-2-fill": "",
		"file-warning-fill": "",
		"folder-add-fill": "",
		erweima: erweima,
		"file-reduce-fill": "",
		huanyuan: huanyuan,
		lianjie: lianjie,
		changyonglogo28: changyonglogo28,
		pengyouquan: pengyouquan,
		zhixiangxia: zhixiangxia,
		QQ: QQ,
		quanxian: quanxian,
		baocun: baocun,
		suoyouwenjian: suoyouwenjian,
		xiangxia1: xiangxia1,
		"wj-gxwj": "",
		xinzeng: xinzeng,
		ziyuanxhdpi: ziyuanxhdpi,
		xiazai: xiazai,
		zhongmingming: zhongmingming,
		yunpan: yunpan,
		"file-code-fill": "",
		"file-add-fill": "",
		tongji: tongji,
		kefu: kefu,
		weibiaoti1: weibiaoti1,
		tongxunlu: tongxunlu,
		xinyongqia: xinyongqia,
		tuichu: tuichu,
		aixinxiantiao: aixinxiantiao,
		shezhi: shezhi,
		jianchaxinbanben: jianchaxinbanben,
		yuyin: yuyin,
		shanchu: shanchu,
		remen: remen,
		tupian: tupian,
		xiangji: xiangji,
		zan: zan,
		zan1: zan1,
		like: like,
		unlike: unlike,
		"like-fill": "",
		"unlike-fill": "",
		pinglun: pinglun,
		jushoucang: jushoucang,
		jushoucanggift: jushoucanggift,
		share: share,
		shoucangfill: shoucangfill,
		fenxiang1: fenxiang1,
		shoucang: shoucang,
		bofang: bofang,
		"bell-off": "",
		daojishi: daojishi,
		cuohao: cuohao,
		duihao: duihao,
		chakan: chakan,
		shoujihaoma: shoujihaoma,
		mima1: mima1,
		zhanghao1: zhanghao1,
		yanzhengma1: yanzhengma1,
		kejian: kejian,
		bukejian: bukejian,
		gengduo1: gengduo1,
		gengduogongneng: gengduogongneng,
		gengduo2: gengduo2,
		gengduo3: gengduo3,
		"a-14Bshanchu": "",
		fanhui1: fanhui1,
		fanhui2: fanhui2,
		sousuo: sousuo,
		sousuo1: sousuo1,
		saoyisao1: saoyisao1,
		xiangxia: xiangxia,
		xiangzuo: xiangzuo,
		fangxingweixuanzhong: fangxingweixuanzhong,
		fangxingxuanzhongfill: fangxingxuanzhongfill,
		fangxingxuanzhong: fangxingxuanzhong,
		yuanxingweixuanzhong: yuanxingweixuanzhong,
		yuanxingxuanzhong: yuanxingxuanzhong,
		yuanxingxuanzhongfill: yuanxingxuanzhongfill,
		danxuan_xuanzhong: danxuan_xuanzhong,
		danxuan_weixuanzhong: danxuan_weixuanzhong,
		gengduo: gengduo,
		fenxiang: fenxiang,
		zhuanfa00: zhuanfa00,
		dingwei: dingwei,
		dingwei1: dingwei1,
		jianshao: jianshao,
		zengjia: zengjia,
		qingkong: qingkong,
		mima: mima,
		zhanghao: zhanghao,
		yanzhengma: yanzhengma
	};

	var AMpcss = /*@__PURE__*/ (function(Component) {
		function AMpcss(props) {
			Component.call(this, props);
		}

		if (Component) AMpcss.__proto__ = Component;
		AMpcss.prototype = Object.create(Component && Component.prototype);
		AMpcss.prototype.constructor = AMpcss;
		AMpcss.prototype.render = function() {
			return;
		};

		return AMpcss;
	})(Component);
	AMpcss.css = {
		"@font-face": {
			fontFamily: '"iconfont_mp"',
			src:
				"url('https://at.alicdn.com/t/c/font_3560231_mff0m4knr.ttf') format('truetype')"
		}
	};

	apivm.define("a-mpcss", AMpcss);

	var AIconfont = /*@__PURE__*/ (function(Component) {
		function AIconfont(props) {
			Component.call(this, props);
		}

		if (Component) AIconfont.__proto__ = Component;
		AIconfont.prototype = Object.create(Component && Component.prototype);
		AIconfont.prototype.constructor = AIconfont;
		AIconfont.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"text",
					{
						style:
							"font-size:" +
							(this.props.size || 16) +
							"px;color:" +
							(this.props.color || "#999") +
							";\n\t\tfont-family: " +
							(api.platform == "mp" ? "iconfont_mp" : "iconfont;") +
							";\n\t\t" +
							(this.props.style || "") +
							";font-weight:400;",
						class: "" + (this.props.class || "")
					},
					icons[this.props.name + ""]
				),
				api.platform == "mp" && apivm.h("a-mpcss", null)
			);
		};

		return AIconfont;
	})(Component);
	AIconfont.css = {
		"@font-face": {
			fontFamily: '"iconfont"',
			src:
				"url('../../components/act/a-iconfont/fonts/iconfont.ttf') format('truetype')"
		}
	};
	apivm.define("a-iconfont", AIconfont);

	var ZSwitch = /*@__PURE__*/ (function(Component) {
		function ZSwitch(props) {
			Component.call(this, props);
		}

		if (Component) ZSwitch.__proto__ = Component;
		ZSwitch.prototype = Object.create(Component && Component.prototype);
		ZSwitch.prototype.constructor = ZSwitch;
		ZSwitch.prototype.change = function(e) {
			var this$1 = this;

			this.props.dataMore.value = e.detail.value;
			setTimeout(function() {
				this$1.fire("change", this$1.props.dataMore);
			}, 0);
		};
		ZSwitch.prototype.render = function() {
			return apivm.h("switch", {
				style:
					"margin: 0;transform: scale(" +
					(this.props.height || 31) / 31 +
					", " +
					(this.props.height || 31) / 31 +
					");" +
					(this.props.style || ""),
				class: "" + (this.props.class || ""),
				checked: this.props.dataMore.value,
				disabled: this.props.disabled || false,
				color: this.props.color,
				onChange: this.change
			});
		};

		return ZSwitch;
	})(Component);
	apivm.define("z-switch", ZSwitch);

	var YAttachments = /*@__PURE__*/ (function(Component) {
		function YAttachments(props) {
			Component.call(this, props);
			this.data = {};
			this.compute = {
				imgList: function() {
					var list = [];
					if (T.isArray(this.props.data) && this.props.data.length) {
						this.props.data.forEach(function(_eItem, _eIndex, _eArr) {
							var fileInfo = G.getFileInfo(_eItem.extName || "unknown");
							if (fileInfo.type != "image") {
								return;
							}
							list.push({
								id: _eItem.id,
								name: _eItem.originalFileName,
								newName: _eItem.newFileName,
								url: myjs.appUrl() + "image/" + _eItem.newFileName,
								fileInfo: fileInfo,
								dotSystemDel: _eItem.dotSystemDel
							});
						});
					}
					return list;
				},
				id: function() {},
				name: function() {},
				newName: function() {},
				url: function() {},
				fileInfo: function() {},
				dotSystemDel: function() {},
				fileList: function() {
					var list = [];
					if (T.isArray(this.props.data) && this.props.data.length) {
						this.props.data.forEach(function(_eItem, _eIndex, _eArr) {
							var fileInfo = G.getFileInfo(_eItem.extName || "unknown");
							if (fileInfo.type == "image") {
								return;
							}
							list.push({
								id: _eItem.id,
								name: _eItem.originalFileName,
								newName: _eItem.newFileName,
								url: myjs.appUrl() + "file/preview/" + _eItem.id,
								fileInfo: fileInfo,
								dotSystemDel: _eItem.dotSystemDel
							});
						});
					}
					return list;
				}
			};
		}

		if (Component) YAttachments.__proto__ = Component;
		YAttachments.prototype = Object.create(Component && Component.prototype);
		YAttachments.prototype.constructor = YAttachments;
		YAttachments.prototype.installed = function() {};
		YAttachments.prototype.openFile = function(e, _item) {
			G.stopBubble(e);
			var param = {};
			param.id = _item.id || _item.url;
			param.suffix = _item.fileInfo.type;
			param.fileSource = this.props.fileSource;
			openWin_filePreviewer(param);
		};
		YAttachments.prototype.openImages = function(e, _item, _index) {
			G.stopBubble(e);
			G.openImgPreviewer({
				index: _index,
				imgs: this.imgList.map(function(obj) {
					return obj.url;
				})
			});
		};
		YAttachments.prototype.delFile = function(e, _item, _index) {
			G.stopBubble(e);
			G.delItemForKey(_item, this.props.data, "id");
			if (!_item.dotSystemDel) {
				T.ajax(
					{u: myjs.appUrl() + "file/clear", _this: this},
					"file/clear" + _item.id,
					function(ret, err) {},
					"删除附件",
					"post",
					{
						body: JSON.stringify({fileIds: [_item.id]})
					}
				);
			}
		};
		YAttachments.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				null,
				this.imgList.length + this.fileList.length > 0
					? apivm.h(
							"view",
							{style: this.props.style},
							apivm.h(
								"view",
								null,
								this.imgList.length > 0 &&
									apivm.h(
										"view",
										{
											class: "commentSendBig_img_box",
											style:
												"margin-bottom:" + (this.fileList.length > 0 ? "1" : "") + "0px;"
										},
										(Array.isArray(this.imgList)
											? this.imgList
											: Object.values(this.imgList)
										).map(function(item$1, index$1) {
											return apivm.h(
												"view",
												{
													class: "commentSendBig_img_item",
													onClick: function(e) {
														return this$1.openImages(e, item$1, index$1);
													}
												},
												apivm.h("image", {
													style: "width:100%;" + G.loadConfigurationSize(44, "h"),
													src: G.showImg(item$1),
													mode: "aspectFill"
												}),
												apivm.h(
													"view",
													{
														class: "commentSendBig_img_clean",
														style:
															"display:" + (this$1.props.type == "2" ? "flex" : "none") + ";",
														onClick: function(e) {
															return this$1.delFile(e, item$1, index$1);
														}
													},
													apivm.h("a-iconfont", {
														name: "qingkong",
														color: "rgba(0,0,0,0.65)",
														size: G.appFontSize + 2
													})
												)
											);
										})
									)
							),
							apivm.h(
								"view",
								null,
								this.fileList.length > 0 &&
									apivm.h(
										"view",
										null,
										(Array.isArray(this.fileList)
											? this.fileList
											: Object.values(this.fileList)
										).map(function(item$1, index$1) {
											return apivm.h(
												"view",
												{
													class: "attachments_item",
													style:
														"margin-top:" +
														(index$1 ? 1 : 0) +
														"0px;background:" +
														(this$1.props.theme == "dark" ? "#FFF" : "transparent") +
														";",
													onClick: function(e) {
														return this$1.openFile(e, item$1, index$1);
													}
												},
												apivm.h(
													"view",
													{style: "width:auto;height:auto;margin-right:10px;"},
													apivm.h("a-iconfont", {
														name: item$1.fileInfo.name,
														color: item$1.fileInfo.color,
														size: G.appFontSize + 6
													})
												),
												apivm.h(
													"view",
													{style: "flex:1;"},
													apivm.h(
														"text",
														{
															class: "text_one" + (T.platform() == "app" ? "" : "2"),
															style:
																G.loadConfiguration() + "color: #666;word-break: break-all;"
														},
														item$1.name
													)
												),
												apivm.h(
													"view",
													null,
													this$1.props.type == "2"
														? apivm.h(
																"view",
																{
																	style: "padding:5px;margin-right:-5px;",
																	onClick: function(e) {
																		return this$1.delFile(e, item$1, index$1);
																	}
																},
																apivm.h("a-iconfont", {
																	name: "qingkong",
																	color: "#333",
																	size: G.appFontSize + 4
																})
														  )
														: null
												)
											);
										})
									)
							)
					  )
					: null
			);
		};

		return YAttachments;
	})(Component);
	YAttachments.css = {
		".attachments_item": {
			width: "auto",
			borderRadius: "4px 4px 4px 4px",
			opacity: "1",
			border: "1px solid #F4F5F7",
			padding: "2px 10px",
			minHeight: "36px",
			flexDirection: "row",
			alignItems: "center"
		},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden"
		},
		".text_one2": {
			WebkitLineClamp: "1",
			WebkitBoxOrient: "vertical",
			display: "-webkit-box",
			overflow: "hidden"
		},
		".commentSendBig_img_box": {flexDirection: "row", flexWrap: "wrap"},
		".commentSendBig_img_item": {
			padding: "10px 10px 0 0",
			width: "25%",
			height: "auto",
			boxSizing: "border-box"
		},
		".commentSendBig_img_clean": {
			position: "absolute",
			zIndex: "999",
			top: "1px",
			right: "3px"
		}
	};
	apivm.define("y-attachments", YAttachments);

	// 加密
	function getFileAdress(_param, _callback) {
		if (_param === void 0) {
			_param = {};
		}
		//文档地址 https://www.yozodcs.com/page/help.html#help9
		T.ajax(
			{u: "https://www.yozodcs.com/fcscloud/file/http?"},
			"onlinefile",
			function(ret, err) {
				if (ret) {
					var data = (ret.data || {}).data;
					if (data) {
						T.ajax(
							{u: "https://www.yozodcs.com/fcscloud/composite/convert?"},
							"onlinefile",
							function(ret, err) {
								if (ret) {
									var viewUrl = (ret.data || {}).viewUrl;
									if (viewUrl) {
										_callback(viewUrl, ret.data);
									} else {
										_callback(null, ret.message || "打开失败，请重试");
									}
								} else {
									_callback(null, "打开失败");
								}
							},
							"生成链接",
							"post",
							{
								values: {
									srcRelativePath: data,
									convertType:
										G.getFileInfo(data.substring(data.lastIndexOf("."))).convertType ||
										"0",
									isDccAsync: 1,
									isCopy: 0,
									noCache: 0,
									fileUrl: _param.url,
									showFooter: 0, //是否显示页脚
									isHeaderBar: 0,
									htmlTitle: "详情"
								}
							},

							{
								"content-type": "application/x-www-form-urlencoded"
							}
						);
					} else {
						_callback(null, ret.message || "打开失败，请重试");
					}
				} else {
					_callback(null, "打开失败");
				}
			},
			"在线转换",
			"post",
			{
				values: {
					fileUrl: _param.url
				}
			},

			{
				"content-type": "application/x-www-form-urlencoded"
			}
		);
	}

	var ZVideo = /*@__PURE__*/ (function(Component) {
		function ZVideo(props) {
			Component.call(this, props);
			this.data = {
				withSrc: "",
				showSrc: "",
				showPoster: "",
				showError: ""
			};
			this.compute = {
				monitor: function() {
					if (this.props.src != this.data.withSrc) {
						this.data.withSrc = this.props.src;
						this.dealWith();
					}
				}
			};
		}

		if (Component) ZVideo.__proto__ = Component;
		ZVideo.prototype = Object.create(Component && Component.prototype);
		ZVideo.prototype.constructor = ZVideo;
		ZVideo.prototype.installed = function() {};
		ZVideo.prototype.dealWith = function() {
			var this$1 = this;

			console.log(
				"withSrc:" +
					this.data.withSrc +
					"-" +
					this.data.showSrc +
					"-" +
					dayjs$1().unix()
			);
			if ((this.data.withSrc + "").indexOf("http") != 0) {
				//不是http开头 说明系统内附件
				var cachePath = T.getPrefs("attach_" + this.data.withSrc);
				console.log(cachePath);
				if (
					cachePath &&
					dayjs$1().unix() - Number(cachePath.split("-attachPath-")[0]) < 86400
				) {
					this.data.showSrc = cachePath.split("-attachPath-")[1];
					this.data.showPoster =
						myjs.tomcatAddress() + "utils2/proxyVideo?" + this.data.showSrc;
					return;
				}
				var src = myjs.appUrl() + "file/preview/" + this.data.withSrc;
				getFileAdress({url: src}, function(ret, err) {
					if (ret) {
						T.ajax(
							{u: ret, dt: "text", _this: this$1},
							"onlinefile",
							function(ret, err) {
								var matchResult = ret ? ret.match(/videoFile = "([^"]+)"/) : "";
								if (matchResult) {
									this$1.data.showError = "";
									function unicodeToChinese(str) {
										return str.replace(/\\u(\w{4})/g, function(match, code) {
											return String.fromCharCode(parseInt(code, 16));
										});
									}
									var path = unicodeToChinese(matchResult[1]).replace(/\\/g, "");
									this$1.data.showSrc = path;
									this$1.data.showPoster =
										myjs.tomcatAddress() + "utils2/proxyVideo?" + this$1.data.showSrc;
									T.setPrefs(
										"attach_" + this$1.data.withSrc,
										dayjs$1().unix() + "-attachPath-" + path
									);
								} else {
									this$1.data.showError = err;
								}
							},
							"附件详情",
							"get"
						);
					} else {
						this$1.data.showError = err;
					}
				});
			} else {
				this.data.showSrc = this.data.withSrc;
				this.data.showPoster =
					myjs.tomcatAddress() + "utils2/proxyVideo?" + this.data.showSrc;
			}
		};
		ZVideo.prototype.again = function(e) {
			if (this.data.showError) {
				this.data.showError = "";
				this.dealWith();
			}
			G.stopBubble(e);
		};
		ZVideo.prototype.render = function() {
			return apivm.h(
				"view",
				{
					a: this.monitor,
					style:
						"width:100%;height: " +
						(T.platform() == "web"
							? api.winWidth > 600
								? 600
								: api.winWidth
							: api.winWidth) *
							0.52 +
						"px;" +
						this.props.style,
					class: this.props.class
				},
				this.data.showSrc
					? apivm.h("video", {
							id: this.props.id,
							controls: true,
							style: "width:100%;height:100%;object-fit: cover;",
							src: this.data.showSrc,
							poster: this.props.poster || this.data.showPoster,
							mode: "aspectFill"
					  })
					: apivm.h(
							"view",
							{
								onClick: this.again,
								style:
									"width:100%;height:100%;align-items: center;justify-content: center;"
							},
							this.data.showError != ""
								? apivm.h(
										"view",
										{
											style:
												"flex-direction:row;align-items: center;justify-content: center;"
										},
										apivm.h("a-iconfont", {
											name: "huanyuan",
											color: "#666",
											style: "font-weight: 600;margin-right:10px;",
											size: G.appFontSize
										}),
										apivm.h(
											"text",
											{style: "color:#666;" + G.loadConfiguration()},
											"视频加载失败"
										)
								  )
								: apivm.h("image", {
										style: "width:50px;height:50px;",
										src: myjs.shareAddress(1) + "img/loading.gif",
										mode: "aspectFill",
										thumbnail: "false"
								  })
					  )
			);
		};

		return ZVideo;
	})(Component);
	apivm.define("z-video", ZVideo);

	var ZRichText = /*@__PURE__*/ (function(Component) {
		function ZRichText(props) {
			Component.call(this, props);
			this.data = {
				showText: null,
				listData: [],
				hasExpand: false, //是否有展开
				isExpand: false //是否展开了
			};
			this.compute = {
				monitor: function() {
					if (this.props.nodes != this.data.showText) {
						this.data.showText = this.props.nodes;
						this.dealWithCon();
					}
				}
			};
		}

		if (Component) ZRichText.__proto__ = Component;
		ZRichText.prototype = Object.create(Component && Component.prototype);
		ZRichText.prototype.constructor = ZRichText;
		ZRichText.prototype.installed = function() {};
		ZRichText.prototype.expandShow = function() {
			this.data.isExpand = !this.data.isExpand;
			this.dealWithCon();
		};
		ZRichText.prototype.dealWithCon = function() {
			var this$1 = this;

			var expText =
				(T.isParameters(this.data.showText) ? this.data.showText : "") + "";
			if (T.isObject(expText) || T.isArray(expText)) {
				expText = JSON.stringify(expText);
			}
			var notTagText = T.removeTag(expText);
			this.data.hasExpand =
				T.isParameters(this.props.expand) && notTagText.length > this.props.expand;
			expText =
				this.data.hasExpand && !this.data.isExpand
					? notTagText.substring(0, this.props.expand) + "..."
					: expText;

			expText = expText.replace(
				/(<style(.*?)<\/style>|<link(.*?)<\/link>|<script(.*?)<\/script>|<!--[\w\W\r\n]*?-->|^\s*|\s*$)/gi,
				""
			);
			var reLabel = function(_bel) {
				var oldText = expText;
				expText = expText.replace(
					new RegExp("<" + _bel + "[^>]*>(.*?)</" + _bel + ">", "gi"),
					"$1"
				);
				if (expText != oldText && expText.indexOf(_bel) != -1) {
					reLabel(_bel);
				}
			};
			["span"].forEach(function(item) {
				reLabel(item);
			});
			expText = expText.replace(/<\s*\/?\s*br\s*\/?\s*>/gi, "</br>");
			expText = expText.replace(/\n/gi, "</br>");
			expText = T.decodeCharacter(expText);
			expText = expText.replace(/<ins(.*?)>(.*?)<\/ins>/gi, "$2");
			if (!expText.startsWith("<") || !expText.endsWith(">")) {
				expText = "<div>" + expText + "</div>";
			}
			// console.log(expText);
			var newText = expText;
			//清空表格td中所有的标签
			var rdRegex = /<td(.*?)>(.*?)<\/td>/gi;
			var match;
			while ((match = rdRegex.exec(expText)) !== null) {
				var nText = T.removeTag(match[2]);
				newText = newText.replace(match[2], nText);
			}
			expText = newText;
			var strRegex = /<\/?\w+[^>]*>/gi;
			var nowIndexs = [],
				viewIndexs = [];
			var startIndex = [];
			expText.replace(strRegex, function(item, index) {
				//循环对应的内容和角标
				var nlabel = item.match(/<\/*([^> ]+)[^>]*>/)[1].toLocaleLowerCase();
				var styleMatch = /style\s*=\s*['"]([^'"]*)['"]/i.exec(item);
				var nowItem = {
					label: nlabel,
					index: index,
					text: item,
					style: styleMatch ? styleMatch[1] : ""
				};
				viewIndexs.push(nowItem);
				// console.log(index + "-" + nlabel + "-" + item);
				if (/^<(p|div|span).*/i.test(item)) {
					var nowItems = {
						index: nowIndexs.length,
						endIndex: 0,
						label: nlabel,
						start: nowItem,
						end: null,
						style: styleMatch ? styleMatch[1] : ""
					};
					startIndex.push(nowItems);
					nowIndexs.push(nowItems);
				} else if (/^<\/(p|div|span)>/i.test(item)) {
					if (startIndex.length) {
						nowIndexs[startIndex[startIndex.length - 1].index].end = nowItem;
						nowIndexs[startIndex[startIndex.length - 1].index].endIndex =
							nowIndexs.length - 1;
						startIndex.pop();
					}
				} else if (/^<.*?>$/.test(item)) {
					nowIndexs.push({
						index: nowIndexs.length,
						endIndex: nowIndexs.length,
						label: nlabel,
						start: nowItem,
						end: nowItem,
						style: styleMatch ? styleMatch[1] : ""
					});
				}
			});
			var showTexts = [];
			var tableData = null,
				isTable = false,
				tableItem = null;
			viewIndexs.forEach(function(item, index) {
				var minIndex = item.index + item.text.length;
				var maxIndex =
					index != viewIndexs.length - 1
						? viewIndexs[index + 1].index
						: expText.length;
				var viewText = expText.substring(minIndex, maxIndex);
				if (item.label == "table") {
					if (!isTable) {
						isTable = true;
						tableData = {
							label: "table",
							widths: [],
							data: [],
							index: minIndex,
							style: item.style
						};
					} else {
						isTable = false;
						showTexts.push(tableData);
					}
					return;
				}
				if (isTable) {
					if (item.text == "</tr>") {
						tableData.data.push(tableItem);
					} else if (item.label == "tr") {
						tableItem = {label: "tr", data: [], index: minIndex, style: item.style};
					} else if (item.text != "</td>" && item.label == "td") {
						var tdWidth = this$1.getStyle(item.style, "width") || "150px";
						if (!tableData.data.length) {
							if (tdWidth.indexOf("%") != -1) {
								//是百分比宽度
								var nW = Number(tdWidth.replace(/%/g, ""));
								tdWidth = ((nW < 30 ? 30 : nW) * G.pageWidth) / 100 + "px";
							}
							tableData.widths.push(tdWidth);
						}
						var showStyle = "";
						showStyle +=
							"border:" +
							(this$1.getStyle(item.style, "border") || "1px solid #ccc") +
							";";
						showStyle +=
							"border-left:" + this$1.getStyle(item.style, "border-left") + ";";
						showStyle +=
							"border-right:" + this$1.getStyle(item.style, "border-right") + ";";
						showStyle +=
							"border-top:" + this$1.getStyle(item.style, "border-top") + ";";
						showStyle +=
							"border-bottom:" + this$1.getStyle(item.style, "border-bottom") + ";";
						showStyle +=
							"background:" + this$1.getStyle(item.style, "background") + ";";
						showStyle += "color:" + this$1.getStyle(item.style, "color") + ";";
						tableItem.data.push({
							label: "td",
							index: minIndex,
							text: viewText,
							style: showStyle
						});
					}
					return;
				}
				if (/^(?!p|div|span).*$/i.test(item.label)) {
					if (
						item.label == "br" &&
						!item.text &&
						showTexts.length &&
						showTexts[showTexts.length - 1].label == "br"
					) {
						return;
					}
					var addItem = {label: item.label, index: item.index, style: item.style};
					minIndex = minIndex + item.text.length;
					var srcMatch = /src\s*=\s*['"]([^'"]*)['"]/i.exec(item.text);
					if (srcMatch) {
						addItem.src = srcMatch[1];
					}
					var hrefMatch = /href\s*=\s*['"]([^'"]*)['"]/i.exec(item.text);
					if (hrefMatch) {
						addItem.href = hrefMatch[1];
					}
					showTexts.push(addItem);
				}
				if (viewText.replace(/^\s*|\s*$/, "")) {
					var showText = {
						label: "text",
						index: minIndex,
						text: viewText,
						style: item.style
					};
					if (item.label == "a") {
						showText.label = "text_a";
						showText.href = showTexts.length
							? showTexts[showTexts.length - 1].href
							: "";
					}
					showTexts.push(showText);
				}
			});
			// console.log(JSON.stringify(nowIndexs));
			// console.log(JSON.stringify(viewIndexs));
			// console.log(JSON.stringify(showTexts));
			this.data.listData = showTexts;
		};
		ZRichText.prototype.openImages = function(e, _item, _index) {
			G.stopBubble(e);
			var imgs = this.data.listData.filter(function(item, index) {
				return item.label == "img";
			});
			console.log(imgs, "imgs");
			G.openImgPreviewer({
				index: G.getItemForKey(_item.index + "", imgs, "index")._i,
				imgs: imgs.map(function(obj) {
					return obj.src;
				})
			});
		};
		ZRichText.prototype.openHrefs = function(e, _item, _index) {
			G.stopBubble(e);
			if (T.platform() == "web") {
				window.open(_item.href);
				return;
			}
			T.openWin(
				"mo_details_url",
				"../mo_details_url/mo_details_url.stml",
				{url: _item.href},
				this
			);
		};
		ZRichText.prototype.getStyle = function(_text, _item) {
			var match = new RegExp(_item + ":s*([^;]+);").exec(_text);
			if (match) {
				var matchText = match[1].replace(/pt/g, "px");
				return matchText;
			}
			return "";
		};
		ZRichText.prototype.nTouchmove = function() {
			G.touchmove();
		};
		ZRichText.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{a: this.monitor, class: this.props.class},
				apivm.h(
					"view",
					null,
					(Array.isArray(this.data.listData)
						? this.data.listData
						: Object.values(this.data.listData)
					).map(function(item$1, index$1) {
						return apivm.h(
							"view",
							null,
							item$1.label == "text"
								? apivm.h(
										"view",
										null,
										apivm.h(
											"text",
											{
												style:
													this$1.props.style +
													"line-height: " +
													G.appFontSize * 1.8 +
													"px;" +
													(this$1.props.detail && T.platform() != "app"
														? "text-indent: 2em;"
														: ""),
												class: "richText"
											},
											this$1.props.detail
												? api.systemType == "android"
													? "					"
													: api.systemType == "ios"
													? "	 "
													: ""
												: "",
											item$1.text
										)
								  )
								: null,
							item$1.label == "text_a"
								? apivm.h(
										"view",
										{
											onClick: function(e) {
												return this$1.openHrefs(e, item$1, index$1);
											}
										},
										apivm.h(
											"text",
											{style: this$1.props.style + "color: blue;", class: "richText"},
											item$1.text
										)
								  )
								: item$1.label == "br"
								? apivm.h(
										"view",
										null,
										apivm.h("view", {style: "height:" + G.appFontSize + "px;"})
								  )
								: item$1.label == "img"
								? apivm.h(
										"view",
										{
											onClick: function(e) {
												return this$1.openImages(e, item$1, index$1);
											}
										},
										apivm.h(
											"view",
											{class: "richImgBox"},
											apivm.h("image", {
												class: "richImg",
												mode: "aspectFill",
												thumbnail: "false",
												src: G.showImg(item$1.src)
											})
										)
								  )
								: item$1.label == "video" || item$1.label == "source"
								? apivm.h(
										"view",
										{style: "margin:5px 0;"},
										item$1.src && apivm.h("z-video", {src: item$1.src})
								  )
								: item$1.label == "table"
								? apivm.h(
										"view",
										null,
										apivm.h(
											"scroll-view",
											{"scroll-x": true, "scroll-y": false},
											apivm.h(
												"view",
												{
													onTouchStart: this$1.nTouchmove,
													onTouchMove: this$1.nTouchmove,
													onTouchStart: this$1.nTouchmove
												},
												(item$1.data || []).map(function(nItem, nIndex) {
													return apivm.h(
														"view",
														{
															class: "richTable_item",
															style: "margin-top:" + (nIndex ? -1 : 0) + "px;"
														},
														(nItem.data || []).map(function(uItem, uIndex) {
															return apivm.h(
																"view",
																{
																	class: "richTable_item_td",
																	style:
																		uItem.style +
																		"width:" +
																		item$1.widths[uIndex] +
																		";margin-left:" +
																		(uIndex ? -1 : 0) +
																		"px;"
																},
																apivm.h(
																	"text",
																	{style: "" + G.loadConfiguration(), class: "richText"},
																	uItem.text
																)
															);
														})
													);
												})
											)
										)
								  )
								: apivm.h("view", null)
						);
					})
				),
				apivm.h(
					"view",
					null,
					this.data.hasExpand &&
						apivm.h(
							"view",
							{
								style:
									"padding: 7px 0;flex-direction:row; align-items: center;justify-content: flex-start;",
								onClick: function() {
									return this$1.expandShow();
								}
							},
							apivm.h(
								"text",
								{style: G.loadConfiguration(-2) + "color:#999"},
								this.data.isExpand ? "收起" : "展开"
							),
							apivm.h("a-iconfont", {
								name: "xiangxiagengduo",
								style:
									"margin-left:5px;transform: rotate(" +
									(this.data.isExpand ? "180" : "0") +
									"deg);",
								color: "#999",
								size: G.appFontSize - 3
							})
						)
				)
			);
		};

		return ZRichText;
	})(Component);
	ZRichText.css = {
		".richImgBox": {alignItems: "center", justifyContent: "center"},
		".richImg": {width: "100%", maxWidth: "100%", maxHeight: "100%"},
		".richTable_item": {flexFlow: "row nowrap"},
		".richTable_item_td": {
			alignItems: "center",
			justifyContent: "center",
			flexShrink: "0",
			padding: "5px"
		},
		".richText": {wordWrap: "break-word", wordBreak: "break-all"}
	};
	apivm.define("z-rich-text", ZRichText);

	var ZPickerTime = /*@__PURE__*/ (function(Component) {
		function ZPickerTime(props) {
			Component.call(this, props);
			this.data = {
				dateSelectLast: null
			};
			this.compute = {
				getDateList: function() {
					var data = [];
					if (!this.props.dataMore) {
						return data;
					}
					var years = [],
						minYear = 2000,
						maxYear = 2100;
					if (this.props.dataMore.minTime) {
						//设置了最小时间
						minYear = dayjs$1(this.props.dataMore.minTime).year();
						maxYear = dayjs$1(this.props.dataMore.minTime)
							.add(100, "year")
							.year();
					}
					if (this.props.dataMore.maxTime) {
						maxYear = dayjs$1(this.props.dataMore.maxTime).year();
					}
					for (var i = minYear; i <= maxYear; i++) {
						years.push({key: i, value: i + "年", desc: i});
					}
					data.push(years);
					if (this.props.dataMore.value.length > 1) {
						var months = [],
							minMonth = 1,
							maxMonth = 12;
						if (this.props.dataMore.minTime && this.props.dataMore.value[0] == 0) {
							minMonth = dayjs$1(this.props.dataMore.minTime).month() + 1;
						}
						if (
							this.props.dataMore.maxTime &&
							this.props.dataMore.value[0] == years.length - 1
						) {
							maxMonth = dayjs$1(this.props.dataMore.maxTime).month() + 1;
						}
						for (var i = minMonth; i <= maxMonth; i++) {
							months.push({
								key: i,
								value: i + "月",
								desc: "-" + (i < 10 ? "0" : "") + i
							});
						}
						data.push(months);
						if (this.props.dataMore.value.length > 2) {
							var days = [],
								minDay = 1,
								maxDay = 0;
							try {
								maxDay = dayjs$1(
									data[0][this.props.dataMore.value[0]].key +
										"-" +
										data[1][this.props.dataMore.value[1]].key +
										"-01"
								).daysInMonth();
							} catch (e) {
								maxDay = 31;
							}
							if (
								this.props.dataMore.minTime &&
								this.props.dataMore.value[0] == 0 &&
								this.props.dataMore.value[1] == 0
							) {
								minDay = dayjs$1(this.props.dataMore.minTime).date();
							}
							if (
								this.props.dataMore.maxTime &&
								this.props.dataMore.value[0] == years.length - 1 &&
								this.props.dataMore.value[1] == months.length - 1
							) {
								maxDay = dayjs$1(this.props.dataMore.maxTime).date();
							}
							for (var i = minDay; i <= maxDay; i++) {
								days.push({
									key: i,
									value: i + "日",
									desc: "-" + (i < 10 ? "0" : "") + i
								});
							}
							data.push(days);
							if (this.props.dataMore.value.length > 3) {
								var hours = [],
									minHours = 0,
									maxHours = 23;
								for (var i = minHours; i <= maxHours; i++) {
									hours.push({
										key: i,
										value: i + "时",
										desc: " " + (i < 10 ? "0" : "") + i
									});
								}
								data.push(hours);
								if (this.props.dataMore.value.length > 4) {
									var minutes = [],
										minMinutes = 0,
										maxMinutes = 59;
									for (var i = minMinutes; i <= maxMinutes; i++) {
										minutes.push({
											key: i,
											value: i + "分",
											desc: ":" + (i < 10 ? "0" : "") + i
										});
									}
									data.push(minutes);
								}
							}
						}
					}
					return data;
				},
				key: function() {},
				value: function() {},
				desc: function() {}
			};
		}

		if (Component) ZPickerTime.__proto__ = Component;
		ZPickerTime.prototype = Object.create(Component && Component.prototype);
		ZPickerTime.prototype.constructor = ZPickerTime;
		ZPickerTime.prototype.multiSelectorChange = function(ref) {
			var this$1 = this;
			var detail = ref.detail;
			//点击确定
			// console.log("multiSelectorChange:"+JSON.stringify(detail));
			this.props.dataMore.value = detail.value;
			this.data.dateSelectLast = null;
			var valueDesc = "";
			this.props.dataMore.value.forEach(function(item, index) {
				valueDesc +=
					this$1.getDateList[index][this$1.props.dataMore.value[index]].desc;
			});
			this.props.dataMore.valueDesc = valueDesc;
			setTimeout(function() {
				this$1.fire("change", this$1.props.dataMore);
				if (this$1.props._this) {
					this$1.props._this.update();
				}
			}, 0);
		};
		ZPickerTime.prototype.multiSelectorCancel = function() {
			//取消时还原
			if (this.data.dateSelectLast) {
				this.props.dataMore.value = JSON.parse(
					JSON.stringify(this.data.dateSelectLast)
				);
			}
		};
		ZPickerTime.prototype.multiSelectorColumnChange = function(ref) {
			var detail = ref.detail;
			//列发生改变
			// console.log("multiSelectorColumnChange:"+JSON.stringify(detail));
			if (!this.data.dateSelectLast) {
				this.data.dateSelectLast = JSON.parse(
					JSON.stringify(this.props.dataMore.value)
				);
			}
			this.props.dataMore.value[detail.column] = detail.value;
		};
		ZPickerTime.prototype.render = function() {
			return apivm.h(
				"view",
				{style: "width:100%;"},
				!this.props.readonly && this.props.dataMore
					? apivm.h(
							"picker",
							{
								id: this.props.id,
								mode: this.props.mode || "multiSelector",
								range: this.getDateList,
								"range-key": "value",
								value: this.props.dataMore.value,
								onCancel: this.multiSelectorCancel,
								onChange: this.multiSelectorChange,
								onColumnchange: this.multiSelectorColumnChange
							},
							this.props.children || null
					  )
					: apivm.h("view", {style: "width:100%;"}, this.props.children || null)
			);
		};

		return ZPickerTime;
	})(Component);
	ZPickerTime.css = {".picker": {zIndex: "999", position: "absolute"}};
	apivm.define("z-picker-time", ZPickerTime);

	var YChooseBook = /*@__PURE__*/ (function(Component) {
		function YChooseBook(props) {
			Component.call(this, props);
			this.data = {};
		}

		if (Component) YChooseBook.__proto__ = Component;
		YChooseBook.prototype = Object.create(Component && Component.prototype);
		YChooseBook.prototype.constructor = YChooseBook;
		YChooseBook.prototype.cleanUser = function(_item, _index, e) {
			if (this.props.noClean) {
				return;
			}
			G.delItemForKey(_index, this.props.listSelect);
			G.stopBubble(e);
		};
		YChooseBook.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: "page"},
				apivm.h(
					"scroll-view",
					{
						style: "padding:10px 3px;",
						class: "selected_notes_scroll_view",
						"scroll-x": true,
						"scroll-y": "false"
					},
					this.props.listSelect &&
						apivm.h(
							"view",
							{class: "flex-row"},
							this.props.showAddBook &&
								apivm.h(
									"view",
									{class: "addBook"},
									apivm.h("a-iconfont", {
										name: "xinzeng",
										color: "#7F7F7F",
										size: G.appFontSize + 20
									})
								),
							(Array.isArray(this.props.listSelect)
								? this.props.listSelect
								: Object.values(this.props.listSelect)
							).map(function(item$1, _eindex) {
								return apivm.h(
									"view",
									{style: "width:90px;margin-right:10px;"},
									apivm.h("image", {
										style: "width:90px;height:100px;",
										src: item$1.coverImgUrl.includes("http")
											? item$1.coverImgUrl
											: appUrl() + "image/" + item$1.coverImgUrl,
										mode: "aspectFill"
									}),
									apivm.h(
										"text",
										{
											class: "text_one" + (T.platform() == "app" ? "" : "2"),
											style: G.loadConfiguration(-2) + ";"
										},
										item$1.bookName
									),
									!this$1.props.noClean &&
										apivm.h(
											"view",
											{
												class: "select_clean",
												onClick: function(e) {
													return this$1.cleanUser(item$1, _eindex, e);
												}
											},
											apivm.h("a-iconfont", {
												name: "qingkong",
												color: "#7F7F7F",
												size: G.appFontSize + 4
											})
										)
								);
							})
						)
				)
			);
		};

		return YChooseBook;
	})(Component);
	YChooseBook.css = {
		".page": {maxHeight: "180px", height: "100%"},
		".select_clean": {
			position: "absolute",
			zIndex: "999",
			right: "-10px",
			top: "-10px"
		},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden"
		},
		".text_one2": {
			WebkitLineClamp: "1",
			WebkitBoxOrient: "vertical",
			display: "-webkit-box",
			overflow: "hidden"
		},
		".flex-row": {flexDirection: "row"},
		".addBook": {
			width: "90px",
			height: "120px",
			marginRight: "10px",
			border: "0.5px dashed lightgray",
			justifyContent: "center",
			alignItems: "center"
		}
	};
	apivm.define("y-choose-book", YChooseBook);

	var ZImage = /*@__PURE__*/ (function(Component) {
		function ZImage(props) {
			Component.call(this, props);
			this.data = {
				imgId: "img_" + getNum(),
				imgMode: this.props.mode || "aspectFill",
				imgThumbnail: isParameters(this.props.thumbnail)
					? this.props.thumbnail
					: true,
				showFilter: false,
				show: true
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					var scrollBox = this.props.scrollBox;
					if (scrollBox) {
						getBoundingClientRect("box_" + this.data.imgId, function(ret) {
							// console.log("scrollBox:----"+JSON.stringify(scrollBox) + "--" + api.winHeight);
							// console.log("img:-------"+JSON.stringify(ret));
							this$1.data.show = !(
								ret.top + ret.height < -150 || ret.top - 150 > api.winHeight
							);
						});
					}
				}
			};
		}

		if (Component) ZImage.__proto__ = Component;
		ZImage.prototype = Object.create(Component && Component.prototype);
		ZImage.prototype.constructor = ZImage;
		ZImage.prototype.load = function(e) {
			if (!this.props.src || !document.getElementById(this.data.imgId)) {
				return;
			}
			var nowProportion = 0;
			var imgWidth =
				platform() == "app"
					? e.detail.width
					: document.getElementById(this.data.imgId).naturalWidth;
			var imgHeight =
				platform() == "app"
					? e.detail.height
					: document.getElementById(this.data.imgId).naturalHeight;
			if (imgWidth && imgHeight) {
				nowProportion = Number((imgWidth / imgHeight).toFixed(2));
			}
			if (nowProportion && this.props.proportionMin && this.props.proportionMax) {
				if (
					nowProportion < Number(this.props.proportionMin) ||
					nowProportion > Number(this.props.proportionMax)
				) {
					this.data.showFilter = true;
					this.data.imgMode = "aspectFit";
				} else {
					this.data.showFilter = false;
					this.data.imgMode = this.props.mode || "aspectFill";
				}
			}
		};
		ZImage.prototype.error = function() {
			this.fire("error");
		};
		ZImage.prototype.render = function() {
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					class: "" + (this.props.class || ""),
					style:
						"overflow:hidden;border-radius: " +
						(this.props.round ? "50%" : "0px") +
						";width:100%;height:100%;" +
						(this.props.style || "")
				},
				this.data.showFilter &&
					this.data.show &&
					apivm.h("image", {
						class: "z_imageFilter",
						mode: "aspectFill",
						src: this.props.src,
						thumbnail: true
					}),
				apivm.h(
					"view",
					{class: "z_image", id: "box_" + this.data.imgId},
					this.data.show &&
						apivm.h("image", {
							id: this.data.imgId,
							class: "xy_100",
							mode: this.data.imgMode,
							src: this.props.src,
							thumbnail: this.data.imgThumbnail,
							onLoad: this.load,
							onError: this.error
						})
				)
			);
		};

		return ZImage;
	})(Component);
	ZImage.css = {
		".z_image": {
			width: "100%",
			height: "100%",
			filter: "none",
			opacity: "1",
			position: "absolute",
			left: "0",
			top: "0"
		},
		".z_imageFilter": {
			width: "100%",
			height: "100%",
			filter: "blur(4px)",
			opacity: "0.7",
			position: "relative",
			left: "0",
			top: "0"
		}
	};
	apivm.define("z-image", ZImage);

	var ZAvatar = /*@__PURE__*/ (function(Component) {
		function ZAvatar(props) {
			Component.call(this, props);
		}

		if (Component) ZAvatar.__proto__ = Component;
		ZAvatar.prototype = Object.create(Component && Component.prototype);
		ZAvatar.prototype.constructor = ZAvatar;
		ZAvatar.prototype.installed = function() {};
		ZAvatar.prototype.imgError = function(e) {};
		ZAvatar.prototype.imgLoad = function(e) {};
		ZAvatar.prototype.render = function() {
			return apivm.h(
				"view",
				{class: "class||''", style: this.props.style || ""},
				apivm.h("z-image", {
					proportionMin: "0.9",
					proportionMax: "1.1",
					class: "z_avatar_img",
					thumbnail: true,
					mode: this.props.mode || "aspectFill",
					style: "border-radius: " + (this.props.radius || "50%") + ";",
					src: G.showImg(
						(T.isObject(this.props.data)
						? this.props.data.url ||
						  this.props.data.headImg ||
						  this.props.data.senderHeadImg
						: this.props.data)
							? this.props.data
							: myjs.appUrl() + "img/default_user_head.jpg"
					),
					onError: this.imgError,
					onLoad: this.imgLoad
				})
			);
		};

		return ZAvatar;
	})(Component);
	ZAvatar.css = {".z_avatar_img": {width: "100%", height: "100%"}};
	apivm.define("z-avatar", ZAvatar);

	var YSelectsBox = /*@__PURE__*/ (function(Component) {
		function YSelectsBox(props) {
			Component.call(this, props);
			this.data = {};
		}

		if (Component) YSelectsBox.__proto__ = Component;
		YSelectsBox.prototype = Object.create(Component && Component.prototype);
		YSelectsBox.prototype.constructor = YSelectsBox;
		YSelectsBox.prototype.installed = function() {};
		YSelectsBox.prototype.clean = function(_item, _index, e) {
			if (this.props.readonly || _item.readonly) {
				return;
			}
			G.delItemForKey(_index, this.props.data);
			G.stopBubble(e);
		};
		YSelectsBox.prototype.nTouchmove = function() {
			G.touchmove();
		};
		YSelectsBox.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"scroll-view",
				{
					class: "selects_box",
					style:
						"" +
						(T.platform() != "app" ? "display: block;white-space: nowrap;" : "") +
						(this.props.dataMore.style || ""),
					"scroll-x": true,
					"scroll-y": "false"
				},
				T.isArray(this.props.data) &&
					this.props.data.map(function(item, index) {
						return !this$1.props.dataMore.genre || this$1.props.dataMore.genre == 1
							? [
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.clean(item, index);
											},
											onTouchStart: this$1.nTouchmove,
											onTouchMove: this$1.nTouchmove,
											onTouchStart: this$1.nTouchmove,
											style:
												"width:" +
												((G.appFontSize + 1) * 3 + 15) +
												"px;" +
												(T.platform() != "app" ? "display: inline-block;" : "")
										},
										apivm.h(
											"view",
											{class: "selects_item"},
											apivm.h(
												"view",
												{style: "padding:5px;"},
												apivm.h("z-avatar", {
													style: G.loadConfigurationSize(29),
													key: item.refresh,
													data: item
												}),
												!this$1.props.readonly && !item.readonly
													? apivm.h(
															"view",
															{
																class: "selects_clean",
																onClick: function(e) {
																	return this$1.clean(item, index, e);
																}
															},
															apivm.h("a-iconfont", {
																name: "qingkong",
																color: "#7F7F7F",
																size: G.appFontSize + 2
															})
													  )
													: null
											),
											apivm.h(
												"text",
												{
													class: "selects_name",
													style:
														"" +
														G.loadConfiguration() +
														G.loadConfigurationSize(50, "w") +
														"height:" +
														(G.appFontSize + 5) +
														"px;"
												},
												item.name
											)
										)
									)
							  ]
							: this$1.props.dataMore.genre == 2 || this$1.props.dataMore.genre == 4
							? [
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.clean(item, index);
											},
											onTouchStart: this$1.nTouchmove,
											onTouchMove: this$1.nTouchmove,
											onTouchStart: this$1.nTouchmove,
											style:
												"width:auto;" +
												(T.platform() != "app" ? "display: inline-block;" : "")
										},
										apivm.h(
											"view",
											{class: "selects_label_box"},
											apivm.h(
												"view",
												{class: "selects_label_item"},
												apivm.h(
													"text",
													{style: G.loadConfiguration() + "color: #666666;"},
													item.name
												)
											),
											!this$1.props.readonly && !item.readonly
												? apivm.h(
														"view",
														{
															class: "selects_clean",
															onClick: function(e) {
																return this$1.clean(item, index, e);
															}
														},
														apivm.h("a-iconfont", {
															name: "qingkong",
															color: "#7F7F7F",
															size: G.appFontSize + 2
														})
												  )
												: null
										)
									)
							  ]
							: [];
					})
			);
		};

		return YSelectsBox;
	})(Component);
	YSelectsBox.css = {
		".selects_box": {
			width: "100%",
			padding: "0 0",
			flexDirection: "row",
			alignItems: "center"
		},
		".selects_item": {
			width: "100%",
			padding: "0px 2px",
			justifyContent: "center",
			alignItems: "center",
			overflow: "hidden"
		},
		".selects_name": {
			color: "#333",
			marginTop: "1px",
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden",
			textAlign: "center"
		},
		".selects_clean": {position: "absolute", zIndex: "999", right: "0", top: "0"},
		".selects_label_box": {padding: "10px 8px 0 8px"},
		".selects_label_item": {
			borderRadius: "2px",
			border: "1px solid #CCCCCC",
			padding: "1px 8px"
		}
	};
	apivm.define("y-selects-box", YSelectsBox);

	var ZRadio = /*@__PURE__*/ (function(Component) {
		function ZRadio(props) {
			Component.call(this, props);
		}

		if (Component) ZRadio.__proto__ = Component;
		ZRadio.prototype = Object.create(Component && Component.prototype);
		ZRadio.prototype.constructor = ZRadio;
		ZRadio.prototype.render = function() {
			return apivm.h("a-iconfont", {
				style:
					(this.props.style || "") +
					";font-size: " +
					(this.props.size || 16) +
					"px;flex-shrink:0;",
				name: this.props.checked
					? this.props.type == 2
						? "danxuan_xuanzhong"
						: "yuanxingxuanzhongfill"
					: "danxuan_weixuanzhong",
				color: this.props.color
			});
		};

		return ZRadio;
	})(Component);
	apivm.define("z-radio", ZRadio);

	var YAddItem = /*@__PURE__*/ (function(Component) {
		function YAddItem(props) {
			Component.call(this, props);
			this.data = {
				propValue: "",
				dataInput: "",
				lastInput: ""
			};
			this.compute = {
				getInput: function() {
					var nowValue = T.isArray(this.props.data.value)
						? JSON.stringify(this.props.data.value)
						: this.props.data.value;
					if (this.data.propValue != nowValue) {
						this.data.propValue = nowValue;
						this.data.lastInput = this.data.dataInput;
						this.data.dataInput = this.props.data.value;
						this.inputIngChild();
					}
					if (
						this.props.data.type == "textManyMultiple" &&
						this.props.data.value.length == 0
					) {
						this.textManyMultipleAdd();
					}
				}
			};
		}

		if (Component) YAddItem.__proto__ = Component;
		YAddItem.prototype = Object.create(Component && Component.prototype);
		YAddItem.prototype.constructor = YAddItem;
		YAddItem.prototype.installed = function() {};
		YAddItem.prototype.inputIngChild = function(e) {
			var this$1 = this;

			setTimeout(function() {
				this$1.fire("input", {
					key: this$1.props.data.key,
					changeKey: "value",
					changeValue: this$1.props.data.value,
					lastValue: this$1.data.lastInput
				});
			}, 0);
		};
		YAddItem.prototype.inputIng = function(e) {
			if (T.isParameters(e)) {
				if (this.props.data.expression) {
					//有正则表达示
					e.detail.value = e.detail.value.replace(
						new RegExp(this.props.data.expression, "g"),
						""
					);
				}
				if (this.props.data.key == "maxVote" && e.detail.value) {
					//投票最大票数
					if (Number(e.detail.value) < 1) {
						e.detail.value = "1";
					} else if (Number(e.detail.value) > 100) {
						e.detail.value = "100";
					}
				}
				this.props.data.value = e.detail.value;
			}
		};
		YAddItem.prototype.clean = function() {
			this.props.data.value = "";
		};
		YAddItem.prototype.textFocus = function() {
			$("#" + this.props.data.key).focus();
		};
		YAddItem.prototype.switchChange = function(e) {
			this.props.data.value = e.detail.value;
		};
		YAddItem.prototype.cSelectbox = function(_item, _index) {
			if (this.props.readonly || this.props.data.readonly || _item.readonly) {
				return;
			}
			if (T.isArray(this.props.data.value)) {
				if (G.getItemForKey(_item.value, this.props.data.value)) {
					G.delItemForKey(_item.value, this.props.data.value);
				} else {
					this.props.data.value.push(_item.value);
				}
			} else {
				this.props.data.value = _item.value;
			}
		};
		YAddItem.prototype.isSelectValue = function(_item) {
			if (T.isArray(this.props.data.value)) {
				//多选为数组
				return G.getItemForKey(_item.value, this.props.data.value);
			} else {
				//单选时
				return _item.value == this.props.data.value;
			}
		};
		YAddItem.prototype.openSelect = function() {
			if (this.props.readonly || this.props.data.readonly) {
				return;
			}
			this.fire("openSelect", this.props.data);
		};
		YAddItem.prototype.addTextClick = function(_type) {
			if (!_type && this.props.data.textType == "time") {
				return;
			}
			this.fire("addTextClick", this.props.data);
		};
		YAddItem.prototype.pickerChange = function(ref) {
			var this$1 = this;
			var detail = ref.detail;

			this.inputIng({detail: {value: detail.valueDesc}});
			setTimeout(function() {
				this$1.addTextClick(1);
			}, 10);
		};
		YAddItem.prototype.addOptions = function() {
			this.props.data.value.push(
				JSON.parse(JSON.stringify(this.props.data.items))
			);
		};
		YAddItem.prototype.cleanOptions = function(_index) {
			G.delItemForKey(_index, this.props.data.value);
		};
		YAddItem.prototype.textManyMultipleAdd = function() {
			this.props.data.value.push(
				JSON.parse(JSON.stringify(this.props.data.items))
			);
			this.inputIngChild();
		};
		YAddItem.prototype.textManyMultipleRemove = function(_index) {
			G.delItemForKey(_index, this.props.data.value);
			this.inputIngChild();
		};
		YAddItem.prototype.identify = function(_type) {
			this.props.data.identify = _type;
			this.fire("identify", this.props.data);
		};
		YAddItem.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					class: "add_item_box",
					style:
						"display:" +
						(T.isParameters(this.props.data.hide)
							? !this.props.data.hide
								? "flex"
								: "none"
							: "flex") +
						";" +
						(this.props.data.style || "")
				},
				apivm.h(
					"view",
					{class: "add_item_warp"},
					apivm.h(
						"text",
						{
							class: "add_item_required",
							style:
								"display:" +
								(!this.props.data.noRequired ? "flex" : "none") +
								";top:20px;"
						},
						"*"
					),

					apivm.h(
						"view",
						{
							style:
								"height: 100%;flex-direction:row; align-items: flex-start;" +
								(this.props.data.lStyle || "")
						},
						apivm.h(
							"text",
							{
								class: "add_item_hint_text",
								style:
									"display:" +
									(this.props.data.type == "user" && this.props.data.value.length > 0
										? "flex"
										: "none") +
									";" +
									G.loadConfiguration(1) +
									(this.props.data.lTextStyle || "")
							},
							this.props.data.title + "(" + this.props.data.value.length + ")"
						),
						apivm.h(
							"text",
							{
								class:
									"add_item_hint_text text_one" + (T.platform() == "app" ? "" : "2"),
								style:
									"display:" +
									(this.props.data.type != "user" || this.props.data.value.length == 0
										? "flex"
										: "none") +
									";" +
									G.loadConfiguration(1) +
									"width:" +
									(G.appFontSize * 4 + (T.systemType() == "ios" ? 6 : 4)) +
									"px;" +
									(this.props.data.lTextStyle || "")
							},
							this.props.data.title
						),
						apivm.h(
							"text",
							{
								style:
									"display:" +
									(this.props.data.titleAdd ? "flex" : "none") +
									";" +
									G.loadConfiguration(-2) +
									"font-weight: 400;color: #999;line-height: 39px;"
							},
							this.props.data.titleAdd
						)
					),
					apivm.h(
						"view",
						{
							a: this.getInput,
							style:
								"flex:1;height: 100%;justify-content: center;" +
								(this.props.data.rBoxStyle || "")
						},
						this.props.data.type == "input"
							? apivm.h(
									"view",
									null,
									this.props.readonly || this.props.data.readonly
										? apivm.h(
												"view",
												{style: "flex-direction:row; align-items: center;"},
												apivm.h(
													"text",
													{style: G.loadConfiguration(1) + "font-weight: 400;color: #333;"},
													this.data.dataInput
												)
										  )
										: !this.props.data.newLine &&
												apivm.h(
													"view",
													{style: "flex-direction:row; align-items: center;"},
													apivm.h("input", {
														id: this.props.data.key,
														style: "" + G.loadConfiguration(1),
														"placeholder-style": "color:#ccc;",
														class: "add_item_input",
														type: "text",
														placeholder: this.props.data.hint,
														onInput: function(e) {
															if (typeof dataInput != "undefined") {
																dataInput = e.target.value;
															} else {
																this$1.data.dataInput = e.target.value;
															}
															this$1.inputIng(e);
														},
														type: "text",
														maxlength: this.props.data.max,
														value:
															typeof dataInput == "undefined" ? this.data.dataInput : dataInput
													}),
													apivm.h(
														"view",
														{
															onClick: this.clean,
															class: "add_item_input_icon",
															style:
																"display:" +
																(this.data.dataInput ? "flex" : "none") +
																";padding: 5px;padding-left:0;"
														},
														apivm.h("a-iconfont", {
															name: "qingkong",
															color: "#666",
															size: G.appFontSize
														})
													)
												)
							  )
							: this.props.data.type == "switch"
							? apivm.h(
									"view",
									null,
									apivm.h(
										"view",
										{
											style:
												"flex-direction:row;flex-wrap: wrap;align-items: center;justify-content: flex-end;" +
												(this.props.data.iStyle || "")
										},
										apivm.h("z-switch", {
											onChange: this.switchChange,
											disabled: this.props.readonly || this.props.data.readonly,
											dataMore: this.props.data,
											color: G.appTheme,
											height: 31
										})
									)
							  )
							: this.props.data.type == "checkbox"
							? apivm.h(
									"view",
									null,
									apivm.h(
										"view",
										{
											style:
												"flex-direction:row;flex-wrap: wrap;align-items: center;" +
												(this.props.data.iStyle || "")
										},
										(Array.isArray(this.props.data.data)
											? this.props.data.data
											: Object.values(this.props.data.data)
										).map(function(item$1, index$1) {
											return apivm.h(
												"view",
												{
													onClick: function() {
														return this$1.cSelectbox(item$1, index$1);
													},
													style:
														"flex-direction:row; align-items: center;padding:5px 0;margin-right:" +
														(index$1 != this$1.props.data.data.length - 1 ? "1" : "") +
														"0px;width:auto;"
												},
												apivm.h("z-radio", {
													checked: this$1.isSelectValue(item$1),
													size: G.appFontSize + 4,
													color:
														this$1.props.readonly ||
														this$1.props.data.readonly ||
														item$1.readonly
															? "#C5C7C9"
															: this$1.isSelectValue(item$1)
															? G.appTheme
															: "#999"
												}),
												apivm.h(
													"text",
													{
														style:
															G.loadConfiguration(1) +
															"color:#333;margin-left:6px;width:" +
															((G.appFontSize + 1) * item$1.title.length + 3) +
															"px;"
													},
													item$1.title
												)
											);
										})
									)
							  )
							: this.props.data.type == "radio"
							? apivm.h(
									"view",
									null,
									apivm.h(
										"view",
										{
											style:
												"flex-direction:row;flex-wrap: wrap;align-items: center;" +
												(this.props.data.iStyle || "")
										},
										(Array.isArray(this.props.data.data)
											? this.props.data.data
											: Object.values(this.props.data.data)
										).map(function(item$1, index$1) {
											return apivm.h(
												"view",
												{
													onClick: function() {
														return this$1.cSelectbox(item$1, index$1);
													},
													style:
														"display:" +
														(!item$1.hide ? "flex" : "none") +
														";flex-direction:row; align-items: center;padding:5px 0;" +
														(index$1 != this$1.props.data.data.length - 1
															? "margin-right:" + (this$1.props.data.rRightW || 10)
															: "0") +
														"px;width:auto;flex-shrink:0;"
												},
												apivm.h("z-radio", {
													checked: this$1.isSelectValue(item$1),
													type: "2",
													size: G.appFontSize + 4,
													color:
														this$1.props.readonly ||
														this$1.props.data.readonly ||
														item$1.readonly
															? "#C5C7C9"
															: this$1.isSelectValue(item$1)
															? G.appTheme
															: "#999"
												}),
												apivm.h(
													"text",
													{
														style:
															G.loadConfiguration(1) +
															"color:" +
															(this$1.props.readonly ||
															this$1.props.data.readonly ||
															item$1.readonly
																? "#C5C7C9"
																: "#333") +
															";margin-left:6px;"
													},
													item$1.title
												)
											);
										})
									)
							  )
							: this.props.data.type == "user"
							? apivm.h(
									"view",
									null,
									apivm.h(
										"view",
										{style: "flex-direction:row;"},
										apivm.h("view", {style: "flex:1;"}),
										!this.props.readonly && !this.props.data.readonly
											? apivm.h(
													"view",
													{
														onClick: function() {
															return this$1.openSelect();
														},
														style: "flex-shrink: 0;flex-direction:row; align-items: center;"
													},
													apivm.h(
														"text",
														{
															style:
																G.loadConfiguration(-2) +
																"color:" +
																G.appTheme +
																";margin-right:3px;flex-shrink: 0;"
														},
														"添加"
													),
													apivm.h("a-iconfont", {
														name: "gengduo1",
														color: G.appTheme,
														size: G.appFontSize + 2
													})
											  )
											: null
									)
							  )
							: this.props.data.type == "select"
							? apivm.h(
									"view",
									null,
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.openSelect();
											},
											style: "flex-direction:row; align-items: center;min-height:32px;"
										},
										apivm.h(
											"view",
											{style: "flex:1;flex-direction:row; align-items: center;"},
											this.props.data.icon
												? apivm.h("a-iconfont", {
														name: this.props.data.icon.name,
														color: this.props.data.icon.color,
														style: "margin-right:6px;",
														size: G.appFontSize + 4
												  })
												: null,
											!this.props.data.showType
												? apivm.h(
														"text",
														{
															style:
																G.loadConfiguration(1) +
																"color:" +
																(this.props.data.value.length > 0 || !this.props.data.addHint
																	? "#333"
																	: "#ccc") +
																";"
														},
														this.props.data.value.length > 0 || !this.props.data.addHint
															? T.isArray(this.props.data.value)
																? this.props.data.value
																		.map(function(e) {
																			return T.isObject(e)
																				? e.name
																				: G.getItemForKey(e, this$1.props.data.data).value;
																		})
																		.join("、")
																: G.getItemForKey(this.props.data.value, this.props.data.data)
																		.value
															: this.props.data.hint
												  )
												: null
										),
										apivm.h("a-iconfont", {
											name: "xiangxia1",
											style: "transform: rotate(-90deg);",
											color: "#303030",
											size: G.appFontSize
										})
									)
							  )
							: this.props.data.type == "text"
							? apivm.h(
									"view",
									null,
									apivm.h(
										"z-picker-time",
										{
											readonly:
												(this.props.readonly ||
													this.props.data.readonly ||
													this.props.data.textType != "time") &&
												!this.props.data.addText,
											dataMore: this.props.data.pickerTime,
											onChange: this.pickerChange
										},
										apivm.h(
											"view",
											{style: "flex-direction:row; align-items: center;min-height:40px;"},
											apivm.h(
												"view",
												null,
												this.props.data.firstIcon &&
													this.props.data.firstIcon.name &&
													apivm.h("a-iconfont", {
														style: "margin-right:6px;",
														name: this.props.data.firstIcon.name,
														color: this.props.data.firstIcon.color || "#333",
														size: G.appFontSize
													})
											),
											apivm.h(
												"text",
												{
													style:
														G.loadConfiguration(1) +
														"flex:1;color:" +
														(this.props.data.value.length > 0 || !this.props.data.addHint
															? "#333"
															: "#ccc") +
														";"
												},
												this.props.data.value.length > 0 || !this.props.data.addHint
													? this.props.data.value
													: this.props.data.hint
											),
											apivm.h(
												"view",
												null,
												this.props.data.addText &&
													apivm.h(
														"text",
														{
															onClick: function() {
																return this$1.addTextClick(0);
															},
															style:
																G.loadConfiguration(1) +
																"color:" +
																(this.props.data.addTColor || G.appTheme) +
																";"
														},
														this.props.data.addText
													)
											),
											apivm.h(
												"view",
												null,
												!this.props.readonly &&
													!this.props.data.readonly &&
													apivm.h("a-iconfont", {
														name: "xiangxia1",
														style: "transform: rotate(-90deg);margin-left:5px;",
														color: "#303030",
														size: G.appFontSize
													})
											)
										)
									)
							  )
							: this.props.data.type == "npcImage"
							? apivm.h(
									"view",
									null,
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.openSelect();
											},
											style: "flex-direction:row; align-items: center;"
										},
										apivm.h("z-avatar", {
											style: "" + G.loadConfigurationSize([20, 32]),
											radius: "0",
											data: this.props.data.value
										}),
										apivm.h("view", {style: "flex:1;"}),
										!this.props.readonly &&
											!this.props.data.readonly &&
											apivm.h("a-iconfont", {
												name: "xiangxia1",
												style: "transform: rotate(-90deg);",
												color: "#303030",
												size: G.appFontSize
											})
									)
							  )
							: this.props.data.type == "attach"
							? apivm.h(
									"view",
									null,
									apivm.h(
										"view",
										{style: "flex-direction:row;"},
										apivm.h("view", {style: "width:1px;flex:1;"}),
										!this.props.readonly &&
											!this.props.data.readonly &&
											(T.isNumber(this.props.data.max) && this.props.data.max > 0
												? this.props.data.max > this.props.data.value.length
												: true)
											? apivm.h(
													"view",
													{
														onClick: function() {
															return this$1.openSelect();
														},
														style:
															"flex-shrink: 0;flex-direction:row; align-items: center;width: auto;"
													},
													apivm.h(
														"text",
														{
															style:
																G.loadConfiguration(-2) +
																"color:" +
																G.appTheme +
																";margin-right:3px;flex-shrink: 0;"
														},
														"添加"
													),
													apivm.h("a-iconfont", {
														name: "gengduo1",
														color: G.appTheme,
														size: G.appFontSize + 2
													})
											  )
											: null
									)
							  )
							: this.props.data.type == "options"
							? apivm.h(
									"view",
									null,
									apivm.h(
										"view",
										{style: "flex-direction:row;"},
										apivm.h("view", {style: "width:1px;flex:1;"}),
										apivm.h(
											"view",
											{
												"v-if":
													"!readonly && !data.readonly && (T.isNumber(data.max)&&data.max!0?data.max>data.value.length:true)",
												onClick: function() {
													return this$1.addOptions();
												},
												style:
													"flex-shrink: 0;flex-direction:row; align-items: center;width: auto;"
											},
											apivm.h(
												"text",
												{
													style:
														G.loadConfiguration(-2) +
														"color:" +
														G.appTheme +
														";margin-right:3px;flex-shrink: 0;"
												},
												"添加"
											),
											apivm.h("a-iconfont", {
												name: "gengduo1",
												color: G.appTheme,
												size: G.appFontSize + 2
											})
										)
									)
							  )
							: this.props.data.type == "addMore"
							? apivm.h(
									"view",
									null,
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.openSelect();
											},
											style: "flex-direction:row; align-items: center;min-height:32px;"
										},
										apivm.h("view", {
											style: "flex:1;flex-direction:row; align-items: center;"
										}),
										apivm.h("a-iconfont", {
											name: "xiangxia1",
											style: "transform: rotate(-90deg);",
											color: "#303030",
											size: G.appFontSize
										})
									)
							  )
							: this.props.data.type == "textManyMultiple"
							? apivm.h(
									"view",
									null,
									apivm.h(
										"view",
										{style: "flex-direction:row;"},
										apivm.h("view", {style: "flex:1;"}),
										!this.props.readonly && !this.props.data.readonly
											? apivm.h(
													"view",
													{
														onClick: function() {
															return this$1.textManyMultipleAdd();
														},
														style: "flex-direction:row; align-items: center;"
													},
													apivm.h(
														"text",
														{
															style:
																G.loadConfiguration(-2) +
																"color:" +
																G.appTheme +
																";margin-right:3px;"
														},
														"新增"
													),
													apivm.h("a-iconfont", {
														name: "gengduo1",
														color: G.appTheme,
														size: G.appFontSize + 2
													})
											  )
											: null
									)
							  )
							: this.props.data.type == "hasBook"
							? apivm.h(
									"view",
									null,
									apivm.h(
										"view",
										{style: "flex-direction:row;"},
										apivm.h("view", {style: "flex:1;"}),
										!this.props.readonly &&
											!this.props.data.readonly &&
											this.props.data.value.length < this.props.data.max
											? apivm.h(
													"view",
													{
														onClick: function() {
															return this$1.openSelect();
														},
														style: "flex-shrink: 0;flex-direction:row; align-items: center;"
													},
													apivm.h(
														"text",
														{
															style:
																G.loadConfiguration(-2) +
																"color:" +
																G.appTheme +
																";margin-right:3px;flex-shrink: 0;"
														},
														"添加"
													),
													apivm.h("a-iconfont", {
														name: "gengduo1",
														color: G.appTheme,
														size: G.appFontSize + 2
													})
											  )
											: null
									)
							  )
							: [],
						this.props.data.titleHint &&
							apivm.h(
								"view",
								null,
								apivm.h(
									"text",
									{style: G.loadConfiguration(-2) + "color:#666666;text-align: right;"},
									this.props.data.titleHint.replace("{max}", this.props.data.max)
								)
							)
					)
				),
				apivm.h(
					"view",
					{
						style:
							"margin-top:" +
							(this.props.data.type == "textarea" ||
							this.props.data.type == "telPhoneCode" ||
							this.props.data.newLine
								? "-10"
								: "0") +
							"px;"
					},
					this.props.data.type == "input" && this.props.data.newLine
						? apivm.h(
								"view",
								null,
								apivm.h(
									"view",
									{style: "flex-direction:row; align-items: center;padding:6px 0;"},
									apivm.h("input", {
										id: this.props.data.key,
										style: "" + G.loadConfiguration(1),
										"placeholder-style": "color:#ccc;",
										class: "add_item_input",
										type: "text",
										placeholder: this.props.data.hint,
										onInput: function(e) {
											if (typeof dataInput != "undefined") {
												dataInput = e.target.value;
											} else {
												this$1.data.dataInput = e.target.value;
											}
											this$1.inputIng(e);
										},
										type: "text",
										maxlength: this.props.data.max,
										value:
											typeof dataInput == "undefined" ? this.data.dataInput : dataInput
									}),
									apivm.h(
										"view",
										{
											onClick: this.clean,
											class: "add_item_input_icon",
											style:
												"display:" +
												(this.data.dataInput ? "flex" : "none") +
												";padding: 5px;padding-left:0;"
										},
										apivm.h("a-iconfont", {
											name: "qingkong",
											color: "#666",
											size: G.appFontSize
										})
									)
								)
						  )
						: this.props.data.type == "textarea"
						? apivm.h(
								"view",
								null,
								apivm.h(
									"view",
									{
										style:
											"min-height: " +
											(this.props.data.height || "110") +
											"px;" +
											(T.systemType() != "android" ? "max-height: 300px;" : "")
									},
									this.props.readonly || this.props.data.readonly
										? apivm.h("z-rich-text", {
												style:
													G.loadConfiguration(1) +
													"color:#333;min-height: " +
													(this.props.data.height || "110") +
													"px;",
												nodes: this.data.dataInput
										  })
										: apivm.h("textarea", {
												id: this.props.data.key,
												style:
													G.loadConfiguration(1) +
													"min-height: " +
													(this.props.data.height || "110") +
													"px;",
												class: "add_item_textarea",
												placeholder: this.props.data.hint,
												"auto-height": true,
												"placeholder-style": "color:#999;",
												value: this.props.data.value,
												"confirm-type": "return",
												onInput: this.inputIng,
												maxlength: this.props.data.max
										  })
								),
								apivm.h(
									"view",
									{style: "flex-direction:row-reverse;margin:5px 0 2px;"},
									apivm.h(
										"text",
										{style: G.loadConfiguration(-4) + "color: #999999;"},
										"已有字数",
										this.data.dataInput.length
									)
								),
								T.platform() == "app" &&
									!this.props.readonly &&
									!this.props.data.readonly
									? apivm.h(
											"view",
											null,
											apivm.h("view", {style: "height:1px;background:#F5F5F5;"}),
											apivm.h(
												"view",
												{style: "flex-direction:row; align-items: center;"},
												apivm.h(
													"view",
													{
														onClick: function() {
															return this$1.identify(1);
														},
														class: "add_item_textarea_add_item",
														style: ""
													},
													apivm.h("a-iconfont", {
														name: "tupian",
														color: "#333",
														size: G.appFontSize + 10
													}),
													apivm.h(
														"text",
														{style: G.loadConfiguration(1) + "color: #333;margin-left:10px;"},
														"图片识别"
													)
												),
												apivm.h("view", {
													style: "height:30px;width:1px;background:#F5F5F5;"
												}),
												apivm.h(
													"view",
													{
														onClick: function() {
															return this$1.identify(2);
														},
														class: "add_item_textarea_add_item",
														style: ""
													},
													apivm.h("a-iconfont", {
														name: "yuyin",
														color: "#333",
														size: G.appFontSize + 10
													}),
													apivm.h(
														"text",
														{style: G.loadConfiguration(1) + "color: #333;margin-left:10px;"},
														"语音输入"
													)
												)
											)
									  )
									: null
						  )
						: this.props.data.type == "user" && this.props.data.value.length > 0
						? apivm.h(
								"view",
								null,
								apivm.h(
									"view",
									{
										style:
											"margin:" +
											(this.props.data.genre == 2 ? "-5" : "0") +
											"px 0 10px 0;"
									},
									apivm.h("y-selects-box", {
										readonly: this.props.readonly || this.props.data.readonly,
										dataMore: {genre: this.props.data.genre},
										data: this.props.data.value
									})
								)
						  )
						: this.props.data.type == "hasBook" && this.props.data.value.length > 0
						? apivm.h(
								"view",
								null,
								apivm.h(
									"view",
									{style: "margin:0px 0 10px 0;max-height:142px;"},
									apivm.h("y-choose-book", {listSelect: this.props.data.value})
								)
						  )
						: this.props.data.type == "select" &&
						  this.props.data.showType == 2 &&
						  this.props.data.value.length > 0
						? apivm.h(
								"view",
								{style: "margin-bottom:17px;"},
								(Array.isArray(this.props.data.value)
									? this.props.data.value
									: Object.values(this.props.data.value)
								).map(function(item$1, index$1) {
									return apivm.h(
										"view",
										{style: "background: #F8F9FA;margin-top:10px;padding:10px;"},
										apivm.h("z-rich-text", {
											expand: 45,
											style: G.loadConfiguration(1) + "color:#333;",
											nodes:
												"" +
												G.getItemForKey(item$1, this$1.props.data.data).value +
												(this$1.props.data.times
													? " " +
													  (this$1.props.data.times["picker" + item$1] || {}).valueDesc
													: "")
										})
									);
								})
						  )
						: this.props.data.type == "attach" && this.props.data.value.length > 0
						? apivm.h(
								"view",
								null,
								apivm.h("y-attachments", {
									style: "margin: -5px 0 10px 0;",
									type: "2",
									data: this.props.data.value
								})
						  )
						: this.props.data.type == "options" && this.props.data.value.length > 0
						? apivm.h(
								"view",
								null,
								(Array.isArray(this.props.data.value)
									? this.props.data.value
									: Object.values(this.props.data.value)
								).map(function(item$1, index$1) {
									return apivm.h(
										"view",
										{style: "flex-direction:row;align-items: center;min-height:56px;"},
										apivm.h(
											"view",
											{style: "width:1px;height:100%;flex:1;"},
											apivm.h(
												"view",
												{style: "flex:1;flex-direction:row;align-items: center;"},
												(Array.isArray(item$1) ? item$1 : Object.values(item$1)).map(
													function(nItem, nIndex) {
														return apivm.h(
															"view",
															{style: "width:" + nItem.width + ";"},
															nItem.type == "input"
																? apivm.h(
																		"view",
																		{style: "flex-direction:row; align-items: center;"},
																		apivm.h("input", {
																			style: "" + G.loadConfiguration(1),
																			"placeholder-style": "color:#ccc;",
																			class: "add_item_input",
																			type: "text",
																			placeholder: "请输入",
																			onInput: function(e) {
																				if (typeof nItem != "undefined") {
																					nItem.value = e.target.value;
																				} else {
																					this$1.data.nItem.value = e.target.value;
																				}
																				this$1.inputIngChild(e);
																			},
																			value:
																				typeof nItem == "undefined"
																					? this$1.data.nItem.value
																					: nItem.value
																		})
																  )
																: []
														);
													}
												)
											),
											apivm.h("view", {
												style:
													"height:1px;background:" +
													(index$1 != this$1.props.data.value.length - 1
														? "#F5F5F5"
														: "transparent") +
													";"
											})
										),
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.cleanOptions(index$1);
												},
												style: "padding: 8px;margin:0 -8px;"
											},
											apivm.h("a-iconfont", {
												name: "jianshao1",
												color: G.appTheme,
												size: G.appFontSize + 2
											})
										)
									);
								})
						  )
						: this.props.data.type == "textManyMultiple" &&
						  this.props.data.value.length > 0
						? apivm.h(
								"view",
								null,
								(Array.isArray(this.props.data.value)
									? this.props.data.value
									: Object.values(this.props.data.value)
								).map(function(item$1, index$1) {
									return apivm.h(
										"view",
										null,
										apivm.h(
											"view",
											{style: "flex-direction:row;align-items: center;"},
											apivm.h("view", {style: "flex:1;"}),
											!this$1.props.readonly && !this$1.props.data.readonly && index$1
												? apivm.h(
														"view",
														{
															onClick: function() {
																return this$1.textManyMultipleRemove(index$1);
															},
															style:
																"flex-direction:row; align-items: center;margin-bottom:10px;"
														},
														apivm.h(
															"text",
															{
																style:
																	G.loadConfiguration(-2) +
																	"color:" +
																	G.appTheme +
																	";margin-right:3px;"
															},
															"删除"
														),
														apivm.h("a-iconfont", {
															name: "jianshao1",
															color: G.appTheme,
															size: G.appFontSize + 2
														})
												  )
												: null
										),
										apivm.h(
											"view",
											{style: "flex-direction:row;flex-wrap: wrap;"},
											(Array.isArray(item$1) ? item$1 : Object.values(item$1)).map(
												function(nItem, nIndex) {
													return apivm.h(
														"view",
														{
															style:
																"background: #F4F5F7;border-radius: 4px;padding:1px 5px 1px 15px;flex-direction:row; align-items: center;" +
																nItem.style
														},
														apivm.h("input", {
															style: "" + G.loadConfiguration(1),
															"placeholder-style": "color:#ccc;",
															class: "add_item_input",
															type: "text",
															placeholder: nItem.hint,
															onInput: function(e) {
																if (typeof nItem != "undefined") {
																	nItem.value = e.target.value;
																} else {
																	this$1.data.nItem.value = e.target.value;
																}
																this$1.inputIngChild(e);
															},
															value:
																typeof nItem == "undefined"
																	? this$1.data.nItem.value
																	: nItem.value
														})
													);
												}
											)
										)
									);
								})
						  )
						: this.props.data.type == "telPhoneCode"
						? apivm.h(
								"view",
								{style: "margin-bottom: 10px;"},
								apivm.h(
									"view",
									{
										style:
											"flex-direction:row;justify-content: space-between;" +
											(this.props.data.style || "")
									},
									apivm.h("input", {
										type: "text",
										placeholder: this.props.data.hint,
										"placeholder-style": "color:#ccc;",
										class: "add_item_input",
										onInput: function(e) {
											if (typeof dataInput != "undefined") {
												dataInput = e.target.value;
											} else {
												this$1.data.dataInput = e.target.value;
											}
											this$1.inputIng(e);
										},
										value:
											typeof dataInput == "undefined" ? this.data.dataInput : dataInput
									}),
									apivm.h(
										"view",
										{onClick: this.openSelect, style: "min-width:90px;"},
										apivm.h(
											"text",
											{
												style:
													"text-align:center;border-radius: 10px;padding: 4px 10px;line-height: 27px;" +
													G.loadConfiguration(-2) +
													"color:#FFFFFF;background:" +
													(this.props.data.codeColor || G.appTheme)
											},
											this.props.data.codeText
										)
									)
								)
						  )
						: []
				),
				apivm.h("view", {
					style:
						"height:1px;background:" +
						(this.props.data.line ? "#F5F5F5" : "transparent") +
						";"
				})
			);
		};

		return YAddItem;
	})(Component);
	YAddItem.css = {
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden"
		},
		".text_one2": {
			WebkitLineClamp: "1",
			WebkitBoxOrient: "vertical",
			display: "-webkit-box",
			overflow: "hidden"
		},
		".add_item_box": {background: "#FFF", padding: "0 16px"},
		".add_item_warp": {
			width: "100%",
			padding: "10px 0",
			flexDirection: "row",
			alignItems: "flex-start"
		},
		".add_item_hint_text": {
			color: "#333",
			marginRight: "14px",
			fontWeight: "600",
			lineHeight: "39px"
		},
		".add_item_input": {
			marginRight: "10px",
			width: "1px",
			flex: "1",
			background: "transparent",
			borderColor: "transparent",
			color: "#333",
			padding: "3px 0",
			height: "30px"
		},
		".add_item_input::placeholder": {color: "#ccc"},
		".add_item_input_icon": {
			alignItems: "center",
			justifyContent: "center",
			width: "auto"
		},
		".add_item_textarea": {
			borderColor: "transparent !important",
			borderRadius: "0",
			padding: "0",
			width: "100%"
		},
		".add_item_textarea::placeholder": {color: "#999"},
		".add_item_textarea_add_item": {
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center",
			flex: "1",
			padding: "10px"
		},
		".add_item_required": {
			position: "absolute",
			zIndex: "999",
			color: "#FF0000",
			left: "-8px",
			fontSize: "16px",
			lineHeight: "20px"
		}
	};
	apivm.define("y-add-item", YAddItem);

	var ImagePreviewer = /*@__PURE__*/ (function(Component) {
		function ImagePreviewer(props) {
			Component.call(this, props);
			this.data = {
				index: 1,
				activeIndex: 0,
				indicator: false,
				statusBarStyle: ""
			};
		}

		if (Component) ImagePreviewer.__proto__ = Component;
		ImagePreviewer.prototype = Object.create(Component && Component.prototype);
		ImagePreviewer.prototype.constructor = ImagePreviewer;
		ImagePreviewer.prototype.current = function() {
			return T.platform() == "web"
				? this.props.activeIndex
				: this.data.activeIndex;
		};
		ImagePreviewer.prototype.closePre = function() {
			this.close();
		};
		ImagePreviewer.prototype.close = function() {
			G.imagePreviewer.show = false;
			T.sendEvent("updatePage");
		};
		ImagePreviewer.prototype.installed = function() {
			// 修复顶部状态栏样式
			if (this.props.type == 1) {
				// api.setStatusBarStyle({ style: "dark" });
				this.data.indicator = true;
			} else if (this.props.type == 2) {
				// api.setStatusBarStyle({ style: "white" });
				this.data.indicator = false;
			}
			this.data.activeIndex = this.props.activeIndex;
			// 滑动到指定图片显示
			if (this.props.activeIndex) {
				this.data.index = parseInt(this.props.activeIndex) + 1;
			}
		};
		ImagePreviewer.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: "image-previewer-page"},
				apivm.h(
					"view",
					{style: "height:1px;flex:1;justify-content: center;"},
					apivm.h(
						"view",
						{style: "height:1px;flex:1;justify-content: center;"},
						apivm.h(
							"swiper",
							{
								onClick: function() {
									return this$1.closePre();
								},
								class: "image-previewer-swiper",
								circular: true,
								current: this.current(),
								"indicator-dots": "true",
								"indicator-color": "#737373",
								"indicator-active-color": "#ffffff"
							},
							(Array.isArray(this.props.imgs)
								? this.props.imgs
								: Object.values(this.props.imgs)
							).map(function(item$1, index$1) {
								return apivm.h(
									"swiper-item",
									{
										onClick: function() {
											return this$1.closePre();
										},
										style: "height:100%;"
									},
									apivm.h("image", {
										class: "image-previewer-img",
										src: G.showImg(item$1),
										thumbnail: "false",
										mode: "aspectFit"
									})
								);
							})
						)
					)
				)
			);
		};

		return ImagePreviewer;
	})(Component);
	ImagePreviewer.css = {
		".image-previewer-page": {height: "100%", background: "#000"},
		".image-previewer-swiper": {height: "100%"},
		".image-previewer-img": {height: "100%", width: "100%"},
		".image-previewer-header": {
			display: "flex",
			flexDirection: "row",
			alignItems: "center",
			flexWrap: "nowrap",
			padding: "0 5px"
		},
		".image-previewer-back": {
			width: "30px",
			height: "45px",
			backgroundRepeat: "no-repeat",
			backgroundPosition: "center",
			backgroundSize: "20px"
		},
		".image-previewer-close": {width: "30px", height: "45px"},
		".image-previewer-title": {
			flex: "1",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexWrap: "nowrap",
			fontSize: "18px",
			textAlign: "center",
			textOverflow: "ellipsis",
			overflow: "hidden",
			whiteSpace: "nowrap",
			color: "#fff",
			height: "45px",
			lineHeight: "45px"
		},
		".image-previewer-placeholder": {
			width: "30px",
			height: "45px",
			marginRight: "5px"
		},
		".image-previewer-right": {width: "30px", height: "45px"}
	};
	apivm.define("image-previewer", ImagePreviewer);

	var ADivider = /*@__PURE__*/ (function(Component) {
		function ADivider(props) {
			Component.call(this, props);
			this.compute = {
				boxClass: function() {
					return "a-divider " + (this.props.class || "");
				},
				lineStyle: function() {
					return this.props["line-color"]
						? "border-top-color:" + this.props["line-color"] + ";"
						: "";
				},
				textStyle: function() {
					return (
						(this.props.color ? "color:" + this.props.color + ";" : "") +
						"" +
						(this.props.style || "")
					);
				}
			};
		}

		if (Component) ADivider.__proto__ = Component;
		ADivider.prototype = Object.create(Component && Component.prototype);
		ADivider.prototype.constructor = ADivider;
		ADivider.prototype.lineClass = function(position) {
			var width =
				this.props["content-position"] == position
					? "a-divider_line-width"
					: "a-divider_line-flex";
			var style = this.props.dashed
				? "a-divider_line-dashed"
				: "a-divider_line-solid";
			return "a-divider_line " + width + " " + style;
		};
		ADivider.prototype.render = function() {
			return apivm.h(
				"view",
				{class: this.boxClass, style: this.props.style || ""},
				this.props.content
					? [
							apivm.h("view", {class: this.lineClass("left"), style: this.lineStyle}),
							apivm.h(
								"text",
								{class: "a-divider_text", style: this.textStyle},
								this.props.content
							),
							apivm.h("view", {class: this.lineClass("right"), style: this.lineStyle})
					  ]
					: apivm.h("view", {class: this.lineClass("center"), style: this.lineStyle})
			);
		};

		return ADivider;
	})(Component);
	ADivider.css = {
		".a-divider": {flexDirection: "row", alignItems: "center"},
		".a-divider_line": {borderTopWidth: "1px", borderTopColor: "#F6F6F6"},
		".a-divider_line-solid": {borderTopStyle: "solid"},
		".a-divider_line-dashed": {borderTopStyle: "dashed"},
		".a-divider_line-width": {width: "10%"},
		".a-divider_line-flex": {flex: "1"},
		".a-divider_text": {
			fontSize: "14px",
			color: "#969799",
			padding: "0 16px",
			textAlign: "center",
			maxWidth: "75%"
		}
	};
	apivm.define("a-divider", ADivider);

	var ZActionsheet = /*@__PURE__*/ (function(Component) {
		function ZActionsheet(props) {
			Component.call(this, props);
			this.data = {
				show: false
			};
			this.compute = {
				isShow: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							if (this.props.dataMore.dotClose) {
								setTimeout(function() {
									this$1.props.dataMore.dotClose = false;
								}, 500);
							}
						}
					}
				}
			};
		}

		if (Component) ZActionsheet.__proto__ = Component;
		ZActionsheet.prototype = Object.create(Component && Component.prototype);
		ZActionsheet.prototype.constructor = ZActionsheet;
		ZActionsheet.prototype.installed = function() {};
		ZActionsheet.prototype.closePage = function() {
			if (this.props.dataMore.dotClose) {
				return;
			}
			this.props.dataMore.show = false;
			T.sendEvent("updatePage");
		};
		ZActionsheet.prototype.itemClick = function(_item, _index) {
			var this$1 = this;

			if (this.props.dataMore.dotClose) {
				return;
			}
			if (_item.disabled) {
				return;
			}
			if (T.isParameters(this.props.active)) {
				this.props.active.key = _item.key;
			}
			_item.buttonIndex = _index + 1;
			setTimeout(function() {
				this$1.fire("click", _item);
				this$1.closePage();
			}, 0);
		};
		ZActionsheet.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.isShow,
					class: "actionSheet_box",
					style: "display:" + (this.props.dataMore.show ? "flex" : "none") + ";"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style: "flex:1;min-height:30%;flex-shrink: 0;"
				}),
				apivm.h(
					"scroll-view",
					{class: "actionSheet_warp", style: "flex-shrink: 1;", "scroll-y": true},
					apivm.h(
						"view",
						null,
						this.props.dataMore.title &&
							apivm.h(
								"text",
								{
									style:
										G.loadConfiguration(-2 + 1) +
										";color:#aaa;text-align: center;padding: 15px;"
								},
								this.props.dataMore.title
							)
					),
					this.props.data.map(function(item, index) {
						return (
							(T.isParameters(item.show) ? item.show : true) && [
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.itemClick(item, index);
										},
										class: "actionSheet_item",
										style:
											"justify-content:" +
											(item.type ? "flex-start" : "center") +
											";opacity: " +
											(item.disabled ? "0.3" : "1") +
											";"
									},
									apivm.h(
										"view",
										null,
										T.isParameters(this$1.props.active) &&
											T.isParameters(this$1.props.active.direction) &&
											this$1.props.active.direction >= 0 &&
											item.key == this$1.props.active.key &&
											apivm.h("a-iconfont", {
												style:
													"margin-left:-25px;margin-right:10px;transform: rotate(" +
													(this$1.props.active.direction == 1 ? "0" : "180") +
													"deg);",
												name: "zhixiangxia",
												color: G.appTheme,
												size: G.appFontSize - 1
											})
									),
									apivm.h(
										"view",
										null,
										item.type == "img"
											? apivm.h("img", {src: "", alt: ""})
											: item.type == "icon"
											? apivm.h("a-iconfont", {
													style:
														"font-weight:" + (item.weight || "400") + ";margin-right:10px;",
													name: item.src,
													color: item.color || "#333",
													size:
														G.appFontSize +
														(T.isParameters(item.size) ? Number(item.size) : 4)
											  })
											: ""
									),
									apivm.h(
										"text",
										{
											style:
												G.loadConfiguration(-2 + 1) +
												";color:" +
												(item.key ==
												(T.isParameters(this$1.props.active) && this$1.props.active.key)
													? G.appTheme
													: "#333")
										},
										item.value
									)
								),
								index != this$1.props.data.length - 1 &&
									!this$1.props.cancel &&
									!this$1.props.dataMore.cancel &&
									apivm.h(
										"view",
										{style: "width:100%;height:1px;padding:0 16px;"},
										apivm.h("a-divider", null)
									)
							]
						);
					})
				),
				apivm.h(
					"view",
					null,
					(this.props.cancel || this.props.dataMore.cancel) &&
						apivm.h(
							"view",
							{
								onClick: function() {
									return this$1.closePage();
								},
								class: "actionSheet_item",
								style:
									"justify-content:center;border-top:10px solid #f6f6f6;background: #FFF; "
							},
							apivm.h(
								"text",
								{style: G.loadConfiguration(-2) + ";color:#333"},
								this.props.dataMore.cancel || "取消"
							)
						)
				),
				apivm.h("view", {
					style: "background:#fff;padding-bottom:" + T.safeArea().bottom + "px;"
				})
			);
		};

		return ZActionsheet;
	})(Component);
	ZActionsheet.css = {
		".actionSheet_box": {
			position: "absolute",
			zIndex: "999",
			left: "0px",
			top: "0px",
			width: "100%",
			height: "100%",
			background: "rgba(0,0,0,0.4)"
		},
		".actionSheet_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px",
			height: "auto"
		},
		".actionSheet_item": {
			width: "100%",
			minHeight: "60px",
			alignItems: "center",
			flexDirection: "row",
			padding: "0 16px"
		}
	};
	apivm.define("z-actionSheet", ZActionsheet);

	var ZSearch = /*@__PURE__*/ (function(Component) {
		function ZSearch(props) {
			Component.call(this, props);
			this.data = {
				propValue: ""
			};
			this.compute = {
				id: function() {
					return this.props.id || "input";
				}
			};
		}

		if (Component) ZSearch.__proto__ = Component;
		ZSearch.prototype = Object.create(Component && Component.prototype);
		ZSearch.prototype.constructor = ZSearch;
		ZSearch.prototype.installed = function() {
			var this$1 = this;
			if (
				T.isParameters(this.props.dataMore) ? this.props.dataMore.autofocus : false
			) {
				//是否自动获取焦点
				setTimeout(function() {
					$("#" + this$1.id).focus();
				}, 150);
			}
		};
		ZSearch.prototype.inputConfirm = function() {
			var this$1 = this;

			$("#" + this.id).blur();
			setTimeout(function() {
				this$1.fire("confirm", {});
			}, 0);
		};
		ZSearch.prototype.inputIng = function() {
			var this$1 = this;

			if (this.props.dataMore.expression) {
				//有正则表达示
				this.props.dataMore.input = this.props.dataMore.input.replace(
					new RegExp(this.props.dataMore.expression, "g"),
					""
				);
			}
			setTimeout(function() {
				this$1.fire("input", {});
			}, 0);
		};
		ZSearch.prototype.inputBlur = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.fire("blur", {});
			}, 0);
		};
		ZSearch.prototype.inputFocus = function() {
			var this$1 = this;

			$("#" + this.id).focus();
			setTimeout(function() {
				this$1.fire("focus", {});
			}, 0);
		};
		ZSearch.prototype.clean = function() {
			var this$1 = this;

			if (!this.i) {
				this.props.dataMore.input = "";
				this.i = 1;
			} else {
				this.props.dataMore.input = " ".repeat(this.i++ % 2);
				this.props.dataMore.input = " ".repeat(this.i++ % 2);
			}
			this.inputIng();
			setTimeout(function() {
				this$1.fire("clean", {});
			}, 0);
		};
		ZSearch.prototype.openLeftFilters = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.fire("leftFilters", {});
			}, 0);
		};
		ZSearch.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					class: "z_search_box " + (this.props.class || ""),
					style:
						"\n\tborder-top-left-radius: " +
						(this.props.round ? "30" : "4") +
						"px;\n\tborder-top-right-radius: " +
						(this.props.round ? "30" : "4") +
						"px;\n\tborder-bottom-left-radius: " +
						(this.props.round ? "30" : "4") +
						"px;\n\tborder-bottom-right-radius: " +
						(this.props.round ? "30" : "4") +
						"px;\n\tbackground: " +
						(this.props.bg || "#F4F5F7") +
						";\n\tjustify-content: " +
						(this.props.justify ||
							(this.props.type == "1" ? "center" : "flex-start")) +
						";\n\t" +
						(this.props.style || "")
				},
				apivm.h(
					"view",
					{style: "width:auto;height:auto;"},
					T.isParameters(this.props.dataMore) &&
						this.props.dataMore.leftFilters &&
						apivm.h(
							"view",
							{
								onClick: function() {
									return this$1.openLeftFilters();
								},
								style:
									"margin-left:-5px;margin-right:15px;flex-direction:row;align-items: center;"
							},
							apivm.h(
								"text",
								{
									style:
										G.loadConfiguration(1) +
										"font-weight: 400;color: #333;margin-right:4px;"
								},
								this.props.leftFiltersText
							),
							apivm.h("a-iconfont", {
								name: "xiangxia",
								color: "#333",
								size: G.appFontSize - 4
							}),
							apivm.h("view", {
								style: "width:1px;height:24px;background:#E5E7E8;margin-left:8px;"
							})
						)
				),
				apivm.h(
					"view",
					{style: "width:auto;height:auto;"},
					(!T.isParameters(this.props.dataMore) ||
						(!this.props.dataMore.dotIcon && !this.props.dataMore.leftFilters)) &&
						apivm.h(
							"view",
							{class: "z_search_input_icon", style: "margin-right:10px;"},
							apivm.h("a-iconfont", {
								class: "z_search_icon",
								name: "sousuo",
								color: "#999",
								size: G.appFontSize - 2
							})
						)
				),
				this.props.type == "1"
					? apivm.h(
							"text",
							{
								class: "z_search_text",
								style:
									"color:#999;line-height: " +
									(G.appFontSize + 2) +
									"px;" +
									G.loadConfiguration(-2)
							},
							this.props.placeholder
					  )
					: [
							apivm.h("input", {
								id: this.id,
								style:
									"" + G.loadConfiguration() + (this.props.dataMore.inputStyle || ""),
								"placeholder-style": "color:#999;",
								class: "z_search_input",
								type: this.props.inputType || "text",
								placeholder: this.props.placeholder,
								onInput: function(e) {
									if (typeof this$1 != "undefined") {
										this$1.props.dataMore.input = e.target.value;
									} else {
										this$1.data.this.props.dataMore.input = e.target.value;
									}
									this$1.inputIng(e);
								},
								maxlength: this.props.dataMore.maxlength,
								"confirm-type": this.props.confirmType || "search",
								"keyboard-type": this.props.keyboardType || "default",
								onConfirm: this.inputConfirm,
								onBlur: this.inputBlur,
								onFocus: this.inputFocus,
								value:
									typeof this == "undefined"
										? this.data.this.props.dataMore.input
										: this.props.dataMore.input
							}),
							this.props.dataMore.input &&
								!this.props.dataMore.dotCleanIcon &&
								apivm.h(
									"view",
									{
										onClick: this.clean,
										class: "z_search_input_icon",
										style: "padding: 5px;padding-left:0;margin-right: -10px;"
									},
									apivm.h("a-iconfont", {
										name: "qingkong",
										color: "#666",
										size: G.appFontSize
									})
								)
					  ]
			);
		};

		return ZSearch;
	})(Component);
	ZSearch.css = {
		".z_search_box": {
			width: "100%",
			height: "100%",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			padding: "2px 15px"
		},
		".z_search_text": {marginLeft: "4px"},
		".z_search_input": {
			paddingLeft: "0px",
			paddingRight: "10px",
			width: "1px",
			flex: "1",
			background: "transparent",
			borderColor: "transparent",
			color: "#333"
		},
		".z_search_input::placeholder": {color: "#999"},
		".z_search_input_icon": {
			alignItems: "center",
			justifyContent: "center",
			width: "auto",
			height: "auto"
		}
	};
	apivm.define("z-search", ZSearch);

	var ZAlert = /*@__PURE__*/ (function(Component) {
		function ZAlert(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				search: {dotIcon: true, show: true, input: "", value: ""}
			};
			this.compute = {
				isShow: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							if (this.props.dataMore.input) {
								this.data.search.input = this.props.dataMore.content;
							}
						}
					}
				}
			};
		}

		if (Component) ZAlert.__proto__ = Component;
		ZAlert.prototype = Object.create(Component && Component.prototype);
		ZAlert.prototype.constructor = ZAlert;
		ZAlert.prototype.installed = function() {};
		ZAlert.prototype.closePage = function(_type) {
			this.props.dataMore.show = false;
			if (!_type) {
				this.fire("cancel");
			}
			T.sendEvent("updatePage");
		};
		ZAlert.prototype.closeStop = function(e) {
			G.stopBubble(e);
		};
		ZAlert.prototype.inputIng = function(e) {
			if (T.isParameters(e)) {
				this.data.search.input = e.detail.value;
			}
		};
		ZAlert.prototype.itemClick = function() {
			var this$1 = this;

			setTimeout(function() {
				if (this$1.props.dataMore.input || this$1.props.dataMore.textarea) {
					this$1.props.dataMore.content = this$1.data.search.input;
				}
				this$1.data.search.input = "";
				this$1.fire("click", this$1.props.dataMore);
				this$1.fire("sure");
				this$1.closePage(1);
			}, 0);
		};
		ZAlert.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.isShow,
					class: "alert_box",
					onClick: this.closeStop,
					style: "display:" + (this.props.dataMore.show ? "flex" : "none") + ";"
				},
				this.data.show
					? apivm.h(
							"view",
							{class: "alert_warp", onClick: this.closeStop},
							apivm.h(
								"view",
								{class: "alert_content_title"},
								this.props.dataMore.title &&
									apivm.h(
										"text",
										{class: "alert_title", style: "" + G.loadConfiguration(4)},
										this.props.dataMore.title
									)
							),
							apivm.h(
								"scroll-view",
								{class: "alert_content_box", "scroll-x": false, "scroll-y": true},
								this.props.dataMore.richText
									? apivm.h("z-rich-text", {
											style: G.loadConfiguration(1) + "color:#333;",
											nodes: this.props.dataMore.content
									  })
									: this.props.dataMore.input
									? apivm.h(
											"view",
											{style: "height:36px;width:100%;"},
											apivm.h("z-search", {
												id: "alert_input",
												dataMore: this.data.search,
												placeholder: this.props.dataMore.placeholder
											})
									  )
									: this.props.dataMore.textarea
									? apivm.h("textarea", {
											id: "alert_input",
											style:
												G.loadConfiguration(1) +
												"height: " +
												(this.props.dataMore.height || "150") +
												"px;",
											class: "alert_textarea",
											placeholder: this.props.dataMore.placeholder,
											"placeholder-style": "color:#999;",
											value: this.data.search.input,
											"confirm-type": "return",
											onInput: this.inputIng
									  })
									: apivm.h(
											"text",
											{class: "alert_content", style: "" + G.loadConfiguration(1)},
											this.props.dataMore.content
									  )
							),
							apivm.h(
								"view",
								{style: "width:100%;height:1px;padding:0 15px;"},
								apivm.h("a-divider", null)
							),
							apivm.h(
								"view",
								{class: "alert_btn_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "alert_btn_item",
										style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}
									},
									apivm.h(
										"text",
										{
											style:
												G.loadConfiguration(1) +
												"color:" +
												(this.props.dataMore.cancel.color == "appTheme"
													? G.appTheme
													: this.props.dataMore.cancel.color) +
												";"
										},
										this.props.dataMore.cancel.text
									)
								),
								apivm.h(
									"view",
									{style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}},
									apivm.h("view", {style: "width:1px;height:30px;background:#F6F6F6;"})
								),
								apivm.h(
									"view",
									{
										onClick: this.itemClick,
										class: "alert_btn_item",
										style: {display: this.props.dataMore.sure.show ? "flex" : "none"}
									},
									apivm.h(
										"text",
										{
											style:
												G.loadConfiguration(1) +
												"color:" +
												(this.props.dataMore.sure.color == "appTheme"
													? G.appTheme
													: this.props.dataMore.sure.color) +
												";"
										},
										this.props.dataMore.sure.text
									)
								)
							)
					  )
					: null
			);
		};

		return ZAlert;
	})(Component);
	ZAlert.css = {
		".alert_box": {
			position: "absolute",
			zIndex: "1001",
			left: "0px",
			top: "0px",
			width: "100%",
			height: "100%",
			background: "rgba(0,0,0,0.4)",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_warp": {background: "#FFF", borderRadius: "10px", width: "320px"},
		".alert_content_box": {margin: "30px 15px", maxHeight: "350px"},
		".alert_title": {
			color: "#333333",
			fontWeight: "bold",
			padding: "20px 20px 0",
			textAlign: "center"
		},
		".alert_content": {
			width: "100%",
			textAlign: "center",
			color: "#333333",
			wordWrap: "break-word"
		},
		".alert_btn_box": {flexDirection: "row", alignItems: "center"},
		".alert_btn_item": {
			flex: "1",
			padding: "10px",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_textarea": {
			borderColor: "#eee",
			borderRadius: "10px",
			padding: "8px",
			width: "100%"
		},
		".alert_textarea::placeholder": {color: "#999"}
	};
	apivm.define("z-alert", ZAlert);

	var YBasePage = /*@__PURE__*/ (function(Component) {
		function YBasePage(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				showPage: false,
				initFrist: true, //首次初始化
				viewappearFrist: true, //是否首次进入页面
				initFrequency: false,
				pageParam: {},
				pageType: ""
			};
			this.compute = {
				isShow: function() {
					var this$1 = this;

					if (this.props.dataMore) {
						if (this.props.dataMore.show != this.data.show) {
							this.data.show = this.props.dataMore.show;
							if (this.data.show) {
								this.baseInit();
								setTimeout(function() {
									this$1.data.showPage = true;
								}, 80);
								console.log("base-page-param：" + JSON.stringify(this.data.pageParam));
							} else {
								this.data.showPage = false;
								if (this.data.pageParam.paramSaveKey) {
									T.removePrefs(this.data.pageParam.paramSaveKey);
								}
								this.fire("baseclose", null);
							}
						}
						//监听刷新事件
						if (this.props.dataMore.pageRefresh == 1) {
							this.props.dataMore.pageRefresh = 0;
							this.pageRefresh();
						}
					}
				}
			};
		}

		if (Component) YBasePage.__proto__ = Component;
		YBasePage.prototype = Object.create(Component && Component.prototype);
		YBasePage.prototype.constructor = YBasePage;
		YBasePage.prototype.installed = function() {
			var this$1 = this;
			if (!this.props.dataMore) {
				this.baseInit();
				console.log("base-page-param：" + JSON.stringify(this.data.pageParam)); //没有dataMore 才是新页面
				// T.addEventListener('viewappear', (ret, err)=> {
				// 	// console.log("我收到了返回事件"+JSON.stringify(T.pageParam(this)));
				// 	if(this.viewappearFrist){
				// 		this.viewappearFrist = false;
				// 		return;
				// 	}
				// 	this.pageRefresh();
				// });
				T.addEventListener("updatePage", function(ret, err) {
					this$1.update();
				});
			}
		};
		YBasePage.prototype.pageRefresh = function() {
			var this$1 = this;
			setTimeout(function() {
				this$1.fire("pageRefresh");
			}, 0);
		};
		YBasePage.prototype.baseInit = function() {
			var this$1 = this;

			//组件内 刚开始会调用多次 这里判断一下 300ms只返回一次
			if (this.data.initFrequency) {
				return;
			}
			this.data.initFrequency = true;
			setTimeout(function() {
				this$1.data.initFrequency = false;
			}, 300);
			this.data.pageParam = T.pageParam(this.props._this);
			if (this.data.pageParam.token) {
				T.setPrefs("sys_token", decodeURIComponent(this.data.pageParam.token));
				if (!T.getPrefs("sys_Mobile")) {
					getLoginInfo({header: {"u-login-areaId": ""}}, function(ret, err) {});
				}
			}
			if (this.data.pageParam.areaId) {
				T.setPrefs("sys_aresId", this.data.pageParam.areaId);
			}
			this.init();
			setTimeout(
				function() {
					this$1.fire("init", {first: this$1.data.initFrist});
					this$1.data.initFrist = false;
				},
				T.systemType() == "android" && !this.props.dataMore ? 300 : 0
			);
		};
		YBasePage.prototype.init = function() {
			this.data.pageType = this.data.pageParam.pageType || "page";
		};
		YBasePage.prototype.close = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.fire("close", null);
			}, 0);
		};
		YBasePage.prototype.penetrate = function() {};
		YBasePage.prototype.alertCallback = function(_type) {
			if (T.isNumber(_type)) {
				T.sendEvent("base_alert_callback", {buttonIndex: _type});
			} else {
				_type.detail.buttonIndex = 1;
				T.sendEvent("base_alert_callback", _type.detail);
			}
		};
		YBasePage.prototype.areaCallback = function(e) {
			T.sendEvent("base_areas_callback", e.detail);
		};
		YBasePage.prototype.actionSheetCallback = function(e) {
			T.sendEvent("base_actionSheet_callback", e.detail);
		};
		YBasePage.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.isShow,
					class: G.htmlClass + " base_page_warp",
					style:
						G.htmlStyle +
						";display:" +
						(this.props.dataMore ? (this.data.showPage ? "flex" : "none") : "flex") +
						";background:" +
						(this.props.dataMore && this.props.dataMore.type == "half"
							? "rgba(0,0,0,0.4)"
							: "#FFF") +
						";"
				},
				G.appTheme && [
					this.props.dataMore &&
						this.props.dataMore.type == "half" &&
						apivm.h("view", {
							onClick: function() {
								return this$1.close();
							},
							style:
								"height:" +
								(this.props.dataMore.boxAuto
									? "1px;flex:1"
									: (this.props.shadowH || "14%") + ";flex-shrink: 0;")
						}),
					apivm.h(
						"view",
						{
							onClick: function() {
								return this$1.penetrate();
							},
							style:
								"background:" +
								(this.props.bg || "#FFF") +
								";height:" +
								(this.props.dataMore && this.props.dataMore.boxAuto
									? "auto;max-height:90%;"
									: "1px;flex:1") +
								";border-radius: " +
								(this.props.dataMore && this.props.dataMore.type == "half"
									? "10px 10px"
									: "0 0") +
								" 0 0;"
						},
						apivm.h(
							"view",
							{style: "flex-shrink: 0;"},
							this.props.dataMore &&
								this.props.dataMore.type == "half" &&
								!this.props.closeH && [
									apivm.h(
										"view",
										{class: "base_page_header_warp", style: "height: 49px;"},
										apivm.h(
											"view",
											{
												class: "base_page_header_main",
												style: this.props.titleStyle || "padding: 0 44px;"
											},
											this.props.titleBox
												? [this.props.children.length >= 3 ? this.props.children[2] : null]
												: [
														apivm.h(
															"text",
															{
																style: G.loadConfiguration(1) + "color:#333",
																class: "base_page_header_main_text"
															},
															G.showTextSize(this.props.title, 8, 1)
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "base_page_header_left_box", style: "height:49px;"},
											this.props.back
												? [this.props.children.length >= 1 ? this.props.children[0] : null]
												: [
														apivm.h(
															"view",
															{
																onClick: function() {
																	return this$1.close();
																},
																class: "base_page_header_btn"
															},
															apivm.h(
																"text",
																{style: G.loadConfiguration(1) + "color:#666;margin:0 4px;"},
																"取消"
															)
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "base_page_header_right_box", style: "height:49px;"},
											this.props.more
												? [this.props.children.length >= 2 ? this.props.children[1] : null]
												: []
										)
									),
									apivm.h(
										"view",
										{style: "width:100%;height:1px;padding:0 16px;"},
										apivm.h("a-divider", null)
									)
								]
						),
						apivm.h(
							"view",
							{
								style:
									"width:100%;height:" +
									(this.props.dataMore && this.props.dataMore.boxAuto
										? "auto"
										: "1px;flex:1") +
									";" +
									(T.platform() != "app" ? "overflow-y: scroll;" : "")
							},

							apivm.h(
								"view",
								null,
								((this.props.dataMore &&
									(this.props.dataMore.type == "full" ||
										this.data.pageParam.pageType == "home")) ||
									!this.props.dataMore) &&
									(this.props.titleBox || this.props.back || this.props.more
										? true
										: G.showHeader(this.props._this) && !this.props.closeH) &&
									apivm.h(
										"view",
										{
											style:
												"width:100%;height:auto;padding-top:" +
												G.headerTop() +
												"px;background:" +
												((this.props._this &&
												this.props._this.data &&
												this.props._this.data.headTheme
													? this.props._this.data.headTheme || ""
													: "") || G.headTheme)
										},
										apivm.h(
											"view",
											{class: "base_page_header_warp", style: "height: 44px;"},
											apivm.h(
												"view",
												{
													class: "base_page_header_main",
													style: this.props.titleStyle || "padding: 0 44px;"
												},
												this.props.titleBox
													? [this.props.children.length >= 3 ? this.props.children[2] : null]
													: [
															apivm.h(
																"text",
																{
																	style:
																		G.loadConfiguration(4) +
																		"color:" +
																		G.getHeadThemeRelatively(this.props._this),
																	class: "base_page_header_main_text"
																},
																G.showTextSize(this.props.title, 8, 1)
															)
													  ]
											),
											apivm.h(
												"view",
												{class: "base_page_header_left_box", style: "height:44px;"},
												apivm.h(
													"view",
													{style: "height: 44px;"},
													this.props.back
														? [
																this.props.children.length >= 1 ? this.props.children[0] : null
														  ]
														: [
																apivm.h(
																	"view",
																	{
																		onClick: function() {
																			return this$1.close();
																		},
																		class: "base_page_header_btn",
																		style: {
																			display: (this.props.dataMore &&
																			this.props.dataMore.type == "full"
																			? true
																			: G.showHeader(this.props._this) &&
																			  this.data.pageType == "page")
																				? "flex"
																				: "none"
																		}
																	},
																	apivm.h("a-iconfont", {
																		name: "fanhui1",
																		color: G.getHeadThemeRelatively(this.props._this),
																		size: G.appFontSize + 1
																	})
																)
														  ]
												)
											),
											apivm.h(
												"view",
												{class: "base_page_header_right_box", style: "height:44px;"},
												this.props.more
													? [this.props.children.length >= 2 ? this.props.children[1] : null]
													: []
											)
										)
									)
							),
							apivm.h(
								"view",
								{
									style:
										"width:100%;height:" +
										(this.props.dataMore && this.props.dataMore.boxAuto
											? "auto"
											: "1px;flex:1") +
										";"
								},
								this.props.children.length >= 4 ? this.props.children[3] : null
							)
						)
					),
					this.props.children.length >= 5
						? this.props.children.filter(function(item, index) {
								return index >= 4;
						  })
						: null,
					!this.props.dataMore && [
						apivm.h(
							"view",
							{
								class: "suspend_box",
								style: "display:" + (G.imagePreviewer.show ? "flex" : "none") + ";"
							},
							G.imagePreviewer.show &&
								apivm.h("image-previewer", {
									imgs: G.imagePreviewer.imgs,
									activeIndex: G.imagePreviewer.activeIndex,
									type: G.imagePreviewer.type
								})
						),
						apivm.h("mo-areas", {
							dataMore: G.areasBox,
							pageParam: G.areasBox.pageParam,
							onChange: this.areaCallback
						}),
						apivm.h("z-actionSheet", {
							dataMore: G.actionSheetBox,
							data: G.actionSheetBox.data,
							active: G.actionSheetBox.active,
							onClick: this.actionSheetCallback
						}),
						apivm.h("z-alert", {
							dataMore: G.alertBox,
							onClick: this.alertCallback,
							onCancel: function() {
								return this$1.alertCallback(2);
							}
						})
					]
				]
			);
		};

		return YBasePage;
	})(Component);
	YBasePage.css = {
		div: {flexShrink: "0", WebkitOverflowScrolling: "touch"},
		".base_page_warp": {
			width: "100%",
			height: "100%",
			position: "absolute",
			zIndex: "999",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0",
			background: "rgba(0,0,0,0.4)"
		},
		".base_page_header_warp": {
			flexDirection: "row",
			width: "100%",
			alignItems: "center",
			flexShrink: "0"
		},
		".base_page_header_main": {
			width: "1px",
			height: "100%",
			flex: "1",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".base_page_header_main_text": {fontWeight: "600", flexShrink: "0"},
		".base_page_header_btn": {
			width: "auto",
			height: "100%",
			minWidth: "36px",
			padding: "0 12px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".base_page_header_btn:active": {background: "rgba(0,0,0,0.05)"},
		".base_page_header_left_box": {
			position: "absolute",
			zIndex: "999",
			left: "0",
			width: "auto",
			height: "44px",
			flexDirection: "row",
			flexShrink: "0"
		},
		".base_page_header_right_box": {
			position: "absolute",
			zIndex: "999",
			right: "0",
			width: "auto",
			height: "44px",
			flexDirection: "row-reverse",
			flexShrink: "0"
		},
		".filterNone": {filter: "none"},
		".filterGray": {filter: "grayscale(1)"},
		".avm-toast": {zIndex: "999"},
		".avm-confirm-mask": {zIndex: "999"},
		".suspend_box": {
			position: "absolute",
			zIndex: "999",
			left: "0px",
			top: "0px",
			width: "100%",
			height: "100%",
			background: "rgba(0,0,0,0.6)"
		}
	};
	apivm.define("y-base-page", YBasePage);

	var ASkeleton = /*@__PURE__*/ (function(Component) {
		function ASkeleton(props) {
			Component.call(this, props);
			this.data = {};
			this.compute = {
				rows: function() {
					var row = this.props.row || 0;
					return Array.from({length: row}).fill("");
				},
				length: function() {},
				avatarClass: function() {
					return (
						"a-skeleton_avatar " +
						(this.props["avatar-shape"] == "square" ? "" : "a-skeleton_round")
					);
				},
				avatarStyle: function() {
					var size = this.props["avatar-size"];
					return size ? "width:" + size + ";height:" + size + ";" : "";
				},
				titleStyle: function() {
					var titleWidth = this.props["title-width"];
					return titleWidth ? "width:" + titleWidth + ";" : "";
				}
			};
		}

		if (Component) ASkeleton.__proto__ = Component;
		ASkeleton.prototype = Object.create(Component && Component.prototype);
		ASkeleton.prototype.constructor = ASkeleton;
		ASkeleton.prototype.beforeRender = function() {
			if (!("loading" in this.props)) {
				this.props.loading = true;
			}
		};
		ASkeleton.prototype.getChildNode = function() {
			return this.props.children.length > 0 ? this.props.children[0] : null;
		};
		ASkeleton.prototype.getRowStyle = function(index) {
			return "width:" + this.getRowWidth(index) + ";";
		};
		ASkeleton.prototype.getRowWidth = function(index) {
			var rowWidth = this.props["row-width"] || "100%";

			if (rowWidth === "100%" && index === this.props.row - 1) {
				return "60%";
			}

			if (Array.isArray(rowWidth)) {
				return rowWidth[index];
			}

			return rowWidth;
		};
		ASkeleton.prototype.render = function() {
			var this$1 = this;
			return (
				(this.props.loading &&
					apivm.h(
						"view",
						{class: "a-skeleton"},
						this.props.avatar &&
							apivm.h("view", {class: this.avatarClass, style: this.avatarStyle}),
						apivm.h(
							"view",
							{style: "flex:1"},
							this.props.title &&
								apivm.h(
									"text",
									{class: "a-skeleton_title", style: this.titleStyle},
									this.props.title
								),
							this.props.row &&
								apivm.h(
									"view",
									null,
									(Array.isArray(this.rows) ? this.rows : Object.values(this.rows)).map(
										function(item$1, index$1) {
											return apivm.h("view", {
												class: "a-skeleton_row",
												style: this$1.getRowStyle(index$1)
											});
										}
									)
								)
						)
					)) ||
				this.getChildNode()
			);
		};

		return ASkeleton;
	})(Component);
	ASkeleton.css = {
		".a-skeleton": {width: "100%", flexDirection: "row", padding: "10px 16px"},
		".a-skeleton_avatar": {
			flexShrink: "0",
			width: "32px",
			height: "32px",
			marginRight: "16px",
			backgroundColor: "#f2f3f5"
		},
		".a-skeleton_round": {borderRadius: "999px"},
		".a-skeleton_title": {
			width: "40%",
			height: "16px",
			backgroundColor: "#f2f3f5"
		},
		".a-skeleton_row": {
			marginTop: "12px",
			width: "40%",
			height: "16px",
			backgroundColor: "#f2f3f5"
		}
	};
	apivm.define("a-skeleton", ASkeleton);

	var YScrollView = /*@__PURE__*/ (function(Component) {
		function YScrollView(props) {
			Component.call(this, props);
			this.data = {
				refreshTriggered: false, //设置当前下拉刷新状态，true 表示下拉刷新已经被触发，false 表示下拉刷新未被触发
				upperThreshold: this.props.upperThreshold || 50, //距顶部/左边多远时，触发 scrolltoupper 事件
				lowerThreshold: this.props.lowerThreshold || 50, //距底部/右边多远时，触发 scrolltolower 事件

				scrollVID: "" //滚动位置
			};
			this.compute = {
				monitor: function() {
					if (this.data.scrollVID != (this.props["scroll-into-view"] || "")) {
						this.data.scrollVID = this.props["scroll-into-view"] || "";
						if (this.data.scrollVID) {
							this.scrollTo(this.data.scrollVID);
						}
					}
				}
			};
		}

		if (Component) YScrollView.__proto__ = Component;
		YScrollView.prototype = Object.create(Component && Component.prototype);
		YScrollView.prototype.constructor = YScrollView;
		YScrollView.prototype.install = function() {};
		YScrollView.prototype.installed = function() {};
		YScrollView.prototype.onscrolltoupper = function(e) {};
		YScrollView.prototype.onscrolltolower = function(e) {
			this.fire("up", {});
		};
		YScrollView.prototype.onscroll = function(ref) {
			var detail = ref.detail;

			this.fire("scroll", detail);
		};
		YScrollView.prototype.onrefresherrefresh = function(e) {
			var this$1 = this;

			this.fire("lower", {});
			this.data.refreshTriggered = true;
			setTimeout(function() {
				this$1.data.refreshTriggered = false;
			}, 150);
		};
		YScrollView.prototype.scrollTo = function(nowView) {
			try {
				if (T.platform() != "mp") {
					var _animated = T.isParameters(this.props["scroll-with-animation"])
						? this.props["scroll-with-animation"]
						: true;
					document
						.getElementById(this.props.id)
						.scrollTo(
							T.platform() == "app"
								? {view: nowView, animated: _animated}
								: {
										top: this.getOffestValue(
											document.getElementById(nowView),
											this.props.id
										).top,
										behavior: _animated ? "smooth" : "instant"
								  }
						);
				}
			} catch (e) {}
		};
		YScrollView.prototype.getOffestValue = function(elem, parentId) {
			var Far = null;
			var topValue = elem && elem.offsetTop;
			var leftValue = elem && elem.offsetLeft;
			var offsetFar = elem && elem.offsetParent;
			while (offsetFar) {
				topValue += offsetFar.offsetTop;
				leftValue += offsetFar.offsetLeft;
				Far = offsetFar;
				offsetFar = offsetFar.offsetParent;
				if (offsetFar.id == parentId) {
					break;
				}
			}
			return {top: topValue, left: leftValue, Far: Far};
		};
		YScrollView.prototype.render = function() {
			return apivm.h(
				"view",
				{a: this.monitor, style: "flex:1;height:1px;" + (this.props.style || "")},
				apivm.h(
					"scroll-view",
					{
						id: "" + this.props.id,
						style: (this.props.style || "") + "background-color:transparent;",
						class: "" + (this.props.class || ""),
						"scroll-x": T.isParameters(this.props["scroll-x"])
							? this.props["scroll-x"]
							: false,
						"scroll-y": T.isParameters(this.props["scroll-y"])
							? this.props["scroll-y"]
							: true,
						bounces: T.isParameters(this.props["bounces"])
							? this.props["bounces"]
							: true,
						"scroll-into-view":
							"" + (T.platform() == "mp" ? this.props["scroll-into-view"] || "" : ""),
						"scroll-with-animation":
							T.platform() == "mp"
								? T.isParameters(this.props["scroll-with-animation"])
									? this.props["scroll-with-animation"]
									: true
								: false,
						"refresher-enabled":
							T.platform() != "web" &&
							(T.isParameters(this.props["refresher-enabled"])
								? this.props["refresher-enabled"]
								: false),
						"refresher-threshold": T.isParameters(this.props["refresher-threshold"])
							? this.props["refresher-threshold"]
							: 65,
						"refresher-background": T.isParameters(this.props["refresher-background"])
							? this.props["refresher-background"]
							: "#FFF",
						"refresher-triggered": this.data.refreshTriggered,
						"upper-threshold": this.upperThreshold,
						"lower-threshold": this.lowerThreshold,
						onScrolltoupper: this.onscrolltoupper,
						onScrolltolower: this.onscrolltolower,
						onRefresherrefresh: this.onrefresherrefresh,
						onScroll: this.onscroll
					},
					apivm.h(
						"view",
						{style: this.props.firsts || ""},
						this.props.children || null
					),
					apivm.h(
						"view",
						null,
						T.isParameters(this.props.data)
							? [
									apivm.h(
										"view",
										{
											class: "y_scroll_box",
											style: {display: this.props.data.skeleton ? "flex" : "none"}
										},
										(Array.isArray([1, 2, 3]) ? [1, 2, 3] : Object.values([1, 2, 3])).map(
											function(item$1, index$1) {
												return apivm.h("a-skeleton", {title: true, row: "3"});
											}
										)
									),
									apivm.h(
										"view",
										{
											class: "y_scroll_box",
											style: {
												display:
													!this.props.data.skeleton &&
													!this.props.data.notList &&
													this.props.data.listLength == 0
														? "flex"
														: "none"
											}
										},
										apivm.h(
											"text",
											{style: G.loadConfiguration(-4) + "color: #CCCCCC;padding:16px;"},
											this.props.data.text || "暂无数据"
										)
									),
									apivm.h(
										"view",
										{
											class: "y_scroll_box",
											onClick: this.onscrolltolower,
											style: {
												display:
													!this.props.data.skeleton &&
													!this.props.data.notList &&
													this.props.data.listLength != 0
														? "flex"
														: "none"
											}
										},
										apivm.h(
											"text",
											{style: G.loadConfiguration(-4) + "color: #CCCCCC;padding:15px;"},
											this.props.data.text
										)
									),
									apivm.h(
										"view",
										{style: {display: !this.props.data.dotFooter ? "flex" : "none"}},
										apivm.h("view", {
											style: "padding-bottom:" + T.safeArea().bottom + "px;"
										})
									)
							  ]
							: null
					)
				)
			);
		};

		return YScrollView;
	})(Component);
	YScrollView.css = {
		".y_scroll_box": {
			alignItems: "center",
			justifyContent: "center",
			width: "100%"
		}
	};
	apivm.define("y-scroll-view", YScrollView);

	var MoActivityLeavereview = /*@__PURE__*/ (function(Component) {
		function MoActivityLeavereview(props) {
			Component.call(this, props);
			this.data = {
				G: G,
				pageParam: {},
				pageType: "",
				title: "",
				id: "",
				userId: "",
				leaveId: "",
				readonly: false, //只读

				pageNo: 1,
				pageSize: 15,
				refreshPageSize: 0,
				pageNot: {
					skeleton: true,
					notList: true,
					listLength: 0,
					type: "0",
					text: "",
					dotFooter: true
				},
				listData: [],

				checkList: [
					{
						type: "radio",
						title: "审批意见",
						hint: "请选择审批意见",
						key: "passStatus",
						lStyle: "flex:1;",
						rBoxStyle: "flex:none;",
						rRightW: "40",
						value: "leavepass",
						noRequired: false,
						line: true,
						data: [
							{value: "leavepass", title: "通过"},
							{value: "leavenopass", title: "不通过"}
						]
					},
					{
						type: "switch",
						title: "是否短信通知",
						hint: "",
						key: "needTextMessageNotice",
						lStyle: "flex:1;",
						lTextStyle: "width:auto;margin-right:20px;",
						rBoxStyle: "flex:none;",
						value: false,
						noRequired: true,
						line: true
					},
					{
						type: "textarea",
						title: "审批说明",
						hint: "请输入审批说明",
						key: "passReason",
						value: "",
						noRequired: true,
						style: "margin-top:10px;",
						line: false
					}
				],

				identifyAudio: {
					show: false
				}
			};
		}

		if (Component) MoActivityLeavereview.__proto__ = Component;
		MoActivityLeavereview.prototype = Object.create(
			Component && Component.prototype
		);
		MoActivityLeavereview.prototype.constructor = MoActivityLeavereview;
		MoActivityLeavereview.prototype.onShow = function() {
			G.onShow(this);
		};
		MoActivityLeavereview.prototype.installed = function() {};
		MoActivityLeavereview.prototype.baseInit = function() {
			this.data.pageParam = T.pageParam(this);
			G.installed(this);
		};
		MoActivityLeavereview.prototype.baseclose = function() {};
		MoActivityLeavereview.prototype.init = function() {
			this.data.pageType = this.data.pageParam.pageType || "page";
			this.data.title = this.data.pageParam.title || "请假审批";
			this.data.id =
				this.data.pageParam.activityId ||
				this.data.pageParam.id ||
				"580930599619919872";
			this.data.userId = this.data.pageParam.userId || G.uId;
			this.getData();
		};
		MoActivityLeavereview.prototype.close = function() {
			if (this.props.dataMore) {
				this.props.dataMore.show = false;
			} else {
				T.closeWin();
			}
		};
		MoActivityLeavereview.prototype.getData = function(_type) {
			var this$1 = this;

			if (!_type) {
				this.data.pageNo = 1;
			}
			var postParam = {
				query: {
					activityId: this.data.id,
					userId: this.data.userId
				}
			};

			T.ajax(
				{u: myjs.appUrl() + "activityleave/info", _this: this},
				"activityleave/info",
				function(ret, err) {
					T.hideProgress();
					this$1.data.pageNot.skeleton = false;
					if (ret && ret.code == 200) {
						var _eItem = ret.data || {};
						this$1.data.leaveId = _eItem.id;
						this$1.data.listData = [
							{
								aLabel: (_eItem.activityLeaveReason || {}).label || "",
								memo: _eItem.memo || "",
								attachments: _eItem.attachments || [],
								time: _eItem.leaveTime || "",

								id: _eItem.userId || "",
								name: _eItem.userName,
								url: _eItem.headImg || _eItem.photo
							}
						];
					} else {
						T.toast(ret ? ret.message || ret.data : T.NET_ERR);
					}
				},
				"请假详情",
				"post",
				{
					body: JSON.stringify(postParam)
				}
			);
		};
		MoActivityLeavereview.prototype.loadMore = function() {};
		MoActivityLeavereview.prototype.submitActive = function() {
			var this$1 = this;

			var param = {
				id: this.data.leaveId,
				currentMessageTemplate: ""
			};

			for (var i = 0; i < this.data.checkList.length; i++) {
				var _eItem = this.data.checkList[i];
				if (
					!_eItem.hide &&
					!_eItem.noRequired &&
					!(T.isArray(_eItem.value) ? _eItem.value.length : _eItem.value)
				) {
					T.toast(_eItem.hint);
					return;
				}
				if (T.isArray(_eItem.value)) {
					param[_eItem.key] = _eItem.value
						.map(function(obj) {
							return obj.id;
						})
						.join(",");
				} else if (_eItem.type == "switch") {
					param[_eItem.key] = _eItem.value ? 1 : 0;
				} else {
					param[_eItem.key] = _eItem.value;
				}
			}
			param = {form: param};
			T.showProgress("审批中");
			this.data.readonly = true;
			T.ajax(
				{u: myjs.appUrl() + "activityleave/examine", _this: this},
				"activityleave/examine",
				function(ret, err) {
					T.hideProgress();
					T.toast(ret ? ret.message || ret.data : T.NET_ERR);
					if (ret && ret.code == 200) {
						setTimeout(function() {
							this$1.close();
						}, 300);
					} else {
						this$1.data.readonly = false;
					}
				},
				"审批",
				"post",
				{
					body: JSON.stringify(param)
				}
			);
		};
		MoActivityLeavereview.prototype.identify = function(e) {
			var this$1 = this;

			var nItem = e.detail;
			if (nItem.identify == 1) {
				T.actionSheet(
					{
						title: "请选择图片来源",
						buttons: ["相机", "相册"]
					},
					function(ret, err) {
						var _index = ret.buttonIndex;
						T.getPicture(
							{
								sourceType: _index == 1 ? "camera" : "photos",
								destinationType: "base64",
								userOne: true
							},
							function(ret, err) {
								var url = ret ? ret.base64Data || ret.data || "" : "";
								if (url) {
									this$1.identifyPic(nItem, url);
								}
							}
						);
					}
				);
			} else {
				this.identifyVideo(nItem);
			}
		};
		MoActivityLeavereview.prototype.identifyPic = function(nItem, _base64) {
			var this$1 = this;

			T.showProgress("转换中");
			if (!this.authTokens) {
				T.ajax(
					{u: myjs.tomcatAddress() + "huawei/HuaWei?type=authTokens", _this: this},
					"authTokens",
					function(ret, err) {
						this$1.authTokens = ret ? ret.result || "" : "";
						if (this$1.authTokens) {
							this$1.identifyPic(nItem, _base64);
						} else {
							T.hideProgress();
							T.toast(T.NET_ERR);
						}
					},
					"华为token",
					"get",
					{},
					{"content-type": "application/x-www-form-urlencoded"}
				);
				return;
			}
			var postValue = {
				image: _base64.replace(/data:image\/(.*?);base64,/, ""),
				detect_direction: true
			};

			T.ajax(
				{u: myjs.tomcatAddress() + "huawei/HuaWei", _this: this},
				"ocrText",
				function(ret, err) {
					T.hideProgress();
					if (!ret) {
						T.toast(T.NET_ERR);
					}
					var result = ret.result || {};
					var words_block_list = result.words_block_list || [];
					if (!T.isArray(words_block_list) || words_block_list.length == 0) {
						T.toast("抱歉，未识别到文字");
					}
					var nowText = "",
						leftNum = -1;
					(rightNum = -1), (maxTextLength = 0), (mTextSize = 0);
					for (var i = 0; i < words_block_list.length; i++) {
						var current_rect = words_block_list[i].location;
						var current_text = (words_block_list[i].words || "").replace(
							/[^\u4e00-\u9fa5]/gi,
							""
						);
						if (current_text.length > mTextSize) {
							mTextSize = current_text.length;
						}
						if (T.isArray(current_rect)) {
							var nLeftNum = current_rect[0][0];
							var nRightNum = current_rect[1][0];
							if (leftNum == -1 || nLeftNum < leftNum) {
								leftNum = nLeftNum;
							}
							if (rightNum == -1 || nRightNum > rightNum) {
								rightNum = nRightNum;
							}
						}
					}
					if (leftNum != -1 && rightNum != -1) {
						maxTextLength = rightNum - leftNum;
					}
					for (var i = 0; i < words_block_list.length; i++) {
						var current_text = words_block_list[i].words;
						var current_rect = words_block_list[i].location;
						if (maxTextLength) {
							var nLeftNum = current_rect[0][0];
							var nRightNum = current_rect[1][0];
							if (Math.abs(nLeftNum - leftNum) > (maxTextLength / mTextSize) * 2) {
								current_text = "\n" + current_text;
							}
							if (Math.abs(nRightNum - rightNum) > (maxTextLength / mTextSize) * 2) {
								current_text = current_text + "\n";
							}
						}
						nowText += current_text;
					}
					console.error(nowText);
					nItem.value += (T.trim(nItem.value) ? "\n" : "") + nowText;
				},
				"图文识别",
				"post",
				{
					values: {type: "ocrText", token: this.authTokens, value: postValue}
				},
				{"content-type": "application/x-www-form-urlencoded"}
			);
		};
		MoActivityLeavereview.prototype.identifyVideo = function(nItem) {
			this.data.identifyAudio = {
				show: true,
				type: 1,
				callback: function(ret) {
					nItem.value += (T.trim(nItem.value) ? "\n" : "") + ret;
				}
			};
		};
		MoActivityLeavereview.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"y-base-page",
				{
					_this: this,
					title: this.data.title,
					dataMore: this.props.dataMore,
					pageParam: this.props.pageParam,
					onInit: this.baseInit,
					onClose: this.close,
					onBaseclose: this.baseclose,
					onPageRefresh: function() {
						return this$1.getData(false);
					}
				},
				apivm.h("view", {style: "width:auto;height:100%;"}),
				apivm.h("view", {style: "width:auto;height:100%;"}),
				apivm.h("view", null),
				apivm.h(
					"view",
					{
						style:
							"width:100%;height:" +
							(this.props.dataMore && this.props.dataMore.boxAuto
								? "auto"
								: "1px;flex:1") +
							";"
					},
					apivm.h(
						"y-scroll-view",
						{
							style: "width:100%;height:1px;flex:1;",
							"refresher-enabled": true,
							data: this.data.pageNot,
							onLower: function() {
								return this$1.getData(0);
							},
							onUp: function() {
								return this$1.loadMore();
							}
						},
						(Array.isArray(this.data.listData)
							? this.data.listData
							: Object.values(this.data.listData)
						).map(function(item$1, index$1) {
							return apivm.h(
								"view",
								null,
								apivm.h(
									"view",
									{class: "leave_item"},
									apivm.h(
										"view",
										{style: "flex-direction:row;align-items: center;margin-bottom:15px;"},
										apivm.h("z-avatar", {
											style: this$1.data.G.loadConfigurationSize(14),
											key: item$1.refresh,
											data: item$1
										}),
										apivm.h(
											"text",
											{
												style:
													this$1.data.G.loadConfiguration() +
													"color: #333333;flex:1;margin-left:10px;"
											},
											item$1.name
										)
									),
									apivm.h(
										"view",
										null,
										item$1.aLabel &&
											apivm.h(
												"text",
												{
													style:
														this$1.data.G.loadConfiguration(2) +
														"color: #333;font-weight: bold;margin-bottom:10px;"
												},
												item$1.aLabel
											)
									),
									apivm.h(
										"view",
										null,
										item$1.memo &&
											apivm.h(
												"text",
												{
													style:
														this$1.data.G.loadConfiguration(1) +
														"color: #333;font-weight: 400;margin-bottom:10px;"
												},
												item$1.memo
											)
									),
									apivm.h(
										"view",
										null,
										item$1.attachments.length > 0 &&
											apivm.h(
												"view",
												{style: "margin-bottom:10px;"},
												apivm.h("y-attachments", {data: item$1.attachments})
											)
									),
									apivm.h(
										"view",
										null,
										apivm.h(
											"text",
											{
												style:
													this$1.data.G.loadConfiguration(-2) +
													"color: #999;font-weight: 400;"
											},
											dayjs$1(item$1.time).format("YYYY-MM-DD HH:mm")
										)
									)
								),

								apivm.h(
									"view",
									{style: "border-top:10px solid #F8F8F8;"},
									(Array.isArray(this$1.data.checkList)
										? this$1.data.checkList
										: Object.values(this$1.data.checkList)
									).map(function(item$1, index$1) {
										return apivm.h(
											"view",
											null,
											apivm.h("y-add-item", {
												data: item$1,
												readonly: this$1.data.readonly,
												onIdentify: this$1.identify
											})
										);
									}),
									apivm.h(
										"view",
										{style: "margin:20px 0;padding:0 37px;"},
										apivm.h("z-button", {
											onClick: function() {
												return this$1.submitActive();
											},
											disabled: this$1.data.readonly,
											round: true,
											style: "padding:7px 15px;",
											fontstyle: this$1.data.G.loadConfiguration(),
											size: this$1.data.G.appFontSize,
											color: this$1.data.G.appTheme,
											text: "提交"
										})
									)
								)
							);
						})
					)
				),

				apivm.h("identify-audio", {_this: this, dataMore: this.data.identifyAudio})
			);
		};

		return MoActivityLeavereview;
	})(Component);
	MoActivityLeavereview.css = {".leave_item": {padding: "15px 16px"}};
	apivm.define("mo-activity-leaveReview", MoActivityLeavereview);
	apivm.render(apivm.h("mo-activity-leaveReview", null), "body");
})();
