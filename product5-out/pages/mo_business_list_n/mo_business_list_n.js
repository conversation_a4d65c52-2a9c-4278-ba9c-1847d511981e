(function() {
	var LOAD_ING = "加载中，请稍候...";
	var LOAD_MORE = "点击加载更多";
	var LOAD_ALL = "已加载完";
	var NET_ERR$1 = "用户您好，系统正在更新，请稍后再试。";

	//参数是否为空
	function isParameters(_obj) {
		return _obj != null && _obj != undefined;
	}
	//是否数组
	function isArray(_obj) {
		return isParameters(_obj) && toString.apply(_obj) === "[object Array]";
	}
	//是否数字
	function isNumber(_obj) {
		return isParameters(_obj) && typeof _obj === "number";
	}
	//是否对象
	function isObject(_obj) {
		return isParameters(_obj) && typeof _obj === "object";
	}
	//是否方法
	function isFunction(_obj) {
		return isParameters(_obj) && typeof _obj === "function";
	}

	//获取随机数
	function getNum() {
		return Math.floor(Math.random() * 100000000);
	}

	//去除首尾空格
	function trim(str) {
		if (String.prototype.trim) {
			return str == null ? "" : String.prototype.trim.call(str);
		} else {
			return str.replace(/(^\s*)|(\s*$)/g, "");
		}
	}

	//合并json
	function setNewJSON(_obj, _newobj, _dotReplace) {
		_obj = _obj || {};
		_newobj = _newobj || {};
		var returnObj = {};
		for (var key in _obj) {
			returnObj[key] = _obj[key];
		}
		for (var key in _newobj) {
			if (
				(_dotReplace && isParameters(returnObj[key])) ||
				isFunction(isParameters(returnObj[key]))
			)
				continue;
			returnObj[key] =
				isArray(returnObj[key]) || !isObject(returnObj[key])
					? _newobj[key]
					: setNewJSON(returnObj[key], _newobj[key]);
		}
		return returnObj;
	}

	//移除字符串所有标签
	function removeTag(str) {
		if (!str) return str;
		return decodeCharacter(
			str
				.replace(/<!--[\w\W\r\n]*?-->/gim, "")
				.replace(/(<[^\s\/>]+)\b[^>]*>/gi, "$1>")
				.replace(/<[^>]+>/g, "")
				.replace(/\s*/g, "")
		);
	}

	//移除字符串所有标签
	function removeTagAll(str) {
		if (!str) return str;
		return decodeCharacter(
			str
				.replace(/<video[^>]*>(.*?)<\/video>/g, "【视频】")
				.replace(/<img[^>]*>/g, "【图片】")
				.replace(/<!--[\w\W\r\n]*?-->/gim, "")
				.replace(/(<[^\s\/>]+)\b[^>]*>/gi, "$1>")
				.replace(/<[^>]+>/g, "")
				.replace(/\s*/g, "")
		);
	}

	//转义字符串
	function decodeCharacter(str) {
		if (!str) return str;
		return str
			.replace(/&amp;/g, "&")
			.replace(/(&nbsp;|&ensp;|&emsp;)/g, " ")
			.replace(/&mdash;/g, "—")
			.replace(/&ldquo;/g, "“")
			.replace(/&rsquo;/g, "’")
			.replace(/&lsquo;/g, "‘")
			.replace(/&rdquo;/g, "”")
			.replace(/&middot;/g, "·")
			.replace(/&hellip;/g, "…")
			.replace(/&quot;/g, '"')
			.replace(/&lt;/g, "<")
			.replace(/&gt;/g, ">");
	}

	//获取平台类型
	function platform() {
		return api.platform;
	}

	//app设置状态栏
	function setStatusBarStyle(_param) {
		try {
			api.setStatusBarStyle(_param);
		} catch (e) {}
	}

	//区域参数
	function safeArea() {
		try {
			return api.safeArea;
		} catch (e) {
			return {top: 0, left: 0, bottom: 0, right: 0};
		}
	}

	//页面参数对象
	function pageParam(_this) {
		try {
			var pageParam =
				(_this && _this.props
					? _this.props.pageParam || (_this.props.dataMore || {}).pageParam
					: null) ||
				api.pageParam ||
				{};
			if (pageParam.paramSaveKey) {
				pageParam = JSON.parse(getPrefs(pageParam.paramSaveKey) || "{}");
			}
			if (platform() == "web" && JSON.stringify(pageParam) == "{}") {
				pageParam = getOtherParam(window.location.href);
			}
			return pageParam;
		} catch (e) {
			return {};
		}
	}

	//获取缓存
	function getPrefs(key) {
		try {
			return api.getPrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				return window.parent[key];
			} else {
				return window[key];
			}
		}
	}

	//设置缓存
	function setPrefs(key, value) {
		if (!isParameters(value)) {
			removePrefs(key);
			return;
		}
		try {
			api.setPrefs({sync: true, key: key, value: value});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				window.parent[key] = value;
			} else {
				window[key] = value;
			}
		}
	}

	//删除缓存
	function removePrefs(key) {
		try {
			api.removePrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				delete window.parent[key];
			} else {
				delete window[key];
			}
		}
	}

	//添加监听
	function addEventListener(name, callback) {
		var keyback = function keyback(ret, err) {
			isFunction(callback) && callback(ret, err);
		};
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				if (!window.baseEventList) window.baseEventList = [];
				if (getItemForKey(name, window.baseEventList)) {
					delItemForKey(name, window.baseEventList);
				}
				window.baseEventList.push({key: name, value: keyback});
			} else {
				api.addEventListener({name: name}, keyback);
			}
		} catch (e) {}
	}
	//移除监听
	function removeEventListener(name) {
		if (
			platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			delItemForKey(name, window.baseEventList);
		} else {
			try {
				api.removeEventListener({name: name});
			} catch (e) {}
		}
	}
	//发送监听
	function sendEvent(name, extra) {
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				var pageframes = window.parent.document.getElementsByTagName("iframe");
				for (var i = 0; i < pageframes.length; i++) {
					if (isArray(pageframes[i].contentWindow.baseEventList)) {
						var sendItem = getItemForKey(
							isObject(name) ? name.name : name,
							pageframes[i].contentWindow.baseEventList
						);
						if (sendItem)
							sendItem.value({value: isObject(name) ? name.extra : extra});
					}
				}
			} else {
				api.sendEvent(isObject(name) ? name : {name: name, extra: extra});
			}
		} catch (e) {}
	}
	//加载框
	function showProgress(_param, modal) {
		var o = {
			style: "default",
			animationType: "fade",
			title: "加载中",
			text: "请稍候...",
			modal: true //是否模态，模态时整个页面将不可交互
		};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.title = isParameters(_param) ? _param : "";
			o.modal = !modal; //是否可以交互 反过来了
		}
		o.title = o.title.toString();
		api.showProgress(o);
	}

	//隐藏加载框
	function hideProgress() {
		api.hideProgress();
	}

	//处理app链接 可以不带app会拼接上 或者需要带上特定参数
	function handleSYSLink(_link) {
		if (!_link) return;
		_link = _link.replace("{{tomcatAddress}}", tomcatAddress());
		_link = _link.replace("{{shareAddress}}", shareAddress());
		_link = _link.replace("{{token}}", encodeURIComponent(getPrefs("sys_token")));
		_link = _link.replace("{{sysUrl}}", appUrl());
		_link = _link.replace("{{areaId}}", areaId()); //当前跳转页面的地区id，例如：430000
		_link = _link.replace("{{userId}}", G.userId); //当前用户id，例如：1
		_link = _link.replace("{{iszx}}", G.sysSign == "zx"); //当前系统类型，例如：true (true为政协，flase为人大)
		_link = _link.replace("{{appTheme}}", G.appTheme); //当前app主题颜色，例如：#3657C0
		_link = _link.replace("{{careMode}}", G.careMode); //当前是否为关怀模式：例如：true (关怀模式下字体大4px)
		if (_link.indexOf("?ndata=") != -1) {
			if (_link.indexOf("sysUrl-zy-") == -1) _link += "-zyz-sysUrl-zy-" + appUrl();
			if (_link.indexOf("sysAreaId-zy-") == -1)
				_link += "-zyz-sysAreaId-zy-" + areaId();
			if (_link.indexOf("iszx-zy-") == -1)
				_link += "-zyz-iszx-zy-" + (G.sysSign == "zx");
			if (_link.indexOf("appTheme-zy-") == -1)
				_link += "-zyz-appTheme-zy-" + G.appTheme;
			if (_link.indexOf("careMode-zy-") == -1)
				_link += "-zyz-careMode-zy-" + G.careMode;
		}
		return _link;
	}

	//打开新页面
	function openWin(name, url, pageParam, _more) {
		url = handleSYSLink(url); //先处理跳转链接
		if (url.indexOf("http") != 0) {
			url =
				platform() == "web"
					? url.substring(url.lastIndexOf("/") + 1)
					: url.indexOf("..") != 0
					? "../" + url.split(".")[0] + "/" + url
					: url;
		}
		var o = {
			name: name,
			url: url,
			pageParam: pageParam || {},
			bounces: false,
			bgColor: "#FFF",
			slidBackEnabled: false, //ios滑动返回
			vScrollBarEnabled: true,
			hScrollBarEnabled: true,
			scaleEnabled: true,
			animation: {
				type: "push",
				subType: "from_right",
				duration: 300
			},

			reload: true, // 去除设置
			allowEdit: true, //去除设置 默认都可以复制粘贴
			delay: 0,
			overScrollMode: "scrolls",
			defaultRefreshHeader: "swipe"
		};

		if (isObject(_more)) {
			o = setNewJSON(o, _more);
		}
		if (G.headTheme != getPrefs("headTheme") && G.headTheme != "transparent") {
			o.pageParam.headTheme = G.headTheme;
		}
		if (
			G.appTheme != getPrefs("appTheme" + G.sysSign) &&
			G.appTheme != (G.sysSign == "rd" ? "#C61414" : "#3088FE")
		) {
			o.pageParam.appTheme = G.appTheme;
		}
		if (
			o.pageParam.areaId != areaId() ||
			(areaId() != getPrefs("sys_aresId") && areaId() != getPrefs("sys_platform"))
		) {
			o.pageParam.areaId = o.pageParam.areaId || areaId();
		}
		if (o.pageParam.paramSaveKey) {
			setPrefs(o.pageParam.paramSaveKey, JSON.stringify(o.pageParam));
			o.pageParam = {paramSaveKey: o.pageParam.paramSaveKey};
		}
		videoPlayRemoves();
		api.openWin(o);
	}

	function closeWin(_param) {
		var o = {};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.name = _param;
		}
		try {
			if (api.pageParam.paramSaveKey) {
				removePrefs(api.pageParam.paramSaveKey);
			}
			videoPlayRemoves();
			api.closeWin(o);
		} catch (e) {}
	}

	function toast(_param, location, global) {
		var o = {
			msg: "",
			duration: 2000,
			location: location || "middle",
			global: global ? true : false
		};

		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.msg = isParameters(_param) ? _param : "";
		}
		o.msg = o.msg.toString();
		api.toast(o);
	}

	//弹出actionSheet
	function actionSheet(_param, _callback) {
		var o = {title: "", cancelTitle: "取消", destructiveTitle: ""};
		o = setNewJSON(o, _param);
		var oldButton = o.buttons || [],
			newButton = [];
		oldButton.forEach(function(item) {
			newButton.push(isObject(item) ? item : {name: item});
		});
		var actionSheetBox = {
			title: o.title,
			cancel: o.cancelTitle,
			data: newButton,
			active: o.active,
			show: true,
			callback: function callback(ret) {
				_callback && _callback(ret);
			},
			pageClose: function pageClose() {
				_param.pageClose && _param.pageClose();
			}
		};

		G.actionSheetPop = actionSheetBox;
	}

	//弹出alert
	function alert(_param, _callback) {
		var o = {title: "", msg: "", buttons: ["确定"]};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.msg = (isParameters(_param) ? _param : "").toString();
		}
		var alertBox = {
			title: o.title,
			content: (o.msg || o.content || "").toString(),
			placeholder: o.placeholder,
			closeType: o.closeType || "1",
			type: o.type || "text",
			timeout: o.timeout || 0,
			autoClose: o.autoClose,
			cancel: {
				show: o.buttons.length > 1 || o.closeType == "2" || o.closeType == "3",
				text: o.buttons[1],
				color: "#333333"
			},

			sure: {
				show: o.buttons.length > 0,
				text: o.buttons[0],
				color: "appTheme"
			},

			show: true,
			callback: function callback(ret) {
				_callback && _callback(ret);
			}
		};

		alertBox = setNewJSON(alertBox, _param.otherParam);
		G.alertPop = alertBox;
	}

	//弹出地区选择
	function openAreas(_param, _callback) {
		var areasBox = {
			show: true,
			callback: function callback(ret) {
				_callback && _callback(ret);
			}
		};

		G.areasPop = setNewJSON(areasBox, _param);
	}

	//网络请求
	function ajax(url, tag, callback, logText, method, data, header) {
		var getUrl = url,
			dataType = "json",
			cacheType = "",
			paramData = {},
			aId = areaId(),
			isWeb = "";
		if (isObject(url)) {
			getUrl = url.u;
			dataType = url.dt || "json";
			cacheType = url.t || "";
			paramData = url.paramData || {};
			aId = url.areaId || areaId();
			isWeb = url.web || "";
		}
		var o = {
			url: getUrl,
			tag: tag,
			method: method || "get",
			cache: false,
			timeout: 120,
			dataType: dataType,
			data: isObject(data) ? data : {},
			headers: setNewJSON(
				{
					"u-login-areaId": aId,
					Authorization: getPrefs("sys_token") || "",
					"content-type": "application/json",
					"u-terminal": G.terminal || "APP"
				},
				header || {}
			)
		};

		o = setNewJSON(o, paramData);
		if (o.url.indexOf("push/rongCloud") != -1) {
			if (o.method == "get") {
				o.url +=
					(o.url.indexOf("?") != -1 ? "&" : "?") +
					"environment=" +
					chatEnvironment();
			} else if (o.data.values) {
				o.data.values.environment = chatEnvironment();
			}
		}
		if (isWeb) {
			(o.headers.Authorization =
				(header || {}).Authorization || getPrefs("public_token") || ""),
				(o.headers["u-terminal"] = "PUBLIC");
		}
		var oldContentType = o.headers["content-type"];
		if (oldContentType == "file") {
			delete o.headers["content-type"];
		}
		if (platform() == "app" && logText) {
			console.log(logText + o.method + "：" + JSON.stringify(o));
		}
		var cbFun = function cbFun(ret, err) {
			if (isFunction(callback)) {
				// if(isObject(err)){
				// 	try{
				// 		ret = JSON.parse(err.msg);
				// 	}catch(e){
				// 		ret = JSON.parse(JSON.stringify(err));
				// 	}
				// 	err = null;
				// }
				if (platform() == "app" && logText) {
					console.log(
						"得到" + logText + "返回结果：" + JSON.stringify(ret ? ret : err)
					);
				}
				if (isObject(ret)) {
					ret.message = ret.message || ret.msg || "";
					var errcode = ret.code || "";
					if ((errcode == 302 || errcode == 2) && cacheType != "login") {
						cleanAllMsg();
						sendEvent({
							name: "index",
							extra: {type: "verificationToken", errmsg: ret.message}
						});
					}
					// if(!isObject(ret.data)){
					// 	ret.data = "";
					// }
				}
				callback(ret, err);
			}
		};
		if (platform() == "web") {
			var xhr = new XMLHttpRequest();
			xhr.open(o.method, o.url);
			for (var header in o.headers) {
				xhr.setRequestHeader(header, o.headers[header]);
			}
			var sendValue = "";
			if (oldContentType.indexOf("x-www-form-urlencoded") != -1) {
				var dValue = o.data.values || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue +=
						(!sendValue ? "" : "&") + vItem + "=" + encodeURIComponent(dValue[vItem]);
				}
			} else if (oldContentType.indexOf("file") != -1) {
				sendValue = new FormData();
				var dValue = o.data.values || {};
				var fileValue = o.data.files || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue.append(vItem, encodeURIComponent(dValue[vItem]));
				}
				for (var vItem in fileValue) {
					sendValue.append(vItem, fileValue[vItem]);
				}
			} else {
				sendValue = o.data.body || JSON.stringify(o.data.values);
			}
			xhr.onreadystatechange = function() {
				if (xhr.readyState === XMLHttpRequest.DONE) {
					var ret, err;
					if (!xhr.responseText) {
						err = {};
					} else {
						var response = this.responseText;
						if (o.dataType == "json") {
							try {
								ret = JSON.parse(response);
							} catch (e) {
								err = {msg: response};
							}
						} else {
							ret = response;
						}
					}
					cbFun(ret, err);
				}
			};
			xhr.send(sendValue);
		} else {
			api.cancelAjax({tag: tag});
			api.ajax(o, cbFun);
		}
	}

	function hasPermission(one_per) {
		if (platform() == "app") {
			if (!one_per) return;
			var rets = api.hasPermission({list: one_per.split(",")});
			if (one_per.indexOf(",") != -1) {
				alert("判断结果：" + JSON.stringify(rets));
				return;
			} else {
				return rets;
			}
		}
	}

	function requestPermission(one_per, callback, _fName) {
		if (platform() == "app") {
			if (!one_per) return;
			api.requestPermission({list: one_per.split(",")}, function(ret) {
				console.log(JSON.stringify(ret));
				ret.list.forEach(function(_eItem, _eIndex, _eArr) {
					sendEvent(_eItem.name + "Per_" + _fName, {granted: _eItem.granted});
				});
				isFunction(callback) && callback(ret, err);
			});
		}
	}

	function confirmPer(perm, _fName, _reason) {
		if (platform() == "app") {
			var has = hasPermission(perm);
			if (!has || !has[0] || !has[0].granted) {
				var hintWord = {
					camera: "相机",
					storage: "存储",
					photos: "照片",
					microphone: "麦克风",
					location: "位置",
					phone: "电话",
					"phone-r": "通话状态",
					"phone-r-log": "通话记录"
				};

				var iosHint =
					"请在" +
					(api.uiMode == "phone" ? "iPhone" : "iPad") +
					"的“设置-隐私-" +
					hintWord[perm] +
					"”中请允许访问" +
					hintWord[perm] +
					(_reason ? "，" + _reason : "。");
				var androidHint =
					"使用该功能需要" +
					hintWord[perm] +
					"权限，" +
					(_reason || "请前往系统设置开启权限。");
				alert(
					{
						title: "无法使用" + hintWord[perm],
						msg: api.systemType == "ios" ? iosHint : androidHint,
						buttons: ["下一步", "取消"]
					},
					function(ret) {
						if (1 == ret.buttonIndex) {
							requestPermission(perm, null, _fName);
						} else {
							sendEvent(perm + "Per_" + _fName, {granted: false});
						}
					}
				);
				return false;
			}
			return true;
		}
		return true;
	}

	//全局页面引用变量
	var G = {
		sysSign: sysSign(), //人大政协标识
		pageWidth: "", //页面总宽度
		onShowNum: -1, //当前页面展示次数 1为首次
		appName: "", //app名字
		appFont: "", //app全局字体
		appFontSize: 0, //app全局字体大小
		appTheme: "", //app全局主题色
		headTheme: "", //head全局主题色
		headColor: "", //标题前景色
		headTitle: "", //标题栏文字
		loginInfo: "",

		careMode: false, //是否启用了关怀模式
		systemtTypeIsPlatform: false, //系统类型是否是平台版
		isAppReview: false, //app是否上架期间 隐藏和显示部分功能
		v: "", //缓存版本号

		uId: "", //普通用户id
		userId: "", //当前用户id 账号id
		userName: "", //当前用户名字
		userImg: "", //当前用户头像
		areaId: "", //当前地区id
		specialRoleKeys: [], //当前用户角色集合
		isAdmin: false, //是否管理员 拥有所有权限
		grayscale: "", //全局置灰
		watermark: "", //全局水印

		// chatInfos:[],
		// chatInfoTask:[],

		alertPop: {
			// alert提示框
			show: false
		},

		actionSheetPop: {
			//actionSheet弹出框
			show: false
		},

		imgPreviewerPop: {
			//图片预览
			show: false
		},

		areasPop: {
			// 全局地区弹窗
			show: false
		},

		numInputPop: {
			// 全局数字弹窗
			show: false
		},

		favoritePop: {
			//收藏弹窗
			show: false
		},

		sharePosterPop: {
			//分享海报
			show: false
		},

		sharePop: {
			//分享
			show: false
		},

		identifyAudioPop: {
			//语音输入
			show: false
		},

		selectPop: {
			//单多选
			show: false
		},

		qrcodePop: {
			//h5扫码
			show: false
		},

		addressBookPop: {
			//通讯录
			show: false
		},

		fileListPop: {
			//选择文件
			show: false
		}
	};

	//默认情况下是否展示标题栏
	function showHeader() {
		return platform() == "app";
	}

	//计算头部离顶上距离
	function headerTop(_type) {
		if (platform() == "mp" && _type) {
			var wxArea = wx.getMenuButtonBoundingClientRect();
			return wxArea.top + (_type == 2 ? wxArea.height : 0);
		}
		return showHeader() ? safeArea().top : 0;
	}

	//动态加载字体和大小
	function loadConfiguration(size) {
		return (
			"font-size:" +
			((G.appFontSize || 0) + (size || 0)) +
			"px;" +
			(G.appFont != "heitiSimplified" ? "font-family:" + G.appFont + ";" : "")
		);
	}

	//动态宽度配置
	function loadConfigurationSize(size, _who) {
		var changeSize = size || 0,
			cssWidth,
			cssHeight;
		if (isArray(size)) {
			cssWidth = "width:" + (G.appFontSize + (size[0] || 0)) + "px;";
			cssHeight = "height:" + (G.appFontSize + (size[1] || 0)) + "px;";
		} else {
			cssWidth = "width:" + (G.appFontSize + changeSize) + "px;";
			cssHeight = "height:" + (G.appFontSize + changeSize) + "px;";
		}
		if (!_who) {
			return cssWidth + cssHeight;
		} else {
			return _who == "w" ? cssWidth : cssHeight;
		}
	}

	//动态计算
	function dataForNum(_num) {
		return Array.from({length: _num || 0}).fill("");
	}

	//获取item参数
	function getItemForKey(_value, _list, _key, _child) {
		if (!isParameters(_list)) return;
		var hasChild = false,
			listLength = _list.length;
		for (var i = 0; i < listLength; i++) {
			var listItem = _list[i];
			if (isArray(listItem)) {
				hasChild = true;
				var result = getItemForKey(_value, listItem, _key, true);
				if (result) return result;
			} else {
				if (!isObject(listItem)) {
					if (listItem === _value) return listItem;
				} else {
					var listItemKey = listItem[_key || "key"];
					if (isArray(listItemKey)) {
						hasChild = true;
						var result = getItemForKey(_value, listItemKey, _key, true);
						if (result) {
							return listItem;
						}
					} else if (!isObject(listItemKey) && listItemKey === _value) {
						listItem["_i"] = i;
						return listItem;
					}
				}
				if (isObject(listItem) && isArray(listItem.children)) {
					hasChild = true;
					var result = getItemForKey(_value, listItem.children, _key, true);
					if (result) return result;
				}
			}
		}
		if (!_child && !hasChild) return false;
	}

	//删除item中的元素
	function delItemForKey(_obj, _list, _key) {
		var contrastObj = !isObject(_obj) ? _obj : _obj[_key || "key"];
		for (var i = 0; i < _list.length; i++) {
			if (
				(!isObject(_list[i]) ? _list[i] : _list[i][_key || "key"]) === contrastObj
			) {
				_list.splice(i, 1);
				delItemForKey(_obj, _list, _key);
				break;
			}
		}
	}

	//web和小程序阻止底部事件
	function stopBubble$1(e) {
		if (!e) return;
		if (platform() == "web") {
			e.preventDefault();
			e.stopPropagation();
		} else if (platform() == "mp") {
			e.$_canBubble = false;
		}
	}

	//移动事件 不左滑关闭屏幕
	function touchmove() {
		G.nTouchmove = true;
		clearTimeout(G.touchmoveTask);
		G.touchmoveTask = setTimeout(function() {
			G.nTouchmove = false;
		}, 1000);
	}

	//适配图片
	function showImg(_item, _add) {
		if (_add === void 0) {
			_add = "";
		}
		var baseUrl = isObject(_item) ? _item.url || "" : _item || "";
		if (!baseUrl) return;
		if (/^(?!h|(\.)|(\/)).*/.test(baseUrl)) {
			baseUrl =
				appUrl() +
				"image/" +
				_add +
				(baseUrl.indexOf("-compress-") > -1
					? baseUrl.split("-compress-")[1]
					: baseUrl);
		}
		return baseUrl + (baseUrl.indexOf("?") != -1 ? "&" : "?") + "v=" + G.v;
	}

	//获取元素位置宽高
	function getBoundingClientRect(_id, _callback) {
		if (!document.getElementById(_id)) return;
		if (platform() == "mp") {
			document
				.getElementById(_id)
				.$$getBoundingClientRect()
				.then(function(res) {
					return _callback(res);
				});
		} else {
			return _callback(document.getElementById(_id).getBoundingClientRect());
		}
	}

	//颜色转成rgba
	function colorRgba(_color, _alpha) {
		if (!_color) return;
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		var color = _color.toLowerCase();
		if (reg.test(color)) {
			if (color.length === 4) {
				var colorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
				}
				color = colorNew;
			}
			var colorChange = [];
			for (var i = 1; i < 7; i += 2) {
				colorChange.push(parseInt("0x" + color.slice(i, i + 2)));
			}
			return (
				"rgba(" +
				colorChange.join(",") +
				"," +
				(isParameters(_alpha) ? _alpha : "1") +
				")"
			);
		} else {
			return color;
		}
	}

	//删除所有缓存
	function cleanAllMsg() {
		var exitPrefs = [
			"isAutoLogin",
			"loginPassword",
			"sys_token",
			"sys_Id",
			"sys_UserID",
			"sys_UserName",
			"sys_AppPhoto",
			"sys_Mobile",
			"sys_Position",
			"sys_aresId",
			"sys_OfficeId",
			"sys_SpecialRoleKeys",
			"sdt_signin_phone",
			"public_token",
			"last14Msg",
			"sys_unread"
		];
		exitPrefs.forEach(function(_eItem) {
			removePrefs(_eItem);
		});
	}

	//列表无数据处理
	function dealData(_type, _this, ret) {
		if (!_type) {
			_this.data.listData = [];
			_this.data.emptyBox.type = ret ? "1" : "2";
			_this.data.emptyBox.text =
				ret && ret.code != 200 ? ret.message || ret.data : "";
		} else {
			_this.data.emptyBox.text = ret
				? ret.code == 200
					? LOAD_ALL
					: ret.message || ret.data
				: NET_ERR$1;
		}
	}

	//保存登录信息
	function saveLogin(_info) {
		var infolist = _info || {};
		setPrefs("sys_Id", infolist.id);
		setPrefs("sys_UserID", infolist.accountId);
		setPrefs("sys_UserName", infolist.userName);
		setPrefs("sys_AppPhoto", infolist.headImg || infolist.photo); //headImg用户头像 photo代表头像
		setPrefs("sys_Mobile", infolist.mobile);
		setPrefs("sys_Position", infolist.position);
		if (!getPrefs("sys_aresId")) {
			setPrefs("sys_aresId", infolist.areaId);
		}
		setPrefs("sys_OfficeId", infolist.officeId);
		setPrefs(
			"sys_SpecialRoleKeys",
			JSON.stringify(infolist.specialRoleKeys || [])
		);
		if (infolist.id) {
			// //修改头像后保存下缓存数据
			setAllChatInfo({
				id: infolist.accountId,
				name: infolist.userName,
				url: infolist.headImg || infolist.photo
			});
		}
		sendEvent({name: "sys_refresh"});
	}

	//获取链接中参数
	function getOtherParam(_url) {
		if (_url.indexOf("?") != -1) {
			_url = _url.substring(_url.indexOf("?") + 1);
		}
		var params = _url.split("&"),
			rp = {};
		for (var j = 0; j < params.length; j++) {
			if (params[j]) {
				var data_key = params[j].substring(0, params[j].indexOf("="));
				if (!data_key) {
					continue;
				}
				var data_value = decodeURIComponent(
					params[j].substring(params[j].indexOf("=") + 1)
				);
				if (data_value.indexOf("{") == 0 || data_value.indexOf("[") == 0)
					data_value = JSON.parse(data_value);
				rp[data_key] = data_value;
			}
		}
		return rp;
	}

	//获取地区参数
	function getAreaForKey(_key, _all) {
		var rItem = null;
		var areas = JSON.parse(getPrefs("sys_areas") || "[]");
		if (_all || !areas.length) {
			areas = JSON.parse(getPrefs("sys_allAreas") || "[]");
		}
		if (areas.length) {
			rItem = getItemForKey(_key, areas, "id");
			if (rItem) {
				rItem.name =
					rItem.name.length > 4 ? rItem.name.substring(0, 4) + "..." : rItem.name;
			}
		}
		return rItem || {};
	}

	//获取文件类型 并返回数据
	function getFileInfo(_name) {
		var name = (_name || "").toLocaleLowerCase(),
			fileInfo = {name: "file-unknow-fill", color: "#bccbd7", type: "unknown"};
		try {
			if (name.indexOf(".") != -1)
				name = name.split(".")[name.split(".").length - 1];
			switch (name) {
				case "xlsx":
				case "xlsm":
				case "xlsb":
				case "xltx":
				case "xltm":
				case "xls":
				case "xlt":
				case "et":
				case "csv":
				case "uos": //excel格式
					fileInfo.name = "file-excel-fill";
					fileInfo.color = "#00bd76";
					fileInfo.type = "excel";
					fileInfo.convertType = "0";
					break;
				case "doc":
				case "docx":
				case "docm":
				case "dotx":
				case "dotm":
				case "dot":
				case "xps":
				case "rtf":
				case "wps":
				case "wpt":
				case "uot": //word格式
					fileInfo.name = "file-word-fill";
					fileInfo.color = "#387efa";
					fileInfo.type = "word";
					fileInfo.convertType = "0";
					break;
				case "pdf": //pdf格式
					fileInfo.name = "file-pdf-fill";
					fileInfo.color = "#e9494a";
					fileInfo.type = "pdf";
					fileInfo.convertType = "20";
					break;
				case "ppt":
				case "pptx":
				case "pps":
				case "pot":
				case "pptm":
				case "potx":
				case "potm":
				case "ppsx":
				case "ppsm":
				case "ppa":
				case "ppam":
				case "dps":
				case "dpt":
				case "uop": //ppt
					fileInfo.name = "file-ppt-fill";
					fileInfo.color = "#ff7440";
					fileInfo.type = "ppt";
					fileInfo.convertType = "0";
					break;
				case "bmp":
				case "gif":
				case "jpg":
				case "pic":
				case "png":
				case "tif":
				case "jpeg":
				case "jpe":
				case "icon":
				case "jfif":
				case "dib": //图片格式 case 'webp':
					fileInfo.name = "file-text-fill";
					fileInfo.color = "#ff7440";
					fileInfo.type = "image";
					fileInfo.convertType = "440";
					break;
				case "txt": //文本
					fileInfo.name = "file-text-fill";
					fileInfo.color = "#2696ff";
					fileInfo.type = "txt";
					fileInfo.convertType = "0";
					break;
				case "rar":
				case "zip":
				case "7z":
				case "tar":
				case "gz":
				case "jar":
				case "ios": //压缩格式
					fileInfo.name = "file-zip-fill";
					fileInfo.color = "#a5b0c0";
					fileInfo.type = "compression";
					fileInfo.convertType = "19";
					break;
				case "mp4":
				case "avi":
				case "flv":
				case "f4v":
				case "webm":
				case "m4v":
				case "mov":
				case "3gp":
				case "rm":
				case "rmvb":
				case "mkv":
				case "mpeg":
				case "wmv": //视频格式
					fileInfo.name = "file-music-fill";
					fileInfo.color = "#e14a4a";
					fileInfo.type = "video";
					fileInfo.convertType = "450";
					break;
				case "mp3":
				case "m4a":
				case "amr":
				case "pcm":
				case "wav":
				case "aiff":
				case "aac":
				case "ogg":
				case "wma":
				case "flac":
				case "alac":
				case "wma":
				case "cda": //音频格式
					fileInfo.name = "file-music-fill";
					fileInfo.color = "#8043ff";
					fileInfo.type = "voice";
					fileInfo.convertType = "660";
					break;
				case "folder": //文件夹
					fileInfo.name = "folder-2-fill";
					fileInfo.color = "#ffd977";
					fileInfo.type = "folder";
					break;
			}
		} catch (e) {
			console.log(e.message);
		}
		return fileInfo;
	}

	//展示省略文字
	function showTextSize(_text, _size, _middle) {
		if (_size && _text) {
			if (_text.length > _size) {
				if (_middle) {
					var mSize = _size / 2;
					var nLast = getSizeText(_text, mSize);
					var nNext = getSizeText(_text, mSize, 1);
					if (nLast.length + nNext.length < _text.length) {
						_text = nLast + "..." + nNext;
					}
				} else {
					var nText = getSizeText(_text, _size);
					_text = nText + (nText.length < _text.length ? "..." : "");
				}
			}
		}
		return _text;
	}

	function getSizeText(_text, _size, _next) {
		var texts = _text.split("");
		var nowSize = 0,
			nowLength = 0;
		if (_next) {
			for (var i = texts.length - 1; i >= 0; i--) {
				nowSize += /[\u4E00-\u9FA5]|[\u0800-\u4E00]/.test(texts[i]) ? 1 : 0.7;
				nowLength++;
				if (nowSize >= _size) {
					break;
				}
			}
			return _text.substring(texts.length - nowLength);
		} else {
			for (var i = 0; i < texts.length; i++) {
				nowSize += /[\u4E00-\u9FA5]|[\u0800-\u4E00]/.test(texts[i]) ? 1 : 0.7;
				nowLength++;
				if (nowSize >= _size) {
					break;
				}
			}
			return _text.substring(0, nowLength);
		}
	}

	//替换非第一个字符为*
	function replaceExceptFirst(inputString) {
		return inputString.replace(/(\S)(\S*)/g, function(match, firstChar, rest) {
			return firstChar + "*".repeat(rest.length);
		});
	}

	//人大政协标识  rd人大 zx政协
	function sysSign() {
		return getPrefs("sys_sign") || "rd";
	}

	//配置地址
	function appUrl() {
		var prot = sysSign() == "rd" ? "20169" : "20170";
		return (
			getPrefs("sys_appUrl") || "https://productpc.cszysoft.com:" + prot + "/lzt/"
		);
	}
	//tomcat配置地址
	function tomcatAddress() {
		return (
			getPrefs("sys_tomcatAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http://cszysoft.com:9090/"
				: "https://cszysoft.com:9091/")
		);
	}
	//分享地址
	function shareAddress(_type) {
		if (_type == 1 && platform() != "mp") return "../../";
		return (
			getPrefs("sys_shareAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http:"
				: "https:") +
				"//cszysoft.com/appShare/" +
				(sysSign() == "rd" ? "platform5rd/" : "platform5zx/")
		);
	}
	//融云唯一前缀
	function chatHeader() {
		return getPrefs("sys_chatHeader") || "platform5" + sysSign();
	}
	//融云正测
	function chatEnvironment() {
		return getPrefs("sys_chatEnvironment") || "1";
	}
	//当前页面地区id
	function areaId() {
		return (
			(G._this && G._this.data.area ? G._this.data.area.key : "") ||
			pageParam().areaId ||
			getPrefs("sys_aresId") ||
			getPrefs("sys_platform") ||
			""
		);
	}

	var SECONDS_A_MINUTE = 60;
	var SECONDS_A_HOUR = SECONDS_A_MINUTE * 60;
	var SECONDS_A_DAY = SECONDS_A_HOUR * 24;
	var SECONDS_A_WEEK = SECONDS_A_DAY * 7;
	var MILLISECONDS_A_SECOND = 1e3;
	var MILLISECONDS_A_MINUTE = SECONDS_A_MINUTE * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_HOUR = SECONDS_A_HOUR * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_DAY = SECONDS_A_DAY * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_WEEK = SECONDS_A_WEEK * MILLISECONDS_A_SECOND; // English locales

	var MS = "millisecond";
	var S = "second";
	var MIN = "minute";
	var H = "hour";
	var D = "day";
	var W = "week";
	var M = "month";
	var Q = "quarter";
	var Y = "year";
	var DATE = "date";
	var FORMAT_DEFAULT = "YYYY-MM-DDTHH:mm:ssZ";
	var INVALID_DATE_STRING = "Invalid Date"; // regex

	var REGEX_PARSE = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/;
	var REGEX_FORMAT = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;

	var en = {
		name: "en",
		weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split(
			"_"
		),
		months: "January_February_March_April_May_June_July_August_September_October_November_December".split(
			"_"
		)
	};

	var padStart = function padStart(string, length, pad) {
		var s = String(string);
		if (!s || s.length >= length) return string;
		return "" + Array(length + 1 - s.length).join(pad) + string;
	};
	var padZoneStr = function padZoneStr(instance) {
		var negMinutes = -instance.utcOffset();
		var minutes = Math.abs(negMinutes);
		var hourOffset = Math.floor(minutes / 60);
		var minuteOffset = minutes % 60;
		return (
			"" +
			(negMinutes <= 0 ? "+" : "-") +
			padStart(hourOffset, 2, "0") +
			":" +
			padStart(minuteOffset, 2, "0")
		);
	};
	var monthDiff = function monthDiff(a, b) {
		if (a.date() < b.date()) return -monthDiff(b, a);
		var wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month());
		var anchor = a.clone().add(wholeMonthDiff, M);
		var c = b - anchor < 0;
		var anchor2 = a.clone().add(wholeMonthDiff + (c ? -1 : 1), M);
		return +(
			-(
				wholeMonthDiff +
				(b - anchor) / (c ? anchor - anchor2 : anchor2 - anchor)
			) || 0
		);
	};
	var absFloor = function absFloor(n) {
		return n < 0 ? Math.ceil(n) || 0 : Math.floor(n);
	};
	var prettyUnit = function prettyUnit(u) {
		var special = {
			M: M,
			y: Y,
			w: W,
			d: D,
			D: DATE,
			h: H,
			m: MIN,
			s: S,
			ms: MS,
			Q: Q
		};

		return (
			special[u] ||
			String(u || "")
				.toLowerCase()
				.replace(/s$/, "")
		);
	};
	var isUndefined = function isUndefined(s) {
		return s === undefined;
	};
	var U = {
		s: padStart,
		z: padZoneStr,
		m: monthDiff,
		a: absFloor,
		p: prettyUnit,
		u: isUndefined
	};

	var L = "en";
	var Ls = {};
	Ls[L] = en;
	var isDayjs = function isDayjs(d) {
		return d instanceof Dayjs;
	};
	var parseLocale = function parseLocale(preset, object, isLocal) {
		var l;
		if (!preset) return L;
		if (typeof preset === "string") {
			var presetLower = preset.toLowerCase();
			if (Ls[presetLower]) {
				l = presetLower;
			}
			if (object) {
				Ls[presetLower] = object;
				l = presetLower;
			}
			var presetSplit = preset.split("-");
			if (!l && presetSplit.length > 1) {
				return parseLocale(presetSplit[0]);
			}
		} else {
			var name = preset.name;
			Ls[name] = preset;
			l = name;
		}
		if (!isLocal && l) L = l;
		return l || (!isLocal && L);
	};
	var dayjs = function dayjs(date, c) {
		if (isDayjs(date)) {
			return date.clone();
		}
		var cfg = typeof c === "object" ? c : {};
		cfg.date = date;
		cfg.args = arguments;
		return new Dayjs(cfg);
	};
	var wrapper = function wrapper(date, instance) {
		return dayjs(date, {
			locale: instance.$L,
			utc: instance.$u,
			x: instance.$x,
			$offset: instance.$offset
		});
	};
	var Utils = U;
	Utils.l = parseLocale;
	Utils.i = isDayjs;
	Utils.w = wrapper;
	var parseDate = function parseDate(cfg) {
		var date = cfg.date,
			utc = cfg.utc;
		if (date === null) return new Date(NaN);
		if (Utils.u(date)) return new Date();
		if (date instanceof Date) return new Date(date);
		if (typeof date === "string" && !/Z$/i.test(date)) {
			var d = date.match(REGEX_PARSE);
			if (d) {
				var m = d[2] - 1 || 0;
				var ms = (d[7] || "0").substring(0, 3);
				if (utc) {
					return new Date(
						Date.UTC(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms)
					);
				}
				return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);
			}
		}
		return new Date(date);
	};
	var Dayjs = (function() {
		function Dayjs(cfg) {
			this.$L = parseLocale(cfg.locale, null, true);
			this.parse(cfg);
		}
		var _proto = Dayjs.prototype;
		_proto.parse = function parse(cfg) {
			this.$d = parseDate(cfg);
			this.$x = cfg.x || {};
			this.init();
		};
		_proto.init = function init() {
			var $d = this.$d;
			this.$y = $d.getFullYear();
			this.$M = $d.getMonth();
			this.$D = $d.getDate();
			this.$W = $d.getDay();
			this.$H = $d.getHours();
			this.$m = $d.getMinutes();
			this.$s = $d.getSeconds();
			this.$ms = $d.getMilliseconds();
		};
		_proto.$utils = function $utils() {
			return Utils;
		};
		_proto.isValid = function isValid() {
			return !(this.$d.toString() === INVALID_DATE_STRING);
		};
		_proto.isSame = function isSame(that, units) {
			var other = dayjs(that);
			return this.startOf(units) <= other && other <= this.endOf(units);
		};
		_proto.isAfter = function isAfter(that, units) {
			return dayjs(that) < this.startOf(units);
		};
		_proto.isBefore = function isBefore(that, units) {
			return this.endOf(units) < dayjs(that);
		};
		_proto.$g = function $g(input, get, set) {
			if (Utils.u(input)) return this[get];
			return this.set(set, input);
		};
		_proto.unix = function unix() {
			return Math.floor(this.valueOf() / 1000);
		};
		_proto.valueOf = function valueOf() {
			return this.$d.getTime();
		};
		_proto.startOf = function startOf(units, _startOf) {
			var _this = this;
			var isStartOf = !Utils.u(_startOf) ? _startOf : true;
			var unit = Utils.p(units);
			var instanceFactory = function instanceFactory(d, m) {
				var ins = Utils.w(
					_this.$u ? Date.UTC(_this.$y, m, d) : new Date(_this.$y, m, d),
					_this
				);
				return isStartOf ? ins : ins.endOf(D);
			};
			var instanceFactorySet = function instanceFactorySet(method, slice) {
				var argumentStart = [0, 0, 0, 0];
				var argumentEnd = [23, 59, 59, 999];
				return Utils.w(
					_this
						.toDate()
						[method].apply(
							_this.toDate("s"),
							(isStartOf ? argumentStart : argumentEnd).slice(slice)
						),
					_this
				);
			};
			var $W = this.$W,
				$M = this.$M,
				$D = this.$D;
			var utcPad = "set" + (this.$u ? "UTC" : "");
			switch (unit) {
				case Y:
					return isStartOf ? instanceFactory(1, 0) : instanceFactory(31, 11);
				case M:
					return isStartOf ? instanceFactory(1, $M) : instanceFactory(0, $M + 1);
				case W: {
					var weekStart = this.$locale().weekStart || 0;
					var gap = ($W < weekStart ? $W + 7 : $W) - weekStart;
					return instanceFactory(isStartOf ? $D - gap : $D + (6 - gap), $M);
				}
				case D:
				case DATE:
					return instanceFactorySet(utcPad + "Hours", 0);
				case H:
					return instanceFactorySet(utcPad + "Minutes", 1);
				case MIN:
					return instanceFactorySet(utcPad + "Seconds", 2);
				case S:
					return instanceFactorySet(utcPad + "Milliseconds", 3);
				default:
					return this.clone();
			}
		};
		_proto.endOf = function endOf(arg) {
			return this.startOf(arg, false);
		};
		_proto.$set = function $set(units, _int) {
			var _C$D$C$DATE$C$M$C$Y$C;
			var unit = Utils.p(units);
			var utcPad = "set" + (this.$u ? "UTC" : "");
			var name = ((_C$D$C$DATE$C$M$C$Y$C = {}),
			(_C$D$C$DATE$C$M$C$Y$C[D] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[DATE] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[M] = utcPad + "Month"),
			(_C$D$C$DATE$C$M$C$Y$C[Y] = utcPad + "FullYear"),
			(_C$D$C$DATE$C$M$C$Y$C[H] = utcPad + "Hours"),
			(_C$D$C$DATE$C$M$C$Y$C[MIN] = utcPad + "Minutes"),
			(_C$D$C$DATE$C$M$C$Y$C[S] = utcPad + "Seconds"),
			(_C$D$C$DATE$C$M$C$Y$C[MS] = utcPad + "Milliseconds"),
			_C$D$C$DATE$C$M$C$Y$C)[unit];
			var arg = unit === D ? this.$D + (_int - this.$W) : _int;
			if (unit === M || unit === Y) {
				var date = this.clone().set(DATE, 1);
				date.$d[name](arg);
				date.init();
				this.$d = date.set(DATE, Math.min(this.$D, date.daysInMonth())).$d;
			} else if (name) this.$d[name](arg);
			this.init();
			return this;
		};
		_proto.set = function set(string, _int2) {
			return this.clone().$set(string, _int2);
		};
		_proto.get = function get(unit) {
			return this[Utils.p(unit)]();
		};
		_proto.add = function add(number, units) {
			var _this2 = this,
				_C$MIN$C$H$C$S$unit;
			number = Number(number);
			var unit = Utils.p(units);
			var instanceFactorySet = function instanceFactorySet(n) {
				var d = dayjs(_this2);
				return Utils.w(d.date(d.date() + Math.round(n * number)), _this2);
			};
			if (unit === M) {
				return this.set(M, this.$M + number);
			}
			if (unit === Y) {
				return this.set(Y, this.$y + number);
			}
			if (unit === D) {
				return instanceFactorySet(1);
			}
			if (unit === W) {
				return instanceFactorySet(7);
			}
			var step =
				((_C$MIN$C$H$C$S$unit = {}),
				(_C$MIN$C$H$C$S$unit[MIN] = MILLISECONDS_A_MINUTE),
				(_C$MIN$C$H$C$S$unit[H] = MILLISECONDS_A_HOUR),
				(_C$MIN$C$H$C$S$unit[S] = MILLISECONDS_A_SECOND),
				_C$MIN$C$H$C$S$unit)[unit] || 1;
			var nextTimeStamp = this.$d.getTime() + number * step;
			return Utils.w(nextTimeStamp, this);
		};
		_proto.subtract = function subtract(number, string) {
			return this.add(number * -1, string);
		};
		_proto.format = function format(formatStr) {
			var _this3 = this;
			var locale = this.$locale();
			if (!this.isValid()) return locale.invalidDate || INVALID_DATE_STRING;
			var str = formatStr || FORMAT_DEFAULT;
			var zoneStr = Utils.z(this);
			var $H = this.$H,
				$m = this.$m,
				$M = this.$M;
			var weekdays = locale.weekdays,
				months = locale.months,
				meridiem = locale.meridiem;
			var getShort = function getShort(arr, index, full, length) {
				return (
					(arr && (arr[index] || arr(_this3, str))) || full[index].slice(0, length)
				);
			};
			var get$H = function get$H(num) {
				return Utils.s($H % 12 || 12, num, "0");
			};
			var meridiemFunc =
				meridiem ||
				function(hour, minute, isLowercase) {
					var m = hour < 12 ? "AM" : "PM";
					return isLowercase ? m.toLowerCase() : m;
				};
			var matches = {
				YY: String(this.$y).slice(-2),
				YYYY: this.$y,
				M: $M + 1,
				MM: Utils.s($M + 1, 2, "0"),
				MMM: getShort(locale.monthsShort, $M, months, 3),
				MMMM: getShort(months, $M),
				D: this.$D,
				DD: Utils.s(this.$D, 2, "0"),
				d: String(this.$W),
				dd: getShort(locale.weekdaysMin, this.$W, weekdays, 2),
				ddd: getShort(locale.weekdaysShort, this.$W, weekdays, 3),
				dddd: weekdays[this.$W],
				H: String($H),
				HH: Utils.s($H, 2, "0"),
				h: get$H(1),
				hh: get$H(2),
				a: meridiemFunc($H, $m, true),
				A: meridiemFunc($H, $m, false),
				m: String($m),
				mm: Utils.s($m, 2, "0"),
				s: String(this.$s),
				ss: Utils.s(this.$s, 2, "0"),
				SSS: Utils.s(this.$ms, 3, "0"),
				Z: zoneStr
			};

			return str.replace(REGEX_FORMAT, function(match, $1) {
				return $1 || matches[match] || zoneStr.replace(":", "");
			});
		};
		_proto.utcOffset = function utcOffset() {
			return -Math.round(this.$d.getTimezoneOffset() / 15) * 15;
		};
		_proto.diff = function diff(input, units, _float) {
			var _C$Y$C$M$C$Q$C$W$C$D$;
			var unit = Utils.p(units);
			var that = dayjs(input);
			var zoneDelta =
				(that.utcOffset() - this.utcOffset()) * MILLISECONDS_A_MINUTE;
			var diff = this - that;
			var result = Utils.m(this, that);
			result =
				((_C$Y$C$M$C$Q$C$W$C$D$ = {}),
				(_C$Y$C$M$C$Q$C$W$C$D$[Y] = result / 12),
				(_C$Y$C$M$C$Q$C$W$C$D$[M] = result),
				(_C$Y$C$M$C$Q$C$W$C$D$[Q] = result / 3),
				(_C$Y$C$M$C$Q$C$W$C$D$[W] = (diff - zoneDelta) / MILLISECONDS_A_WEEK),
				(_C$Y$C$M$C$Q$C$W$C$D$[D] = (diff - zoneDelta) / MILLISECONDS_A_DAY),
				(_C$Y$C$M$C$Q$C$W$C$D$[H] = diff / MILLISECONDS_A_HOUR),
				(_C$Y$C$M$C$Q$C$W$C$D$[MIN] = diff / MILLISECONDS_A_MINUTE),
				(_C$Y$C$M$C$Q$C$W$C$D$[S] = diff / MILLISECONDS_A_SECOND),
				_C$Y$C$M$C$Q$C$W$C$D$)[unit] || diff;
			return _float ? result : Utils.a(result);
		};
		_proto.daysInMonth = function daysInMonth() {
			return this.endOf(M).$D;
		};
		_proto.$locale = function $locale() {
			return Ls[this.$L];
		};
		_proto.locale = function locale(preset, object) {
			if (!preset) return this.$L;
			var that = this.clone();
			var nextLocaleName = parseLocale(preset, object, true);
			if (nextLocaleName) that.$L = nextLocaleName;
			return that;
		};
		_proto.clone = function clone() {
			return Utils.w(this.$d, this);
		};
		_proto.toDate = function toDate() {
			return new Date(this.valueOf());
		};
		_proto.toJSON = function toJSON() {
			return this.isValid() ? this.toISOString() : null;
		};
		_proto.toISOString = function toISOString() {
			return this.$d.toISOString();
		};
		_proto.toString = function toString() {
			return this.$d.toUTCString();
		};
		return Dayjs;
	})();
	var proto = Dayjs.prototype;
	dayjs.prototype = proto;
	[
		["$ms", MS],
		["$s", S],
		["$m", MIN],
		["$H", H],
		["$W", D],
		["$M", M],
		["$y", Y],
		["$D", DATE]
	].forEach(function(g) {
		proto[g[1]] = function(input) {
			return this.$g(input, g[0], g[1]);
		};
	});
	dayjs.extend = function(plugin, option) {
		if (!plugin.$i) {
			plugin(option, Dayjs, dayjs);
			plugin.$i = true;
		}
		return dayjs;
	};
	dayjs.locale = parseLocale;
	dayjs.isDayjs = isDayjs;
	dayjs.unix = function(timestamp) {
		return dayjs(timestamp * 1e3);
	};
	dayjs.en = Ls[L];
	dayjs.Ls = Ls;
	dayjs.p = {};

	//获取列表未读
	function getListUnread(_param, _callback) {
		ajaxProcess(
			{
				url: appUrl() + "redDot/unread",
				param: {businessCode: _param.code},
				tag: "unread",
				name: "未读"
			},
			function(ret, err) {
				_param.data = ret ? ret.data || [] : [];
				_callback && _callback(ret, err);
			}
		);
	}

	//获取文本管理列表
	function getListApptext(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "apptext/type/" + _param.pt,
				param: setNewJSON({}, _param.param),
				tag: "apptext",
				name: "文本管理",
				header: {Authorization: ""}
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.pt = _param.pt;
						item.code = "apptext";
						item.id = _eItem.id || "";
						item.title = _eItem.title || "";
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取资讯栏目
	function getColumn5(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "newsColumn/app/list",
				param: setNewJSON({pageNo: "1", pageSize: "999"}, _param.param),
				areaId: _param.areaId,
				tag: "newsColumn",
				name: "栏目"
			},
			function(ret, err) {
				_callback && _callback(ret, err);
			}
		);
	}

	//获取通知公告栏目
	function getColumn22(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "notificationChannel/list",
				param: setNewJSON({pageNo: "1", pageSize: "999"}, _param.param),
				tag: "notificationChannel",
				name: "栏目"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.key = _eItem.id || "";
						item.value = _eItem.channelName || "";
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取学习培训栏目
	function getColumn31(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "studypaper/mycount",
				param: setNewJSON({query: {businessCode: "study_online"}}, _param.param),
				tag: "studypaperCol",
				name: "栏目"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.key = _eItem.code || "";
						item.value = _eItem.name || "";
						item.count = _eItem.count || "";
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取资讯列表
	function getList5(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "newsContent/app/batchList",
				param: setNewJSON({query: {isNeedContent: false}}, _param.param),
				areaId: _param.areaId,
				tag: "batchList",
				name: "列表"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.code = _eItem.moduleType == "1" ? module5.code : module7.code;
						item.sourceModuleName = _eItem.sourceModuleName || "";
						item.showCode = _eItem.showCodeName || "";
						item.title = _eItem.title || "";
						item.content = removeTag(_eItem.content || "");
						item.id = _eItem.sourceId || _eItem.id || "";
						var infoPic = _eItem.infoPic || "";
						item.url = infoPic
							? infoPic.indexOf(",") != -1
								? infoPic.split(",").map(function(obj) {
										return {url: obj};
								  })
								: infoPic
							: "";
						item.videoHrefs = _eItem.infoVideo || "";
						var fileLink = _eItem.fileLink || [];
						if (isParameters(fileLink) && fileLink.length) {
							var useType1 = [],
								useType2 = [];
							fileLink.forEach(function(_mItem) {
								if (
									_mItem.fileId ||
									(isArray(_mItem.attachments) && _mItem.attachments.length)
								) {
									if (_mItem.useType == "1" && useType1.length < 3) {
										useType1.push({url: _mItem.attachments[0].newFileName});
									} else if (_mItem.useType == "2") {
										useType2.push(
											_mItem.fileId ||
												(_mItem.attachments.length ? _mItem.attachments[0].id : "")
										);
									}
								}
							});
							if (useType1.length) {
								item.url = useType1.length > 1 ? useType1 : useType1[0].url;
							}
							if (useType2.length && useType2[0]) {
								item.videoHrefs = useType2[0];
							}
						}
						item.source = _eItem.source || "";
						item.isTop = _eItem.isTop || "";
						item.link = _eItem.contentType == 2 ? _eItem.linkUrl || "" : "";
						item.time = _eItem.pubTime || "";
						if (item.videoHrefs) {
							item.poster = item.url || "../../image/bg_launch.png";
							item.autoplay = false;
						}
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取活动
	function getList10(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "servantactivity/list",
				param: setNewJSON({}, _param.param),
				tag: "servantactivity",
				name: "活动"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.code = module10.code;
						item.businessCode = module10.businessCode;
						item.id = _eItem.id || "";
						item.title = _eItem.title || "";
						item.content = removeTag(_eItem.content || "");
						item.state = _eItem.activityStatus;
						item.startTime = _eItem.beginTime;
						item.endTime = _eItem.endTime;
						item.signupStopTime = _eItem.signupStopTime; //报名截止
						item.joinBeginTime = _eItem.joinBeginTime; //签到开始
						item.joinEndTime = _eItem.joinEndTime; //签到结束
						item.joinStatus = _eItem.joinStatus || ""; //当前状态 nosign暂未参与 sign已报名 join已签到 leaveing请假中 leavepass请假通过 leavenopass请假未通过
						var isSignUp = dayjs().isBefore(dayjs(item.signupStopTime));
						var isSignIn =
							dayjs(item.joinBeginTime).isBefore(dayjs()) &&
							dayjs().isBefore(dayjs(item.joinEndTime));
						if (
							isSignIn &&
							(item.joinStatus == "sign" || item.joinStatus == "join")
						) {
							item.state = "签到中";
						} else if (isSignUp) {
							//在报名截止之前
							item.state = "报名中";
						} else if (isSignIn) {
							//在签到中
							item.state = "签到中";
						}
						item.name = _eItem.publishOrganize;
						item.place = _eItem.address;
						item.isRedDot = _eItem.isRedDot || ""; //是否未读
						item.canSign = _eItem.canSign; //是否能报名
						item.rightState =
							{
								leaveing: "请假待审批",
								leavepass: "请假通过",
								leavenopass: "请假未通过"
							}[item.joinStatus] || "";
						if (!item.rightState && item.state == "已结束") {
							item.rightState = item.joinStatus == "nosign" ? "未参与" : "已参与";
						}
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//个人消息
	function getList14(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "boxMessage/list",
				param: setNewJSON({}, _param.param),
				tag: "boxMessagelist",
				name: "个人消息"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.code = module14.code;
						item.isDelete = _eItem.isDisabled == 1;
						item.title = _eItem.content || "";
						item.isRedDot = _eItem.hasRead === false ? 1 : 0; //是否未读
						item.time = dayjs(_eItem.createDate).format("YYYY-MM-DD HH:mm");
						item.id = _eItem.businessId || "";
						item.mobileRedirectUrl = _eItem.mobileRedirectUrl;
						item.businessName = _eItem.moduleName;
						item.businessCode = _eItem.businessCode;
						item.moduleCode = _eItem.moduleCode;
						item.oldCode = module14.businessCode;
						item.oldId = _eItem.id;
						item.addParam = _eItem.extParam;
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取通知公告
	function getList22(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "notification/list",
				param: setNewJSON(
					{query: {isDraft: 0}, tableId: "id_message_notification"},
					_param.param
				),
				tag: "notification/list",
				name: "通知公告"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.code = module22.code;
						item.businessCode = module22.businessCode;
						item.id = _eItem.id || "";
						item.title = _eItem.theme || "";
						item.content = removeTag(_eItem.content || "");
						item.time = _eItem.publishTime || _eItem.createDate || "";
						item.source = _eItem.publishOfficeId || "";
						item.channelId = _eItem.channelId || ""; //类型
						item.isTop = _eItem.isTop || ""; //置顶
						item.attachmentIds = _eItem.attachmentIds || ""; //附件
						item.isReceipt = _eItem.isReceipt || ""; //回执1
						item.isRedDot = _eItem.isRedDot || ""; //是否未读
						var urgentLevel = _eItem.urgentLevel || {};
						if (urgentLevel.value) {
							item.urgentLevel = {
								color: {"1": "#67C23A", "2": "#E6A23C", "3": "#F56C6C"}[
									urgentLevel.value
								],
								text: urgentLevel.name
							};
						}
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取培训考试
	function getList31(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "studypaper/mylist",
				param: setNewJSON({query: {businessCode: "study_online"}}, _param.param),
				tag: "studypaper/mylist",
				name: "培训考试"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.code = module31.code;
						item.businessCode = module31.businessCode;
						item.id = _eItem.id || "";
						item.title = _eItem.name || "";
						item.info = _eItem.info;
						item.examineId = _eItem.examineId;
						item.startTime = _eItem.beginTime; //开始时间
						item.endTime = _eItem.endTime; //结束时间
						item.state = "进行中";
						if (item.startTime && item.endTime) {
							item.state =
								dayjs().valueOf() < item.startTime
									? "未开始"
									: dayjs().valueOf() > item.endTime
									? "已截止"
									: "进行中";
						}
						var studyLimitType = (_eItem.studyLimitType || {}).value;
						item.studyLimitType = studyLimitType;
						item.limitCount =
							studyLimitType == 1
								? _eItem.limitCount + "分钟"
								: _eItem.studyLimitType.label;
						item.examStatus = _eItem.examStatus || "1"; //1没参与 2正在参与 3已参与
						item.isPass = _eItem.isPass; //合格状态
						item.score = _eItem.score || "0"; //	我的考试得分
						item.appState = 0; //0查看详情 1查看结果 2立即考试 3继续考试 4已考试
						item.openTime =
							(_eItem.studyOpenType || {}).value == 2 ? _eItem.openTime : "";
						if (item.state == "未开始") {
							item.addText = "考试马上就要开始了，请耐心等待！";
						} else if (item.state == "已截止") {
							if (item.examStatus == 1) {
								item.addText = "您未参与本次考试，没有成绩";
							} else {
								if (item.openTime && dayjs().valueOf() < item.openTime) {
									item.addText = "您已参与本次考试，请耐心等待考试成绩！";
									item.appState = 4;
								} else {
									item.addText = "您已参与本次考试，考试得分：";
									item.showscore = true;
									item.appState = 1;
								}
							}
						} else {
							if (item.examStatus == 1) {
								item.addText = "您未参与本次考试，赶紧来考试吧！";
								item.appState = 2;
							} else if (item.examStatus == 2) {
								item.addText = "你已参与本次考试，但未交卷，请继续考试！";
								item.appState = 3;
							} else {
								if (item.openTime && dayjs().valueOf() < item.openTime) {
									item.addText = "您已参与本次考试，请耐心等待考试成绩！";
									item.appState = 4;
								} else {
									item.addText = "您已参与本次考试，考试得分：";
									item.showscore = true;
									item.appState = 1;
								}
							}
						}
						item.btnText = ["查看详情", "查看结果", "立即考试", "继续考试", "已考试"][
							item.appState
						];
						item.joinCount = _eItem.joinCount; //参与人数
						item.isRedDot = _eItem.isRedDot || ""; //是否未读
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取培训考试成绩单
	function getList31_1(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "studyexamine/list",
				param: setNewJSON(
					{query: {businessCode: "study_online", userId: G.uId}},
					_param.param
				),
				tag: "studyexamine/list",
				name: "成绩单"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.code = module31_1.code;
						item.businessCode = module31_1.businessCode;
						item.id = _eItem.paperId || ""; //id
						item.title = _eItem.paperName || ""; //标题
						item.fullScore = _eItem.fullScore || "";
						item.scoreLevel = _eItem.scoreLevel;
						item.examCount = _eItem.examCount;
						item.time = _eItem.overTime;
						item.score = _eItem.score;
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取当前工作人员管理工作站栏目
	function getWorkerStation(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "stationNotice/minestation",
				param: setNewJSON({query: {isUsing: 1}}, _param.param),
				tag: "minestation",
				name: "工作站"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.id = _eItem.id || "";
						item.name = _eItem.name || "";
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取工作站新资讯栏目
	function getColumn50_1(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "open_api/stationNewsColumn/selecttree",
				param: setNewJSON({query: {isUsing: 1}}, _param.param),
				areaId: _param.areaId,
				tag: "stationNewsColumn",
				name: "栏目"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.key = _eItem.id;
						item.value = _eItem.name || "";
						item.type = "station";
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取工作站新资讯列表
	function getList50_1(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "stationNews/list",
				param: setNewJSON({}, _param.param),
				areaId: _param.areaId,
				tag: _param.tag || "list50_1",
				name: "工作站资讯"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.code = module50_1.code;
						item.showCode = (_eItem.stationShowCode || {}).value || "";
						item.title = _eItem.infoTitle || "";
						item.content = removeTag(_eItem.infoContent || "");
						item.id = _eItem.id || "";
						var infoPic = _eItem.infoPic || "";
						item.url = infoPic
							? infoPic.indexOf(",") != -1
								? infoPic.split(",").map(function(obj) {
										return {url: obj};
								  })
								: infoPic
							: "";
						item.videoHrefs = _eItem.infoVideo || "";
						item.source = _eItem.source || "";
						item.isTop = _eItem.isTop || "";
						item.link = _eItem.contentType == 2 ? _eItem.linkUrl || "" : "";
						item.time = _eItem.pubTime || "";
						if (item.videoHrefs) {
							item.poster = item.url || "../../image/bg_launch.png";
							item.autoplay = false;
						}
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取工作站留言列表
	function getList50_2(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "stationLetter/managelist",
				param: setNewJSON({tableId: "id_station_letter"}, _param.param),
				areaId: _param.areaId,
				tag: "list50_2",
				name: "工作站留言"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.code = module50_2.code;
						item.id = _eItem.id || "";
						item.title = _eItem.title || "";
						item.checkStatus = _eItem.checkStatus || 0;
						item.hasAnswer = _eItem.hasAnswer;
						item.hasEvaluate = _eItem.hasEvaluate;
						item.evaluateStatus = _eItem.evaluateStatus || 0;
						var tag = item.checkStatus;
						if (item.hasEvaluate && item.hasEvaluate != "0") {
							tag = 4;
						} else if (item.hasAnswer) {
							tag = 3;
						}
						item.tagNum = tag;
						item.tag = [
							{text: "待审核", color: "#e6a23c"},
							{text: "未回复", color: "#F6931C"},
							{text: "审核不通过", color: "#BF2222"},
							{text: "已回复", color: "#3894FF"},
							{text: "已评价", color: "#2BBD4B"}
						][tag];
						item.tag1 = [
							{text: "未回复", color: "#F6931C"},
							{text: "已回复", color: "#3894FF"},
							{text: "已评价", color: "#2BBD4B"}
						][
							item.hasEvaluate && item.hasEvaluate != "0" ? 2 : item.hasAnswer ? 1 : 0
						];
						if (item.hasEvaluate && !item.evaluateStatus) {
							item.tag2 = {text: "待审核", color: "#e6a23c"};
						} else if (item.checkStatus == 0 || item.checkStatus == 2) {
							item.tag2 = [
								{text: "待审核", color: "#e6a23c"},
								{text: "审核不通过", color: "#BF2222"}
							][item.checkStatus == 0 ? 0 : 1];
						}
						item.userId = _eItem.senderId || "";
						item.source = replaceExceptFirst(_eItem.senderUserName || "");
						item.time = dayjs(_eItem.receiveTime).format("YYYY-MM-DD HH:mm");
						item.receiverId = _eItem.receiverId || "";
						item.stationId = _eItem.stationId || "";
						item.mine = (_param.pageParam || {}).mine;
						item.btn = "";
						if (item.mine) {
							item.btn = tag == 3 && item.userId == G.userId ? "2" : "";
						} else {
							var reviews =
								getItemForKey("station_worker", G.specialRoleKeys) ||
								getItemForKey("station_member", G.specialRoleKeys);
							if (tag == 0 && _eItem.showCheckStatus == 0 && reviews) {
								item.btn = "0";
							} else {
								var review =
									G.isAdmin || getItemForKey("station_worker", G.specialRoleKeys); // || getItemForKey('station_admin',G.specialRoleKeys)
								item.btn = tag == 1 && review && item.userId != G.userId ? "1" : "";
							}
						}
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取工作站活动列表
	function getList50_3(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "open_api/stationActivity/list",
				param: setNewJSON({tableId: "id_station_activity"}, _param.param),
				areaId: _param.areaId,
				tag: "list50_3",
				name: "工作站活动"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.code = module50_3.code;
						item.id = _eItem.id || "";
						item.title = _eItem.title || "";
						item.tag = {text: "未开始", color: "#e6a23c"};
						if (_eItem.activityStatus == "进行中") {
							item.tag = {text: "进行中", color: "#3894FF"};
						} else if (_eItem.activityStatus == "已结束") {
							item.tag = {text: "已结束", color: "#777676"};
						}
						item.time =
							dayjs(_eItem.beginTime).format("YYYY-MM-DD HH:mm") +
							"至" +
							dayjs(_eItem.endTime).format("YYYY-MM-DD HH:mm");
						item.label = (_eItem.stationActivityType || {}).name;
						item.address = _eItem.address;
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取工作站任务列表
	function getList50_4(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "stationNotice/listmine",
				param: setNewJSON({}, _param.param),
				areaId: _param.areaId,
				tag: "list50_4",
				name: "工作站任务"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.code = module50_4.code;
						item.businessCode = module50_4.businessCode;
						item.id = _eItem.id || "";
						item.title = _eItem.name || "";
						item.content = removeTag(_eItem.content || "");
						item.time = _eItem.publishTime || _eItem.createDate || "";
						item.source = _eItem.publishOfficeId || "";
						item.publishStation = _eItem.publishStation || "";
						item.channelId = _eItem.noticeTaskName || ""; //类型
						item.isTop = _eItem.isTop || ""; //置顶
						item.attachmentIds = _eItem.attachmentIds || ""; //附件
						item.hasCallback = _eItem.hasCallback || ""; //回执1
						item.isRedDot = _eItem.hasRead == 0; //是否未读
						item.stationId = _param.param.stationId;
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取工作站视频会议列表
	function getList50_6(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "stationMeeting/listmine",
				param: setNewJSON({}, _param.param),
				areaId: _param.areaId,
				tag: _param.tag || "list50_video",
				name: "视频会议"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.code = module50_6.code;
						item.id = _eItem.id || "";
						item.stationId = _eItem.stationId;
						item.publishStationName = _eItem.publishStationName;
						item.title = _eItem.theme || "";
						item.startTime = dayjs(_eItem.startTime).format("YYYY-MM-DD HH:mm");
						item.endTime = dayjs(_eItem.endTime).format("YYYY-MM-DD HH:mm");
						item.tag = {text: "未开始", color: "#F6931C"};
						item.btn = true;
						if (_eItem.meetingStatus == "进行中") {
							item.tag = {text: "进行中", color: G.appTheme};
						} else if (_eItem.meetingStatus == "已结束") {
							item.tag = {text: "已结束", color: "#666666"};
							item.btn = false;
						}
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取工作站履职补录
	function getList50_7(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "stationdutiesroom/listapp",
				param: setNewJSON({}, _param.param),
				areaId: _param.areaId,
				tag: _param.tag || "stationdutiesroom",
				name: "履职补录"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.code = module50_7.code;
						item.id = _eItem.id || "";
						item.name = _eItem.userName || "";
						item.url = _eItem.photo || _eItem.headImg || "";
						item.stationId = _eItem.stationId;
						item.title = _eItem.title || "";
						item.content = showTextSize(removeTagAll(_eItem.content || ""), 50);
						item.areas = _eItem.areas || "";
						item.time = dayjs(_eItem.createDate).format("YYYY-MM-DD HH:mm");
						item.attachments = _eItem.attachments || [];
						item.label = (_eItem.stationActivityType || {}).name;
						item.commentNum = _eItem.commentCount || 0;
						item.likeNum = _eItem.praisesCount || 0;
						item.likeIs = _eItem.hasPraises ? true : false;
						item.passStatus = _eItem.passStatus || 0;
						var tag = item.passStatus;
						if (tag == 0 || tag == 2) {
							item.tag =
								tag == 0
									? {text: "待审核", color: "#e6a23c"}
									: {text: "审核不通过", color: "#BF2222"};
						}
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取工作站履职补录
	function getList50_7_re(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "stationdutiesroom/list",
				param: setNewJSON({tableId: "id_station_duties_room"}, _param.param),
				areaId: _param.areaId,
				tag: _param.tag || "stationdutiesroomlist",
				name: "补录审核"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.code = module50_7_re.code;
						item.id = _eItem.id || "";
						item.name = _eItem.userName || "";
						item.url = _eItem.photo || _eItem.headImg || "";
						item.stationId = _eItem.stationId;
						item.title = _eItem.title || "";
						item.content = showTextSize(removeTagAll(_eItem.content || ""), 50);
						item.areas = _eItem.areas || "";
						item.time = dayjs(_eItem.createDate).format("YYYY-MM-DD HH:mm");
						item.attachments = _eItem.attachments || [];
						item.label = (_eItem.stationActivityType || {}).name;
						item.commentNum = _eItem.commentCount || 0;
						item.likeNum = _eItem.praisesCount || 0;
						item.likeIs = _eItem.hasPraises ? true : false;
						item.passStatus = [
							{text: "待审核", color: "#F6931C"},
							{text: "已通过", color: "#50C614"},
							{text: "审核未通过", color: "#999999"}
						][_eItem.passStatus || 0];
						if (item.label) {
							item.tag = {text: item.label, color: "#999999"};
						}
						item.isDuty = _eItem.isDuty || 0;
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取反馈历史
	function getList53(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "personSuggestion/list",
				param: setNewJSON({}, _param.param),
				areaId: _param.areaId,
				tag: _param.tag || "feedhistory",
				name: "反馈历史"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.code = module53.code;
						item.id = _eItem.id || "";
						item.name = _eItem.userName || "";
						item.url = _eItem.photo || _eItem.headImg || "";
						item.title = _eItem.title || "";
						item.content = _eItem.content || "";
						item.time = dayjs(_eItem.createDate).format("YYYY-MM-DD HH:mm");
						item.attachments = (_eItem.contentPictures || []).map(function(obj) {
							return {extName: "png", newFileName: obj};
						});
						item.tag = {
							text: (_eItem.personSuggestionType || {}).name,
							color: "#999999"
						};
						item.receiveDetail = (_eItem.receiveDetailVos || []).map(function(obj) {
							return {
								time: dayjs(obj.createDate).format("YYYY-MM-DD HH:mm"),
								content: obj.receiveContent
							};
						});
						item.receiveOpen = false;
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取视频会议(小鱼)
	function getList54(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "videoConnection/list",
				param: setNewJSON({}, _param.param),
				areaId: _param.areaId,
				tag: _param.tag || "videoConnection",
				name: "视频会议"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.code = module54.code;
						item.id = _eItem.id || "";
						item.title = _eItem.theme || "";
						item.state = _eItem.meetingStatus || "";
						item.startTime = dayjs(_eItem.startTime).format("YYYY-MM-DD HH:mm");
						item.endTime = dayjs(_eItem.endTime).format("YYYY-MM-DD HH:mm");
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取工作站资讯详情
	function getDetails50_1(_param, _callback) {
		ajax(
			{u: appUrl() + "stationNews/info"},
			_param.tag || "details" + _param.param.detailId,
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || {} : {};
				if (data.id) {
					var unshiftContent = "",
						pushContent = "";
					var infoPic = data.infoPic || "";
					var infoContent = data.infoContent || "";
					infoPic = infoPic
						? infoPic.indexOf(",") != -1
							? infoPic.split(",")[0]
							: infoPic
						: "";
					var infoVideo = data.infoVideo || "";
					var match = infoContent.match(/<img[^>]+src="([^">]+)"/);
					var cImg = match ? match[1] : "";
					if (!infoPic && cImg) {
						infoPic = cImg;
					}
					if (infoVideo) {
						unshiftContent += '<video src="' + infoVideo + '"></video></br>';
					}
					ret.dealWith = {
						code: module50_1.code,
						name: {pin: "一站一品", news: "信息发布", state: "国家机关进站"}[
							data.businessCode
						],
						id: data.id,
						title: data.infoTitle || "",
						subTitle: data.infoSubtitle || "",
						content: unshiftContent + infoContent + pushContent,
						url: infoPic,
						attachments: data.attachments || [],
						time: dayjs(data.pubTime).format("YYYY-MM-DD"),
						source: data.infoSource,
						leftAdd: [{text: dayjs(data.pubTime).format("YYYY-MM-DD")}],
						rightAdd: [],
						share: data.isShare == "1",
						comment: false,
						commentList: false,
						screen: data.isScreen || "1"
					};
				}
				_callback(ret, err);
			},
			"详情",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	var module5 = {
		name: "资讯",
		code: "5",
		businessCode: "informationContent",
		behaviorCode: "information_content"
	};
	var module6 = {
		name: sysSign() == "rd" ? "意见征集" : "网络议政",
		code: "6",
		businessCode: "opinioncollect"
	};
	var module7 = {name: "专题", code: "7", businessCode: "infoSubject"};
	var module9 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息",
		code: "9",
		businessCode: "cppcc_member"
	};
	var module9_1 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息变更申请",
		code: "9_1",
		businessCode: "cppccMemberCheckPrepare"
	};
	var module10 = {name: "活动", code: "10", businessCode: "servantActivity"};
	var module12 = {
		name: sysSign() == "rd" ? "圈子" : "委员说",
		code: "12",
		businessCode: "styleCircle"
	};
	var module14 = {name: "消息", code: "14", businessCode: "box_message"};
	var module16 = {name: "云盘", code: "16", businessCode: "pan_pubshare"};
	var module22 = {name: "通知公告", code: "22", businessCode: "notification"};
	var module31 = {name: "培训考试", code: "31", businessCode: "study_online"};
	var module31_1 = {
		name: "成绩单",
		code: "31_1",
		businessCode: "study_online_1"
	};
	var module32 = {name: "掌上建议", code: "32", businessCode: "suggestion"};
	var module33 = {name: "掌上提案", code: "33", businessCode: "proposal"};
	var module35 = {
		name: "立法征询",
		code: "35",
		businessCode: "legislationOpinion",
		onlyBusinessCode: "legis_contact_point"
	};
	var module37 = {name: "掌上议案", code: "37", businessCode: "motion"};
	var module39_3 = {name: "联系点资讯", code: "39_3"};
	var module40 = {name: "微建议", code: "40", businessCode: "min_suggest"};
	var module50_1 = {name: "工作站资讯", code: "50_1"};
	var module50_2 = {name: "工作站留言", code: "50_2"};
	var module50_3 = {name: "工作站活动", code: "50_3"};
	var module50_4 = {name: "工作站任务接收", code: "50_4"};
	var module50_6 = {name: "工作站视频会议", code: "50_6"};
	var module50_7 = {
		name: "工作站履职补录",
		code: "50_7",
		businessCode: "stationDutiesRoom"
	};
	var module50_7_re = {name: "补录审核", code: "50_7_re"};
	var module51 = {name: "履职大厅", code: "51"};
	var module53 = {name: "意见反馈", code: "53"};
	var module54 = {name: "视频会议(小鱼)", code: "54"};

	//打开链接
	function openWin_url(_param) {
		_param.url = handleSYSLink(_param.url);
		if (
			(_param.wopen || _param.url.indexOf("wopen") != -1) &&
			platform() == "web"
		) {
			window.open(_param.url);
		} else {
			openWin("mo_details_url", "../mo_details_url/mo_details_url.stml", _param);
		}
	}

	//打开搜索
	function openWin_search(_param) {
		openWin("mo_search", "../mo_search/mo_search.stml", _param);
	}

	//打开通用新增新
	function openWin_add_n(_param) {
		openWin("mo_add", "../mo_add_n/mo_add_n.stml", _param);
	}

	//打开通用列表新
	function openWin_listn(_param) {
		openWin(
			"mo_business_list_n" + _param.code,
			"../mo_business_list_n/mo_business_list_n.stml",
			_param
		);
	}

	//打开app文本
	function openWin_apptext(_item) {
		var openPage = _item.id ? "mo_details_n" : "mo_business_list_n";
		_item.code = _item.code || "apptext";
		openWin(
			openPage + (_item.id || _item.code),
			"../" + openPage + "/" + openPage + ".stml",
			_item
		);
	}

	//打开图片预览
	function openWin_imgPreviewer(_param) {
		G.imgPreviewerPop = {
			show: true,
			index: _param.index,
			imgs: _param.imgs
		};

		console.log(JSON.stringify(G.imgPreviewerPop));
	}

	//打开附件预览
	function openWin_filePreviewer(_param) {
		openWin("mo_details_url", "../mo_details_url/mo_details_url.stml", _param);
	}

	//打开聊天
	function openWin_chat(_param) {
		var openPage = "mo_chat";
		var myParam = {
			conversationType: _param.conversationType,
			targetId: _param.targetId || _param.id
		};

		if (_param.paramMore) {
			myParam = setNewJSON(myParam, _param.paramMore);
		}
		openWin(
			openPage + myParam.targetId,
			"../" + openPage + "/" + openPage + ".stml",
			myParam
		);
	}

	//打开详情------------------------------------------------------------------------------------

	//打开资讯
	function openWin_news(_item) {
		var openPage =
			_item.code == "7" ? "mo_news_topic" : _item.id ? "mo_details_n" : "mo_news";
		var param = {};
		param.id = _item.id;
		param.code = module5.code;
		if (_item.link) {
			openWin_url({url: _item.link});
		} else {
			openWin(
				openPage + (_item.id || _item.code),
				"../" + openPage + "/" + openPage + ".stml",
				param
			);
		}
		addBehaviorRecord({id: param.id, behaviorCode: module5.behaviorCode});
	}

	//打开意见征集 网络议政
	function openWin_opinioncollect(_item) {
		var openPage = _item.id ? "mo_details" : "";
		var param = {};
		param.id = _item.id;
		param.code = module6.code;
		openWin(
			openPage + (_item.id || module6.code),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
	}

	//打开代表信息审核
	function openWin_npcinfo_review(_item) {
		var openPage = _item.id ? "mo_npcinfo_review_details" : "mo_npcinfo_review";
		var param = {};
		param.code = module9_1.code;
		param = setNewJSON(param, _item);
		openWin(
			openPage + (_item.id || module9_1.code),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
	}

	//打开活动
	function openWin_activity(_item) {
		var openPage = _item.id ? "mo_activity_details" : "mo_business_list_n";
		var param = {};
		param.id = _item.id;
		param.code = module10.code;
		openWin(
			openPage + (_item.id || module10.code),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
		if (_item.isRedDot == 1) {
			addRedDot(_item);
		}
	}

	//打开云盘
	function openWin_pan(_item) {
		var openPage = "mo_cloud_disk";
		var param = {};
		param.code = module16.code;
		param.defaultType = _item.defaultType;
		openWin(
			openPage + (_item.id || module16.code),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
	}

	//打开通知公告
	function openWin_notice(_item) {
		var openPage = _item.id ? "mo_notice_detail" : "mo_business_list_n";
		var param = {};
		param.id = _item.id;
		param.areaId = _item.areaId;
		param.code = _item.code || module22.code;
		param.title = _item.id ? "" : "通知公告";
		openWin(
			openPage + (_item.id || module22.code),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
	}

	//打开培训考试
	function openWin_study(_item) {
		console.log(JSON.stringify(_item));
		var openPage = _item.id ? "mo_exam_answers" : "mo_business_list_n";
		var param = {};
		param.id = _item.id;
		param.code = module31.code;
		if (param.id && param.id.indexOf(",") != -1) {
			var dataId = param.id.split(",");
			param.id = dataId[0];
			param.topicId = dataId[1];
		}
		openWin(
			openPage + (param.id || module31.code),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
		if (_item.isRedDot == 1) {
			addRedDot(_item);
		}
	}

	//打开建议
	function openWin_suggestion(_item) {
		var openPage = _item.id ? "mo_suggestion_details" : "mo_suggestion";
		_item.code = _item.code || module32.code;
		openWin(
			openPage + (_item.id || module32.code),
			"../" + openPage + "/" + openPage + ".stml",
			_item
		);
	}

	//打开提案
	function openWin_proposal(_item) {
		var openPage = _item.id ? "mo_proposal_details" : "mo_proposal";
		_item.code = _item.code || module33.code;
		openWin(
			openPage + (_item.id || module33.code),
			"../" + openPage + "/" + openPage + ".stml",
			_item
		);
	}

	//打开立法征询
	function openWin_legislative(_item) {
		var openPage = _item.id
			? "mo_legislative_consultation_detail"
			: "mo_legislative_consultation";
		var param = {};
		param.id = _item.id;
		param.code = _item.code || module35.code;
		param = setNewJSON(param, _item);
		openWin(
			openPage + (_item.id || module35.code),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
	}

	//打开议案
	function openWin_motion(_item) {
		var openPage = _item.id ? "mo_motion_details" : "mo_business_list";
		_item.code = _item.code || module37.code;
		openWin(
			openPage + (_item.id || module37.code),
			"../" + openPage + "/" + openPage + ".stml",
			_item
		);
	}

	//打开微建议
	function openWin_microAdvice(_item) {
		var openPage = _item.id ? "mo_min_suggest_details" : "mo_min_suggest";
		_item.code = _item.code || module40.code;
		openWin(
			openPage + (_item.id || module40.code),
			"../" + openPage + "/" + openPage + ".stml",
			_item
		);
	}

	//打开履职大厅
	function openWin_performancehall(_item) {
		var openPage = _item.id
			? "mo_performance_dynamics_examine_details"
			: "mo_performance_dynamics_examine";
		_item.code = _item.code || "48_1" || module51.code;
		openWin(
			openPage + (_item.id || module51.code),
			"../" + openPage + "/" + openPage + ".stml",
			_item
		);
	}

	//打开代表工作站资讯
	function openWin_workstation_news(_item) {
		var openPage = _item.id ? "mo_details_n" : "mo_business_list_n";
		var param = {};
		param.code = _item.code || module50_1.code;
		param = setNewJSON(param, _item);
		if (_item.link) {
			openWin_url({url: _item.link});
			getDetails50_1({param: {detailId: param.id}}, function() {});
		} else {
			openWin(
				openPage + (_item.id || module50_1.code),
				"../" + openPage + "/" + openPage + ".stml",
				param
			);
		}
	}

	//打开代表工作站留言
	function openWin_workstation_letter(_item) {
		var openPage = _item.id ? "mo_details_n" : "mo_business_list_n";
		var param = {};
		param.code = _item.code || module50_2.code;
		param = setNewJSON(param, _item);
		openWin(
			openPage + (_item.id || module50_2.code),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
	}

	//打开代表工作站活动
	function openWin_workstation_activity(_item) {
		var openPage = _item.id ? "mo_details_n" : "mo_business_list_n";
		var param = {};
		param.code = _item.code || module50_3.code;
		param = setNewJSON(param, _item);
		openWin(
			openPage + (_item.id || module50_3.code),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
	}

	//打开代表工作站任务下达
	function openWin_workstation_task(_item) {
		var openPage = _item.id ? "mo_details_n" : "mo_business_list_n";
		var param = {};
		param.code = _item.code || module50_4.code;
		param = setNewJSON(param, _item);
		openWin(
			openPage + (_item.id || module50_4.code),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
		if (_item.id) {
			addBehaviorRecord({id: _item.id, behaviorCode: "stationNotice"});
		}
	}

	//打开代表工作站视频会议
	function openWin_workstation_video(_item) {
		var openPage = _item.id ? "mo_workstation_video" : "mo_business_list_n";
		var param = {};
		param.code = _item.code || module50_6.code;
		param = setNewJSON(param, _item);
		openWin(
			openPage + (_item.id || module50_6.code),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
	}

	//打开代表工作站履职补录
	function openWin_workstation_dutieshelp(_item) {
		var openPage = _item.id ? "mo_details_n" : "mo_business_list_n";
		var param = {};
		param.code = _item.code || module50_7.code;
		param = setNewJSON(param, _item);
		openWin(
			openPage + (_item.id || module50_7.code),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
	}

	//打开代表工作站履职补录审核
	function openWin_workstation_dutieshelp_review(_item) {
		var openPage = _item.id ? "mo_details_n" : "mo_business_list_n";
		var param = {};
		param.code = _item.code || module50_7_re.code;
		param = setNewJSON(param, _item);
		openWin(
			openPage + (_item.id || module50_7_re.code),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
	}

	//打开联系点资讯
	function openWin_lawpoint_news(_item) {
		var openPage = _item.id ? "mo_details_n" : "mo_business_list_n";
		var param = {};
		param.code = _item.code || module39_3.code;
		param = setNewJSON(param, _item);
		openWin(
			openPage + (_item.id || module39_3.code),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
	}

	//打开视频会议(小鱼)
	function openWin_videoconference(_item) {
		var openPage = _item.id ? "mo_videoconference_details" : "mo_business_list_n";
		var param = {};
		param.code = _item.code || module54.code;
		param = setNewJSON(param, _item);
		openWin(
			openPage + (_item.id || module54.code),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
	}

	//根据配置加入不同的厂商会议
	function joinMeeting(_param) {
		showProgress();
		getAppConfig(["VideoMeetinVender"], function(ret) {
			var data = ret ? ret.data || {} : {};
			var VideoMeetinVender = data.VideoMeetinVender || "xy"; //对接厂商
			switch (VideoMeetinVender) {
				case "xy":
					hideProgress();
					openWin_url({
						url:
							shareAddress().replace("http:", "https:") +
							"html/xy-meeting.html?meetingNum=" +
							_param.meetingNumber +
							"&pwd=" +
							_param.meetingPassword +
							"&displayName=" +
							(_param.name || G.userName) +
							"&theme=" +
							G.appTheme.replace("#", "")
					});
					break;
				default:
					ajax(
						{u: appUrl() + "/stationMeeting/genMeetingToken"},
						"genMeetingToken",
						function(ret, err) {
							hideProgress();
							if (!ret || ret.code != 200) {
								toast(ret ? ret.message || ret.data : NET_ERR);
								return;
							}
							var token = ret.data;
							openWin_url({
								url:
									shareAddress().replace("http:", "https:") +
									"html/yl-meeting.html?meetingNum=" +
									_param.meetingNumber +
									"&pwd=" +
									_param.meetingPassword +
									"&displayName=" +
									(_param.name || G.userName) +
									"&theme=" +
									G.appTheme.replace("#", "") +
									"&accessToken=" +
									token
							});
						},
						"token",
						"post",
						{
							body: JSON.stringify({})
						}
					);

					break;
			}
		});
	}

	//获取配置信息 登录后
	function getAppConfig(_param, _callback) {
		var postParam = {
			codes: isArray(_param) ? _param : _param.codes
		};

		ajax(
			{u: appUrl() + "config/read"},
			"config/read",
			function(ret, err) {
				_callback && _callback(ret, err);
			},
			"取app配置",
			"post",
			{
				body: JSON.stringify(postParam)
			}
		);
	}

	//获取登录信息
	function getLoginInfo(_param, _callback) {
		ajax(
			{u: appUrl() + "login/user"},
			"login/user",
			function(ret, err) {
				if (ret && ret.code == 200 && ret.data.id != "anonymous") {
					saveLogin(ret.data);
				}
				_callback && _callback(ret, err);
			},
			"用户信息",
			"post",
			{
				values: _param.param
			},
			_param.header
		);
	}

	// 增加已读
	function addRedDot(_param, _callback) {
		var param = {businessCode: _param.businessCode, businessId: _param.id};
		ajax(
			{u: appUrl() + "redDot/sign"},
			"redDot" + param.businessId,
			function(ret, err) {
				_callback && _callback(ret, err);
			},
			"设置已读",
			"post",
			{
				body: JSON.stringify(param)
			}
		);

		_param.isRedDot = "0";
	}

	// 增加阅读量
	function addBehaviorRecord(_param) {
		ajax(
			{u: appUrl() + ("behavior/record/" + _param.behaviorCode + "/" + _param.id)},
			"behavior/record",
			function(ret, err) {},
			"阅读",
			"post",
			{
				body: JSON.stringify({})
			}
		);
	}

	//设置列表未读
	function setListUnread(_param, _callback) {
		ajaxAlert(
			{
				msg: "确定全部已读吗?",
				url: appUrl() + "redDot/signBatch",
				param: {businessCode: _param.code},
				tag: "sunread",
				name: "全部已读"
			},
			function(ret, err) {
				_callback && _callback(ret, err);
			}
		);
	}

	//弹窗提示
	function ajaxAlert(_param, _callback) {
		var param = {
			title: "提示",
			msg: _param.msg || "",
			buttons: ["确定", "取消"]
		};

		if (_param.alertParam) {
			param = setNewJSON(param, _param.alertParam);
		}
		console.log(_param.url + "\n" + JSON.stringify(_param.param));
		alert(param, function(ret) {
			if (ret.buttonIndex == "1") {
				ajaxProcess(_param, _callback);
			}
		});
	}

	//请求
	function ajaxProcess(_param, _callback) {
		if (_param.toast) showProgress(_param.toast);
		ajax(
			{u: _param.url, areaId: _param.areaId, web: _param.web},
			_param.tag || "ajaxProcess",
			function(ret, err) {
				hideProgress();
				if (_param.toast) {
					toast(ret ? ret.message || ret.data : NET_ERR$1);
				}
				_callback && _callback(ret, err);
			},
			_param.name || "\u64CD\u4F5C",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	// 获取所有地区 //area/tree
	function getSysAreas(_param, _callback) {
		var otherUrl = (_param || {}).url;
		ajax(
			{u: otherUrl || appUrl() + "login/areas", areaId: getPrefs("sys_platform")},
			otherUrl || "areas",
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					setPrefs(otherUrl ? "sys_allAreas" : "sys_areas", JSON.stringify(data));
					areaNotice({key: ""});
				}
				_callback(ret, err);
			},
			"所有地区",
			"post",
			{
				body: JSON.stringify({query: {isUsing: 1}})
			}
		);
	}

	//地区切换通知
	function areaNotice(extra) {
		[
			"module",
			"news1",
			"news2",
			"my",
			"negotiable",
			"addressBook",
			"workstation"
		].forEach(function(_eItem) {
			sendEvent({name: "areaChange_" + _eItem, extra: extra});
		});
	}

	function getAllChatInfo() {
		var chatInfos = JSON.parse(getPrefs("chatInfos") || "[]"),
			addInfo = [];
		chatInfos.forEach(function(_eItem) {
			if (_eItem.id) {
				if (/^(?!h|(\.)|(\/)).*/.test(_eItem.url)) {
					_eItem.url = showImg(_eItem.url);
				}
				addInfo.push(_eItem);
			}
		});
		G.chatInfos = addInfo;
		setPrefs("chatInfos", JSON.stringify(G.chatInfos));
		return G.chatInfos;
	}

	function setAllChatInfo(_item) {
		getAllChatInfo();
		var nowInfo = JSON.parse(JSON.stringify(G.chatInfos));
		if (isArray(_item)) {
			_item.forEach(function(_eItem) {
				if (_eItem.id) {
					delItemForKey(_eItem.id, nowInfo, "id");
					nowInfo.push(_eItem);
				}
			});
		} else {
			if (!_item.id) {
				return;
			}
			delItemForKey(_item.id, nowInfo, "id");
			nowInfo.push(_item);
		}
		setPrefs("chatInfos", JSON.stringify(nowInfo));
		setRongChatInfo();
	}

	function setRongChatInfo() {
		if (platform() == "app" && api.require("zyRongCloudRTC")) {
			getAllChatInfo();
			var setUser = {
				apply: 1,
				users: G.chatInfos.map(function(obj) {
					return {id: chatHeader() + obj.id, name: obj.name, url: obj.url};
				}),
				groupUser: JSON.parse(getPrefs("rongGroupUser") || "[]"), //当前通话的群组成员列表id集合
				totalNumber: 10 //语音或视频最大人数
			};
			// console.log("融云设置信息："+JSON.stringify(setUser));
			api.require("zyRongCloudRTC").setUsers(setUser, function(ret) {
				removePrefs("rongGroupUser");
				setRongChatInfo();
				setTimeout(
					function() {
						sendEvent({name: "chat_refresh"});
					},
					api.systemType == "ios" ? 600 : 1500
				);
			});
		}
	}

	//打开扫码
	function openScan(_param, _callback) {
		function scanResult(_content) {
			if (_content.indexOf(chatHeader() + "|") != -1) {
				var params = _content.split("|");
				if (params.length < 3) {
					alert({title: "识别结果为：", msg: _content, buttons: ["关闭"]}, function(
						ret,
						err
					) {});
					return;
				}
				switch (params[1]) {
					case "login": //登录
						showProgress("登录中");
						ajax(
							{u: appUrl() + "scanCodeLogin/receipt/appToken?qrCodeId=" + params[2]},
							"qrCodeId",
							function(ret, err) {
								hideProgress();
								toast(ret ? ret.message || ret.data : NET_ERR$1);
								if (ret && ret.code == 200) {
									_callback && _callback();
								}
							},
							params[1]
						);
						break;
					case "activityCode":
						activityOption(
							{
								id: params[2],
								state: "签到中",
								signInCommand: params[3],
								toast: "签到中"
							},
							_callback
						);
						break;
					case "groupQr": //扫群二维码
						var _id = params[2];
						showProgress();
						ajax(
							{u: appUrl() + "chatGroup/info"},
							"chatGroup/info" + _id,
							function(ret, err) {
								hideProgress();
								var data = ret ? ret.data || {} : {};
								if (!ret || ret.code != "200" || !data.id) {
									toast("获取群信息失败，请稍候重试");
									return;
								}
								var memberUserIds = data.memberUserIds || [];
								if (getItemForKey(G.userId, memberUserIds)) {
									openWin_chat({
										conversationType: "GROUP",
										targetId: chatHeader() + data.id
									});
									return;
								}
								showProgress("加入中");
								ajax(
									{u: appUrl() + "chatGroup/edit"},
									"chatGroup/edit" + _id,
									function(ret, err) {
										hideProgress();
										if (!ret || ret.code != "200") {
											toast("加入群组失败，请稍候重试");
											return;
										}
										openWin_chat({
											conversationType: "GROUP",
											targetId: chatHeader() + data.id,
											paramMore: {joinType: "groupQr"}
										});
									},
									"\u52A0\u5165\u7FA4\u7EC4" + _id,
									"post",
									{
										body: JSON.stringify({
											form: {groupName: data.groupName, id: data.id},
											memberUserIds: memberUserIds.concat([G.userId]),
											ownerUserId: data.ownerUserId
										})
									}
								);
							},
							"\u83B7\u53D6\u7FA4\u4FE1\u606F" + _id,
							"post",
							{
								body: JSON.stringify({detailId: _id})
							}
						);

						break;
					case "toWygzsSignApp":
						var url = "";
						if (params.length > 4) {
							url = params[2] + "app/wygzsApp/operationActivityUserByUser?";
						} else {
							alert(
								{title: "提示", msg: "后台未配置有误,请联系管理员", buttons: ["关闭"]},
								function(ret, err) {}
							);
							return;
						}
						var paramObj = {activityId: params[4], type: 2, status: 1}; //type:2 签到操作 , status: 1签到
						showProgress("签到中");
						ajax(
							{u: url},
							"serByUser",
							function(ret, err) {
								hideProgress();
								toast(ret ? ret.message || ret.data : NET_ERR$1);
								if (ret && ret.code == 200) {
									var workstationsUrl =
										params[3] +
										"/#/activitiesDetail?id=" +
										params[4] +
										"&token={{token}}&areaId={{areaId}}";
									setTimeout(function() {
										// openDetails({code:"8897",link:workstationsUrl,title:'详情'},null,_this);
									}, 1500);
								}
							},
							"联络站扫码签到接口",
							"post",
							{
								values: paramObj
							},
							{
								"content-type": "application/x-www-form-urlencoded"
							}
						);

						break;
					case "zhtmeetingSignIn": //会议签到--智会通
						var url = "";
						if (params.length > 3) {
							url = params[2] + "appMeetSign/tosignin?";
						} else {
							alert(
								{title: "提示", msg: "后台未配置有误,请联系管理员", buttons: ["关闭"]},
								function(ret, err) {}
							);
							return;
						}
						var paramObj = {
							meetId: params[3],
							activityId: params[3],
							conferenceId: params[3],
							dataId: params[3],
							signInType: "qrCode",
							userId: G.userId,
							type: "signIn"
						};

						showProgress("签到中");
						ajax(
							{u: url},
							"tosignin",
							function(ret, err) {
								hideProgress();
								toast(ret ? ret.message || ret.data : NET_ERR$1);
							},
							"会议系统签到接口",
							"post",
							{
								values: paramObj
							},
							{
								"content-type": "application/x-www-form-urlencoded"
							}
						);

						break;
					case "meettingCode": //会务系统签到
						meettingOption(
							{
								id: params[2],
								state: "签到中",
								signInCommand: params[3],
								toast: "签到中"
							},
							_callback
						);
						break;
					case "noticeCode": //公告详情
						openWin_notice({id: params[2]});
						break;
					default:
						alert({title: "识别结果为：", msg: _content, buttons: ["关闭"]}, function(
							ret,
							err
						) {});
						break;
				}
			} else if (_content.indexOf("http") == 0) {
				if (
					(_content.indexOf("pages/index/index.html?") != -1 ||
						_content.indexOf("pages/index/?") != -1) &&
					_content.indexOf(".stml") != -1
				) {
					var d = JSON.parse(
						decodeURI(_content.substring(_content.indexOf("?") + 1))
					);
					d.p.qr = 1;
					openWin(d.n, d.u, d.p);
				} else if (_content.indexOf(appUrl() + "viewing/") != -1) {
					showProgress("打开中");
					ajax(
						{u: appUrl() + "longLink/" + _content.replace(appUrl() + "viewing/", "")},
						"qrCodeId",
						function(ret, err) {
							hideProgress();
							if (ret && ret.code == 200 && ret.data.indexOf("http") == 0) {
								var d = JSON.parse(
									decodeURI(ret.data.substring(ret.data.indexOf("?") + 1))
								);
								d.p.qr = 1;
								openWin(d.n, d.u, d.p);
							} else {
								toast(ret ? ret.message || ret.data : NET_ERR$1);
							}
						},
						"获取长链接"
					);
				} else {
					openWin_url({url: _content});
				}
			} else {
				alert({title: "识别结果为：", msg: _content, buttons: ["关闭"]}, function(
					ret,
					err
				) {});
			}
		}
		if (platform() == "app") {
			var preName = "camera";
			if (
				!confirmPer(
					preName,
					"getPicture",
					"用于打开摄像头并扫码，若取消将无法使用扫一扫功能"
				)
			) {
				addEventListener(preName + "Per_" + "getPicture", function(ret, err) {
					if (ret.value.granted) {
						openScan(_param, _callback);
					}
					removeEventListener(preName + "Per_" + "getPicture");
				});
				return;
			}
			api.require("zyHmsScan").openDefaultView({}, function(ret) {
				if (ret.status) {
					setTimeout(function() {
						scanResult(ret.result);
					}, 300);
				}
			});
		} else if (platform() == "mp") {
			wx.scanCode({
				success: function success(ret) {
					setTimeout(function() {
						scanResult(ret.result);
					}, 300);
				}
			});
		} else {
			G.qrcodePop = {
				show: true,
				callback: function callback(ret) {
					scanResult(ret.result);
				}
			};
		}
	}

	// 活动签到报名等操作
	function activityOption(_param, _callback) {
		var url = "",
			param = {
				form: {
					activityId: _param.id
				}
			};

		if (_param.state == "报名中") {
			if (_param.joinStatus == "nosign") {
				url = appUrl() + "activityperson/sign";
			} else {
				url = appUrl() + "activityperson/cancelsign";
			}
		} else if (_param.state == "签到中") {
			url = appUrl() + "activityperson/join";
			param.signInCommand = _param.signInCommand;
		}
		ajaxProcess(
			{
				toast: _param.toast || "操作中",
				url: url,
				param: param,
				name: _param.state
			},
			function(ret, err) {
				_callback && _callback(ret, err);
			}
		);
	}

	// 会务系统会议签到操作
	function meettingOption(_param, _callback) {
		if (!_param.id || !_param.signInCommand) {
			toast("此会议二维码有误！请联系管理员");
			return;
		}
		var url = appUrl() + "zyMeetUser/qrSignIn";
		var param = {
			signInCommand: _param.signInCommand,
			form: {
				meetId: _param.id
			}
		};

		ajaxProcess(
			{
				toast: _param.toast || "操作中",
				url: url,
				param: param,
				name: _param.state
			},
			function(ret, err) {
				_callback && _callback(ret, err);
			}
		);
	}

	// 点赞/取消点赞
	function optionPraises(_param, _other) {
		var param = {
			businessCode: _param.code,
			businessId: _param.id
		};

		param = setNewJSON(param, _other);
		if (_param.likeIs) {
			param = {form: param};
		}
		ajax(
			{u: appUrl() + ("praises/" + (_param.likeIs ? "add" : "dels"))},
			"praises/add",
			function(ret, err) {},
			(_param.likeIs ? "" : "取消") + "\u70B9\u8D5E",
			"post",
			{
				body: JSON.stringify(param)
			}
		);
	}

	function dealVideoId(_id) {
		return _id.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, "");
	}

	//当前页面播放视频集合
	function videoPlayPush(_id) {
		if (!G.playVideos) {
			G.playVideos = [];
		}
		if (getItemForKey(_id, G.playVideos)) {
			return;
		}
		videoPlayRemoves();
		G.playVideos.push(_id);
		console.log("当前播放：" + JSON.stringify(G.playVideos));
	}

	//去除某个视频播放
	function videoPlayRemove(_id) {
		delItemForKey(_id, G.playVideos);
	}

	//去除所有播放
	function videoPlayRemoves() {
		(G.playVideos || []).forEach(function(_id) {
			document.getElementById(_id) && document.getElementById(_id).pause();
			videoPlayRemove(_id);
		});
	}

	var ZDivider = /*@__PURE__*/ (function(Component) {
		function ZDivider(props) {
			Component.call(this, props);
		}

		if (Component) ZDivider.__proto__ = Component;
		ZDivider.prototype = Object.create(Component && Component.prototype);
		ZDivider.prototype.constructor = ZDivider;
		ZDivider.prototype.render = function() {
			return apivm.h(
				"view",
				{style: "width:100%;height:1px;padding:0 16px;" + this.props.style || null},
				apivm.h("view", {
					style:
						"border-bottom:1px solid " +
						(this.props.color || "rgba(0,0,0,0.03)") +
						";"
				})
			);
		};

		return ZDivider;
	})(Component);
	apivm.define("z-divider", ZDivider);

	var Qrcode = /*@__PURE__*/ (function(Component) {
		function Qrcode(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				isCallback: false
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							setTimeout(
								function() {
									this$1.baseInit();
								},
								platform() == "app" ? 50 : 0
							);
						}
					}
				}
			};
		}

		if (Component) Qrcode.__proto__ = Component;
		Qrcode.prototype = Object.create(Component && Component.prototype);
		Qrcode.prototype.constructor = Qrcode;
		Qrcode.prototype.baseInit = function() {
			var this$1 = this;

			var dm = this.props.dataMore;
			this.sendLon = "";
			this.data.isCallback = false;
			getBoundingClientRect("map_box", function(ret) {
				if (platform() == "web") {
					window.addEventListener("message", function(event) {
						this$1.getFrameMsg(event.data);
					});
					document.getElementById("qrframe").addEventListener("load", function() {
						this$1.sendFrameMsg(dm, "init");
					});
				}
			});
		};
		Qrcode.prototype.penetrate = function() {};
		Qrcode.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		Qrcode.prototype.getFrameMsg = function(_obj) {
			switch (_obj.mType) {
				case "setData":
					if (this.data.isCallback) {
						return;
					}
					this.data.isCallback = true;
					this.props.dataMore.callback({result: _obj.result});
					this.closePage();
					break;
			}
		};
		Qrcode.prototype.sendFrameMsg = function(_obj, _type) {
			var sendMsg = {};
			if (isObject(_obj)) {
				for (var value in _obj) {
					if (!isFunction(_obj[value])) {
						sendMsg[value] = _obj[value];
					}
				}
			}
			sendMsg.mType = _type;
			document.getElementById("qrframe").contentWindow.postMessage(sendMsg, "*");
		};
		Qrcode.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style: "height:20%;flex-shrink: 0;"
				}),
				this.props.dataMore.show &&
					apivm.h(
						"view",
						{
							class: "flex_h pages_warp",
							onClick: function() {
								return this$1.penetrate();
							}
						},
						apivm.h(
							"view",
							{class: "header_warp flex_row"},
							apivm.h(
								"view",
								{class: "header_main xy_center"},
								apivm.h(
									"text",
									{
										style: loadConfiguration(1) + "font-weight: 600;color:" + G.headColor
									},
									this.props.dataMore.title || "扫一扫"
								)
							),
							apivm.h(
								"view",
								{class: "header_left_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{style: loadConfiguration(1) + "color:#666;margin:0 4px;"},
										"关闭"
									)
								)
							),
							apivm.h("view", {class: "header_right_box"})
						),
						apivm.h("z-divider", null),
						apivm.h(
							"view",
							{id: "map_box", class: "flex_h"},
							platform() == "web"
								? apivm.h("frame", {
										id: "qrframe",
										class: "xy_100",
										name: "qrframe",
										allow: "microphone *;camera *;geolocation *;",
										url: shareAddress() + "html/qrcode.html"
								  })
								: null
						)
					)
			);
		};

		return Qrcode;
	})(Component);
	Qrcode.css = {
		".pages_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		}
	};
	apivm.define("qrcode", Qrcode);

	var ZInput = /*@__PURE__*/ (function(Component) {
		function ZInput(props) {
			Component.call(this, props);
			this.data = {
				inputId: this.props.id || "z_input" + getNum()
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.autoFocus) {
						this.props.dataMore.autoFocus = false;
						this.props.dataMore.inputId = this.data.inputId;
						setTimeout(function() {
							document.getElementById(this$1.data.inputId).focus();
						}, 500);
					}
					if (this.props.dataMore.inputId != this.data.inputId) {
						this.props.dataMore.inputId = this.data.inputId;
					}
				}
			};
		}

		if (Component) ZInput.__proto__ = Component;
		ZInput.prototype = Object.create(Component && Component.prototype);
		ZInput.prototype.constructor = ZInput;
		ZInput.prototype.inputConfirm = function(e) {
			document.getElementById(this.data.inputId).blur();
			this.fire("confirm", e.detail);
		};
		ZInput.prototype.inputIng = function(e) {
			var nValue = (e.detail || {}).value;
			if (!nValue) {
				//解决安卓上清空输入无效的问题
				if (!this.i) {
					this.props.dataMore.value = "";
					this.i = 1;
				} else {
					this.props.dataMore.value = " ".repeat(this.i++ % 2);
					this.props.dataMore.value = " ".repeat(this.i++ % 2);
				}
			} else {
				this.props.dataMore.value = nValue;
			}
			if (this.props.dataMore.number) {
				if (!this.props.dataMore.value) {
					return;
				}
				if (/[^-?\d+(\.\d+)?$]/.test(this.props.dataMore.value)) {
					this.props.dataMore.value = this.props.dataMore.value.replace(
						/[^-?\d+(\.\d+)?$]/g,
						""
					);
					toast("请输入数字！");
					return;
				}
			}
			if (this.props.dataMore.expression) {
				//有正则表达示
				this.props.dataMore.value = this.props.dataMore.value.replace(
					new RegExp(this.props.dataMore.expression, "g"),
					""
				);
			}
			this.fire("input", e.detail);
		};
		ZInput.prototype.inputBlur = function(e) {
			this.fire("blur", e.detail);
		};
		ZInput.prototype.inputFocus = function(e) {
			document.getElementById(this.data.inputId).focus();
			this.fire("focus", e.detail);
		};
		ZInput.prototype.clean = function() {
			var this$1 = this;

			this.inputIng({detail: {value: ""}});
			this.fire("clean");
			if (this.props.dataMore.cleanFocus) {
				setTimeout(function() {
					document.getElementById(this$1.data.inputId).focus();
				}, 150);
			}
		};
		ZInput.prototype.switchLook = function() {
			this.props.dataMore.isLook = !this.props.dataMore.isLook;
			this.props.dataMore.inputType = this.props.dataMore.isLook
				? "text"
				: "password";
		};
		ZInput.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "z_input_box " + (this.props.class || ""),
					style:
						"border-radius:" +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;background:" +
						(this.props.bg || "rgba(0,0,0,0.05)") +
						";justify-content: " +
						(this.props.justify || "flex-start") +
						";" +
						(this.props.style || "")
				},
				apivm.h(
					"view",
					null,
					this.props.children.length >= 1 && this.props.children[0]
				),
				apivm.h(
					"view",
					null,
					!this.props.dataMore.dotIcon &&
						!this.props.dotIcon &&
						apivm.h(
							"view",
							{onClick: this.inputConfirm, style: "padding: 5px;marign-right:5px;"},
							apivm.h("a-iconfont", {
								name: this.props.dataMore.icon || "sousuo",
								color: "#999",
								size: G.appFontSize + (this.props.dataMore.iconSize || 0)
							})
						)
				),
				this.props.type == 2
					? apivm.h(
							"text",
							{
								style:
									"line-height:" +
									(G.appFontSize + 14) +
									"px;color:#999;" +
									loadConfiguration()
							},
							this.props.dataMore.placeholder || this.props.placeholder
					  )
					: apivm.h("input", {
							id: this.data.inputId,
							style:
								loadConfiguration() +
								"height:" +
								(G.appFontSize + 14) +
								"px;" +
								(this.props.dataMore.inputStyle || ""),
							"placeholder-style": "color:#ccc;",
							class: "z_input_input flex_w",
							type: this.props.dataMore.inputType || "text",
							placeholder:
								this.props.dataMore.placeholder ||
								this.props.dataMore.hint ||
								this.props.placeholder ||
								"请输入" + (this.props.dataMore.title || ""),
							onInput: function(e) {
								if (typeof this$1 != "undefined") {
									this$1.props.dataMore.value = e.target.value;
								} else {
									this$1.data.this.props.dataMore.value = e.target.value;
								}
								this$1.inputIng(e);
							},
							maxlength: this.props.dataMore.maxlength || this.props.dataMore.max,
							disabled:
								(isParameters(this.props.disabled) ? this.props.disabled : false) ||
								isParameters(this.props.readonly)
									? this.props.readonly
									: false,
							"confirm-type":
								this.props.confirmType || this.props.dataMore.confirmType || "search",
							"keyboard-type": this.props.dataMore.keyboardType || "default",
							onConfirm: this.inputConfirm,
							onBlur: this.inputBlur,
							onFocus: this.inputFocus,
							value:
								typeof this == "undefined"
									? this.data.this.props.dataMore.value
									: this.props.dataMore.value
					  }),
				apivm.h(
					"view",
					null,
					this.props.dataMore.value &&
						!this.props.dataMore.dotCleanIcon &&
						apivm.h(
							"view",
							{onClick: this.clean, style: "padding: 5px;"},
							apivm.h("a-iconfont", {
								name: "qingkong",
								color: "#666",
								size: G.appFontSize
							})
						)
				),
				apivm.h(
					"view",
					null,
					isParameters(this.props.dataMore.isLook) &&
						this.props.dataMore.value &&
						apivm.h(
							"view",
							{
								onClick: function() {
									return this$1.switchLook();
								},
								style: "padding:5px 10px 5px 3px;"
							},
							apivm.h("a-iconfont", {
								name: this.props.dataMore.isLook ? "kejian" : "bukejian",
								color: "#919191",
								size: G.appFontSize + 4
							})
						)
				),
				apivm.h(
					"view",
					null,
					this.props.children.length >= 2 && this.props.children[1]
				)
			);
		};

		return ZInput;
	})(Component);
	ZInput.css = {
		".z_input_box": {
			width: "100%",
			flexDirection: "row",
			padding: "2px 5px 2px 8px",
			alignItems: "center"
		},
		".z_input_input": {
			background: "transparent",
			borderColor: "transparent",
			color: "#333",
			paddingRight: "5px"
		},
		".z_input_input::placeholder": {color: "#ccc"}
	};
	apivm.define("z-input", ZInput);

	var Areas = /*@__PURE__*/ (function(Component) {
		function Areas(props) {
			Component.call(this, props);
			this.data = {
				show: false,

				area: {key: "", value: ""},
				areas: [],

				areaFirst: {index: 0, data: []},
				areaSecond: {index: -1, data: []},
				areaThird: {index: -1, data: []},
				sourceData: [],

				search: {value: "", placeholder: "搜索关键词"},
				searchShow: false,
				searchData: []
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							setTimeout(
								function() {
									this$1.baseInit();
								},
								platform() == "app" ? 50 : 0
							);
						}
					}
				}
			};
		}

		if (Component) Areas.__proto__ = Component;
		Areas.prototype = Object.create(Component && Component.prototype);
		Areas.prototype.constructor = Areas;
		Areas.prototype.baseInit = function() {
			var this$1 = this;

			var dm = this.props.dataMore;
			var areas = JSON.parse(
				(dm.all ? getPrefs("sys_allAreas") : "") || getPrefs("sys_areas") || "[]"
			);
			if (areas.length) {
				var sourceData = areas.concat(areas[0].children);
				sourceData[0].children = [];
				if (JSON.stringify(this.data.sourceData) != JSON.stringify(sourceData)) {
					this.data.sourceData = sourceData;
					this.data.areaFirst.index = 0;
					this.data.areaSecond.index = -1;
					this.data.areaSecond.data = [];
					this.data.areaThird.index = [];
				}
			}
			this.data.areaFirst.data = this.data.sourceData;

			getSysAreas(
				{
					url: dm.all ? appUrl() + "area/tree" : "",
					areaId: dm.all ? getPrefs("sys_platform") : ""
				},
				function(ret) {
					if (ret && ret.code == 200 && isArray(ret.data) && ret.data.length > 0) {
						var _data = ret.data;
						this$1.data.sourceData = _data.concat(_data[0].children);
						this$1.data.sourceData[0].children = [];
						this$1.data.areaFirst.data = this$1.data.sourceData;
					}
				}
			);

			this.data.area.key = dm.key || (!dm.dt ? areaId() : "");
			if (this.data.area.key) {
				this.data.area.value = getAreaForKey(
					this.data.area.key,
					this.props.dataMore.all
				).name;
			}
		};
		Areas.prototype.penetrate = function() {};
		Areas.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		Areas.prototype.caSearchInput = function(e) {
			if (this.data.search.value) {
				this.searchStart(this.data.sourceData, 0);
				this.data.searchData.sort(function(a, b) {
					return b.weights - a.weights;
				});
			}
		};
		Areas.prototype.searchStart = function(_list, _start) {
			if (!isParameters(_list)) {
				return;
			}
			if (_start == 0) {
				this.data.searchData = [];
			}
			_start++;
			var searchArr = this.data.search.value.split("");
			for (var i = 0; i < _list.length; i++) {
				var weights = 0;
				for (var j = 0; j < searchArr.length; j++) {
					if (_list[i].name.indexOf(searchArr[j]) != -1) {
						weights++;
					}
				}
				if (weights > 0) {
					_list[i].weights = weights;
					this.data.searchData.push(_list[i]);
				}
				if (_list[i].children && _list[i].children.length) {
					this.searchStart(_list[i].children, _start);
				}
			}
		};
		Areas.prototype.cckAreaItem = function(_item, _index, _level) {
			if (_level != 1 && (!isArray(_item.children) || !_item.children.length)) {
				_level = -1;
			}
			switch (_level) {
				case 1:
					this.data.areaFirst.index = _index;
					this.data.areaSecond.index = -1;
					this.data.areaSecond.data = this.data.areaFirst.data[
						this.data.areaFirst.index
					].children;
					break;
				case 2:
					this.data.areaSecond.index = _index;
					this.data.areaThird.index = -1;
					this.data.areaThird.data = this.data.areaSecond.data[
						this.data.areaSecond.index
					].children;
					break;
				case 3:
					break;
				default:
					if (this.data.area.key == _item.id) {
						toast("当前地区已是【" + _item.name + "】");
						return;
					}
					this.switchArea(_item);
					break;
			}
		};
		Areas.prototype.switchArea = function(_item) {
			var this$1 = this;

			var nowInfo = getAreaForKey(_item.id);
			var extra = {
				id: _item.id,
				key: _item.id,
				name: _item.name,
				value: _item.name
			};
			if (!nowInfo.id) {
				sendEvent({name: "areaChange_" + this.props.dataMore.all, extra: extra});
			} else if (!this.props.dataMore.ds) {
				setPrefs("sys_aresId", _item.id);
				areaNotice(extra);
			}
			G.areasPop.callback(extra);
			if (!this.props.dataMore.dt) {
				toast("切换成功");
			}
			setTimeout(function() {
				this$1.closePage();
			}, 500);
		};
		Areas.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style: "height:15%;flex-shrink: 0;"
				}),
				this.props.dataMore.show &&
					apivm.h(
						"view",
						{
							class: "flex_h pages_warp",
							onClick: function() {
								return this$1.penetrate();
							}
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							{class: "header_warp flex_row"},
							apivm.h(
								"view",
								{class: "header_main xy_center"},
								apivm.h(
									"text",
									{
										style: loadConfiguration(1) + "font-weight: 600;color:" + G.headColor
									},
									this.props.dataMore.title || "地区切换"
								)
							),
							apivm.h(
								"view",
								{class: "header_left_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{style: loadConfiguration(1) + "color:#666;margin:0 4px;"},
										"关闭"
									)
								)
							)
						),
						apivm.h("z-divider", null),
						apivm.h(
							"view",
							{class: "flex_h"},
							apivm.h(
								"view",
								{class: "now_box"},
								this.data.area.key
									? apivm.h(
											"view",
											{class: "flex_row"},
											apivm.h(
												"text",
												{style: loadConfiguration(2) + "color:#333;"},
												"当前选择："
											),
											apivm.h(
												"text",
												{style: loadConfiguration(2) + "color:#333;font-weight: 600;"},
												this.data.area.value
											)
									  )
									: null
							),
							apivm.h(
								"view",
								{style: "padding:4px 16px;"},
								apivm.h("z-input", {
									dataMore: this.data.search,
									onInput: this.caSearchInput
								})
							),
							apivm.h(
								"view",
								{style: "height:1px;flex:1;flex-direction:row;"},
								this.data.areaFirst.data.length > 0 &&
									apivm.h(
										"scroll-view",
										{
											class: "flex_w",
											style:
												"background:#F4F5F7;max-width: " + (G.appFontSize + 17) * 3 + "px;",
											"scroll-y": true
										},
										(Array.isArray(this.data.areaFirst.data)
											? this.data.areaFirst.data
											: Object.values(this.data.areaFirst.data)
										).map(function(item$1, index$1) {
											return apivm.h(
												"view",
												{
													onClick: function() {
														return this$1.cckAreaItem(item$1, index$1, 1);
													},
													class: "areas_item flex_row",
													style:
														"background:" +
														(index$1 == this$1.data.areaFirst.index
															? "#FFFFFF"
															: "transparent") +
														";"
												},
												apivm.h(
													"text",
													{
														class: "areas_item_text",
														style:
															loadConfiguration(2) +
															"font-weight:" +
															(index$1 == this$1.data.areaFirst.index ? "6" : "4") +
															"00;"
													},
													item$1.name
												),
												apivm.h("a-iconfont", {
													name: "xiangzuo",
													color:
														index$1 == this$1.data.areaFirst.index ? G.appTheme : "#ccc",
													style: "transform: rotate(180deg);",
													size: G.appFontSize - 2
												})
											);
										})
									),
								this.data.areaFirst.data.length > 0 &&
									apivm.h(
										"scroll-view",
										{class: "flex_w", "scroll-y": true},
										apivm.h(
											"view",
											{
												class: "areas_item",
												onClick: function() {
													return this$1.cckAreaItem(
														this$1.data.areaFirst.data[this$1.data.areaFirst.index],
														-1,
														-1
													);
												}
											},
											apivm.h(
												"text",
												{class: "areas_item_text", style: "" + loadConfiguration(2)},
												this.data.areaFirst.data[this.data.areaFirst.index].name
											)
										),
										(Array.isArray(this.data.areaSecond.data)
											? this.data.areaSecond.data
											: Object.values(this.data.areaSecond.data)
										).map(function(item$1, index$1) {
											return apivm.h(
												"view",
												{
													onClick: function() {
														return this$1.cckAreaItem(item$1, index$1, 2);
													},
													class: "areas_item flex_row",
													style:
														"background:" +
														(index$1 == this$1.data.areaSecond.index
															? "#F4F5F7"
															: "transparent") +
														";"
												},
												apivm.h(
													"text",
													{
														class: "areas_item_text",
														style:
															loadConfiguration(2) +
															"font-weight: " +
															(index$1 == this$1.data.areaSecond.index ? "6" : "4") +
															"00;"
													},
													item$1.name
												),
												isArray(item$1.children) &&
													item$1.children.length > 0 &&
													apivm.h("a-iconfont", {
														name: "xiangzuo",
														color:
															index$1 == this$1.data.areaSecond.index ? G.appTheme : "#ccc",
														style: "transform: rotate(180deg);",
														size: G.appFontSize - 2
													})
											);
										})
									),
								this.data.areaSecond.index > -1 &&
									apivm.h(
										"scroll-view",
										{class: "flex_w", "scroll-y": true},
										apivm.h(
											"view",
											{
												class: "areas_item",
												onClick: function() {
													return this$1.cckAreaItem(
														this$1.data.areaSecond.data[this$1.data.areaSecond.index],
														-1,
														-1
													);
												}
											},
											apivm.h(
												"text",
												{class: "areas_item_text", style: "" + loadConfiguration(2)},
												this.data.areaSecond.data[this.data.areaSecond.index].name,
												"(本级)"
											)
										),
										(Array.isArray(this.data.areaThird.data)
											? this.data.areaThird.data
											: Object.values(this.data.areaThird.data)
										).map(function(item$1, index$1) {
											return apivm.h(
												"view",
												{
													onClick: function() {
														return this$1.cckAreaItem(item$1, index$1, -1);
													},
													class: "areas_item flex_row",
													style:
														"background:" +
														(index$1 == this$1.data.areaThird.index
															? "#FFFFFF"
															: "transparent") +
														";"
												},
												apivm.h(
													"text",
													{
														class: "areas_item_text",
														style:
															loadConfiguration(2) +
															"font-weight: " +
															(index$1 == this$1.data.areaThird.index ? "6" : "4") +
															"00;"
													},
													item$1.name
												)
											);
										})
									),
								apivm.h(
									"view",
									{
										class: "page_box",
										style: "display:" + (this.data.search.value ? "flex" : "none") + ";"
									},
									apivm.h(
										"scroll-view",
										{class: "xy_100 search_box", "scroll-y": true},
										(Array.isArray(this.data.searchData)
											? this.data.searchData
											: Object.values(this.data.searchData)
										).map(function(item$1, index$1) {
											return apivm.h(
												"view",
												{
													onClick: function() {
														return this$1.cckAreaItem(item$1, index$1, -1);
													},
													class: "search_item"
												},
												item$1.name.split("").map(function(nItem, nIndex) {
													return [
														apivm.h(
															"text",
															{
																style:
																	loadConfiguration(2) +
																	"color: " +
																	(this$1.data.search.value.indexOf(nItem) != -1
																		? G.appTheme
																		: "#333") +
																	";"
															},
															nItem
														)
													];
												})
											);
										})
									)
								)
							)
						)
					)
			);
		};

		return Areas;
	})(Component);
	Areas.css = {
		".pages_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		},
		".now_box": {padding: "10px 16px"},
		".areas_item": {width: "100%", padding: "15px 6px 15px 16px"},
		".areas_item_text": {color: "#333"},
		".search_box": {
			padding: "10px 0",
			width: "100%",
			height: "100%",
			backgroundColor: "#FFF"
		},
		".search_item": {padding: "15px 16px", flexDirection: "row", flexWrap: "wrap"}
	};
	apivm.define("areas", Areas);

	var ZTag = /*@__PURE__*/ (function(Component) {
		function ZTag(props) {
			Component.call(this, props);
		}

		if (Component) ZTag.__proto__ = Component;
		ZTag.prototype = Object.create(Component && Component.prototype);
		ZTag.prototype.constructor = ZTag;
		ZTag.prototype.getTagStyle = function() {
			var color = this.props.color || G.appTheme;
			var rc = "",
				rbg = "",
				rbor = "";
			switch (this.props.type) {
				case "2":
					rc = color;
					rbg = "#FFF";
					rbor = colorRgba(color);
					break;
				case "3":
					rc = "#FFF";
					rbg = color;
					rbor = color;
					break;
				default:
					rc = color;
					rbg = colorRgba(color, 0.1);
					rbor = "transparent";
					break;
			}

			return (
				"color:" +
				rc +
				";background:" +
				rbg +
				";border: 1px solid " +
				rbor +
				";border-radius: " +
				(this.props.roundSize || 2) +
				"px;"
			);
		};
		ZTag.prototype.render = function() {
			return apivm.h(
				"view",
				{style: "flex-direction:row;"},
				apivm.h(
					"text",
					{
						id: "" + (this.props.id || ""),
						class: "tag_box " + (this.props.class || ""),
						style:
							"" +
							loadConfiguration((this.props.size || 0) - 2) +
							this.getTagStyle() +
							(this.props.style || "")
					},
					this.props.text || this.props.children
				)
			);
		};

		return ZTag;
	})(Component);
	ZTag.css = {".tag_box": {padding: "1px 10px"}};
	apivm.define("z-tag", ZTag);

	var ZVideo = /*@__PURE__*/ (function(Component) {
		function ZVideo(props) {
			Component.call(this, props);
			this.data = {
				controls: true
			};
			this.compute = {
				getSrc: function() {
					var src = this.props.src;
					if (src.indexOf("http") != 0) {
						src = appUrl() + "file/preview/" + src;
					}
					return src;
				}
			};
		}

		if (Component) ZVideo.__proto__ = Component;
		ZVideo.prototype = Object.create(Component && Component.prototype);
		ZVideo.prototype.constructor = ZVideo;
		ZVideo.prototype.firstPause = function() {
			var this$1 = this;

			if (!this.isPause) {
				this.isPause = true;
				if (platform() == "app" && !this.props.poster) {
					this.data.controls = false;
					setTimeout(function() {
						if (!this$1.props.autoplay) {
							document.getElementById(dealVideoId(this$1.props.src)) &&
								document.getElementById(dealVideoId(this$1.props.src)).pause();
						}
						this$1.data.controls = true;
					}, 50);
				} else {
					this.data.controls = true;
				}
			}
		};
		ZVideo.prototype.loadedmetadata = function(e) {
			if (api.systemType != "ios") {
				this.isPause = false;
				this.firstPause();
			}
		};
		ZVideo.prototype.play = function() {
			videoPlayPush(dealVideoId(this.props.src));
		};
		ZVideo.prototype.pause = function() {
			videoPlayRemove(dealVideoId(this.props.src));
		};
		ZVideo.prototype.render = function() {
			return apivm.h("video", {
				id: dealVideoId(this.props.src),
				style: "width:100%;height:" + G.pageWidth * 0.56 + "px;",
				controls: this.data.controls,
				autoplay:
					(platform() == "app" && !this.props.poster) || this.props.autoplay,
				onLoadedmetadata: this.loadedmetadata,
				onWaiting: this.firstPause,
				onPlay: this.play,
				onPause: this.pause,
				onEnded: this.pause,
				src: this.getSrc,
				poster: showImg(this.props.poster)
			});
		};

		return ZVideo;
	})(Component);
	apivm.define("z-video", ZVideo);

	var Item5 = /*@__PURE__*/ (function(Component) {
		function Item5(props) {
			Component.call(this, props);
		}

		if (Component) Item5.__proto__ = Component;
		Item5.prototype = Object.create(Component && Component.prototype);
		Item5.prototype.constructor = Item5;
		Item5.prototype.handClick = function(e) {
			stopBubble$1(e);
		};
		Item5.prototype.openVideos = function(e, _item, _index) {
			this.handClick(e);
			_item.autoplay = true;
			_item.showCode = "QUANLANSP";
		};
		Item5.prototype.showAxis = function() {
			if (!this.props.index) {
				return true;
			}
			if (
				this.props.item.isTop == "1" &&
				this.props.list[this.props.index - 1].isTop == 1
			) {
				return false;
			}
			if (
				dayjs(this.props.item.time).format("YYYY-MM-DD") !=
				dayjs(this.props.list[this.props.index - 1].time).format("YYYY-MM-DD")
			) {
				return true;
			}
			return false;
		};
		Item5.prototype.getPlayState = function() {
			var playItem = {src: "shengyin1", text: "播放"};
			if (this.props.item.id == this.props.floatModule.id) {
				switch (this.props.floatModule.state + "") {
					case "0":
						playItem.src = "shengyin1";
						playItem.text = "播放";
						break;
					case "1":
						playItem.src = "shengyin";
						playItem.text = "暂停";
						break;
					case "2":
						playItem.src = "shengyinjingyin";
						playItem.text = "继续播放";
						break;
					case "3":
						playItem.src = "shengyin1";
						playItem.text = "重新播放";
						break;
				}
			} else {
				playItem.src = "shengyin1";
				playItem.text = "播放";
			}
			return playItem;
		};
		Item5.prototype.playing = function(e) {
			this.handClick(e);
			if (this.props.item.id == this.props.floatModule.id) {
				if (this.props.floatModule.state == "1") {
					sendEvent({name: "index", extra: {type: "pauseStartPlay"}});
				} else {
					sendEvent({name: "index", extra: {type: "reStartPlay"}});
				}
			} else {
				sendEvent({
					name: "index",
					extra: {
						type: "startPlay",
						floatType: this.props.item.code,
						id: this.props.item.id,
						src: this.props.item.content
					}
				});
			}
		};
		Item5.prototype.openDetail = function(e) {
			openWin_news(this.props.item);
			this.handClick(e);
		};
		Item5.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					class: "news_item",
					style:
						"padding-left:" + (this.props.layoutId == "SJCXS" ? "24" : "16") + "px;",
					onClick: this.openDetail
				},
				apivm.h("view", {
					class: "news_point",
					style:
						"display:" +
						(this.props.layoutId == "SJCXS" && this.showAxis() ? "block" : "none") +
						";background:" +
						G.appTheme +
						";"
				}),
				apivm.h("view", {
					class: "news_line_top",
					style:
						"display:" +
						(this.props.layoutId == "SJCXS" ? "block" : "none") +
						";background:" +
						colorRgba(G.appTheme, this.props.index ? 0.15 : 0) +
						";"
				}),
				apivm.h("view", {
					class: "news_line_bottom",
					style:
						"display:" +
						(this.props.layoutId == "SJCXS" ? "block" : "none") +
						";background:" +
						colorRgba(
							G.appTheme,
							isArray(this.props.list) &&
								this.props.index == this.props.list.length - 1
								? 0
								: 0.15
						) +
						";"
				}),
				apivm.h(
					"view",
					null,
					this.props.layoutId == "SJCXS" &&
						apivm.h(
							"text",
							{
								style:
									"display:" +
									(this.showAxis() ? "block" : "none") +
									";" +
									loadConfiguration(-2) +
									"font-weight: 600;color:#333;margin-bottom:10px;"
							},
							this.props.item.isTop == 1
								? "置顶"
								: dayjs(this.props.item.time).format("YYYY-MM-DD")
						)
				),
				apivm.h(
					"view",
					null,
					((this.props.item.showCode == "QUANLANSP" && this.props.item.videoHrefs) ||
						isArray(this.props.item.url) ||
						this.props.item.code == "7") && [
						apivm.h(
							"view",
							{class: "flex_row"},
							apivm.h(
								"view",
								null,
								this.props.item.code == "7" &&
									apivm.h("view", {
										style:
											loadConfigurationSize(-2, "h") +
											"width:3px;background:" +
											G.appTheme +
											";margin-right:4px;"
									})
							),
							apivm.h(
								"view",
								{class: "flex_row", style: "flex:1;flex-wrap: wrap;"},
								this.props.item.title.split("").map(function(nItem, nIndex) {
									return (
										nIndex <
											Math.floor((G.pageWidth - 40) / (G.appFontSize + 2)) -
												(this$1.props.item.code == "7" ? 3 : 1) &&
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"margin:2px 0;font-weight: 600;color: " +
													(this$1.props.search &&
													this$1.props.search.result &&
													this$1.props.search.result.indexOf(nItem) > -1
														? G.appTheme
														: "#333") +
													";"
											},
											nIndex ==
												Math.floor((G.pageWidth - 40) / (G.appFontSize + 2)) -
													(this$1.props.item.code == "7" ? 3 : 1) -
													1
												? "..."
												: nItem
										)
									);
								})
							),
							apivm.h(
								"view",
								{class: "flex_row", style: "margin-left:5px;"},
								this.props.item.code == "7" &&
									apivm.h(
										"text",
										{
											style:
												loadConfiguration(-1) +
												"color:#999;flex-shrink: 0;margin-right:2px;"
										},
										"查看"
									),
								apivm.h(
									"view",
									null,
									apivm.h("a-iconfont", {
										name: "xiangxia1",
										style: "transform: rotate(-90deg);",
										color: "#666",
										size: "" + G.appFontSize
									})
								)
							)
						),
						apivm.h(
							"view",
							{style: "margin-top:10px;"},
							this.props.item.videoHrefs && this.props.item.showCode == "QUANLANSP"
								? apivm.h(
										"view",
										{onClick: this.handClick},
										apivm.h("z-video", {
											autoplay: this.props.item.autoplay,
											poster: this.props.item.poster,
											src: this.props.item.videoHrefs
										})
								  )
								: isArray(this.props.item.url)
								? apivm.h(
										"view",
										{class: "flex_row"},
										(Array.isArray(this.props.item.url)
											? this.props.item.url
											: Object.values(this.props.item.url)
										).map(function(nItem, nIndex) {
											return apivm.h(
												"view",
												{style: "width:33.33%;height:84px;margin: 0 2px;"},
												apivm.h("image", {
													class: "xy_100",
													src: showImg(nItem),
													mode: "aspectFill",
													thumbnail: "false"
												})
											);
										})
								  )
								: apivm.h("image", {
										style: "width:100%;height: " + G.pageWidth * 0.52 + "px;",
										src: showImg(this.props.item),
										mode: "aspectFill",
										thumbnail: "false"
								  })
						),
						apivm.h(
							"view",
							null,
							this.props.item.code != "7" &&
								apivm.h(
									"view",
									{style: "margin-top:10px;", class: "flex_row"},
									apivm.h(
										"view",
										{class: "flex_w flex_row"},
										this.props.layoutId != "SJCXS" &&
											apivm.h(
												"text",
												{
													class: "c_999 " + (this.props.item.url ? "text_one" : ""),
													style: "margin-right:10px;" + loadConfiguration(-4) + ";"
												},
												dayjs(this.props.item.time).format("YYYY-MM-DD")
											),
										apivm.h(
											"text",
											{class: "c_999 text_one", style: loadConfiguration(-4) + ";"},
											this.props.item.source
										)
									),
									apivm.h(
										"view",
										{style: "flex-shrink: 0;"},
										isObject(this.props.floatModule) &&
											this.props.item.content &&
											platform() == "app" &&
											api.require("voiceRecognizer") &&
											apivm.h(
												"view",
												{onClick: this.playing, class: "flex_row"},
												apivm.h("a-iconfont", {
													name: this.getPlayState().src,
													color: G.appTheme,
													size: G.appFontSize
												}),
												apivm.h(
													"text",
													{
														style:
															"margin-left:2px;" +
															loadConfiguration(-3) +
															";color:" +
															G.appTheme +
															";"
													},
													this.getPlayState().text
												)
											)
									),
									apivm.h(
										"view",
										{style: "flex-shrink: 0;"},
										this.props.item.sourceModuleName &&
											apivm.h(
												"z-tag",
												{style: "margin-left:10px;", type: "2", color: G.appTheme},
												this.props.item.sourceModuleName
											)
									)
								)
						)
					]
				),
				apivm.h(
					"view",
					null,
					(this.props.item.showCode != "QUANLANSP" || !this.props.item.videoHrefs) &&
						!isArray(this.props.item.url) &&
						this.props.item.code != "7" &&
						apivm.h(
							"view",
							{class: "flex_row"},
							apivm.h(
								"view",
								null,
								this.props.item.url &&
									apivm.h(
										"view",
										{class: "news_item_img"},
										apivm.h("image", {
											class: "xy_100",
											src: showImg(this.props.item),
											mode: "aspectFill",
											thumbnail: "false"
										}),
										this.props.item.videoHrefs &&
											this.props.item.showCode == "ZUOCESP" &&
											apivm.h(
												"view",
												{
													style:
														"position:absolute;z-index:1;left:0;top:0;right:0;bottom:0;align-items: center;justify-content: center;",
													onClick: function(e) {
														return this$1.openVideos(
															e,
															this$1.props.item,
															this$1.props.index
														);
													}
												},
												apivm.h("image", {
													mode: "aspectFill",
													style: loadConfigurationSize(22),
													thumbnail: "false",
													src: shareAddress(1) + "image/icon_play.png"
												})
											)
									)
							),
							apivm.h(
								"view",
								{class: "flex_w"},
								apivm.h(
									"view",
									{class: "flex_row", style: "flex-wrap: wrap;"},
									this.props.item.title.split("").map(function(nItem, nIndex) {
										return (
											nIndex <
												Math.floor(
													(G.pageWidth - 40 - (this$1.props.item.url ? 122 : 0)) /
														(G.appFontSize + 2)
												) *
													2 &&
											apivm.h(
												"text",
												{
													style:
														loadConfiguration(1) +
														"margin:2px 0;font-weight: 600;color: " +
														(this$1.props.search &&
														this$1.props.search.result &&
														this$1.props.search.result.indexOf(nItem) > -1
															? G.appTheme
															: "#333") +
														";"
												},
												nIndex ==
													Math.floor(
														(G.pageWidth - 40 - (this$1.props.item.url ? 122 : 0)) /
															(G.appFontSize + 2)
													) *
														2 -
														1
													? "..."
													: nItem
											)
										);
									})
								),
								apivm.h(
									"view",
									null,
									this.props.item.url &&
										apivm.h(
											"text",
											{
												class: "c_999 text_one",
												style: "margin-top:2px;" + loadConfiguration(-4) + ";"
											},
											this.props.item.source
										)
								),
								apivm.h(
									"view",
									{class: "flex_row", style: "margin-top:3px;"},
									apivm.h(
										"view",
										{class: "flex_w flex_row"},
										this.props.layoutId != "SJCXS" &&
											apivm.h(
												"text",
												{
													class: "c_999 " + (this.props.item.url ? "text_one" : ""),
													style: "margin-right:10px;" + loadConfiguration(-4) + ";"
												},
												dayjs(this.props.item.time).format("YYYY-MM-DD")
											),
										!this.props.item.url &&
											apivm.h(
												"text",
												{class: "c_999 text_one", style: loadConfiguration(-4) + ";"},
												this.props.item.source
											)
									),
									apivm.h(
										"view",
										{style: "flex-shrink: 0;"},
										isObject(this.props.floatModule) &&
											this.props.item.content &&
											platform() == "app" &&
											api.require("voiceRecognizer") &&
											apivm.h(
												"view",
												{onClick: this.playing, class: "flex_row"},
												apivm.h("a-iconfont", {
													name: this.getPlayState().src,
													color: G.appTheme,
													size: G.appFontSize
												}),
												apivm.h(
													"text",
													{
														style:
															"margin-left:2px;" +
															loadConfiguration(-4) +
															";color:" +
															G.appTheme +
															";"
													},
													this.getPlayState().text
												)
											)
									),
									apivm.h(
										"view",
										{style: "flex-shrink: 0;"},
										this.props.item.sourceModuleName &&
											apivm.h(
												"z-tag",
												{style: "margin-left:10px;", type: "2", color: G.appTheme},
												this.props.item.sourceModuleName
											)
									)
								)
							)
						)
				)
			);
		};

		return Item5;
	})(Component);
	Item5.css = {
		".c_999": {color: "#999"},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden",
			display: "inline"
		},
		".news_item": {padding: "15px 16px"},
		".news_item_img": {
			width: "112px",
			height: "84px",
			marginRight: "10px",
			borderRadius: "2px"
		},
		".news_point": {
			position: "absolute",
			zIndex: "999",
			left: "10px",
			top: "21px",
			borderRadius: "50%",
			width: "7px",
			height: "7px"
		},
		".news_line_top": {
			position: "absolute",
			zIndex: "999",
			left: "13px",
			top: "0",
			height: "24px",
			width: "1px"
		},
		".news_line_bottom": {
			position: "absolute",
			zIndex: "999",
			left: "13px",
			top: "24px",
			bottom: "0",
			width: "1px"
		}
	};
	apivm.define("item5", Item5);

	var YScrollView = /*@__PURE__*/ (function(Component) {
		function YScrollView(props) {
			Component.call(this, props);
			this.data = {
				mId: "",
				scroll_view: "", //滚动到id
				scroll_animation: true, //滚动动画
				refreshEd: false
			};
			this.compute = {
				monitor: function() {
					if (!this.data.mId) {
						var nowId = this.props.id || "scroll_view" + getNum();
						this.data.mId = !document.getElementById(nowId)
							? nowId
							: "scroll_view" + getNum();
						(this.props.dataMore || {}).id = this.data.mId;
					}
					var dataMore = this.props.dataMore || {};
					if (this.data.scroll_view != dataMore.scroll_view) {
						this.data.scroll_animation = isParameters(dataMore.scroll_animation)
							? dataMore.scroll_animation
							: true;
						this.data.scroll_view = dataMore.scroll_view || "";
						if (this.data.scroll_view) {
							this.scrollTo(this.data.scroll_view);
						}
					}
				}
			};
		}

		if (Component) YScrollView.__proto__ = Component;
		YScrollView.prototype = Object.create(Component && Component.prototype);
		YScrollView.prototype.constructor = YScrollView;
		YScrollView.prototype.onrefresherrefresh = function(e) {
			var this$1 = this;

			if (this.props._this && isFunction(this.props._this.pageRefresh)) {
				this.props._this.pageRefresh({detail: {}});
			} else {
				this.fire("lower", {});
			}
			this.data.refreshEd = true;
			setTimeout(function() {
				this$1.data.refreshEd = false;
			}, 800);
		};
		YScrollView.prototype.onscrolltolower = function(e) {
			if (this.props._this && isFunction(this.props._this.loadMore)) {
				this.props._this.loadMore({detail: {}});
			} else {
				this.fire("up", {});
			}
		};
		YScrollView.prototype.onscroll = function(ref) {
			var detail = ref.detail;

			if (this.props._this && isFunction(this.props._this.pageScroll)) {
				this.props._this.pageScroll({detail: detail});
			} else {
				this.fire("scroll", detail);
			}
		};
		YScrollView.prototype.scrollTo = function(nowView) {
			var this$1 = this;

			var _animated = this.data.scroll_animation;
			if (document.getElementById(this.data.mId) && platform() != "mp") {
				var scrollTo =
					platform() == "app"
						? {view: nowView, animated: _animated}
						: this.props.dataMore.direction == "horizontal"
						? {
								left: this.getOffestValue(document.getElementById(nowView)).left,
								behavior: _animated ? "smooth" : "instant"
						  }
						: {
								top: this.getOffestValue(document.getElementById(nowView)).top,
								behavior: _animated ? "smooth" : "instant"
						  };
				document.getElementById(this.data.mId).scrollTo(scrollTo);
			}
			setTimeout(function() {
				this$1.props.dataMore.scroll_view = "";
			}, 500);
		};
		YScrollView.prototype.getOffestValue = function(elem) {
			var Far = null;
			var topValue = elem && elem.offsetTop;
			var leftValue = elem && elem.offsetLeft;
			var offsetFar = elem && elem.offsetParent;
			while (offsetFar) {
				if (offsetFar.id == this.data.mId) {
					break;
				}
				topValue += offsetFar.offsetTop;
				leftValue += offsetFar.offsetLeft;
				Far = offsetFar;
				offsetFar = offsetFar.offsetParent;
				if (offsetFar.id == this.data.mId) {
					break;
				}
			}
			return {top: topValue, left: leftValue, Far: Far};
		};
		YScrollView.prototype.render = function() {
			return apivm.h(
				"scroll-view",
				{
					s: this.monitor,
					id: "" + this.data.mId,
					style: "flex:1;" + (this.props.style || ""),
					class: "" + (this.props.class || ""),
					"refresher-background": "rgba(0,0,0,0)",
					"scroll-x": isParameters(this.props["scroll-x"])
						? this.props["scroll-x"]
						: false,
					"scroll-y": isParameters(this.props["scroll-y"])
						? this.props["scroll-y"]
						: true,
					bounces: isParameters(this.props["bounces"])
						? this.props["bounces"]
						: false,
					"scroll-into-view": platform() == "mp" ? this.data.scroll_view : null,
					"scroll-with-animation":
						platform() == "mp" ? this.data.scroll_animation : null,
					"refresher-enabled": isParameters(this.props["refresh"])
						? this.props["refresh"] &&
						  (platform() != "app" ? !this.props._this.props.dataMore : true)
						: false,
					"refresher-triggered": this.data.refreshEd,
					onScrolltolower: this.onscrolltolower,
					onRefresherrefresh: this.onrefresherrefresh,
					onScroll: this.onscroll
				},
				this.props.children
			);
		};

		return YScrollView;
	})(Component);
	apivm.define("y-scroll-view", YScrollView);

	var ZTabsTwo = /*@__PURE__*/ (function(Component) {
		function ZTabsTwo(props) {
			Component.call(this, props);
			this.data = {
				scrollView: {
					scroll_view: "",
					direction: "horizontal"
				}
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (
						!this.show &&
						this.props.dataMore.data.length &&
						api.systemType == "ios"
					) {
						this.show = true;
						setTimeout(function() {
							document.getElementById(this$1.data.scrollView.id) &&
								document
									.getElementById(this$1.data.scrollView.id)
									.scrollTo({position: "upper", animated: false});
						}, 390);
					}
				},
				position: function() {},
				animated: function() {}
			};
		}

		if (Component) ZTabsTwo.__proto__ = Component;
		ZTabsTwo.prototype = Object.create(Component && Component.prototype);
		ZTabsTwo.prototype.constructor = ZTabsTwo;
		ZTabsTwo.prototype.tabClick = function(_item) {
			if (_item.readonly) {
				this.fire("readonly", _item);
				return;
			}
			if (this.props.dotDefault && this.props.dataMore.key == _item.key) {
				this.props.dataMore.key = "";
				this.fire("change", {key: this.props.dataMore.key});
				return;
			}
			if (this.props.dataMore.key == _item.key) {
				return;
			}
			this.props.dataMore.key = _item.key;
			this.fire("change", {key: this.props.dataMore.key});
			this.data.scrollView.scroll_view =
				(this.props.id || "") + "_two_" + _item.key;
		};
		ZTabsTwo.prototype.nTouchmove = function() {
			touchmove();
		};
		ZTabsTwo.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					style: "flex-direction:row;" + (this.props.style || ""),
					class: "" + (this.props.class || "")
				},
				apivm.h(
					"y-scroll-view",
					{
						style: "display: block;white-space: nowrap;",
						_this: this,
						dataMore: this.data.scrollView,
						"scroll-x": true,
						"scroll-y": false
					},
					(Array.isArray(this.props.dataMore.data || [])
						? this.props.dataMore.data || []
						: Object.values(this.props.dataMore.data || [])
					).map(function(item$1, index$1) {
						return apivm.h(
							"view",
							{
								id: (this$1.props.id || "") + "_two_" + item$1.key,
								class: "tabs_two_item",
								onClick: function() {
									return this$1.tabClick(item$1);
								},
								style:
									"width:" +
									(this$1.props.average
										? 100 / this$1.props.dataMore.data.length + "%"
										: "auto") +
									";",
								onTouchStart: this$1.nTouchmove,
								onTouchMove: this$1.nTouchmove,
								onTouchEnd: this$1.nTouchmove
							},
							apivm.h(
								"view",
								{
									class: "tabs_two_ibox",
									style:
										"background:" +
										(this$1.props.dataMore.key == item$1.key
											? this$1.props.color || G.appTheme
											: this$1.props.dColor || "#F8F8F8") +
										";"
								},
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(this$1.props.size) +
											";color:#" +
											(this$1.props.dataMore.key == item$1.key ? "fff" : "666") +
											";"
									},
									item$1.value
								)
							)
						);
					})
				)
			);
		};

		return ZTabsTwo;
	})(Component);
	ZTabsTwo.css = {
		".tabs_two_item": {
			flexDirection: "row",
			justifyContent: "center",
			display: "inline-block",
			padding: "8px 5px",
			textAlign: "center"
		},
		".tabs_two_ibox": {
			flexDirection: "row",
			justifyContent: "center",
			display: "inline-block",
			padding: "2px 8px",
			borderRadius: "2px"
		}
	};
	apivm.define("z-tabs-two", ZTabsTwo);

	var ZTabsOne = /*@__PURE__*/ (function(Component) {
		function ZTabsOne(props) {
			Component.call(this, props);
			this.data = {
				scrollView: {
					scroll_view: "",
					direction: "horizontal"
				}
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (
						!this.show &&
						this.props.dataMore.data.length &&
						api.systemType == "ios"
					) {
						this.show = true;
						setTimeout(function() {
							document.getElementById(this$1.data.scrollView.id) &&
								document
									.getElementById(this$1.data.scrollView.id)
									.scrollTo({position: "upper", animated: false});
						}, 390);
					}
				},
				position: function() {},
				animated: function() {}
			};
		}

		if (Component) ZTabsOne.__proto__ = Component;
		ZTabsOne.prototype = Object.create(Component && Component.prototype);
		ZTabsOne.prototype.constructor = ZTabsOne;
		ZTabsOne.prototype.tabClick = function(_item) {
			if (_item.readonly) {
				this.fire("readonly", _item);
				return;
			}
			if (this.props.dotDefault && this.props.dataMore.key == _item.key) {
				this.props.dataMore.key = "";
				this.fire("change", {key: this.props.dataMore.key});
				return;
			}
			if (this.props.dataMore.key == _item.key) {
				this.fire("dotchange", {key: this.props.dataMore.key});
				return;
			}
			this.props.dataMore.key = _item.key;
			this.fire("change", {key: this.props.dataMore.key});
			this.data.scrollView.scroll_view =
				(this.props.id || "") + "tabs_" + _item.key;
		};
		ZTabsOne.prototype.nTouchmove = function() {
			touchmove();
		};
		ZTabsOne.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					style: "flex-direction:row;" + (this.props.style || ""),
					class: "" + (this.props.class || "")
				},
				apivm.h(
					"y-scroll-view",
					{
						style: "display: block;white-space: nowrap;",
						_this: this,
						dataMore: this.data.scrollView,
						"scroll-x": true,
						"scroll-y": false
					},
					(Array.isArray(this.props.dataMore.data || [])
						? this.props.dataMore.data || []
						: Object.values(this.props.dataMore.data || [])
					).map(function(item$1, index$1) {
						return apivm.h(
							"view",
							{
								id: (this$1.props.id || "") + "tabs_" + item$1.key,
								class: "tabs_item",
								onClick: function() {
									return this$1.tabClick(item$1);
								},
								style:
									"width:" +
									(this$1.props.average
										? 100 / this$1.props.dataMore.data.length + "%"
										: "auto") +
									";",
								onTouchStart: this$1.nTouchmove,
								onTouchMove: this$1.nTouchmove,
								onTouchEnd: this$1.nTouchmove
							},
							apivm.h(
								"text",
								{
									class: "tabs_text",
									style:
										loadConfiguration(
											this$1.props.dataMore.key == item$1.key
												? this$1.props.activeSize
												: this$1.props.size
										) +
										";color:#" +
										(this$1.props.dataMore.key == item$1.key ? "333" : "666") +
										";font-weight: " +
										(this$1.props.dataMore.key == item$1.key ? "8" : "4") +
										"00;"
								},
								item$1.value
							),
							apivm.h("view", {
								class: "tabs_line",
								style:
									"background:" +
									(this$1.props.dataMore.key != item$1.key
										? "transparent"
										: this$1.props.color || G.appTheme) +
									";" +
									(this$1.props.lineS || "")
							})
						);
					})
				)
			);
		};

		return ZTabsOne;
	})(Component);
	ZTabsOne.css = {
		".tabs_item": {
			display: "inline-block",
			padding: "6px 9px",
			textAlign: "center"
		},
		".tabs_text": {textAlign: "center"},
		".tabs_line": {
			width: "80%",
			maxWidth: "38px",
			height: "2px",
			margin: "2px auto 1px auto"
		}
	};
	apivm.define("z-tabs-one", ZTabsOne);

	var ZImage = /*@__PURE__*/ (function(Component) {
		function ZImage(props) {
			Component.call(this, props);
			this.data = {
				imgId: "img_" + getNum(),
				imgMode: this.props.mode || "aspectFill",
				imgThumbnail: isParameters(this.props.thumbnail)
					? this.props.thumbnail
					: true,
				showFilter: false,
				show: true
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					var scrollBox = this.props.scrollBox;
					if (scrollBox) {
						getBoundingClientRect("box_" + this.data.imgId, function(ret) {
							// console.log("scrollBox:----"+JSON.stringify(scrollBox) + "--" + api.winHeight);
							// console.log("img:-------"+JSON.stringify(ret));
							this$1.data.show = !(
								ret.top + ret.height < -150 || ret.top - 150 > api.winHeight
							);
						});
					}
				}
			};
		}

		if (Component) ZImage.__proto__ = Component;
		ZImage.prototype = Object.create(Component && Component.prototype);
		ZImage.prototype.constructor = ZImage;
		ZImage.prototype.load = function(e) {
			if (!this.props.src || !document.getElementById(this.data.imgId)) {
				return;
			}
			var nowProportion = 0;
			var imgWidth =
				platform() == "app"
					? e.detail.width
					: document.getElementById(this.data.imgId).naturalWidth;
			var imgHeight =
				platform() == "app"
					? e.detail.height
					: document.getElementById(this.data.imgId).naturalHeight;
			if (imgWidth && imgHeight) {
				nowProportion = Number((imgWidth / imgHeight).toFixed(2));
			}
			if (nowProportion && this.props.proportionMin && this.props.proportionMax) {
				if (
					nowProportion < Number(this.props.proportionMin) ||
					nowProportion > Number(this.props.proportionMax)
				) {
					this.data.showFilter = true;
					this.data.imgMode = "aspectFit";
				} else {
					this.data.showFilter = false;
					this.data.imgMode = this.props.mode || "aspectFill";
				}
			}
		};
		ZImage.prototype.error = function() {
			this.fire("error");
		};
		ZImage.prototype.render = function() {
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					class: "" + (this.props.class || ""),
					style:
						"overflow:hidden;border-radius: " +
						(this.props.round ? "50%" : "0px") +
						";width:100%;height:100%;" +
						(this.props.style || "")
				},
				this.data.showFilter &&
					this.data.show &&
					apivm.h("image", {
						class: "z_imageFilter",
						mode: "aspectFill",
						src: this.props.src,
						thumbnail: true
					}),
				apivm.h(
					"view",
					{class: "z_image", id: "box_" + this.data.imgId},
					this.data.show &&
						apivm.h("image", {
							id: this.data.imgId,
							class: "xy_100",
							mode: this.data.imgMode,
							src: this.props.src,
							thumbnail: this.data.imgThumbnail,
							onLoad: this.load,
							onError: this.error
						})
				)
			);
		};

		return ZImage;
	})(Component);
	ZImage.css = {
		".z_image": {
			width: "100%",
			height: "100%",
			filter: "none",
			opacity: "1",
			position: "absolute",
			left: "0",
			top: "0"
		},
		".z_imageFilter": {
			width: "100%",
			height: "100%",
			filter: "blur(4px)",
			opacity: "0.7",
			position: "relative",
			left: "0",
			top: "0"
		}
	};
	apivm.define("z-image", ZImage);

	var ZButton = /*@__PURE__*/ (function(Component) {
		function ZButton(props) {
			Component.call(this, props);
		}

		if (Component) ZButton.__proto__ = Component;
		ZButton.prototype = Object.create(Component && Component.prototype);
		ZButton.prototype.constructor = ZButton;
		ZButton.prototype.componentsClick = function(e) {
			stopBubble$1(e);
			if (!this.props.disabled && !this.props.readonly) {
				this.fire("click", {});
			}
		};
		ZButton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					id: "" + (this.props.id || ""),
					class: "z_button " + (this.props.class || ""),
					style:
						"border-radius:" +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;border-color:" +
						(this.props.color || G.appTheme) +
						";background:" +
						(this.props.plain ? "#FFF" : this.props.color || G.appTheme) +
						";opacity:" +
						(this.props.disabled ? "0.5" : "1") +
						";" +
						(this.props.style || ""),
					onClick: function(e) {
						return this$1.componentsClick(e);
					}
				},
				this.props.icon &&
					apivm.h("a-iconfont", {
						name: this.props.icon,
						style: "margin-right:5px;",
						color: this.props.plain ? this.props.color || G.appTheme : "#FFF",
						size: G.appFontSize - 1 + (this.props.size || 0)
					}),
				apivm.h(
					"text",
					{
						style:
							"color:" +
							(this.props.plain ? this.props.color || G.appTheme : "#FFF") +
							";" +
							loadConfiguration(this.props.size || 0)
					},
					this.props.text || this.props.children
				)
			);
		};

		return ZButton;
	})(Component);
	ZButton.css = {
		".z_button": {
			padding: "7px 12px",
			borderWidth: "1px",
			borderStyle: "solid",
			boxSizing: "border-box",
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("z-button", ZButton);

	var ZEmpty = /*@__PURE__*/ (function(Component) {
		function ZEmpty(props) {
			Component.call(this, props);
		}

		if (Component) ZEmpty.__proto__ = Component;
		ZEmpty.prototype = Object.create(Component && Component.prototype);
		ZEmpty.prototype.constructor = ZEmpty;
		ZEmpty.prototype.getShowImg = function() {
			if (this.props.dataMore.type == 1 || this.props.dataMore.type == 2) {
				return showImg(
					shareAddress(1) + "image/icon_empty_" + this.props.dataMore.type + ".png"
				);
			}
			return showImg(this.props.dataMore.type);
		};
		ZEmpty.prototype.getShowText = function() {
			return (
				this.props.dataMore.text ||
				(this.props.dataMore.type == 1
					? "暂无数据"
					: this.props.dataMore.type == 2
					? "网络异常，请点击重试"
					: "")
			);
		};
		ZEmpty.prototype.refresh = function() {
			showProgress("刷新中");
			this.fire("refresh");
			setTimeout(function() {
				hideProgress();
			}, 2500);
		};
		ZEmpty.prototype.up = function() {
			if (this.props._this && isFunction(this.props._this.loadMore)) {
				this.props._this.loadMore({detail: {}});
			} else {
				this.fire("up", {});
			}
		};
		ZEmpty.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"view",
					null,
					this.props.dataMore.type &&
						this.props.dataMore.type != "load" &&
						apivm.h(
							"view",
							{
								id: "" + (this.props.id || ""),
								style: "margin:100px 0;" + (this.props.style || ""),
								class: "xy_center " + (this.props.class || "")
							},
							apivm.h("image", {
								style: "width:200px;height:200px;",
								src: this.getShowImg(),
								mode: "aspectFill"
							}),
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(this.props.size || 0) +
										"color:#666;padding:10px 20px;text-align: center;"
								},
								this.getShowText()
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == 2 &&
									apivm.h(
										"z-button",
										{
											style: "width:132px;margin-top:20px;",
											color: G.appTheme,
											round: true,
											onClick: this.refresh
										},
										"刷新"
									)
							),
							this.props.children
						)
				),
				apivm.h(
					"view",
					null,
					!this.props.dataMore.type &&
						this.props.dataMore.text &&
						apivm.h(
							"view",
							{class: "xy_center", onClick: this.up},
							apivm.h(
								"text",
								{style: loadConfiguration(-4) + "color:#bbb;padding:15px;"},
								this.props.dataMore.text
							)
						)
				)
			);
		};

		return ZEmpty;
	})(Component);
	apivm.define("z-empty", ZEmpty);

	var ZSkeleton = /*@__PURE__*/ (function(Component) {
		function ZSkeleton(props) {
			Component.call(this, props);
		}

		if (Component) ZSkeleton.__proto__ = Component;
		ZSkeleton.prototype = Object.create(Component && Component.prototype);
		ZSkeleton.prototype.constructor = ZSkeleton;
		ZSkeleton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{style: "width:100%;"},
				(Array.isArray(dataForNum(this.props.cell || 3))
					? dataForNum(this.props.cell || 3)
					: Object.values(dataForNum(this.props.cell || 3))
				).map(function(item$1, index$1, list) {
					return apivm.h(
						"view",
						{class: "z_skeleton"},
						apivm.h(
							"view",
							null,
							(Array.isArray(dataForNum(this$1.props.row || 4))
								? dataForNum(this$1.props.row || 4)
								: Object.values(dataForNum(this$1.props.row || 4))
							).map(function(item$1, index$1, list) {
								return apivm.h("view", {
									class: "z_skeleton_row",
									style:
										"width:" +
										(!index$1 ? 40 : index$1 == list.length - 1 ? 60 : 100) +
										"%;"
								});
							})
						)
					);
				})
			);
		};

		return ZSkeleton;
	})(Component);
	ZSkeleton.css = {
		".z_skeleton": {width: "100%", padding: "10px 16px"},
		".z_skeleton_row": {
			marginTop: "12px",
			height: "16px",
			backgroundColor: "#f2f3f5"
		}
	};
	apivm.define("z-skeleton", ZSkeleton);

	var baoguo_hezi_o = "";
	var fuzhilianjiexian = "";
	var fuzhilianjiemian = "";
	var wenjian = "";
	var tianjia = "";
	var tianjiawenjian = "";
	var sousuowenjian = "";
	var wenjianshangchuan = "";
	var dalou = "";
	var wucan = "";
	var zengjia1 = "";
	var xinsui = "";
	var aixin = "";
	var jianshao2 = "";
	var yishoucang = "";
	var yuyinshibie = "";
	var shuruxiangce = "";
	var tupianshibie = "";
	var shuruwenjian = "";
	var shurupaizhao = "";
	var tongji4 = "";
	var pinglun1 = "";
	var gerentongxunlu = "";
	var fenxiang2 = "";
	var dianzan = "";
	var shoucang1 = "";
	var sousuo2 = "";
	var jiancexinbanben = "";
	var shezhi1 = "";
	var guanhuaimoshi = "";
	var xiugaimima = "";
	var anquantuichu = "";
	var dianhua = "";
	var shipintianchong = "";
	var xiangxiagengduo = "";
	var mubiao = "";
	var jianshao1 = "";
	var biaoqian = "";
	var duigou = "";
	var jinengbiaoqian = "";
	var jiantou_xiangyouliangci = "";
	var toupiao = "";
	var riqi = "";
	var gonggao = "";
	var tongji3 = "";
	var paihang = "";
	var mn_paiming_fill = "";
	var shaixuan = "";
	var envelope = "";
	var nvshangjia = "";
	var nan = "";
	var tongji1 = "";
	var tuceng = "";
	var tongji2 = "";
	var biaoqianlan_shouye = "";
	var shengyinjingyin = "";
	var shengyin = "";
	var shengyin1 = "";
	var tianxie = "";
	var erweima = "";
	var huanyuan = "";
	var lianjie = "";
	var changyonglogo28 = "";
	var pengyouquan = "";
	var zhixiangxia = "";
	var QQ = "";
	var quanxian = "";
	var baocun = "";
	var suoyouwenjian = "";
	var xiangxia1 = "";
	var xinzeng = "";
	var ziyuanxhdpi = "";
	var xiazai = "";
	var zhongmingming = "";
	var yunpan = "";
	var tongji = "";
	var kefu = "";
	var weibiaoti1 = "";
	var tongxunlu = "";
	var xinyongqia = "";
	var tuichu = "";
	var aixinxiantiao = "";
	var shezhi = "";
	var jianchaxinbanben = "";
	var yuyin = "";
	var shanchu = "";
	var remen = "";
	var tupian = "";
	var xiangji = "";
	var zan = "";
	var zan1 = "";
	var like = "";
	var unlike = "";
	var pinglun = "";
	var jushoucang = "";
	var jushoucanggift = "";
	var share = "";
	var shoucangfill = "";
	var fenxiang1 = "";
	var shoucang = "";
	var bofang = "";
	var daojishi = "";
	var cuohao = "";
	var duihao = "";
	var chakan = "";
	var shoujihaoma = "";
	var mima1 = "";
	var zhanghao1 = "";
	var yanzhengma1 = "";
	var kejian = "";
	var bukejian = "";
	var gengduo1 = "";
	var gengduogongneng = "";
	var gengduo2 = "";
	var gengduo3 = "";
	var fanhui1 = "";
	var fanhui2 = "";
	var sousuo = "";
	var sousuo1 = "";
	var saoyisao1 = "";
	var xiangxia = "";
	var xiangzuo = "";
	var fangxingweixuanzhong = "";
	var fangxingxuanzhongfill = "";
	var fangxingxuanzhong = "";
	var yuanxingweixuanzhong = "";
	var yuanxingxuanzhong = "";
	var yuanxingxuanzhongfill = "";
	var danxuan_xuanzhong = "";
	var danxuan_weixuanzhong = "";
	var gengduo = "";
	var fenxiang = "";
	var zhuanfa00 = "";
	var dingwei = "";
	var dingwei1 = "";
	var jianshao = "";
	var zengjia = "";
	var qingkong = "";
	var mima = "";
	var zhanghao = "";
	var yanzhengma = "";
	var icons = {
		baoguo_hezi_o: baoguo_hezi_o,
		fuzhilianjiexian: fuzhilianjiexian,
		fuzhilianjiemian: fuzhilianjiemian,
		wenjian: wenjian,
		tianjia: tianjia,
		tianjiawenjian: tianjiawenjian,
		sousuowenjian: sousuowenjian,
		wenjianshangchuan: wenjianshangchuan,
		"zuanshi-L": "",
		dalou: dalou,
		wucan: wucan,
		zengjia1: zengjia1,
		xinsui: xinsui,
		aixin: aixin,
		jianshao2: jianshao2,
		yishoucang: yishoucang,
		yuyinshibie: yuyinshibie,
		shuruxiangce: shuruxiangce,
		tupianshibie: tupianshibie,
		shuruwenjian: shuruwenjian,
		shurupaizhao: shurupaizhao,
		tongji4: tongji4,
		pinglun1: pinglun1,
		gerentongxunlu: gerentongxunlu,
		fenxiang2: fenxiang2,
		dianzan: dianzan,
		shoucang1: shoucang1,
		sousuo2: sousuo2,
		jiancexinbanben: jiancexinbanben,
		shezhi1: shezhi1,
		guanhuaimoshi: guanhuaimoshi,
		xiugaimima: xiugaimima,
		anquantuichu: anquantuichu,
		dianhua: dianhua,
		shipintianchong: shipintianchong,
		"31dianhua": "",
		xiangxiagengduo: xiangxiagengduo,
		mubiao: mubiao,
		jianshao1: jianshao1,
		biaoqian: biaoqian,
		"gantanhao-xianxingyuankuang": "",
		duigou: duigou,
		jinengbiaoqian: jinengbiaoqian,
		jiantou_xiangyouliangci: jiantou_xiangyouliangci,
		toupiao: toupiao,
		riqi: riqi,
		gonggao: gonggao,
		tongji3: tongji3,
		paihang: paihang,
		mn_paiming_fill: mn_paiming_fill,
		"line-084": "",
		"line-085": "",
		shaixuan: shaixuan,
		envelope: envelope,
		"envelope-open": "",
		"a-4_huaban1": "",
		nvshangjia: nvshangjia,
		nan: nan,
		tongji1: tongji1,
		"weibiaoti--": "",
		tuceng: tuceng,
		tongji2: tongji2,
		biaoqianlan_shouye: biaoqianlan_shouye,
		shengyinjingyin: shengyinjingyin,
		shengyin: shengyin,
		shengyin1: shengyin1,
		tianxie: tianxie,
		"file-download-fill": "",
		"file-damage-fill": "",
		"file-excel-fill": "",
		"file-copy-fill": "",
		"file-edit-fill": "",
		"file-music-fill": "",
		"file-gif-fill": "",
		"file-history-fill": "",
		"file-lock-fill": "",
		"file-info-fill": "",
		"file-pdf-fill": "",
		"file-text-fill": "",
		"file-ppt-fill": "",
		"file-upload-fill": "",
		"file-unknow-fill": "",
		"file-word-fill": "",
		"file-search-fill": "",
		"file-transfer-fill": "",
		"file-zip-fill": "",
		"folder-2-fill": "",
		"file-warning-fill": "",
		"folder-add-fill": "",
		erweima: erweima,
		"file-reduce-fill": "",
		huanyuan: huanyuan,
		lianjie: lianjie,
		changyonglogo28: changyonglogo28,
		pengyouquan: pengyouquan,
		zhixiangxia: zhixiangxia,
		QQ: QQ,
		quanxian: quanxian,
		baocun: baocun,
		suoyouwenjian: suoyouwenjian,
		xiangxia1: xiangxia1,
		"wj-gxwj": "",
		xinzeng: xinzeng,
		ziyuanxhdpi: ziyuanxhdpi,
		xiazai: xiazai,
		zhongmingming: zhongmingming,
		yunpan: yunpan,
		"file-code-fill": "",
		"file-add-fill": "",
		tongji: tongji,
		kefu: kefu,
		weibiaoti1: weibiaoti1,
		tongxunlu: tongxunlu,
		xinyongqia: xinyongqia,
		tuichu: tuichu,
		aixinxiantiao: aixinxiantiao,
		shezhi: shezhi,
		jianchaxinbanben: jianchaxinbanben,
		yuyin: yuyin,
		shanchu: shanchu,
		remen: remen,
		tupian: tupian,
		xiangji: xiangji,
		zan: zan,
		zan1: zan1,
		like: like,
		unlike: unlike,
		"like-fill": "",
		"unlike-fill": "",
		pinglun: pinglun,
		jushoucang: jushoucang,
		jushoucanggift: jushoucanggift,
		share: share,
		shoucangfill: shoucangfill,
		fenxiang1: fenxiang1,
		shoucang: shoucang,
		bofang: bofang,
		"bell-off": "",
		daojishi: daojishi,
		cuohao: cuohao,
		duihao: duihao,
		chakan: chakan,
		shoujihaoma: shoujihaoma,
		mima1: mima1,
		zhanghao1: zhanghao1,
		yanzhengma1: yanzhengma1,
		kejian: kejian,
		bukejian: bukejian,
		gengduo1: gengduo1,
		gengduogongneng: gengduogongneng,
		gengduo2: gengduo2,
		gengduo3: gengduo3,
		"a-14Bshanchu": "",
		fanhui1: fanhui1,
		fanhui2: fanhui2,
		sousuo: sousuo,
		sousuo1: sousuo1,
		saoyisao1: saoyisao1,
		xiangxia: xiangxia,
		xiangzuo: xiangzuo,
		fangxingweixuanzhong: fangxingweixuanzhong,
		fangxingxuanzhongfill: fangxingxuanzhongfill,
		fangxingxuanzhong: fangxingxuanzhong,
		yuanxingweixuanzhong: yuanxingweixuanzhong,
		yuanxingxuanzhong: yuanxingxuanzhong,
		yuanxingxuanzhongfill: yuanxingxuanzhongfill,
		danxuan_xuanzhong: danxuan_xuanzhong,
		danxuan_weixuanzhong: danxuan_weixuanzhong,
		gengduo: gengduo,
		fenxiang: fenxiang,
		zhuanfa00: zhuanfa00,
		dingwei: dingwei,
		dingwei1: dingwei1,
		jianshao: jianshao,
		zengjia: zengjia,
		qingkong: qingkong,
		mima: mima,
		zhanghao: zhanghao,
		yanzhengma: yanzhengma
	};

	var AMpcss = /*@__PURE__*/ (function(Component) {
		function AMpcss(props) {
			Component.call(this, props);
		}

		if (Component) AMpcss.__proto__ = Component;
		AMpcss.prototype = Object.create(Component && Component.prototype);
		AMpcss.prototype.constructor = AMpcss;
		AMpcss.prototype.render = function() {
			return;
		};

		return AMpcss;
	})(Component);
	AMpcss.css = {
		"@font-face": {
			fontFamily: '"iconfont_mp"',
			src:
				"url('https://at.alicdn.com/t/c/font_3560231_mff0m4knr.ttf') format('truetype')"
		}
	};

	apivm.define("a-mpcss", AMpcss);

	var AIconfont = /*@__PURE__*/ (function(Component) {
		function AIconfont(props) {
			Component.call(this, props);
		}

		if (Component) AIconfont.__proto__ = Component;
		AIconfont.prototype = Object.create(Component && Component.prototype);
		AIconfont.prototype.constructor = AIconfont;
		AIconfont.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"text",
					{
						style:
							"font-size:" +
							(this.props.size || 16) +
							"px;color:" +
							(this.props.color || "#999") +
							";\n\t\tfont-family: " +
							(api.platform == "mp" ? "iconfont_mp" : "iconfont;") +
							";\n\t\t" +
							(this.props.style || "") +
							";font-weight:400;",
						class: "" + (this.props.class || "")
					},
					icons[this.props.name + ""]
				),
				api.platform == "mp" && apivm.h("a-mpcss", null)
			);
		};

		return AIconfont;
	})(Component);
	AIconfont.css = {
		"@font-face": {
			fontFamily: '"iconfont"',
			src:
				"url('../../components/act/a-iconfont/fonts/iconfont.ttf') format('truetype')"
		}
	};
	apivm.define("a-iconfont", AIconfont);

	var YBasePage = /*@__PURE__*/ (function(Component) {
		function YBasePage(props) {
			Component.call(this, props);
			this.data = {
				show: false, //为组件时显示隐藏
				initFrist: true, //首次初始化
				title: "" //标题栏
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					if (!this.props.dataMore) {
						if (this.headTitle != G.headTitle) {
							this.headTitle = G.headTitle;
							this.setHeader();
						}
						if (this.headTheme != (G.showHeadTheme || G.headTheme)) {
							this.headTheme = G.showHeadTheme || G.headTheme;
							this.setHeadTheme();
						}
					} else {
						if (this.props.dataMore.show != this.data.show) {
							this.data.show = this.props.dataMore.show;
							videoPlayRemoves();
							if (this.data.show) {
								if (this.data.initFrist) {
									setTimeout(function() {
										this$1.baseInit();
									}, 110);
								} else {
									this.pageRefresh();
								}
							} else {
								if (this.props._this && isFunction(this.props._this.baseClose)) {
									this.props._this.baseClose({detail: {}});
								} else {
									this.fire("baseClose");
								}
							}
						}
					}
					if (
						(!this.props.dataMore || this.data.show) &&
						G.onShowNum != this.onShowNum
					) {
						this.onShowNum = G.onShowNum;
						if (this.isShow && this.onShowNum > 0) {
							this.pageRefresh();
							if (!this.props.dataMore) {
								console.log(
									(api.winName || "") +
										"第" +
										G.onShowNum +
										"次返回：" +
										JSON.stringify(this.props._this.data.pageParam)
								);
							}
						}
						this.isShow = true;
					}
				},
				detail: function() {}
			};
		}

		if (Component) YBasePage.__proto__ = Component;
		YBasePage.prototype = Object.create(Component && Component.prototype);
		YBasePage.prototype.constructor = YBasePage;
		YBasePage.prototype.installed = function() {
			var this$1 = this;
			var that = this.props._this;
			that.data.pageParam = pageParam(that);
			that.data.pageType = that.data.pageParam.pageType || "page";
			clearTimeout(that.data.oneTask);
			that.data.oneTask = setTimeout(function() {
				console.log("当前页面参数：" + JSON.stringify(that.data.pageParam));
				G.chatInfos = [];
				G.chatInfoTask = [];
				setTimeout(function() {
					if (!this$1.props.dataMore) {
						if (!G.headTitle) {
							G.headTitle = that.data.pageParam.title || that.data.dTitle || "";
						}
					} else {
						this$1.data.title = that.data.pageParam.title || that.data.dTitle || "";
					}
				}, 400);
				if (!this$1.props.dataMore) {
					G._this = that;
					if (that.data.pageParam.token) {
						setPrefs("sys_token", decodeURIComponent(that.data.pageParam.token));
						if (!getPrefs("sys_Mobile")) {
							getLoginInfo({header: {"u-login-areaId": ""}}, function(ret, err) {});
						}
					}
					if (that.data.pageParam.areaId) {
						setPrefs("sys_aresId", that.data.pageParam.areaId);
					}
					addEventListener("sys_refresh", function(ret) {
						this$1.initConfiguration();
					});
					var traverseList = function(_obj, _option) {
						for (var key in _obj) {
							var item = _obj[key];
							if (
								isObject(item) &&
								!isArray(item) &&
								key.endsWith("Pop") &&
								isParameters(item.show) &&
								item.show
							) {
								if (_option) {
									item.show = false;
								}
								return false;
							}
						}
						return true;
					};
					addEventListener("keyback", function(ret, err) {
						if (G.alertPop.show) {
							if (G.alertPop.cancel.show) {
								G.alertPop.show = false;
							}
							return;
						}
						if (!traverseList(G, true) || !traverseList(that.data, true)) {
							return;
						}
						this$1.close();
					});
					addEventListener("swiperight", function(ret) {
						if (G.nTouchmove) {
							return;
						}
						if (!traverseList(G, false) || !traverseList(that.data, false)) {
							return;
						}
						this$1.close(1);
					});
					this$1.initConfiguration();
					this$1.baseInit();
				}
			}, 50);
		};
		YBasePage.prototype.baseInit = function() {
			var this$1 = this;

			setTimeout(
				function() {
					var detail = {first: this$1.data.initFrist};
					if (this$1.props._this && isFunction(this$1.props._this.baseInit)) {
						this$1.props._this.baseInit({detail: detail});
					} else {
						this$1.fire("init", detail);
					}
					this$1.data.initFrist = false;
				},
				!this.props.dataMore ? 300 : 0
			);
		};
		YBasePage.prototype.close = function(_type) {
			if (this.props._this && isFunction(this.props._this.close)) {
				this.props._this.close({detail: {type: _type}});
			} else {
				this.fire("close", {type: _type});
			}
		};
		YBasePage.prototype.cTitle = function() {
			this.fire("cTitle");
		};
		YBasePage.prototype.pageRefresh = function(_frist) {
			if (!this.props.dataMore) {
				this.setHeadTheme();
			}
			if (!_frist) {
				if (this.props._this && isFunction(this.props._this.pageRefresh)) {
					this.props._this.pageRefresh({detail: {back: true}});
				} else {
					this.fire("pageRefresh", {back: true});
				}
			}
		};
		YBasePage.prototype.initConfiguration = function() {
			G.pageWidth =
				platform() == "web"
					? api.winWidth > api.winHeight
						? 600
						: api.winWidth
					: api.winWidth;
			G.showCaptcha = getPrefs("enableShortAuth") == "true";
			G.appName = getPrefs("sys_systemName") || "";
			G.terminal = getPrefs("terminal") || "";
			G.appFont = getPrefs("appFont") || "heitiSimplified";
			G.appFontSize = Number(getPrefs("appFontSize") || "16");
			G.sysSign = sysSign();
			G.appTheme =
				this.props._this.data.pageParam.appTheme ||
				getPrefs("appTheme" + G.sysSign) ||
				(G.sysSign == "rd" ? "#C61414" : "#3088FE");
			var headTheme =
				this.props._this.data.headTheme ||
				this.props._this.data.pageParam.headTheme ||
				getPrefs("headTheme") ||
				"transparent";
			G.headTheme = headTheme == "appTheme" ? G.appTheme : headTheme;
			G.careMode = parseInt(G.appFontSize) > 16;
			G.systemtTypeIsPlatform = getPrefs("sys_systemType") == "platform"; //系统类型是否是平台版
			G.loginInfo = getPrefs("sys_systemLoginContact") || "";
			if (platform() == "app") {
				G.isAppReview =
					JSON.parse(getPrefs("sys_appReviewVersion") || "{}")[api.systemType] ==
					api.appVersion;
			}
			G.v = getPrefs("sys_appVersion") || "";

			G.uId = getPrefs("sys_Id") || "";
			G.userId = getPrefs("sys_UserID") || "";
			G.userName = getPrefs("sys_UserName") || "";
			G.userImg = getPrefs("sys_AppPhoto") || "";
			G.areaId = getPrefs("sys_aresId") || "";
			G.specialRoleKeys = JSON.parse(getPrefs("sys_SpecialRoleKeys") || "[]");
			G.isAdmin =
				G.userId == "1" ||
				getItemForKey("dc_admin", G.specialRoleKeys) ||
				getItemForKey("admin", G.specialRoleKeys);
			G.grayscale = getPrefs("sys_grayscale") || "";
			var watermark = getPrefs("sys_watermark") || "";
			if (platform() == "app") {
				api.screenLayerFilter({
					region: "window",
					sat: G.grayscale == "true" ? 0 : 1
				});
			}
			if (watermark && watermark != "false") {
				var t = watermark
					.replace("user", G.userName)
					.replace("phone", getPrefs("sys_Mobile") || "")
					.replace("true", G.appName);
				if (t) {
					G.watermark =
						tomcatAddress() +
						"utils2/watermark?s=" +
						G.appFontSize +
						"&t=" +
						t +
						"&w=" +
						G.pageWidth +
						"&h=" +
						api.winHeight;
				}
			} else {
				G.watermark = false;
			}

			this.pageRefresh(true);
			if (platform() == "web") {
				var fontStyleId = "fontStyle";
				if (document.getElementById(fontStyleId)) {
					//存在的时候先删除
					document
						.getElementById(fontStyleId)
						.parentNode.removeChild(document.getElementById(fontStyleId));
				}
				var fontStyle = document.createElement("style");
				fontStyle.id = fontStyleId;
				switch (G.appFont) {
					case "shusongSimplified":
						fontStyle.innerText =
							"@font-face{font-family: shusongSimplified;src: url('../../res/fz_shusong_simplified.ttf')}";
						break;
					case "kaitiSimplified":
						fontStyle.innerText =
							"@font-face{font-family: kaitiSimplified;src: url('../../res/fz_kaiti_simplified.ttf')}";
						break;
					case "heitiTraditional":
						fontStyle.innerText =
							"@font-face{font-family: heitiTraditional;src: url('../../res/fz_heiti_traditional.ttf')}";
						break;
				}

				document.getElementsByTagName("head")[0].appendChild(fontStyle);
				this.fitWidth();
			}
		};
		YBasePage.prototype.fitWidth = function() {
			if (platform() == "web") {
				$("body").style.width = "100%";
				$("body").style.maxWidth = G.pageWidth + "px";
				$("body").style.minWidth = "300px";
				$("body").style.margin = "auto";
				$("body").style.position = "relative";
			}
		};
		YBasePage.prototype.isColorDarkOrLight = function(hexcolor) {
			try {
				var colors = colorRgba(hexcolor).match(/\d+\.?\d*/g);
				var red = colors[1],
					green = colors[2],
					blue = colors[3],
					brightness;
				brightness = (red * 299 + green * 587 + blue * 114) / 255000;
				return brightness >= 0.5 ? "light" : "dark";
			} catch (e) {
				return "";
			}
		};
		YBasePage.prototype.setHeadTheme = function() {
			var colorDarkOrLight = this.isColorDarkOrLight(
				this.headTheme == "transparent" ? "#FFF" : this.headTheme
			);
			G.headColor = colorDarkOrLight == "dark" ? "#FFF" : "#333";
			setStatusBarStyle({
				style: colorDarkOrLight == "light" ? "dark" : "light",
				color: "rgba(0,0,0,0)"
			});
		};
		YBasePage.prototype.setHeader = function() {
			if (this.props.dataMore) {
				return;
			}
			this.data.title = G.headTitle;
			if (platform() == "web") {
				if (window.parent) {
					window.parent.document.title = this.data.title;
				} else {
					document.title = this.data.title;
				}
			} else if (platform() == "mp") {
				wx.setNavigationBarTitle({title: this.data.title});
			}
		};
		YBasePage.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "page_box",
					class:
						"page_box page_bg " +
						(G.grayscale == "true" ? "filterGray" : "filterNone"),
					style: {display: !this.props.dataMore || this.data.show ? "flex" : "none"}
				},
				apivm.h(
					"view",
					{class: "watermark_box"},
					G.watermark &&
						apivm.h("image", {
							class: "xy_100",
							src: G.watermark,
							mode: "aspectFill",
							thumbnail: "false"
						})
				),
				G.appTheme && [
					apivm.h(
						"view",
						{class: "xy_100"},

						apivm.h(
							"view",
							{
								class: "flex_shrink",
								style: {
									display:
										!this.props.dataMore || !this.props.dataMore.closeH ? "flex" : "none"
								}
							},
							!this.props.closeH &&
								(this.props.titleBox ||
									this.props.back ||
									this.props.more ||
									showHeader()) &&
								apivm.h(
									"view",
									{
										style:
											"height:auto;padding-top:" +
											headerTop() +
											"px;background:" +
											(this.props._this.data.headTheme || G.headTheme)
									},
									apivm.h(
										"view",
										{class: "header_warp flex_row"},
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.cTitle();
												},
												class: "header_main xy_center",
												style: this.props.titleStyle || null
											},
											this.props.titleBox && this.props.children.length >= 3
												? [this.props.children[2]]
												: [
														apivm.h(
															"text",
															{
																style:
																	loadConfiguration(4) + "font-weight: 600;color:" + G.headColor
															},
															this.data.title
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "header_left_box"},
											this.props.back && this.props.children.length >= 1
												? [this.props.children[0]]
												: [
														apivm.h(
															"view",
															{
																onClick: function() {
																	return this$1.close();
																},
																class: "header_btn",
																style: {
																	display:
																		showHeader() && this.props._this.data.pageType == "page"
																			? "flex"
																			: "none"
																}
															},
															apivm.h("a-iconfont", {
																name: "fanhui1",
																color: G.headColor,
																size: G.appFontSize + 1
															})
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "header_right_box"},
											this.props.more &&
												this.props.children.length >= 2 &&
												this.props.children[1]
										)
									)
								)
						),
						this.props.children.length >= 4 && this.props.children[3]
					),
					this.props.children.length >= 5
						? this.props.children.filter(function(item, index) {
								return index >= 4;
						  })
						: null,
					!this.props.dataMore && [
						apivm.h("previewer-img", {dataMore: G.imgPreviewerPop}),
						apivm.h("areas", {dataMore: G.areasPop}),
						apivm.h("select-item", {dataMore: G.selectPop}),
						apivm.h("z-actionSheet", {dataMore: G.actionSheetPop}),
						apivm.h("z-alert", {dataMore: G.alertPop})
					]
				]
			);
		};

		return YBasePage;
	})(Component);
	YBasePage.css = {
		".avm-toast,.avm-confirm-mask": {zIndex: "999"},
		element: {width: "auto", height: "auto"},
		".flex_shrink,div": {flexShrink: "0", WebkitOverflowScrolling: "touch"},
		".flex_w": {flex: "1", width: "1px"},
		".flex_h": {flex: "1", height: "1px"},
		".flex_row": {flexDirection: "row", alignItems: "center"},
		".xy_center": {alignItems: "center", justifyContent: "center"},
		".xy_100": {width: "100%", height: "100%"},
		".page_box": {
			position: "absolute",
			zIndex: "999",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		},
		".page_bg": {background: "#FFF"},
		".header_warp": {height: "44px"},
		".header_main": {
			height: "100%",
			flex: "1",
			flexDirection: "row",
			padding: "0 44px"
		},
		".header_left_box": {
			position: "absolute",
			zIndex: "999",
			left: "0",
			width: "auto",
			height: "44px"
		},
		".header_right_box": {
			position: "absolute",
			zIndex: "999",
			right: "0",
			width: "auto",
			height: "44px"
		},
		".header_btn": {
			width: "auto",
			height: "100%",
			minWidth: "36px",
			padding: "0 12px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".header_btn:active": {background: "rgba(0,0,0,0.05)"},
		".filterNone": {filter: "none"},
		".filterGray": {filter: "grayscale(1)"},
		".watermark_box": {
			position: "absolute",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		}
	};
	apivm.define("y-base-page", YBasePage);

	var MoNews = /*@__PURE__*/ (function(Component) {
		function MoNews(props) {
			Component.call(this, props);
			this.data = {
				pageParam: {},
				pageType: "",
				MG: !this.props.dataMore ? G : null,
				searchHint: "",
				emptyBox: {
					type: "load", //load加载中 1
					text: ""
				},

				pageNo: 1,
				pageSize: 15,
				refreshPageSize: 0,
				listData: [],

				area: {key: "", value: ""},
				floatModule: {state: "", id: ""},

				newModule: 1,
				tabBox: {key: "", data: []},
				tabChildBox: {key: "", data: []},

				layoutId: "ZHXS", //类型  ZHXS综合 TWXS图文 SPXS视频 SJCXS时间轴
				layoutCid: "ZHXSTT1", //子集类型 ZHXSTT1 ZHXSTT2 ZHXSTT3（头条类型）SPXSLB SPXSSP（列表、大屏列表视频）

				carouselList: [],
				carouselOld: "",
				carouselIndex: 0,

				scrollView: {
					scroll_view: ""
				}
			};
		}

		if (Component) MoNews.__proto__ = Component;
		MoNews.prototype = Object.create(Component && Component.prototype);
		MoNews.prototype.constructor = MoNews;
		MoNews.prototype.onShow = function() {
			G.onShowNum++;
		};
		MoNews.prototype.baseInit = function(ref) {
			var this$1 = this;
			var detail = ref.detail;

			this.data.newModule = this.data.pageParam.module || 1;
			this.data.searchHint =
				this.data.pageParam.searchHint ||
				(this.data.newModule == 1 ? "时政资讯" : "搜索");
			this.pageName = "news" + this.data.newModule;
			addEventListener("playState_" + this.pageName, function(ret, err) {
				this$1.data.floatModule = ret.value;
			});
			addEventListener("areaChange_" + this.pageName, function(ret, err) {
				if (ret.value.key) {
					this$1.data.area.key = ret.value.key;
					this$1.pageRefresh();
				}
				this$1.data.area.value = getAreaForKey(this$1.data.area.key, 1).name;
			});
			this.data.area.key = areaId();
			this.data.area.value = getAreaForKey(this.data.area.key, 1).name;
			this.pageRefresh();
		};
		MoNews.prototype.pageRefresh = function() {
			this.getColumn(1);
			this.getData(0);
		};
		MoNews.prototype.close = function() {
			if (this.props.dataMore) {
				this.props.dataMore.show = false;
			} else {
				closeWin();
			}
		};
		MoNews.prototype.tabChange = function(_type, dot) {
			if (_type == 1) {
				this.data.layoutId = this.data.tabBox.key
					? getItemForKey(this.data.tabBox.key, this.data.tabBox.data).layoutId
					: "ZHXS";
				this.data.layoutCid = this.data.tabBox.key
					? getItemForKey(this.data.tabBox.key, this.data.tabBox.data).layoutCid
					: "ZHXSTT1";
				if (!dot) {
					this.data.tabChildBox.key = "";
					this.data.tabChildBox.data = [];
				}
				if (this.data.tabBox.key) {
					this.getColumn(2);
				}
			} else {
				this.data.layoutId = this.data.tabChildBox.key
					? getItemForKey(this.data.tabChildBox.key, this.data.tabChildBox.data)
							.layoutId
					: getItemForKey(this.data.tabBox.key, this.data.tabBox.data).layoutId;
				this.data.layoutCid = this.data.tabChildBox.key
					? getItemForKey(this.data.tabChildBox.key, this.data.tabChildBox.data)
							.layoutCid
					: getItemForKey(this.data.tabBox.key, this.data.tabBox.data).layoutCid;
			}
			if (!dot) {
				this.data.emptyBox.type = "load";
				this.data.listData = [];
			}
			if (dot != this.data.tabBox.key) {
				this.getData(0);
			}
		};
		MoNews.prototype.tabdotChange = function() {
			if (this.data.tabChildBox.key) {
				this.data.tabChildBox.key = "";
				this.getData(0);
			}
		};
		MoNews.prototype.getData = function(_type) {
			var this$1 = this;

			if (!this.data.tabBox.key) {
				return;
			}
			var postParam = {
				pageNo: !_type ? 1 : this.data.pageNo,
				pageSize:
					!_type && this.data.refreshPageSize > this.data.pageSize
						? this.data.refreshPageSize
						: this.data.pageSize,
				keyword: "",
				query: {
					moduleId: this.data.newModule,
					columnId: this.data.tabChildBox.key || this.data.tabBox.key || null,
					passFlag: 1
				}
			};

			if (postParam.pageNo == 1) {
				if (this.data.layoutId == "ZHXS") {
					this.getTopList();
				} else {
					this.data.carouselList = [];
					this.data.carouselOld = "";
				}
			}
			getList5({param: postParam, areaId: this.data.area.key}, function(ret) {
				var data = ret ? ret.dealWith || [] : [];
				if (!isArray(data) || !data.length) {
					dealData(_type, this$1, ret);
					return;
				}
				if (!_type) {
					this$1.data.listData = data;
				} else {
					this$1.data.listData = this$1.data.listData.concat(data);
				}
				this$1.data.emptyBox.type = "";
				this$1.data.emptyBox.text =
					data.length >= postParam.pageSize ? LOAD_MORE : LOAD_ALL;
				this$1.data.pageNo =
					Math.ceil(this$1.data.listData.length / this$1.data.pageSize) + 1;
				this$1.data.refreshPageSize =
					Math.ceil(this$1.data.listData.length / this$1.data.pageSize) *
					this$1.data.pageSize;
			});
		};
		MoNews.prototype.loadMore = function() {
			if (
				(this.data.emptyBox.text == LOAD_MORE ||
					this.data.emptyBox.text == NET_ERR$1) &&
				this.data.pageNo != 1
			) {
				this.data.emptyBox.text = LOAD_ING;
				this.getData(this.data.listData.length ? 1 : 0);
			}
		};
		MoNews.prototype.getTopList = function() {
			var this$1 = this;

			var postParam = {
				pageNo: 1,
				pageSize: Number(getPrefs("infomationTop") || "5"),
				query: {
					moduleId: this.data.newModule,
					columnId: this.data.tabChildBox.key || this.data.tabBox.key || null
				}
			};

			ajax(
				{u: appUrl() + "newsContent/app/topList"},
				"topList",
				function(ret, err) {
					var data = ret ? ret.data || [] : [];
					var dataLength = data ? data.length : 0;
					var nowList = [];
					if (data && isArray(data) && dataLength != 0) {
						for (var i = 0; i < dataLength; i++) {
							var item = {},
								itemData = data[i];
							item.code = module5.code;
							item.businessCode = module5.businessCode;
							item.id = itemData.id || "";
							item.title = itemData.infoTitle || "";
							item.url = itemData.infoPic || "";
							var image = itemData.fileLinks || [];
							if (isParameters(image) && image.length) {
								var useType1 = getItemForKey("1", image, "useType");
								if (useType1 && useType1.attachments && useType1.attachments.length) {
									item.url = useType1.attachments[0].newFileName;
								}
							}
							var attachments = itemData.attachments || [];
							if (!item.url && attachments.length) {
								item.url = attachments[0].newFileName;
							}
							item.link = itemData.contentType == 2 ? itemData.linkUrl || "" : "";
							nowList.push(item);
						}
					}
					if (this$1.data.carouselIndex >= nowList.length) {
						this$1.data.carouselIndex = 0;
					}
					if (platform() == "web") {
						if (
							this$1.data.carouselOld &&
							this$1.data.carouselOld != JSON.stringify(nowList)
						) {
							this$1.data.carouselList = [];
							setTimeout(function() {
								this$1.data.carouselList = nowList;
							}, 10);
						} else {
							this$1.data.carouselList = nowList;
						}
					} else {
						this$1.data.carouselList = nowList;
					}
					this$1.data.carouselOld = JSON.stringify(nowList);
				},
				"头条",
				"post",
				{
					body: JSON.stringify(postParam)
				}
			);
		};
		MoNews.prototype.getColumn = function(_type) {
			var this$1 = this;

			getColumn5(
				{
					param: {
						query: {
							moduleId: this.data.newModule,
							parentId: _type == 1 ? "0" : this.data.tabBox.key
						}
					},

					areaId: this.data.area.key
				},
				function(ret, err) {
					hideProgress();
					var data = ret ? ret.data || [] : [];
					var nowList = [];
					var oldKey = this$1.data.tabBox.key;
					if (isArray(data) && data.length > 0) {
						for (var i = 0; i < data.length; i++) {
							var id = data[i].id;
							var name = data[i].name;
							var layoutId = data[i].layoutCode;
							var layoutCid = data[i].layoutChildrenCode;
							var item = {
								value: name,
								key: id,
								layoutId: layoutId,
								layoutCid: layoutCid
							};
							nowList.push(item);
						}
						if (_type == 1) {
							this$1.data.tabBox.data = nowList;
							if (
								!this$1.data.tabBox.key ||
								!getItemForKey(this$1.data.tabBox.key, nowList)
							) {
								this$1.data.tabBox.key = nowList.length ? nowList[0].key : "";
							}
							this$1.tabChange(1, oldKey);
						} else if (_type == 2) {
							this$1.data.tabChildBox.data = nowList;
							if (
								this$1.data.tabChildBox.key &&
								!getItemForKey(this$1.data.tabChildBox.key, nowList)
							) {
								this$1.data.tabChildBox.key = "";
								this$1.getData(0);
							}
						}
					} else {
						if (_type == 1) {
							this$1.data.tabBox.key = "";
							this$1.data.tabChildBox.key = "";
							this$1.data.tabBox.data = [];
							this$1.data.tabChildBox.data = [];
							this$1.data.listData = [];
							this$1.data.emptyBox.type = ret ? "1" : "2";
							this$1.data.emptyBox.text =
								ret && ret.code != 200 ? ret.message || ret.data : "";
						} else {
							if (this$1.data.tabChildBox.key) {
								this$1.data.tabChildBox.key = "";
								this$1.getData(0);
							}
							this$1.data.tabChildBox.data = [];
						}
					}
				}
			);
		};
		MoNews.prototype.openAreas = function() {
			openAreas(
				{
					all: this.data.newModule == 1 ? this.pageName : "",
					key: this.data.area.key
				},
				function(ret) {}
			);
		};
		MoNews.prototype.openSearch = function() {
			openWin_search({code: "5", module: this.data.newModule});
		};
		MoNews.prototype.carouselChange = function(e) {
			this.data.carouselIndex = e.detail.current || 0;
		};
		MoNews.prototype.carouselText = function() {
			try {
				return this.data.carouselList.length
					? this.data.carouselList[this.data.carouselIndex].title
					: "";
			} catch (e) {
				return "";
			}
		};
		MoNews.prototype.openCarousel = function(_item) {
			openWin_news(_item);
		};
		MoNews.prototype.nTouchmove = function() {
			touchmove();
		};
		MoNews.prototype.pageScroll = function(ref) {
			var detail = ref.detail;

			var scrollTop = detail.scrollTop;
			var videos = this.data.listData.filter(function(item) {
				return item.videoHrefs;
			});
			if (videos.length && isArray(G.playVideos) && G.playVideos.length) {
				videos.forEach(function(_eItem) {
					var vId = dealVideoId(_eItem.videoHrefs);
					if (G.playVideos[0] != vId) {
						return;
					}
					if (document.getElementById(vId)) {
						getBoundingClientRect(vId, function(ret) {
							if (ret.top < headerTop() || ret.bottom > api.winHeight) {
								videoPlayRemoves();
							}
						});
					}
				});
			}
		};
		MoNews.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"y-base-page",
				{_this: this, dataMore: this.props.dataMore, closeH: true},
				apivm.h("view", null),
				apivm.h("view", null),
				apivm.h("view", null),
				apivm.h(
					"view",
					{class: "flex_h"},
					apivm.h(
						"view",
						{
							style:
								"display:" +
								(!this.props.dataMore || !this.props.dataMore.closeH
									? "flex"
									: "none") +
								";width:100%;height:auto;padding-top:" +
								headerTop() +
								"px;"
						},
						apivm.h(
							"view",
							{class: "header_warp flex_row", style: "padding:0 16px;"},
							apivm.h(
								"view",
								null,
								showHeader() &&
									this.data.pageType != "home" &&
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.close();
											},
											class: "header_btn",
											style: "margin:0 5px 0 -10px;"
										},
										apivm.h("a-iconfont", {
											name: "fanhui1",
											color: "#333",
											size: G.appFontSize + 1
										})
									)
							),
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.openSearch();
									},
									class: "flex_w"
								},
								apivm.h("z-input", {
									style: "padding:0;",
									round: true,
									type: "2",
									justify: "center",
									placeholder: this.data.searchHint,
									dataMore: {}
								})
							),
							apivm.h(
								"view",
								null,
								!G.isAppReview &&
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.openAreas();
											},
											style: "margin-left: 22px;flex-direction:row; align-items: center;"
										},
										apivm.h(
											"text",
											{style: loadConfiguration(4) + "font-weight: 600; color: #333;"},
											this.data.area.value
										),
										apivm.h("a-iconfont", {
											style: "margin-left:12px;",
											name: "gengduogongneng",
											color: "#333",
											size: G.appFontSize + 4
										})
									)
							)
						)
					),
					apivm.h(
						"view",
						{style: "padding:0 7px;"},
						(api.systemType != "android" || this.data.tabBox.data.length > 0) &&
							apivm.h("z-tabs-one", {
								dataMore: this.data.tabBox,
								onChange: function() {
									return this$1.tabChange(1);
								},
								onDotchange: this.tabdotChange
							})
					),
					apivm.h(
						"view",
						{style: "padding:0 11px;"},
						(api.systemType != "android" || this.data.tabChildBox.data.length > 0) &&
							apivm.h("z-tabs-two", {
								dotDefault: true,
								dataMore: this.data.tabChildBox,
								onChange: function() {
									return this$1.tabChange(2);
								}
							})
					),
					apivm.h(
						"y-scroll-view",
						{
							_this: this,
							dataMore: this.data.scrollView,
							refresh: true,
							onScroll: this.pageScroll
						},
						apivm.h(
							"view",
							null,
							!this.data.emptyBox.type &&
								this.data.layoutId == "ZHXS" &&
								this.data.carouselList.length > 0 && [
									apivm.h(
										"view",
										{class: "swiper_box"},
										apivm.h(
											"swiper",
											{
												style: "height: " + G.pageWidth * 0.56 + "px;",
												autoplay: this.data.carouselList.length > 1,
												circular: true,
												"previous-margin": this.data.layoutCid == "ZHXSTT3" ? 20 : 0,
												"next-margin": this.data.layoutCid == "ZHXSTT3" ? 20 : 0,
												onChange: this.carouselChange
											},
											(Array.isArray(this.data.carouselList)
												? this.data.carouselList
												: Object.values(this.data.carouselList)
											).map(function(_item, _index) {
												return apivm.h(
													"swiper-item",
													{class: "xy_100"},
													apivm.h(
														"view",
														{
															onClick: function() {
																return this$1.openCarousel(_item);
															},
															style:
																"width:100%;height:100%;padding:" +
																(this$1.data.layoutCid == "ZHXSTT3" &&
																_index != this$1.data.carouselIndex
																	? "10"
																	: "0") +
																"px 0;border-radius:" +
																(this$1.data.layoutCid == "ZHXSTT3" &&
																_index == this$1.data.carouselIndex
																	? 5
																	: 0) +
																"px;overflow: hidden;",
															onTouchStart: this$1.nTouchmove,
															onTouchMove: this$1.nTouchmove
														},
														apivm.h("z-image", {
															src: showImg(_item),
															thumbnail: false,
															proportionMin: "1.26",
															proportionMax: "1.86"
														})
													)
												);
											})
										)
									),
									apivm.h(
										"view",
										{
											class: "news_swiper_text_box",
											style:
												"margin-top:" +
												(this.data.layoutCid == "ZHXSTT3" ? 0 : -20) +
												"px;margin-bottom:" +
												(this.data.layoutCid == "ZHXSTT3" ? 0 : 20) +
												"px;"
										},
										apivm.h(
											"view",
											null,
											this.data.layoutCid == "ZHXSTT1" &&
												apivm.h(
													"view",
													{
														class: "news_swiper_text_warp",
														style: "height:" + (G.appFontSize * 1.3 * 2 + 16) + "px;"
													},
													apivm.h("image", {
														class: "news_swiper_text_tag",
														style: "" + loadConfigurationSize(18, "h"),
														src: shareAddress(1) + "image/icon_img_hot_" + G.sysSign + ".png",
														mode: "aspectFill",
														thumbnail: "false"
													}),
													apivm.h(
														"text",
														{
															class: "news_swiper_text",
															style:
																loadConfiguration() +
																"line-height:" +
																G.appFontSize * 1.3 +
																"px;" +
																(api.systemType != "android" ? "max-" : "") +
																"height:" +
																G.appFontSize * 1.3 * 2 +
																"px;"
														},
														this.carouselText()
													)
												)
										),
										apivm.h(
											"view",
											{class: "news_swiper_point_box"},
											(Array.isArray(this.data.carouselList)
												? this.data.carouselList
												: Object.values(this.data.carouselList)
											).map(function(item$1, index$1) {
												return apivm.h("view", {
													style:
														"background:" +
														(index$1 == this$1.data.carouselIndex
															? colorRgba(G.appTheme, "0.7")
															: "#eee") +
														";margin-left:" +
														(index$1 ? "6" : "0") +
														"px;",
													class: "news_swiper_point"
												});
											})
										)
									)
								]
						),
						apivm.h(
							"view",
							null,
							!this.data.emptyBox.type &&
								this.data.listData.map(function(item, index) {
									return [
										apivm.h("item5", {
											floatModule: this$1.data.floatModule,
											layoutId: this$1.data.layoutId,
											_this: this$1,
											list: this$1.data.listData,
											item: item,
											index: index
										})
									];
								})
						),
						apivm.h(
							"view",
							null,
							this.data.emptyBox.type == "load" && apivm.h("z-skeleton", null)
						),
						apivm.h("z-empty", {
							_this: this,
							dataMore: this.data.emptyBox,
							onRefresh: this.pageRefresh
						})
					)
				)
			);
		};

		return MoNews;
	})(Component);
	MoNews.css = {
		".news_swiper_text_box": {padding: "0 16px", width: "100%", height: "auto"},
		".news_swiper_text_warp": {
			width: "100%",
			height: "auto",
			boxShadow: "0px 2px 25px 1px rgba(116, 136, 163, 0.1)",
			background: "#FFF",
			borderTopLeftRadius: "4px",
			borderTopRightRadius: "4px",
			borderBottomRightRadius: "4px",
			borderBottomLeftRadius: "4px",
			padding: "8px 10px",
			flexDirection: "row",
			alignItems: "center"
		},
		".news_swiper_text_tag": {width: "auto", marginRight: "7px", flexShrink: "0"},
		".news_swiper_text": {
			fontWeight: "600",
			color: "#333",
			WebkitLineClamp: "2",
			WebkitBoxOrient: "vertical",
			display: "-webkit-box",
			overflow: "hidden",
			wordWrap: "break-word",
			textOverflow: "ellipsis",
			whiteSpace: "normal !important"
		},
		".news_swiper_point_box": {
			padding: "6px 6px 5px 6px",
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		},
		".news_swiper_point": {width: "16px", height: "3px", borderRadius: "2px"}
	};
	apivm.define("mo-news", MoNews);

	var YSuspendedBtns = /*@__PURE__*/ (function(Component) {
		function YSuspendedBtns(props) {
			Component.call(this, props);
		}

		if (Component) YSuspendedBtns.__proto__ = Component;
		YSuspendedBtns.prototype = Object.create(Component && Component.prototype);
		YSuspendedBtns.prototype.constructor = YSuspendedBtns;
		YSuspendedBtns.prototype.itemclick = function(_item) {
			this.fire("click", _item);
		};
		YSuspendedBtns.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					class: "suspendedBtn_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";bottom:" +
						(safeArea().bottom + 98) +
						"px;"
				},
				apivm.h(
					"view",
					{style: "flex-direction:column-reverse;"},
					this.props.dataMore.data.map(function(item, index) {
						return [
							(isParameters(item.show) ? item.show : true) &&
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.itemclick(item);
										},
										class: "suspendedBtn_item",
										style:
											"margin-bottom:" +
											(index ? 10 : 0) +
											"px;" +
											loadConfigurationSize(item.bgSize || 28) +
											"background: " +
											(item.bg == "appTheme" ? G.appTheme : item.bg || G.appTheme) +
											";" +
											item.iStyle
									},
									item.type == "img"
										? apivm.h("image", {
												src: item.src,
												class: "xy_100",
												mode: "aspectFill",
												alt: ""
										  })
										: item.type == "text"
										? [
												item.src &&
													apivm.h("a-iconfont", {
														style: "" + (item.style || ""),
														name: item.src,
														color:
															item.color == "appTheme" ? G.appTheme : item.color || "#fff",
														size:
															G.appFontSize + (isParameters(item.size) ? Number(item.size) : 4)
													}),
												apivm.h(
													"text",
													{
														style:
															loadConfiguration(-2) +
															"color:" +
															(item.color == "appTheme" ? G.appTheme : item.color || "#fff") +
															";"
													},
													item.value
												)
										  ]
										: apivm.h("a-iconfont", {
												style: "" + (item.style || ""),
												name: item.src,
												color: item.color == "appTheme" ? G.appTheme : item.color || "#fff",
												size:
													G.appFontSize + (isParameters(item.size) ? Number(item.size) : 4)
										  })
								)
						];
					})
				)
			);
		};

		return YSuspendedBtns;
	})(Component);
	YSuspendedBtns.css = {
		".suspendedBtn_box": {position: "absolute", zIndex: "999", right: "16px"},
		".suspendedBtn_item": {
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center",
			boxShadow: "0px 4px 12px 1px rgba(24,64,118,0.15)",
			borderTopLeftRadius: "54px",
			borderTopRightRadius: "54px",
			borderBottomRightRadius: "54px",
			borderBottomLeftRadius: "54px"
		}
	};
	apivm.define("y-suspended-btns", YSuspendedBtns);

	var ZActionsheet = /*@__PURE__*/ (function(Component) {
		function ZActionsheet(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				dotClose: true
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.data.dotClose = true;
							setTimeout(function() {
								this$1.data.dotClose = false;
							}, 300);
						} else {
							G.actionSheetPop.pageClose();
						}
					}
				}
			};
		}

		if (Component) ZActionsheet.__proto__ = Component;
		ZActionsheet.prototype = Object.create(Component && Component.prototype);
		ZActionsheet.prototype.constructor = ZActionsheet;
		ZActionsheet.prototype.closePage = function() {
			if (this.data.dotClose) {
				return;
			}
			this.props.dataMore.show = false;
		};
		ZActionsheet.prototype.itemClick = function(_item, _index) {
			if (this.data.dotClose || _item.disabled) {
				return;
			}
			_item.buttonIndex = _index + 1;
			G.actionSheetPop.callback(_item);
			this.closePage();
		};
		ZActionsheet.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				this.data.show && [
					apivm.h("view", {
						onClick: function() {
							return this$1.closePage();
						},
						class: "actionSheet_cancel"
					}),
					apivm.h(
						"view",
						{
							class: this.props.dataMore.title ? "actionSheet_warp" : "",
							style: "flex-shrink: 0;"
						},
						this.props.dataMore.title &&
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(-1) +
										";color:#aaa;text-align: center;padding: 15px;"
								},
								this.props.dataMore.title
							)
					),
					apivm.h(
						"scroll-view",
						{
							class: !this.props.dataMore.title ? "actionSheet_warp" : "",
							style: "height: auto;background: #FFF;flex-shrink: 1;",
							"scroll-y": true
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						(this.props.dataMore.data || []).map(function(item, index) {
							return (
								(isParameters(item.show) ? item.show : true) && [
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.itemClick(item, index);
											},
											class: "actionSheet_item",
											style:
												"justify-content:" +
												(item.justify || "center") +
												";opacity: " +
												(item.disabled ? "0.3" : "1") +
												";"
										},
										apivm.h(
											"view",
											null,
											item.icon &&
												apivm.h("a-iconfont", {
													style: "margin-right:10px;",
													name: item.icon,
													color: (isParameters(this$1.props.dataMore.active) &&
													isObject(this$1.props.dataMore.active)
													? item.id === this$1.props.dataMore.active.id
													: item.name === this$1.props.dataMore.active)
														? G.appTheme
														: item.color || "#333",
													size:
														G.appFontSize + (isParameters(item.size) ? Number(item.size) : 4)
												})
										),
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(-1) +
													";color:" +
													((isParameters(this$1.props.dataMore.active) &&
													isObject(this$1.props.dataMore.active)
													? item.id === this$1.props.dataMore.active.id
													: item.name === this$1.props.dataMore.active)
														? G.appTheme
														: item.color || "#333")
											},
											item.name
										)
									),
									index != this$1.props.dataMore.data.length - 1 &&
										!this$1.props.dataMore.cancel &&
										apivm.h(
											"view",
											{style: "width:100%;height:1px;padding:0 15px;flex-shrink: 0;"},
											apivm.h("view", {style: "height:1px;background: #F6F6F6;"})
										)
								]
							);
						})
					),
					apivm.h(
						"view",
						{style: "flex-shrink: 0;"},
						this.props.dataMore.cancel &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.closePage();
									},
									class: "actionSheet_item",
									style:
										"justify-content:center;border-top:10px solid #f6f6f6;background: #FFF; "
								},
								apivm.h(
									"text",
									{style: loadConfiguration(-2) + ";color:#333"},
									this.props.dataMore.cancel || "取消"
								)
							)
					),
					apivm.h("view", {
						style:
							"background:#fff;flex-shrink: 0;padding-bottom:" +
							safeArea().bottom +
							"px;"
					})
				]
			);
		};

		return ZActionsheet;
	})(Component);
	ZActionsheet.css = {
		".actionSheet_cancel": {flex: "1", minHeight: "20%", flexShrink: "0"},
		".actionSheet_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		},
		".actionSheet_item": {
			width: "100%",
			minHeight: "60px",
			alignItems: "center",
			flexDirection: "row",
			padding: "5px 16px"
		}
	};
	apivm.define("z-actionSheet", ZActionsheet);

	var YFiltersLine = /*@__PURE__*/ (function(Component) {
		function YFiltersLine(props) {
			Component.call(this, props);
			this.data = {
				scrollView: {
					scroll_view: "",
					direction: "horizontal"
				},

				list: []
			};
			this.compute = {
				monitor: function() {
					if (
						JSON.stringify(this.data.list) !=
						JSON.stringify(
							this.props.dataMore.data.filter(function(item) {
								return item.sType == "line" && item.show;
							})
						)
					) {
						this.data.list = this.props.dataMore.data.filter(function(item) {
							return item.sType == "line" && item.show;
						});
					}
				}
			};
		}

		if (Component) YFiltersLine.__proto__ = Component;
		YFiltersLine.prototype = Object.create(Component && Component.prototype);
		YFiltersLine.prototype.constructor = YFiltersLine;
		YFiltersLine.prototype.tabClick = function(_item) {
			var this$1 = this;

			if (_item.readonly) {
				this.fire("readonly", _item);
				return;
			}
			this.data.scrollView.scroll_view = "filters_line_" + _item.key;
			var hasClean = true;
			if (isNumber(_item.defaultValue) || _item.defaultValue) {
				hasClean = false;
			}
			actionSheet(
				{
					title: "提示",
					buttons: (hasClean ? [{id: "", name: "清空"}] : []).concat(_item.data),
					active: _item.value ? {id: _item.value} : null
				},
				function(ret) {
					var _nItem = ret;
					if (isArray(_item.value)) {
						if (getItemForKey(_nItem.id, _item.value)) {
							delItemForKey(_nItem.id, _item.value);
						} else {
							_item.value.push(_nItem.id);
						}
					} else {
						if (hasClean) {
							_item.value = _nItem.id == _item.value ? _item.defaultValue : _nItem.id;
						} else {
							_item.value = _nItem.id;
						}
					}
					this$1.fire("sure");
				}
			);
		};
		YFiltersLine.prototype.nTouchmove = function() {
			touchmove();
		};
		YFiltersLine.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					style: "flex-direction:row;" + (this.props.style || ""),
					class: "" + (this.props.class || "")
				},
				apivm.h(
					"y-scroll-view",
					{
						style: "display: block;white-space: nowrap;",
						_this: this,
						dataMore: this.data.scrollView,
						"scroll-x": true,
						"scroll-y": false
					},
					this.data.list.map(function(item, index) {
						return (
							(isParameters(item.show) ? item.show : true) && [
								apivm.h(
									"view",
									{
										id: "filters_line_" + item.key,
										class: "filters_line_item",
										onClick: function() {
											return this$1.tabClick(item);
										},
										style:
											"width:" +
											(this$1.props.average
												? 100 / this$1.data.list.length + "%"
												: "auto") +
											";",
										onTouchStart: this$1.nTouchmove,
										onTouchMove: this$1.nTouchmove,
										onTouchEnd: this$1.nTouchmove
									},
									apivm.h(
										"view",
										{class: "filters_line_ibox"},
										apivm.h(
											"view",
											{class: "flex_row"},
											apivm.h(
												"text",
												{
													style:
														loadConfiguration(-2) +
														";color:" +
														(item.value ? G.appTheme : "#333") +
														";margin-right:6px;"
												},
												item.title
											),
											apivm.h("a-iconfont", {
												name: "xiangxia",
												color: item.value ? G.appTheme : "#333",
												size: G.appFontSize - 4
											})
										)
									)
								)
							]
						);
					})
				)
			);
		};

		return YFiltersLine;
	})(Component);
	YFiltersLine.css = {
		".filters_line_item": {
			flexDirection: "row",
			justifyContent: "center",
			display: "inline-block",
			padding: "8px 5px",
			textAlign: "center"
		},
		".filters_line_ibox": {
			flexDirection: "row",
			justifyContent: "center",
			display: "inline-block",
			padding: "2px 8px"
		}
	};
	apivm.define("y-filters-line", YFiltersLine);

	var ZRadio = /*@__PURE__*/ (function(Component) {
		function ZRadio(props) {
			Component.call(this, props);
		}

		if (Component) ZRadio.__proto__ = Component;
		ZRadio.prototype = Object.create(Component && Component.prototype);
		ZRadio.prototype.constructor = ZRadio;
		ZRadio.prototype.render = function() {
			return apivm.h("a-iconfont", {
				size: G.appFontSize + (this.props.size || 0),
				name: this.props.checked
					? this.props.type == 2
						? "danxuan_xuanzhong"
						: this.props.type == 3
						? "fangxingxuanzhongfill"
						: "yuanxingxuanzhongfill"
					: this.props.type == 3
					? "fangxingweixuanzhong"
					: "danxuan_weixuanzhong",
				color: this.props.checked ? this.props.color || G.appTheme : "#999"
			});
		};

		return ZRadio;
	})(Component);
	apivm.define("z-radio", ZRadio);

	var YFilters = /*@__PURE__*/ (function(Component) {
		function YFilters(props) {
			Component.call(this, props);
			this.data = {
				show: false
			};
			this.compute = {
				monitor: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
					}
				}
			};
		}

		if (Component) YFilters.__proto__ = Component;
		YFilters.prototype = Object.create(Component && Component.prototype);
		YFilters.prototype.constructor = YFilters;
		YFilters.prototype.pageClick = function() {
			this.props.dataMore.show = false;
		};
		YFilters.prototype.itemclick = function(e, _item) {
			if (_item.type == "select") {
				_item.open = !_item.open;
			} else {
				this.fire("callback", _item);
			}
			stopBubble$1(e);
		};
		YFilters.prototype.reset = function(e) {
			this.props.dataMore.data.forEach(function(_eItem, _eIndex, _eArr) {
				if (_eItem.sType) {
					return;
				}
				_eItem.value = isArray(_eItem.defaultValue)
					? _eItem.defaultValue.concat([])
					: _eItem.defaultValue;
			});
			stopBubble$1(e);
		};
		YFilters.prototype.sure = function(e) {
			this.fire("sure", {});
			this.pageClick();
			stopBubble$1(e);
		};
		YFilters.prototype.nItemclick = function(e, _nItem, _item) {
			if (_nItem.disabled) {
				return;
			}
			if (this.props.dataMore.onlyOne) {
				//只能单选
				if (isArray(_item.value)) {
					if (_item.value[0]) {
						if (getItemForKey(_nItem.id, _item.value)) {
							delItemForKey(_nItem.id, _item.value);
						}
					} else {
						_item.value.push(_nItem.id);
					}
				}
			} else {
				if (isArray(_item.value)) {
					if (getItemForKey(_nItem.id, _item.value)) {
						delItemForKey(_nItem.id, _item.value);
					} else {
						_item.value.push(_nItem.id);
					}
				} else {
					_item.value = _nItem.id == _item.value ? _item.defaultValue : _nItem.id;
				}
			}
			this.fire("change", _item);
			stopBubble$1(e);
		};
		YFilters.prototype.isSelectValue = function(_nItem, _item) {
			if (isArray(_item.value)) {
				//多选为数组
				return getItemForKey(_nItem.id, _item.value);
			} else {
				//单选时
				return _nItem.id === _item.value;
			}
		};
		YFilters.prototype.getShowText = function(_item) {
			var showText = "";
			if (isArray(_item.value)) {
				_item.value.forEach(function(_eItem, _eIndex, _eArr) {
					var nowItem = getItemForKey(_eItem, _item.data, "id");
					showText += (showText ? "、" : "") + (nowItem ? nowItem.name : "");
				});
			} else {
				var nowItem = getItemForKey(_item.value, _item.data, "id");
				showText = nowItem ? nowItem.name : "";
			}
			return showText;
		};
		YFilters.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);",
					onClick: this.pageClick
				},
				apivm.h(
					"scroll-view",
					{class: "filters_warp", bounces: false, "scroll-y": true},
					this.props.dataMore.data.map(function(item, index) {
						return (
							(isParameters(item.show) ? item.show : true) &&
							!item.sType && [
								apivm.h(
									"view",
									{
										class: "filters_item",
										onClick: function(e) {
											return this$1.itemclick(e, item);
										}
									},
									apivm.h(
										"view",
										{class: "filters_item_warp flex_row"},
										apivm.h(
											"text",
											{
												style: loadConfiguration(1) + "color: #333;max-width:156px;",
												class: "text_one"
											},
											item.title
										),
										apivm.h("view", {style: "flex:1;"}),
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color: #999;margin-right:6px;max-width:156px;",
												class: "text_one"
											},
											this$1.getShowText(item)
										),
										apivm.h("a-iconfont", {
											name: "xiangxia1",
											style: "transform: rotate(" + (item.open ? "0" : "-90") + "deg);",
											color: "#303030",
											size: G.appFontSize - 1
										})
									)
								),
								apivm.h(
									"view",
									null,
									(isParameters(item.open) ? item.open : false) &&
										apivm.h(
											"view",
											{class: "filters_items_box"},
											(Array.isArray(item.data)
												? item.data
												: Object.values(item.data)
											).map(function(nItem, nIndex) {
												return apivm.h(
													"view",
													{
														class: "filters_items flex_row",
														onClick: function(e) {
															return this$1.nItemclick(e, nItem, item);
														}
													},
													apivm.h(
														"text",
														{
															style:
																loadConfiguration(1) +
																"color: " +
																(this$1.isSelectValue(nItem, item) ? G.appTheme : "#333") +
																";flex:1;"
														},
														nItem.name
													),
													apivm.h("z-radio", {
														checked: this$1.isSelectValue(nItem, item),
														type: isArray(item.value) ? 3 : 1,
														size: 4,
														color: item.disabled
															? "#C5C7C9"
															: this$1.isSelectValue(nItem, item)
															? G.appTheme
															: "#999"
													})
												);
											})
										)
								),
								apivm.h(
									"view",
									{style: "padding: 0px 16px;"},
									apivm.h("view", {style: "width:100%;height:1px;background: #f8f8f8;"})
								)
							]
						);
					})
				),
				apivm.h(
					"view",
					{class: "flex_row flex_shrink"},
					apivm.h(
						"view",
						{
							class: "filters_btn_item xy_center flex_shrink",
							style: "background: #FFF;border-bottom-left-radius: 10px;",
							onClick: this.reset
						},
						apivm.h("text", {style: loadConfiguration() + "color: #333;"}, "重置")
					),
					apivm.h(
						"view",
						{
							class: "filters_btn_item xy_center flex_shrink",
							style:
								"background: " + G.appTheme + ";border-bottom-right-radius: 10px;",
							onClick: this.sure
						},
						apivm.h("text", {style: loadConfiguration() + "color: #FFF;"}, "确定")
					)
				),
				apivm.h("view", {style: "height:30%;flex-shrink:0;"})
			);
		};

		return YFilters;
	})(Component);
	YFilters.css = {
		".filters_warp": {height: "auto", background: "#FFF"},
		".filters_item": {minHeight: "60px", width: "100%", padding: "0px 16px"},
		".filters_item_warp": {minHeight: "59px"},
		".filters_btn_item": {width: "50%", height: "42px"},
		".filters_items_box": {padding: "0 36px 9px 36px"},
		".filters_items": {minHeight: "42px"},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden",
			display: "inline"
		}
	};
	apivm.define("y-filters", YFilters);

	var ItemApptext = /*@__PURE__*/ (function(Component) {
		function ItemApptext(props) {
			Component.call(this, props);
		}

		if (Component) ItemApptext.__proto__ = Component;
		ItemApptext.prototype = Object.create(Component && Component.prototype);
		ItemApptext.prototype.constructor = ItemApptext;
		ItemApptext.prototype.openDetails = function(e) {
			stopBubble$1(e);
			var item = this.props.item;
			openWin_apptext(item);
		};
		ItemApptext.prototype.render = function() {
			return apivm.h(
				"view",
				{onClick: this.openDetails, class: "alist_item"},
				apivm.h(
					"view",
					{class: "alist_warp"},
					apivm.h(
						"text",
						{style: "" + loadConfiguration(), class: "alist_text"},
						this.props.item.title
					),
					apivm.h("a-iconfont", {
						name: "xiangxia1",
						style: "transform: rotate(-90deg);margin:1px 10px 0;",
						color: "#666",
						size: "" + G.appFontSize
					})
				)
			);
		};

		return ItemApptext;
	})(Component);
	ItemApptext.css = {
		".alist_item": {padding: "0 15px"},
		".alist_warp": {
			borderTop: "1px solid #EEEEEE",
			flexDirection: "row",
			alignItems: "center",
			height: "52px"
		},
		".alist_text": {color: "#333333", width: "1px", flex: "1"}
	};
	apivm.define("item-apptext", ItemApptext);

	var Item54 = /*@__PURE__*/ (function(Component) {
		function Item54(props) {
			Component.call(this, props);
		}

		if (Component) Item54.__proto__ = Component;
		Item54.prototype = Object.create(Component && Component.prototype);
		Item54.prototype.constructor = Item54;
		Item54.prototype.openDetail = function() {
			openWin_videoconference({id: this.props.item.id});
		};
		Item54.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: "itemlist_box", onClick: this.openDetail},
				apivm.h(
					"view",
					{
						class: "itemlist_warp",
						style:
							"background-color: rgba(255, 255, 255, " + (G.watermark ? 0.6 : 1) + ");"
					},
					apivm.h(
						"view",
						{
							style:
								"flex-direction:row;align-items: center;margin:-10px -10px 0;height:43px;"
						},
						apivm.h(
							"view",
							{
								style:
									"position: absolute;right:0;top:0;width:92px;height:70px;overflow: hidden;"
							},
							apivm.h(
								"view",
								{
									style:
										"position: absolute;width: 135%;top: 9px;left: 2px;background:" +
										(this.props.item.state == "进行中" ? G.appTheme : "#E7E7E7") +
										";padding:5px;align-items: center;justify-content: center;transform: rotate(39deg);"
								},
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(-2) +
											"color:" +
											(this.props.item.state == "进行中" ? "#fff" : "#999") +
											";"
									},
									this.props.item.state
								)
							)
						)
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "flex-wrap: wrap;margin-bottom:15px;"},
						this.props.item.title.split("").map(function(nItem, nIndex) {
							return apivm.h(
								"text",
								{
									style:
										loadConfiguration(1) +
										"margin:2px 0;color: " +
										(this$1.props.search &&
										this$1.props.search.result &&
										this$1.props.search.result.indexOf(nItem) > -1
											? G.appTheme
											: "#333") +
										";"
								},
								nItem
							);
						})
					),
					apivm.h(
						"view",
						null,
						apivm.h(
							"view",
							{
								style: "flex-direction:row;align-items: flex-start;margin-bottom:10px;"
							},
							apivm.h(
								"text",
								{style: loadConfiguration(-2) + "color:#999;margin-right:14px;"},
								"开始时间"
							),
							apivm.h(
								"text",
								{style: loadConfiguration(-2) + "color:#333;flex:1px;"},
								this.props.item.startTime
							)
						),
						apivm.h(
							"view",
							{style: "flex-direction:row;align-items: flex-start;"},
							apivm.h(
								"text",
								{style: loadConfiguration(-2) + "color:#999;margin-right:14px;"},
								"结束时间"
							),
							apivm.h(
								"text",
								{style: loadConfiguration(-2) + "color:#333;flex:1px;"},
								this.props.item.endTime
							)
						)
					)
				)
			);
		};

		return Item54;
	})(Component);
	Item54.css = {
		".itemlist_box": {padding: "8px 16px"},
		".itemlist_warp": {
			boxShadow: "0px 2px 10px 1px rgba(24,64,118,0.08)",
			borderTopLeftRadius: "4px",
			borderTopRightRadius: "4px",
			borderBottomLeftRadius: "4px",
			borderBottomRightRadius: "4px",
			padding: "15px 10px"
		}
	};
	apivm.define("item54", Item54);

	var PreviewerImg = /*@__PURE__*/ (function(Component) {
		function PreviewerImg(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				frameName: "previewerImg"
			};
			this.compute = {
				monitor: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.props.dataMore.watermark = G.watermark;
							this.baseInit();
						} else {
							if (platform() == "app" && this.UIPhotoViewer) {
								this.UIPhotoViewer.close();
							}
						}
					}
					if (this.data.show) {
						if (this.nowParam != JSON.stringify(this.props.dataMore)) {
							this.nowParam = JSON.stringify(this.props.dataMore);
							this.sendMessage();
						}
					}
				}
			};
		}

		if (Component) PreviewerImg.__proto__ = Component;
		PreviewerImg.prototype = Object.create(Component && Component.prototype);
		PreviewerImg.prototype.constructor = PreviewerImg;
		PreviewerImg.prototype.baseInit = function() {
			var this$1 = this;

			var data = this.props.dataMore;
			switch (platform()) {
				case "app":
					if (G.watermark) {
						//有水印使用web
						addEventListener(this.data.frameName + "_msg", function() {
							this$1.closePage();
						});
						api.setFrameClient({frameName: this.data.frameName}, function(ret, err) {
							if (ret.state == 2) {
								this$1.isLoading = true;
								setTimeout(function() {
									this$1.sendMessage();
								}, 400);
							}
						});
						return;
					}
					this.UIPhotoViewer = api.require("UIPhotoViewer");
					this.UIPhotoViewer.open(
						{
							images: data.imgs,
							activeIndex: data.index,
							gestureClose: true,
							bgColor: "#000"
						},
						function(ret, err) {
							switch (ret.eventType) {
								case "click":
									this$1.closePage();
									break;
								case "gestureColse":
									this$1.UIPhotoViewer = null;
									this$1.closePage();
									break;
								case "longPress":
									var nowImg = data.imgs[ret.index];
									api.actionSheet(
										{
											buttons: ["保存到相册"],
											cancelTitle: "取消"
										},
										function(ret) {
											switch (ret.buttonIndex) {
												case 1:
													api.saveMediaToAlbum(
														{
															path: nowImg
														},
														function(ret) {
															toast(ret && ret.status ? "已保存到手机相册" : "保存失败");
														}
													);
													break;
											}
										}
									);
									break;
							}
						}
					);
					break;
				case "mp":
					wx.previewImage({
						urls: data.imgs,
						current: data.imgs[data.index],
						complete: function() {
							this$1.closePage();
						}
					});

					break;
				case "web":
					window.addEventListener("message", function(event) {
						this$1.closePage();
					});
					document
						.getElementById(this.data.frameName)
						.addEventListener("load", function() {
							this$1.isLoading = true;
							this$1.sendMessage();
						});
					break;
			}
		};
		PreviewerImg.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		PreviewerImg.prototype.sendMessage = function() {
			if (this.isLoading && this.props.dataMore.show) {
				if (platform() == "web") {
					if (!document.getElementById(this.data.frameName)) {
						return;
					}
					var targetWindow = document.getElementById(this.data.frameName)
						.contentWindow;
					targetWindow.postMessage(this.nowParam, "*");
				} else if (platform() == "app") {
					sendEvent(this.data.frameName + "_open", this.props.dataMore);
				}
			}
		};
		PreviewerImg.prototype.render = function() {
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0);"
				},
				this.props.dataMore.show &&
					(platform() == "web" || G.watermark) &&
					apivm.h("frame", {
						id: this.data.frameName,
						class: "xy_100",
						name: this.data.frameName,
						url: shareAddress() + "html/previewerImg.html",
						pageParam: {name: this.data.frameName},
						useWKWebView: true,
						scaleEnabled: true,
						allowEdit: true
					})
			);
		};

		return PreviewerImg;
	})(Component);
	apivm.define("previewer-img", PreviewerImg);

	var ZRichText = /*@__PURE__*/ (function(Component) {
		function ZRichText(props) {
			Component.call(this, props);
			this.data = {
				showText: null,
				listData: [],
				hasExpand: false, //是否有展开
				isExpand: false, //是否展开了
				appDetailsStyle: ""
			};
			this.compute = {
				monitor: function() {
					if (this.props.nodes != this.data.showText) {
						this.data.showText = this.props.nodes;
						this.dealWithCon();
					}
				}
			};
		}

		if (Component) ZRichText.__proto__ = Component;
		ZRichText.prototype = Object.create(Component && Component.prototype);
		ZRichText.prototype.constructor = ZRichText;
		ZRichText.prototype.expandShow = function(e) {
			stopBubble$1(e);
			this.data.isExpand = !this.data.isExpand;
			this.dealWithCon();
		};
		ZRichText.prototype.dealWithCon = function() {
			var this$1 = this;

			var expText =
				(isParameters(this.data.showText) ? this.data.showText : "") + "";
			if (isObject(expText) || isArray(expText)) {
				expText = JSON.stringify(expText);
			}
			this.data.appDetailsStyle = getPrefs("appDetailsStyle");
			if (this.data.appDetailsStyle == "edit") {
				this.data.showText = expText;
				return;
			}

			var notTagText = removeTag(expText);
			this.data.hasExpand =
				isParameters(this.props.expand) && notTagText.length > this.props.expand;
			expText =
				this.data.hasExpand && !this.data.isExpand
					? notTagText.substring(0, this.props.expand) + "..."
					: expText;

			expText = expText.replace(
				/(<style(.*?)<\/style>|<link(.*?)<\/link>|<script(.*?)<\/script>|<!--[\w\W\r\n]*?-->|^\s*|\s*$)/gi,
				""
			);
			var reLabel = function(_bel) {
				var oldText = expText;
				expText = expText.replace(
					new RegExp("<" + _bel + "[^>]*>(.*?)</" + _bel + ">", "gi"),
					"$1"
				);
				if (expText != oldText && expText.indexOf(_bel) != -1) {
					reLabel(_bel);
				}
			};
			["span"].forEach(function(item) {
				reLabel(item);
			});
			expText = expText.replace(/<\s*\/?\s*br\s*\/?\s*>/gi, "</br>");
			expText = expText.replace(/\n/gi, "</br>");
			expText = decodeCharacter(expText);
			expText = expText.replace(/<ins(.*?)>(.*?)<\/ins>/gi, "$2");
			if (!expText.startsWith("<") || !expText.endsWith(">")) {
				expText = "<div>" + expText + "</div>";
			}
			// console.log("解析一："+expText);
			var newText = expText;
			//清空表格td中所有的标签
			var rdRegex = /<td(.*?)>(.*?)<\/td>/gi;
			var match;
			while ((match = rdRegex.exec(expText)) !== null) {
				var nText = removeTag(match[2]);
				newText = newText.replace(match[2], nText);
			}
			expText = newText;
			// console.log("解析二："+expText);
			var strRegex = /<\/?\w+[^>]*>/gi;
			var nowIndexs = [],
				viewIndexs = [];
			var startIndex = [];
			expText.replace(strRegex, function(item, index) {
				//循环对应的内容和角标
				var nlabel = item.match(/<\/*([^> ]+)[^>]*>/)[1].toLocaleLowerCase();
				var styleMatch = /style\s*=\s*['"]([^'"]*)['"]/i.exec(item);
				var nStyle = styleMatch ? styleMatch[1] : "";
				if (nlabel == "img") {
					nStyle = nStyle
						.replace("width: auto;", "width:100%;")
						.replace("width:auto;", "width:100%;");
				}
				var nowItem = {label: nlabel, index: index, text: item, style: nStyle};
				viewIndexs.push(nowItem);
				// console.log(index + "-" + nlabel + "-" + item);
				if (/^<(p|div|span).*/i.test(item)) {
					var nowItems = {
						index: nowIndexs.length,
						endIndex: 0,
						label: nlabel,
						start: nowItem,
						end: null,
						style: nStyle
					};
					startIndex.push(nowItems);
					nowIndexs.push(nowItems);
				} else if (/^<\/(p|div|span)>/i.test(item)) {
					if (startIndex.length) {
						nowIndexs[startIndex[startIndex.length - 1].index].end = nowItem;
						nowIndexs[startIndex[startIndex.length - 1].index].endIndex =
							nowIndexs.length - 1;
						startIndex.pop();
					}
				} else if (/^<.*?>$/.test(item)) {
					nowIndexs.push({
						index: nowIndexs.length,
						endIndex: nowIndexs.length,
						label: nlabel,
						start: nowItem,
						end: nowItem,
						style: nStyle
					});
				}
			});
			var showTexts = [];
			var tableData = null,
				isTable = false,
				tableItem = null;
			viewIndexs.forEach(function(item, index) {
				var minIndex = item.index + item.text.length;
				var maxIndex =
					index != viewIndexs.length - 1
						? viewIndexs[index + 1].index
						: expText.length;
				var viewText = expText.substring(minIndex, maxIndex);
				if (item.label == "table") {
					if (!isTable) {
						isTable = true;
						tableData = {
							label: "table",
							widths: [],
							data: [],
							index: minIndex,
							style: item.style
						};
					} else {
						isTable = false;
						showTexts.push(tableData);
					}
					return;
				}
				if (isTable) {
					if (item.text == "</tr>") {
						tableData.data.push(tableItem);
					} else if (item.label == "tr") {
						tableItem = {label: "tr", data: [], index: minIndex, style: item.style};
					} else if (item.text != "</td>" && item.label == "td") {
						var widthMatch = item.text.match(/width="([^"]*)"/);
						var tdWidth = widthMatch ? widthMatch[1] + "px" : null;
						if (!tdWidth) {
							tdWidth = this$1.getStyle(item.style, "width") || "150px";
						}
						if (!tableData.data.length) {
							if (tdWidth.indexOf("%") != -1) {
								//是百分比宽度
								var nW = Number(tdWidth.replace(/%/g, ""));
								tdWidth = ((nW < 30 ? 30 : nW) * G.pageWidth) / 100 + "px";
							}
							tableData.widths.push(tdWidth);
						}
						var showStyle = "";
						showStyle +=
							"border:" +
							(this$1.getStyle(item.style, "border") || "1px solid #ccc") +
							";";
						showStyle +=
							"border-left:" + this$1.getStyle(item.style, "border-left") + ";";
						showStyle +=
							"border-right:" + this$1.getStyle(item.style, "border-right") + ";";
						showStyle +=
							"border-top:" + this$1.getStyle(item.style, "border-top") + ";";
						showStyle +=
							"border-bottom:" + this$1.getStyle(item.style, "border-bottom") + ";";
						showStyle +=
							"background:" + this$1.getStyle(item.style, "background") + ";";
						showStyle += "color:" + this$1.getStyle(item.style, "color") + ";";
						tableItem.data.push({
							label: "td",
							index: minIndex,
							text: viewText,
							style: showStyle
						});
					}
					return;
				}
				if (/^(?!p|div|span).*$/i.test(item.label)) {
					if (
						item.label == "br" &&
						!item.text &&
						showTexts.length &&
						showTexts[showTexts.length - 1].label == "br"
					) {
						return;
					}
					var addItem = {label: item.label, index: item.index, style: item.style};
					minIndex = minIndex + item.text.length;
					var srcMatch = /src\s*=\s*['"]([^'"]*)['"]/i.exec(item.text);
					if (srcMatch) {
						addItem.src = srcMatch[1];
					}
					var hrefMatch = /href\s*=\s*['"]([^'"]*)['"]/i.exec(item.text);
					if (hrefMatch) {
						addItem.href = hrefMatch[1];
					}
					if (addItem.label == "img" && !addItem.src);
					else {
						showTexts.push(addItem);
					}
				}
				if (viewText.replace(/^\s*|\s*$/, "")) {
					var showText = {
						label: "text",
						index: minIndex,
						text: viewText,
						style: item.style
					};
					if (item.label == "a") {
						showText.label = "text_a";
						showText.href = showTexts.length
							? showTexts[showTexts.length - 1].href
							: "";
					}
					showTexts.push(showText);
				}
			});
			// console.log(JSON.stringify(nowIndexs));
			// console.log(JSON.stringify(viewIndexs));
			// console.log(JSON.stringify(showTexts));
			this.data.listData = showTexts;
		};
		ZRichText.prototype.openImages = function(e, _item, _index) {
			stopBubble$1(e);
			var imgs = this.data.listData.filter(function(item, index) {
				return item.label == "img";
			});
			openWin_imgPreviewer({
				index: getItemForKey(_item.index, imgs, "index")._i,
				imgs: imgs.map(function(obj) {
					return obj.src;
				})
			});
		};
		ZRichText.prototype.openHrefs = function(e, _item, _index) {
			stopBubble$1(e);
			if (!_item.href) {
				return;
			}
			if (platform() == "web") {
				window.open(_item.href);
				return;
			}
			openWin_url({url: _item.href});
		};
		ZRichText.prototype.getStyle = function(_text, _item) {
			var match = new RegExp('[;"]s*' + _item + "s*:s*([^;]+);").exec(_text);
			if (match) {
				var matchText = match[1].replace(/pt/g, "px");
				return matchText;
			}
			return "";
		};
		ZRichText.prototype.nTouchmove = function() {
			touchmove();
		};
		ZRichText.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					style: "" + (this.props.style || ""),
					class: "" + (this.props.class || "")
				},
				apivm.h(
					"view",
					null,
					this.data.appDetailsStyle != "edit" &&
						this.data.listData.map(function(item, index) {
							return [
								apivm.h(
									"view",
									null,
									item.label == "text" &&
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(this$1.props.size || 0) +
													"line-height:" +
													(G.appFontSize + (this$1.props.size || 0)) * 1.8 +
													"px;" +
													(this$1.props.detail &&
													((platform() == "mp" && item.text.indexOf("　") != 0) ||
														(platform() == "web" &&
															item.text.indexOf(" ") != 0 &&
															item.text.indexOf("　") != 0))
														? "text-indent:2em;"
														: ""),
												class: "richText"
											},
											this$1.props.detail &&
												item.text.indexOf(" ") != 0 &&
												item.text.indexOf("　") != 0
												? api.systemType == "android"
													? "					"
													: api.systemType == "ios"
													? "	 "
													: ""
												: "",
											item.text
										)
								),
								apivm.h(
									"view",
									null,
									item.label == "text_a" &&
										apivm.h(
											"view",
											{
												onClick: function(e) {
													return this$1.openHrefs(e, item, index);
												}
											},
											apivm.h(
												"text",
												{
													style: loadConfiguration(this$1.props.size || 0) + "color: blue;",
													class: "richText"
												},
												item.text
											)
										)
								),
								apivm.h(
									"view",
									null,
									item.label == "br" &&
										apivm.h("view", {
											style:
												"height:" + (G.appFontSize - 6 + (this$1.props.size || 0)) + "px;"
										})
								),
								apivm.h(
									"view",
									null,
									item.label == "img" &&
										apivm.h(
											"view",
											{
												class: "richImgBox",
												onClick: function(e) {
													return this$1.openImages(e, item, index);
												}
											},
											apivm.h("image", {
												class: "richImg",
												style: item.style || "",
												mode: "widthFix",
												thumbnail: "false",
												src: item.src
											})
										)
								),
								apivm.h(
									"view",
									null,
									(item.label == "video" || item.label == "source") &&
										apivm.h(
											"view",
											{class: "richImgBox"},
											item.src && apivm.h("z-video", {src: item.src})
										)
								),
								apivm.h(
									"view",
									null,
									item.label == "table" &&
										apivm.h(
											"scroll-view",
											{"scroll-x": true, "scroll-y": false},
											apivm.h(
												"view",
												{
													onTouchStart: this$1.nTouchmove,
													onTouchMove: this$1.nTouchmove,
													onTouchEnd: this$1.nTouchmove
												},
												(item.data || []).map(function(nItem, nIndex) {
													return apivm.h(
														"view",
														{
															class: "richTable_item",
															style: "margin-top:" + (nIndex ? -1 : 0) + "px;"
														},
														(nItem.data || []).map(function(uItem, uIndex) {
															return apivm.h(
																"view",
																{
																	class: "richTable_item_td",
																	style:
																		uItem.style +
																		"width:" +
																		item.widths[uIndex] +
																		";margin-left:" +
																		(uIndex ? -1 : 0) +
																		"px;"
																},
																apivm.h(
																	"text",
																	{
																		style:
																			loadConfiguration(this$1.props.size || 0) +
																			"text-align: center;",
																		class: "richText"
																	},
																	uItem.text
																)
															);
														})
													);
												})
											)
										)
								)
							];
						})
				),
				apivm.h(
					"view",
					null,
					this.data.appDetailsStyle == "edit" &&
						apivm.h("rich-text", {nodes: this.data.showText})
				),
				apivm.h(
					"view",
					null,
					this.data.hasExpand &&
						apivm.h(
							"view",
							{
								style:
									"padding: 7px 0;flex-direction:row; align-items: center;justify-content: flex-start;",
								onClick: this.expandShow
							},
							apivm.h(
								"text",
								{style: loadConfiguration((this.props.size || 0) - 2) + "color:#999"},
								this.data.isExpand ? "收起" : "展开"
							),
							apivm.h("a-iconfont", {
								name: "xiangxiagengduo",
								style:
									"margin-left:5px;transform: rotate(" +
									(this.data.isExpand ? "180" : "0") +
									"deg);",
								color: "#999",
								size: G.appFontSize - 3
							})
						)
				)
			);
		};

		return ZRichText;
	})(Component);
	ZRichText.css = {
		".richImgBox": {
			alignItems: "center",
			justifyContent: "center",
			margin: "10px 0"
		},
		".richImg": {maxWidth: "100% !important"},
		".richTable_item": {flexFlow: "row nowrap"},
		".richTable_item_td": {
			alignItems: "center",
			justifyContent: "center",
			flexShrink: "0",
			padding: "5px"
		},
		".richText": {wordWrap: "break-word", wordBreak: "break-all"}
	};
	apivm.define("z-rich-text", ZRichText);

	var YAttachments = /*@__PURE__*/ (function(Component) {
		function YAttachments(props) {
			Component.call(this, props);
			this.data = {
				oldData: "",
				imgList: [],
				fileList: []
			};
			this.compute = {
				monitor: function() {
					var data = this.props.data;
					if (isArray(data) && this.data.oldData != JSON.stringify(data)) {
						this.data.oldData = JSON.stringify(data);
						var imgList = [],
							fileList = [];
						data.forEach(function(_eItem, _eIndex, _eArr) {
							var fileInfo = getFileInfo(_eItem.extName);
							var item = {
								id: _eItem.id,
								name: _eItem.originalFileName,
								newName: _eItem.newFileName,
								url:
									fileInfo.type == "image"
										? appUrl() + "image/" + _eItem.newFileName
										: appUrl() + "file/preview/" + _eItem.id,
								fileInfo: fileInfo,
								dotSystemDel: _eItem.dotSystemDel
							};

							if (fileInfo.type != "image") {
								fileList.push(item);
							} else {
								imgList.push(item);
							}
						});
						this.data.imgList = imgList;
						this.data.fileList = fileList;
					}
				},
				id: function() {},
				name: function() {},
				newName: function() {},
				url: function() {},
				fileInfo: function() {},
				dotSystemDel: function() {}
			};
		}

		if (Component) YAttachments.__proto__ = Component;
		YAttachments.prototype = Object.create(Component && Component.prototype);
		YAttachments.prototype.constructor = YAttachments;
		YAttachments.prototype.openFile = function(e, _item) {
			stopBubble$1(e);
			var param = {};
			param.id = _item.id || _item.url;
			param.suffix = _item.fileInfo.type;
			param.fileSource = this.props.fileSource;
			openWin_filePreviewer(param);
		};
		YAttachments.prototype.openImages = function(e, _item, _index) {
			stopBubble$1(e);
			openWin_imgPreviewer({
				index: _index,
				imgs: this.data.imgList.map(function(obj) {
					return obj.url;
				})
			});
		};
		YAttachments.prototype.delFile = function(e, _item, _index) {
			stopBubble$1(e);
			delItemForKey(_item, this.props.data, "id");
			this.fire("change", this.props.data);
			if (!_item.dotSystemDel) {
				ajax(
					{u: appUrl() + "file/clear"},
					"clear" + _item.id,
					function(ret, err) {},
					"删除附件",
					"post",
					{
						body: JSON.stringify({fileIds: [_item.id]})
					}
				);
			}
		};
		YAttachments.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{s: this.monitor},
				(this.data.imgList.length > 0 || this.data.fileList.length > 0) &&
					apivm.h(
						"view",
						{style: this.props.style || ""},
						apivm.h(
							"view",
							null,
							this.data.imgList.length > 0 &&
								apivm.h(
									"view",
									{
										class: "attach_img_box",
										style:
											"margin-bottom:" +
											(this.data.fileList.length > 0 ? "1" : "") +
											"0px;"
									},
									(Array.isArray(this.data.imgList)
										? this.data.imgList
										: Object.values(this.data.imgList)
									).map(function(item$1, index$1) {
										return apivm.h(
											"view",
											{
												class: "attach_img_item",
												onClick: function(e) {
													return this$1.openImages(e, item$1, index$1);
												}
											},
											apivm.h("image", {
												style: "width:100%;" + loadConfigurationSize(44, "h"),
												src: showImg(item$1),
												mode: "aspectFill"
											}),
											this$1.props.type == "2" &&
												apivm.h(
													"view",
													{
														class: "attach_img_clean",
														onClick: function(e) {
															return this$1.delFile(e, item$1, index$1);
														}
													},
													apivm.h("a-iconfont", {
														name: "qingkong",
														color: "rgba(0,0,0,0.65)",
														size: G.appFontSize + 2
													})
												)
										);
									})
								)
						),
						apivm.h(
							"view",
							null,
							this.data.fileList.length > 0 &&
								apivm.h(
									"view",
									null,
									(Array.isArray(this.data.fileList)
										? this.data.fileList
										: Object.values(this.data.fileList)
									).map(function(item$1, index$1) {
										return apivm.h(
											"view",
											{
												class: "attach_item",
												style:
													"margin-top:" + (index$1 ? 1 : 0) + "0px;background:transparent;",
												onClick: function(e) {
													return this$1.openFile(e, item$1, index$1);
												}
											},
											apivm.h(
												"view",
												{style: "margin-right:10px;"},
												apivm.h("a-iconfont", {
													name: item$1.fileInfo.name,
													color: item$1.fileInfo.color,
													size: G.appFontSize + 6
												})
											),
											apivm.h(
												"view",
												{class: "flex_w"},
												apivm.h(
													"text",
													{
														class: "text_one",
														style: loadConfiguration() + "color: #666;word-break: break-all;"
													},
													item$1.name
												)
											),
											this$1.props.type == "2" &&
												apivm.h(
													"view",
													{
														style: "padding:5px;margin-right:-5px;",
														onClick: function(e) {
															return this$1.delFile(e, item$1, index$1);
														}
													},
													apivm.h("a-iconfont", {
														name: "qingkong",
														color: "#333",
														size: G.appFontSize + 4
													})
												)
										);
									})
								)
						)
					)
			);
		};

		return YAttachments;
	})(Component);
	YAttachments.css = {
		".attach_item": {
			borderRadius: "4px",
			border: "1px solid #F4F5F7",
			padding: "2px 10px",
			minHeight: "36px",
			flexDirection: "row",
			alignItems: "center"
		},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden",
			display: "inline"
		},
		".attach_img_box": {flexDirection: "row", flexWrap: "wrap"},
		".attach_img_item": {
			padding: "10px 10px 0 0",
			width: "25%",
			height: "auto",
			boxSizing: "border-box"
		},
		".attach_img_clean": {position: "absolute", top: "1px", right: "3px"}
	};
	apivm.define("y-attachments", YAttachments);

	var Item53 = /*@__PURE__*/ (function(Component) {
		function Item53(props) {
			Component.call(this, props);
		}

		if (Component) Item53.__proto__ = Component;
		Item53.prototype = Object.create(Component && Component.prototype);
		Item53.prototype.constructor = Item53;
		Item53.prototype.openReShow = function() {
			this.props.item.receiveOpen = !this.props.item.receiveOpen;
		};
		Item53.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: "itemlist_box"},
				apivm.h(
					"view",
					{
						class: "itemlist_warp",
						style:
							"background-color: rgba(255, 255, 255, " + (G.watermark ? 0.6 : 1) + ");"
					},
					apivm.h(
						"view",
						{class: "flex_row", style: "margin-bottom:12px;"},
						apivm.h(
							"view",
							{class: "flex_w"},
							this.props.item.tag &&
								apivm.h(
									"z-tag",
									{type: this.props.item.tag.type, color: this.props.item.tag.color},
									this.props.item.tag.text
								)
						),
						apivm.h(
							"text",
							{style: loadConfiguration(-2) + "color: #666;"},
							this.props.item.time
						)
					),
					apivm.h("z-rich-text", {nodes: this.props.item.content}),
					apivm.h("y-attachments", {
						style: "padding: 10px 0px;",
						data: this.props.item.attachments
					}),
					apivm.h(
						"view",
						null,
						this.props.item.receiveDetail.length > 0 &&
							apivm.h(
								"view",
								null,
								apivm.h(
									"view",
									{class: "flex_row", style: "padding: 15px 0;"},
									apivm.h("view", {
										style:
											loadConfigurationSize(-1, "h") +
											"width:3px; border-radius: 10px;background:" +
											G.appTheme +
											";"
									}),
									apivm.h(
										"text",
										{
											style:
												loadConfiguration() +
												"flex:1;font-weight: 600;color: #333333;margin-left:5px;"
										},
										"回复(",
										this.props.item.receiveDetail.length,
										")"
									),
									this.props.item.receiveDetail.length > 1 &&
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.openReShow();
												},
												class: "flex_row"
											},
											apivm.h(
												"text",
												{style: "color: #999;" + loadConfiguration(-2)},
												this.props.item.receiveOpen ? "收起" : "展开"
											),
											apivm.h("a-iconfont", {
												name: "xiangxia1",
												style:
													"transform: rotate(" +
													(this.props.item.receiveOpen ? "18" : "") +
													"0deg);margin-left:1px;",
												color: "#999",
												size: G.appFontSize - 1
											})
										)
								),
								apivm.h(
									"view",
									null,
									this.props.item.receiveDetail.map(function(nItem, nIndex) {
										return (
											(this$1.props.item.receiveOpen || nIndex < 1) && [
												apivm.h(
													"text",
													{
														style:
															loadConfiguration(-2) +
															"margin-top:" +
															(nIndex ? 15 : 0) +
															"px;color: #666;"
													},
													nItem.time
												),
												apivm.h("z-rich-text", {nodes: nItem.content})
											]
										);
									})
								)
							)
					)
				)
			);
		};

		return Item53;
	})(Component);
	Item53.css = {
		".itemlist_box": {padding: "8px 16px"},
		".itemlist_warp": {
			boxShadow: "0px 2px 10px 1px rgba(24,64,118,0.08)",
			borderTopLeftRadius: "4px",
			borderTopRightRadius: "4px",
			borderBottomLeftRadius: "4px",
			borderBottomRightRadius: "4px",
			padding: "15px 10px"
		}
	};
	apivm.define("item53", Item53);

	var Item507Re = /*@__PURE__*/ (function(Component) {
		function Item507Re(props) {
			Component.call(this, props);
		}

		if (Component) Item507Re.__proto__ = Component;
		Item507Re.prototype = Object.create(Component && Component.prototype);
		Item507Re.prototype.constructor = Item507Re;
		Item507Re.prototype.openDetail = function(e) {
			stopBubble$1(e);
			var item = this.props.item;
			openWin_workstation_dutieshelp_review({id: item.id});
		};
		Item507Re.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: "itemlist_box"},
				apivm.h(
					"view",
					{
						class: "itemlist_warp",
						style:
							"background-color: rgba(255, 255, 255, " +
							(G.watermark ? 0.6 : 1) +
							");",
						onClick: this.openDetail
					},
					apivm.h(
						"view",
						{class: "flex_row"},
						apivm.h(
							"view",
							{class: "flex_w"},
							this.props.item.tag &&
								apivm.h(
									"z-tag",
									{
										style: "margin-bottom:12px;",
										type: this.props.item.tag.type,
										color: this.props.item.tag.color
									},
									this.props.item.tag.text
								)
						)
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "flex-wrap: wrap;"},
						this.props.item.title.split("").map(function(nItem, nIndex) {
							return (
								nIndex < Math.floor((G.pageWidth - 60) / G.appFontSize) * 2 &&
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(1) +
											"margin:2px 0;font-weight: 500;color: " +
											(this$1.props.search &&
											this$1.props.search.result &&
											this$1.props.search.result.indexOf(nItem) > -1
												? G.appTheme
												: "#333") +
											";"
									},
									nIndex == Math.floor((G.pageWidth - 60) / G.appFontSize) * 2 - 1
										? "..."
										: nItem
								)
							);
						})
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "margin-top:10px;"},
						apivm.h(
							"view",
							{class: "flex_w"},
							!this.props.item.isDuty &&
								apivm.h(
									"z-tag",
									{type: "2", color: "#666", size: "-4", roundSize: "30"},
									"动态"
								)
						),
						apivm.h(
							"text",
							{
								style:
									loadConfiguration(-2) + "color:" + this.props.item.passStatus.color
							},
							this.props.item.passStatus.text
						)
					)
				)
			);
		};

		return Item507Re;
	})(Component);
	Item507Re.css = {
		".itemlist_box": {padding: "8px 16px"},
		".itemlist_warp": {
			boxShadow: "0px 2px 10px 1px rgba(24,64,118,0.08)",
			borderTopLeftRadius: "4px",
			borderTopRightRadius: "4px",
			borderBottomLeftRadius: "4px",
			borderBottomRightRadius: "4px",
			padding: "15px 10px"
		}
	};
	apivm.define("item50-7-re", Item507Re);

	var ZAvatar = /*@__PURE__*/ (function(Component) {
		function ZAvatar(props) {
			Component.call(this, props);
			this.data = {
				esrc: appUrl() + "img/default_user_head.jpg",
				asrc: null,
				bsrc: ""
			};
			this.compute = {
				monitor: function() {
					var url = isObject(this.props.src) ? this.props.src.url : this.props.src;
					if (this.data.asrc != url || !this.data.asrc) {
						this.data.asrc = url || this.data.esrc;
						this.data.bsrc = "";
					}
				}
			};
		}

		if (Component) ZAvatar.__proto__ = Component;
		ZAvatar.prototype = Object.create(Component && Component.prototype);
		ZAvatar.prototype.constructor = ZAvatar;
		ZAvatar.prototype.error = function() {
			this.data.bsrc = this.data.esrc;
		};
		ZAvatar.prototype.render = function() {
			return apivm.h(
				"view",
				{s: this.monitor, class: "xy_100"},
				apivm.h("z-image", {
					round: true,
					mode: "scaleToFill",
					thumbnail: "false",
					src: showImg(this.data.bsrc || this.data.asrc, "150x150-compress-"),
					onError: this.error
				})
			);
		};

		return ZAvatar;
	})(Component);
	apivm.define("z-avatar", ZAvatar);

	var Item507 = /*@__PURE__*/ (function(Component) {
		function Item507(props) {
			Component.call(this, props);
		}

		if (Component) Item507.__proto__ = Component;
		Item507.prototype = Object.create(Component && Component.prototype);
		Item507.prototype.constructor = Item507;
		Item507.prototype.openDetail = function(e) {
			stopBubble$1(e);
			var item = this.props.item;
			openWin_workstation_dutieshelp({id: item.id});
		};
		Item507.prototype.cckLike = function(e) {
			stopBubble$1(e);
			var item = this.props.item;
			if (item.likeIs) {
				if (item.likeNum > 0) {
					item.likeNum--;
				}
			} else {
				item.likeNum++;
			}
			item.likeIs = !item.likeIs;
			optionPraises({
				code: module50_7.businessCode,
				id: item.id,
				likeIs: item.likeIs
			});
		};
		Item507.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				null,
				apivm.h(
					"view",
					{class: "itemlist_box"},
					apivm.h(
						"view",
						{
							class: "flex_row",
							style: "align-items: flex-start;",
							onClick: this.openDetail
						},
						apivm.h(
							"view",
							{style: loadConfigurationSize(30) + "margin-right:11px;"},
							apivm.h("z-avatar", {src: this.props.item})
						),
						apivm.h(
							"view",
							{class: "flex_w"},
							apivm.h(
								"view",
								{class: "flex_row"},
								apivm.h(
									"text",
									{style: loadConfiguration() + "color: #333;"},
									this.props.item.name
								),
								apivm.h(
									"text",
									{
										style: loadConfiguration(-2) + "flex:1;margin-left:15px;color: #999;"
									},
									this.props.item.areas
								),
								this.props.item.tag &&
									apivm.h(
										"z-tag",
										{type: this.props.item.tag.type, color: this.props.item.tag.color},
										this.props.item.tag.text
									)
							),
							apivm.h(
								"text",
								{style: loadConfiguration(-4) + "flex:1;margin-top:7px;color: #999;"},
								this.props.item.time
							),
							apivm.h(
								"view",
								{
									style:
										"flex:1;flex-wrap: wrap;flex-direction: row;align-items: flex-start;margin-top:7px;"
								},
								this.props.item.title.split("").map(function(nItem, nIndex) {
									return apivm.h(
										"text",
										{
											style:
												loadConfiguration(1) +
												"margin:2px 0;font-weight: 600;color: " +
												(this$1.props.search &&
												this$1.props.search.result &&
												this$1.props.search.result.indexOf(nItem) > -1
													? G.appTheme
													: "#333") +
												";"
										},
										nItem
									);
								})
							),
							apivm.h(
								"text",
								{style: loadConfiguration(1) + "color: #333;margin-top:7px;"},
								this.props.item.content
							),
							apivm.h(
								"view",
								null,
								this.props.item.attachments.length > 0 &&
									apivm.h("y-attachments", {
										style: "margin-top:7px;",
										data: this.props.item.attachments
									})
							),
							apivm.h(
								"view",
								null,
								this.props.item.label &&
									apivm.h(
										"view",
										{class: "flex_row", style: "margin-top:7px;"},
										apivm.h("a-iconfont", {
											style: "transform: rotateY(180deg)",
											name: "biaoqian",
											color: G.appTheme,
											size: G.appFontSize - 2
										}),
										apivm.h(
											"text",
											{style: loadConfiguration(-2) + "color: #666;margin-left:4px;"},
											this.props.item.label
										)
									)
							),
							apivm.h(
								"view",
								{class: "flex_row"},
								apivm.h(
									"view",
									{class: "flex_row flex_w", style: "padding:10px;"},
									apivm.h("a-iconfont", {
										name: "pinglun",
										color: "#666666",
										size: G.appFontSize + 4
									}),
									apivm.h(
										"text",
										{style: loadConfiguration(-2) + "color:#666666;margin-left:5px;"},
										this.props.item.commentNum
									)
								),
								apivm.h(
									"view",
									{
										class: "flex_row flex_w",
										style: "padding:10px;",
										onClick: this.cckLike
									},
									apivm.h("a-iconfont", {
										name: this.props.item.likeIs ? "like-fill" : "like",
										color: this.props.item.likeIs ? "#F6931C" : "#666666",
										size: G.appFontSize + 4
									}),
									apivm.h(
										"text",
										{
											style:
												loadConfiguration(-2) +
												"color:" +
												(this.props.item.likeIs ? "#F6931C" : "#666666") +
												";margin-left:5px;"
										},
										this.props.item.likeNum
									)
								)
							)
						)
					)
				),
				apivm.h(
					"view",
					{style: "padding:0 16px;"},
					apivm.h("view", {style: "width:100%;height:1px;background:#f8f8f8;"})
				)
			);
		};

		return Item507;
	})(Component);
	Item507.css = {".itemlist_box": {padding: "8px 16px"}};
	apivm.define("item50-7", Item507);

	var Item506 = /*@__PURE__*/ (function(Component) {
		function Item506(props) {
			Component.call(this, props);
		}

		if (Component) Item506.__proto__ = Component;
		Item506.prototype = Object.create(Component && Component.prototype);
		Item506.prototype.constructor = Item506;
		Item506.prototype.openDetail = function(e) {
			stopBubble$1(e);
			var item = this.props.item;
			openWin_workstation_video({id: item.id});
		};
		Item506.prototype.btnclick = function(_type) {
			var item = this.props.item;
			openWin_workstation_video({id: item.id, open: 1});
		};
		Item506.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: "itemlist_box"},
				apivm.h(
					"view",
					{
						class: "itemlist_warp",
						style:
							"background-color: rgba(255, 255, 255, " +
							(G.watermark ? 0.6 : 1) +
							");",
						onClick: this.openDetail
					},
					apivm.h(
						"view",
						{class: "flex_row"},
						apivm.h(
							"view",
							{class: "flex_w"},
							this.props.item.tag &&
								apivm.h(
									"z-tag",
									{type: this.props.item.tag.type, color: this.props.item.tag.color},
									this.props.item.tag.text
								)
						)
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "margin-top:10px;flex-wrap: wrap;"},
						this.props.item.title.split("").map(function(nItem, nIndex) {
							return (
								nIndex < Math.floor((G.pageWidth - 52) / (G.appFontSize + 2)) * 2 &&
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(1) +
											"margin:2px 0;font-weight: 600;color: " +
											(this$1.props.search &&
											this$1.props.search.result &&
											this$1.props.search.result.indexOf(nItem) > -1
												? G.appTheme
												: "#333") +
											";"
									},
									nIndex == Math.floor((G.pageWidth - 52) / (G.appFontSize + 2)) * 2 - 1
										? "..."
										: nItem
								)
							);
						})
					),
					apivm.h(
						"view",
						{style: "flex-direction:row; align-items: flex-start;margin-top:8px;"},
						apivm.h("a-iconfont", {
							name: "daojishi",
							style: "padding:2px 5px 0 0;",
							color: G.appTheme,
							size: G.appFontSize - 1
						}),
						apivm.h(
							"view",
							{class: "flex_w"},
							apivm.h(
								"text",
								{style: loadConfiguration(-2) + "color: #333;"},
								"开始时间: ",
								this.props.item.startTime
							),
							apivm.h(
								"text",
								{style: loadConfiguration(-2) + "margin-top:3px;mcolor: #333;"},
								"结束时间: ",
								this.props.item.endTime
							)
						)
					),
					apivm.h(
						"text",
						{style: loadConfiguration(-2) + "margin-top:8px;flex:1;color: #999999;"},
						"发起方: ",
						this.props.item.publishStationName
					),
					this.props.item.btn &&
						apivm.h(
							"view",
							{class: "flex_row xy_center"},
							apivm.h("z-button", {
								onClick: function() {
									return this$1.btnclick(1);
								},
								round: true,
								style: "margin-top:10px;padding:7px 7px;width:210px;",
								color: G.appTheme,
								text: "进入会议"
							})
						)
				)
			);
		};

		return Item506;
	})(Component);
	Item506.css = {
		".itemlist_box": {padding: "8px 16px"},
		".itemlist_warp": {
			boxShadow: "0px 2px 10px 1px rgba(24,64,118,0.08)",
			borderTopLeftRadius: "4px",
			borderTopRightRadius: "4px",
			borderBottomLeftRadius: "4px",
			borderBottomRightRadius: "4px",
			padding: "15px 10px"
		},
		".itemlist_tag": {marginTop: "8px", marginRight: "10px"}
	};
	apivm.define("item50-6", Item506);

	var Item504 = /*@__PURE__*/ (function(Component) {
		function Item504(props) {
			Component.call(this, props);
		}

		if (Component) Item504.__proto__ = Component;
		Item504.prototype = Object.create(Component && Component.prototype);
		Item504.prototype.constructor = Item504;
		Item504.prototype.openDetail = function(e) {
			var item = this.props.item;
			openWin_workstation_task({id: item.id});
			this.props.item.isRedDot = 0;
			stopBubble$1(e);
		};
		Item504.prototype.showDot = function() {
			return (
				(this.props.item.isRedDot == 1 || this.props.item.hasComplete === 0) &&
				!this.props.item.isDelete
			);
		};
		Item504.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: "itemlist_box"},
				apivm.h(
					"text",
					{
						style: loadConfiguration(-2) + "color:#999;margin:8px;text-align: center;"
					},
					dayjs(this.props.item.time).format("YYYY-MM-DD HH:mm")
				),
				apivm.h(
					"view",
					{
						class: "itemlist_warp",
						style:
							"background-color: rgba(255, 255, 255, " +
							(G.watermark ? 0.6 : 1) +
							");",
						onClick: this.openDetail
					},
					apivm.h(
						"view",
						null,
						this.showDot() &&
							apivm.h("view", {
								style:
									loadConfigurationSize(-6) +
									"position:absolute;top: -5px;right: -3px;background: #f92323;border-radius: 50%;"
							})
					),
					apivm.h(
						"view",
						{class: "flex_row"},
						apivm.h("a-iconfont", {
							name: "shengyin1",
							color: G.appTheme,
							size: "" + (G.appFontSize + 4)
						}),
						apivm.h(
							"text",
							{style: loadConfiguration(-2) + "color:#333;margin-left:8px;"},
							this.props.item.channelId
						)
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "margin-top:10px;flex-wrap: wrap;"},
						this.props.item.title.split("").map(function(nItem, nIndex) {
							return (
								nIndex < Math.floor((G.pageWidth - 52) / (G.appFontSize + 2)) * 2 &&
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(1) +
											"margin:2px 0;font-weight: 600;color: " +
											(this$1.props.search &&
											this$1.props.search.result &&
											this$1.props.search.result.indexOf(nItem) > -1
												? G.appTheme
												: "#333") +
											";"
									},
									nIndex == Math.floor((G.pageWidth - 52) / (G.appFontSize + 2)) * 2 - 1
										? "..."
										: nItem
								)
							);
						})
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "margin-top:10px;flex-wrap: wrap;"},
						this.props.item.content.split("").map(function(nItem, nIndex) {
							return (
								nIndex < Math.floor((G.pageWidth - 52) / G.appFontSize) * 2 &&
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(-2) +
											"margin:1px 0;color: " +
											(this$1.props.search &&
											this$1.props.search.result &&
											this$1.props.search.result.indexOf(nItem) > -1
												? G.appTheme
												: "#666") +
											";"
									},
									nIndex == Math.floor((G.pageWidth - 52) / G.appFontSize) * 2 - 1
										? "..."
										: nItem
								)
							);
						})
					),
					apivm.h(
						"text",
						{style: loadConfiguration(-2) + "margin-top:8px;flex:1;color: #999999;"},
						"下达单位: ",
						this.props.item.publishStation
					),
					apivm.h(
						"view",
						{class: "flex_row"},
						this.props.item.isTop == 1 &&
							apivm.h(
								"z-tag",
								{
									style: "padding: 0px 8px;border-radius:9px;",
									class: "itemlist_tag",
									size: -4,
									type: "1",
									color: "#666"
								},
								"置顶"
							),
						this.props.item.hasCallback == 1 &&
							apivm.h(
								"z-tag",
								{
									style: "padding: 0px 8px;border-radius:9px;",
									class: "itemlist_tag",
									size: -4,
									type: "2",
									color: "#F6931C"
								},
								"回执"
							),
						this.props.item.attachmentIds &&
							apivm.h(
								"z-tag",
								{
									style: "padding: 0px 8px;border-radius:9px;",
									class: "itemlist_tag",
									size: -4,
									type: "3",
									color: "#1A74DA"
								},
								"附件"
							)
					)
				)
			);
		};

		return Item504;
	})(Component);
	Item504.css = {
		".itemlist_box": {padding: "8px 16px"},
		".itemlist_warp": {
			boxShadow: "0px 2px 10px 1px rgba(24,64,118,0.08)",
			borderTopLeftRadius: "4px",
			borderTopRightRadius: "4px",
			borderBottomLeftRadius: "4px",
			borderBottomRightRadius: "4px",
			padding: "15px 10px"
		},
		".itemlist_tag": {marginTop: "8px", marginRight: "10px"}
	};
	apivm.define("item50-4", Item504);

	var Item5031 = /*@__PURE__*/ (function(Component) {
		function Item5031(props) {
			Component.call(this, props);
		}

		if (Component) Item5031.__proto__ = Component;
		Item5031.prototype = Object.create(Component && Component.prototype);
		Item5031.prototype.constructor = Item5031;
		Item5031.prototype.openDetail = function(e) {
			stopBubble$1(e);
			var item = this.props.item;
			openWin_workstation_activity({id: item.id});
		};
		Item5031.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: "itemlist_box"},
				apivm.h(
					"view",
					{
						class: "itemlist_warp",
						style:
							"background-color: rgba(255, 255, 255, " +
							(G.watermark ? 0.6 : 1) +
							");",
						onClick: this.openDetail
					},
					apivm.h(
						"view",
						{class: "flex_row"},
						apivm.h(
							"view",
							{class: "flex_w"},
							this.props.item.tag &&
								apivm.h(
									"z-tag",
									{type: this.props.item.tag.type, color: this.props.item.tag.color},
									this.props.item.tag.text
								)
						),
						this.props.item.label &&
							apivm.h("z-tag", {color: G.appTheme}, this.props.item.label)
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "flex-wrap: wrap;margin-top:12px;"},
						this.props.item.title.split("").map(function(nItem, nIndex) {
							return (
								nIndex < Math.floor((G.pageWidth - 60) / G.appFontSize) * 2 &&
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(1) +
											"margin:2px 0;font-weight: 500;color: " +
											(this$1.props.search &&
											this$1.props.search.result &&
											this$1.props.search.result.indexOf(nItem) > -1
												? G.appTheme
												: "#333") +
											";"
									},
									nIndex == Math.floor((G.pageWidth - 60) / G.appFontSize) * 2 - 1
										? "..."
										: nItem
								)
							);
						})
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "margin-top:15px;"},
						apivm.h("a-iconfont", {
							name: "daojishi",
							color: G.appTheme,
							size: G.appFontSize - 3
						}),
						apivm.h(
							"text",
							{
								style:
									loadConfiguration(-2) +
									"margin-left:8px;flex:1;color: #999999;margin-right:10px;"
							},
							"活动时间: ",
							this.props.item.time
						)
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "margin-top:10px;"},
						apivm.h("a-iconfont", {
							name: "dingwei1",
							color: G.appTheme,
							size: G.appFontSize - 3
						}),
						apivm.h(
							"text",
							{
								style:
									loadConfiguration(-2) +
									"margin-left:8px;flex:1;color: #999999;margin-right:10px;"
							},
							"活动地点: ",
							this.props.item.address
						)
					)
				)
			);
		};

		return Item5031;
	})(Component);
	Item5031.css = {
		".itemlist_box": {padding: "8px 16px"},
		".itemlist_warp": {
			boxShadow: "0px 2px 10px 1px rgba(24,64,118,0.08)",
			borderTopLeftRadius: "4px",
			borderTopRightRadius: "4px",
			borderBottomLeftRadius: "4px",
			borderBottomRightRadius: "4px",
			padding: "15px 10px"
		}
	};
	apivm.define("item50-3-1", Item5031);

	var Item5021 = /*@__PURE__*/ (function(Component) {
		function Item5021(props) {
			Component.call(this, props);
		}

		if (Component) Item5021.__proto__ = Component;
		Item5021.prototype = Object.create(Component && Component.prototype);
		Item5021.prototype.constructor = Item5021;
		Item5021.prototype.openDetail = function(e) {
			stopBubble$1(e);
			var item = this.props.item;
			openWin_workstation_letter({id: item.id, mine: item.mine, option: 1});
		};
		Item5021.prototype.btnclick = function(_type) {
			var item = this.props.item;
			openWin_workstation_letter({
				id: item.id,
				mine: item.mine,
				option: 1,
				open: 1
			});
		};
		Item5021.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: "itemlist_box"},
				apivm.h(
					"view",
					{
						class: "itemlist_warp",
						style:
							"background-color: rgba(255, 255, 255, " +
							(G.watermark ? 0.6 : 1) +
							");",
						onClick: this.openDetail
					},
					apivm.h(
						"view",
						{class: "flex_row"},

						this.props.item.tag1 &&
							apivm.h(
								"z-tag",
								{
									type: this.props.item.tag1.type,
									style: "margin-right:15px;",
									color: this.props.item.tag1.color
								},
								this.props.item.tag1.text
							),
						this.props.item.tag2 &&
							apivm.h(
								"z-tag",
								{
									type: this.props.item.tag2.type,
									style: "margin-right:15px;",
									color: this.props.item.tag2.color
								},
								this.props.item.tag2.text
							)
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "flex-wrap: wrap;margin-top:12px;"},
						this.props.item.title.split("").map(function(nItem, nIndex) {
							return (
								nIndex < Math.floor((G.pageWidth - 60) / (G.appFontSize + 3)) * 2 &&
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(1) +
											"margin:2px 0;font-weight: 500;color: " +
											(this$1.props.search &&
											this$1.props.search.result &&
											this$1.props.search.result.indexOf(nItem) > -1
												? G.appTheme
												: "#333") +
											";"
									},
									nIndex == Math.floor((G.pageWidth - 60) / (G.appFontSize + 3)) * 2 - 1
										? "..."
										: nItem
								)
							);
						})
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "margin-top:15px;"},
						apivm.h(
							"text",
							{
								style:
									loadConfiguration(-2) + "flex:1;color: #999999;margin-right:10px;"
							},
							"反映对象: ",
							this.props.item.receiverId || this.props.item.stationId
						)
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "margin-top:10px;"},
						apivm.h(
							"view",
							null,
							this.props.item.source &&
								apivm.h(
									"text",
									{style: loadConfiguration(-2) + "color: #999999;margin-right:10px;"},
									this.props.item.source
								)
						),
						apivm.h(
							"view",
							null,
							this.props.item.time &&
								apivm.h(
									"text",
									{style: loadConfiguration(-2) + "color: #999999;margin-right:10px;"},
									this.props.item.time
								)
						)
					),
					this.props.item.btn &&
						apivm.h(
							"view",
							{class: "flex_row xy_center"},
							apivm.h("z-button", {
								onClick: function() {
									return this$1.btnclick(1);
								},
								round: true,
								style: "margin-top:10px;padding:7px 7px;width:210px;",
								color: G.appTheme,
								text:
									this.props.item.btn == "0"
										? "去审核"
										: this.props.item.btn == 1
										? "去回复"
										: "去评价"
							})
						)
				)
			);
		};

		return Item5021;
	})(Component);
	Item5021.css = {
		".itemlist_box": {padding: "8px 16px"},
		".itemlist_warp": {
			boxShadow: "0px 2px 10px 1px rgba(24,64,118,0.08)",
			borderTopLeftRadius: "4px",
			borderTopRightRadius: "4px",
			borderBottomLeftRadius: "4px",
			borderBottomRightRadius: "4px",
			padding: "15px 10px"
		}
	};
	apivm.define("item50-2-1", Item5021);

	var Item501 = /*@__PURE__*/ (function(Component) {
		function Item501(props) {
			Component.call(this, props);
		}

		if (Component) Item501.__proto__ = Component;
		Item501.prototype = Object.create(Component && Component.prototype);
		Item501.prototype.constructor = Item501;
		Item501.prototype.handClick = function(e) {
			stopBubble$1(e);
		};
		Item501.prototype.openVideos = function(e, _item, _index) {
			this.handClick(e);
			_item.autoplay = true;
			_item.showCode = "QUANLANSP";
		};
		Item501.prototype.showAxis = function() {
			if (!this.props.index) {
				return true;
			}
			if (
				this.props.item.isTop == "1" &&
				this.props.list[this.props.index - 1].isTop == 1
			) {
				return false;
			}
			if (
				dayjs(this.props.item.time).format("YYYY-MM-DD") !=
				dayjs(this.props.list[this.props.index - 1].time).format("YYYY-MM-DD")
			) {
				return true;
			}
			return false;
		};
		Item501.prototype.getPlayState = function() {
			var playItem = {src: "shengyin1", text: "播放"};
			if (this.props.item.id == this.props.floatModule.id) {
				switch (this.props.floatModule.state + "") {
					case "0":
						playItem.src = "shengyin1";
						playItem.text = "播放";
						break;
					case "1":
						playItem.src = "shengyin";
						playItem.text = "暂停";
						break;
					case "2":
						playItem.src = "shengyinjingyin";
						playItem.text = "继续播放";
						break;
					case "3":
						playItem.src = "shengyin1";
						playItem.text = "重新播放";
						break;
				}
			} else {
				playItem.src = "shengyin1";
				playItem.text = "播放";
			}
			return playItem;
		};
		Item501.prototype.playing = function(e) {
			this.handClick(e);
			if (this.props.item.id == this.props.floatModule.id) {
				if (this.props.floatModule.state == "1") {
					sendEvent({name: "index", extra: {type: "pauseStartPlay"}});
				} else {
					sendEvent({name: "index", extra: {type: "reStartPlay"}});
				}
			} else {
				sendEvent({
					name: "index",
					extra: {
						type: "startPlay",
						floatType: this.props.item.code,
						id: this.props.item.id,
						src: this.props.item.content
					}
				});
			}
		};
		Item501.prototype.openDetail = function(e) {
			this.handClick(e);
			var item = this.props.item;
			if (item.onlyCode == module39_3.code) {
				openWin_lawpoint_news({id: item.id});
				return;
			}
			openWin_workstation_news({id: item.id, link: item.link});
		};
		Item501.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					class: "news_item",
					style:
						"padding-left:" + (this.props.layoutId == "SJCXS" ? "24" : "16") + "px;",
					onClick: this.openDetail
				},
				apivm.h("view", {
					class: "news_point",
					style:
						"display:" +
						(this.props.layoutId == "SJCXS" && this.showAxis() ? "block" : "none") +
						";background:" +
						G.appTheme +
						";"
				}),
				apivm.h("view", {
					class: "news_line_top",
					style:
						"display:" +
						(this.props.layoutId == "SJCXS" ? "block" : "none") +
						";background:" +
						colorRgba(G.appTheme, this.props.index ? 0.15 : 0) +
						";"
				}),
				apivm.h("view", {
					class: "news_line_bottom",
					style:
						"display:" +
						(this.props.layoutId == "SJCXS" ? "block" : "none") +
						";background:" +
						colorRgba(
							G.appTheme,
							isArray(this.props.list) &&
								this.props.index == this.props.list.length - 1
								? 0
								: 0.15
						) +
						";"
				}),
				apivm.h(
					"view",
					null,
					this.props.layoutId == "SJCXS" &&
						apivm.h(
							"text",
							{
								style:
									"display:" +
									(this.showAxis() ? "block" : "none") +
									";" +
									loadConfiguration(-2) +
									"font-weight: 600;color:#333;margin-bottom:10px;"
							},
							this.props.item.isTop == 1
								? "置顶"
								: dayjs(this.props.item.time).format("YYYY-MM-DD")
						)
				),
				apivm.h(
					"view",
					null,
					((this.props.item.showCode == "QUANLANSP" && this.props.item.videoHrefs) ||
						isArray(this.props.item.url) ||
						this.props.item.code == "7") && [
						apivm.h(
							"view",
							{class: "flex_row"},
							apivm.h(
								"view",
								null,
								this.props.item.code == "7" &&
									apivm.h("view", {
										style:
											loadConfigurationSize(-2, "h") +
											"width:3px;background:" +
											G.appTheme +
											";margin-right:4px;"
									})
							),
							apivm.h(
								"view",
								{class: "flex_row", style: "flex:1;flex-wrap: wrap;"},
								this.props.item.title.split("").map(function(nItem, nIndex) {
									return (
										nIndex <
											Math.floor((G.pageWidth - 40) / (G.appFontSize + 2)) -
												(this$1.props.item.code == "7" ? 3 : 1) &&
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"margin:2px 0;font-weight: 600;color: " +
													(this$1.props.search &&
													this$1.props.search.result &&
													this$1.props.search.result.indexOf(nItem) > -1
														? G.appTheme
														: "#333") +
													";"
											},
											nIndex ==
												Math.floor((G.pageWidth - 40) / (G.appFontSize + 2)) -
													(this$1.props.item.code == "7" ? 3 : 1) -
													1
												? "..."
												: nItem
										)
									);
								})
							),
							apivm.h(
								"view",
								{class: "flex_row", style: "margin-left:5px;"},
								this.props.item.code == "7" &&
									apivm.h(
										"text",
										{
											style:
												loadConfiguration(-1) +
												"color:#999;flex-shrink: 0;margin-right:2px;"
										},
										"查看"
									),
								apivm.h(
									"view",
									null,
									apivm.h("a-iconfont", {
										name: "xiangxia1",
										style: "transform: rotate(-90deg);",
										color: "#666",
										size: "" + G.appFontSize
									})
								)
							)
						),
						apivm.h(
							"view",
							{style: "margin-top:10px;"},
							this.props.item.videoHrefs && this.props.item.showCode == "QUANLANSP"
								? apivm.h(
										"view",
										{onClick: this.handClick},
										apivm.h("z-video", {
											autoplay: this.props.item.autoplay,
											poster: this.props.item.poster,
											src: this.props.item.videoHrefs
										})
								  )
								: isArray(this.props.item.url)
								? apivm.h(
										"view",
										{class: "flex_row"},
										(Array.isArray(this.props.item.url)
											? this.props.item.url
											: Object.values(this.props.item.url)
										).map(function(nItem, nIndex) {
											return apivm.h(
												"view",
												{style: "width:33.33%;height:84px;margin: 0 2px;"},
												apivm.h("image", {
													class: "xy_100",
													src: showImg(nItem),
													mode: "aspectFill",
													thumbnail: "false"
												})
											);
										})
								  )
								: apivm.h("image", {
										style: "width:100%;height: " + G.pageWidth * 0.52 + "px;",
										src: showImg(this.props.item),
										mode: "aspectFill",
										thumbnail: "false"
								  })
						),
						apivm.h(
							"view",
							null,
							this.props.item.code != "7" &&
								apivm.h(
									"view",
									{style: "margin-top:10px;", class: "flex_row"},
									apivm.h(
										"view",
										{class: "flex_w flex_row"},
										this.props.layoutId != "SJCXS" &&
											apivm.h(
												"text",
												{
													class: "c_999 " + (this.props.item.url ? "text_one" : ""),
													style: "margin-right:10px;" + loadConfiguration(-4) + ";"
												},
												dayjs(this.props.item.time).format("YYYY-MM-DD")
											),
										apivm.h(
											"text",
											{class: "c_999 text_one", style: loadConfiguration(-4) + ";"},
											this.props.item.source
										)
									),
									apivm.h(
										"view",
										{style: "flex-shrink: 0;"},
										isObject(this.props.floatModule) &&
											this.props.item.content &&
											platform() == "app" &&
											api.require("voiceRecognizer") &&
											apivm.h(
												"view",
												{onClick: this.playing, class: "flex_row"},
												apivm.h("a-iconfont", {
													name: this.getPlayState().src,
													color: G.appTheme,
													size: G.appFontSize
												}),
												apivm.h(
													"text",
													{
														style:
															"margin-left:2px;" +
															loadConfiguration(-3) +
															";color:" +
															G.appTheme +
															";"
													},
													this.getPlayState().text
												)
											)
									),
									apivm.h(
										"view",
										{style: "flex-shrink: 0;"},
										this.props.item.sourceModuleName &&
											apivm.h(
												"z-tag",
												{style: "margin-left:10px;", type: "2", color: G.appTheme},
												this.props.item.sourceModuleName
											)
									)
								)
						)
					]
				),
				apivm.h(
					"view",
					null,
					(this.props.item.showCode != "QUANLANSP" || !this.props.item.videoHrefs) &&
						!isArray(this.props.item.url) &&
						this.props.item.code != "7" &&
						apivm.h(
							"view",
							{class: "flex_row"},
							apivm.h(
								"view",
								null,
								this.props.item.url &&
									apivm.h(
										"view",
										{class: "news_item_img"},
										apivm.h("image", {
											class: "xy_100",
											src: showImg(this.props.item),
											mode: "aspectFill",
											thumbnail: "false"
										}),
										this.props.item.videoHrefs &&
											this.props.item.showCode == "ZUOCESP" &&
											apivm.h(
												"view",
												{
													style:
														"position:absolute;z-index:1;left:0;top:0;right:0;bottom:0;align-items: center;justify-content: center;",
													onClick: function(e) {
														return this$1.openVideos(
															e,
															this$1.props.item,
															this$1.props.index
														);
													}
												},
												apivm.h("image", {
													mode: "aspectFill",
													style: loadConfigurationSize(22),
													thumbnail: "false",
													src: shareAddress(1) + "image/icon_play.png"
												})
											)
									)
							),
							apivm.h(
								"view",
								{class: "flex_w"},
								apivm.h(
									"view",
									{class: "flex_row", style: "flex-wrap: wrap;"},
									this.props.item.title.split("").map(function(nItem, nIndex) {
										return (
											nIndex <
												Math.floor(
													(G.pageWidth - 70 - (this$1.props.item.url ? 122 : 0)) /
														(G.appFontSize + 2)
												) *
													2 &&
											apivm.h(
												"text",
												{
													style:
														loadConfiguration(1) +
														"margin:2px 0;font-weight: 600;color: " +
														(this$1.props.search &&
														this$1.props.search.result &&
														this$1.props.search.result.indexOf(nItem) > -1
															? G.appTheme
															: "#333") +
														";"
												},
												nIndex ==
													Math.floor(
														(G.pageWidth - 70 - (this$1.props.item.url ? 122 : 0)) /
															(G.appFontSize + 2)
													) *
														2 -
														1
													? "..."
													: nItem
											)
										);
									})
								),
								apivm.h(
									"view",
									null,
									this.props.item.url &&
										apivm.h(
											"text",
											{
												class: "c_999 text_one",
												style: "margin-top:2px;" + loadConfiguration(-4) + ";"
											},
											this.props.item.source
										)
								),
								apivm.h(
									"view",
									{class: "flex_row", style: "margin-top:3px;"},
									apivm.h(
										"view",
										{class: "flex_w flex_row"},
										this.props.layoutId != "SJCXS" &&
											apivm.h(
												"text",
												{
													class: "c_999 " + (this.props.item.url ? "text_one" : ""),
													style: "margin-right:10px;" + loadConfiguration(-4) + ";"
												},
												dayjs(this.props.item.time).format("YYYY-MM-DD")
											),
										!this.props.item.url &&
											apivm.h(
												"text",
												{class: "c_999 text_one", style: loadConfiguration(-4) + ";"},
												this.props.item.source
											)
									),
									apivm.h(
										"view",
										{style: "flex-shrink: 0;"},
										isObject(this.props.floatModule) &&
											this.props.item.content &&
											platform() == "app" &&
											api.require("voiceRecognizer") &&
											apivm.h(
												"view",
												{onClick: this.playing, class: "flex_row"},
												apivm.h("a-iconfont", {
													name: this.getPlayState().src,
													color: G.appTheme,
													size: G.appFontSize
												}),
												apivm.h(
													"text",
													{
														style:
															"margin-left:2px;" +
															loadConfiguration(-3) +
															";color:" +
															G.appTheme +
															";"
													},
													this.getPlayState().text
												)
											)
									),
									apivm.h(
										"view",
										{style: "flex-shrink: 0;"},
										this.props.item.sourceModuleName &&
											apivm.h(
												"z-tag",
												{style: "margin-left:10px;", type: "2", color: G.appTheme},
												this.props.item.sourceModuleName
											)
									)
								)
							)
						)
				)
			);
		};

		return Item501;
	})(Component);
	Item501.css = {
		".c_999": {color: "#999"},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden",
			display: "inline"
		},
		".news_item": {padding: "15px 16px"},
		".news_item_img": {
			width: "112px",
			height: "84px",
			marginRight: "10px",
			borderRadius: "2px"
		},
		".news_point": {
			position: "absolute",
			zIndex: "999",
			left: "10px",
			top: "21px",
			borderRadius: "50%",
			width: "7px",
			height: "7px"
		},
		".news_line_top": {
			position: "absolute",
			zIndex: "999",
			left: "13px",
			top: "0",
			height: "24px",
			width: "1px"
		},
		".news_line_bottom": {
			position: "absolute",
			zIndex: "999",
			left: "13px",
			top: "24px",
			bottom: "0",
			width: "1px"
		}
	};
	apivm.define("item50-1", Item501);

	var Item311 = /*@__PURE__*/ (function(Component) {
		function Item311(props) {
			Component.call(this, props);
		}

		if (Component) Item311.__proto__ = Component;
		Item311.prototype = Object.create(Component && Component.prototype);
		Item311.prototype.constructor = Item311;
		Item311.prototype.openDetail = function(e) {
			openWin_study(this.props.item);
			stopBubble$1(e);
		};
		Item311.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: "itemlist_box"},
				apivm.h(
					"view",
					{
						class: "itemlist_warp",
						style:
							"background-color: rgba(255, 255, 255, " +
							(G.watermark ? 0.6 : 1) +
							");",
						onClick: this.openDetail
					},
					apivm.h(
						"view",
						{class: "flex_row", style: "flex-wrap: wrap;"},
						apivm.h("a-iconfont", {
							name: "suoyouwenjian",
							style: "margin-right:5px;",
							color: "#ccc",
							size: G.appFontSize + 4
						}),
						this.props.item.title.split("").map(function(nItem, nIndex) {
							return (
								nIndex < Math.floor((G.pageWidth - 72) / (G.appFontSize + 2)) * 2 &&
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(1) +
											"margin:2px 0;font-weight: 600;color: " +
											(this$1.props.search &&
											this$1.props.search.result &&
											this$1.props.search.result.indexOf(nItem) > -1
												? G.appTheme
												: "#333") +
											";"
									},
									nIndex == Math.floor((G.pageWidth - 72) / (G.appFontSize + 2)) * 2 - 1
										? "..."
										: nItem
								)
							);
						})
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "margin-top:6px;"},
						apivm.h(
							"view",
							{class: "flex_w"},
							apivm.h(
								"text",
								{style: loadConfiguration(-2) + "color: #333;margin-top:4px;"},
								"总分",
								this.props.item.fullScore,
								"、",
								this.props.item.scoreLevel
							),
							apivm.h(
								"text",
								{style: loadConfiguration(-2) + "color: #333;margin-top:6px;"},
								"完成耗时：",
								this.props.item.examCount,
								"分钟"
							),
							apivm.h(
								"text",
								{style: loadConfiguration(-2) + "color: #999;margin-top:10px;"},
								dayjs(this.props.item.time).format("YYYY-MM-DD HH:ss")
							)
						),
						apivm.h(
							"view",
							{
								style:
									loadConfigurationSize(65) +
									"margin:0 30px;align-items: center;justify-content: center;border-radius:50%;background: linear-gradient(to bottom, rgba(246,147,28,0.02), rgba(246,147,28,0.1));"
							},
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(4) +
										"color: #F6931C;font-weight: 600;margin-top: 10px;"
								},
								this.props.item.score,
								"分"
							),
							apivm.h(
								"text",
								{style: loadConfiguration(-4) + "color: #58370F;margin-top:5px;"},
								"得分"
							)
						)
					)
				)
			);
		};

		return Item311;
	})(Component);
	Item311.css = {
		".itemlist_box": {padding: "8px 16px"},
		".itemlist_warp": {
			boxShadow: "0px 2px 10px 1px rgba(24,64,118,0.08)",
			borderTopLeftRadius: "4px",
			borderTopRightRadius: "4px",
			borderBottomLeftRadius: "4px",
			borderBottomRightRadius: "4px",
			padding: "15px 10px"
		},
		".countdown_time": {
			minWidth: "20px",
			minHeight: "20px",
			borderRadius: "1px 1px 1px 1px",
			margin: "0 2px",
			justifyContent: "center",
			alignItems: "center",
			padding: "0 3px"
		}
	};
	apivm.define("item31-1", Item311);

	/**
	 * 插槽支持
	 * @param VNode
	 * @param children
	 * @param host
	 */
	function slotSupport(VNode, children, host) {
		var slots = {
			default: []
		};

		var dfsScope = function dfsScope(node) {
			if (node.nodeName === "text" && node.attributes) {
				var text = node.children[0];
				if (
					node.attributes.scoped ||
					(typeof text === "string" && text.startsWith("[[") && text.endsWith("]]"))
				) {
					var path = text.replace(/[\[\]]/g, "").split(/[.[]/);
					if (node.attributes.scoped) {
						path = node.attributes.scoped;
					} else {
						node.attributes.scoped = path;
					}
					var data = host;
					path.forEach(function(p) {
						return (data = data[p]);
					});
					node.children[0] = JSON.stringify(data);
				}
			}

			if (Array.isArray(node.children) && node.children.length) {
				node.children.forEach(dfsScope);
			}
		};

		children.forEach(function(node) {
			if (node) {
				if (
					node.nodeName === "template" &&
					node.attributes &&
					node.attributes._slot
				) {
					var slotInfo = node.attributes._slot.split(":");
					if (slotInfo.length === 2) {
						node.attributes.scope = slotInfo[1];
						node.attributes._slot = slotInfo[0];
					}
					slots[node.attributes._slot] = node;
				} else {
					slots.default.push(node);
				}
				dfsScope(node);
			}
		});

		if (Object.keys(slots).length === 1 && slots.default.length === 0) {
			// 没有 slot 项目 直接返回 不要再去查找组件
			return VNode;
		}

		var dfs = function dfs(node) {
			if (node.attributes && node.attributes._slot) {
				if (slots[node.attributes._slot]) {
					if (node.attributes._slot === "default") {
						node.children = [].concat(node.children, slots[node.attributes._slot]);
					} else {
						node.children = slots[node.attributes._slot].children;
						node.attributes = Object.assign(
							slots[node.attributes._slot].attributes,
							node.attributes
						);
					}
				}
				// delete node.attributes._slot;
			} else if (Array.isArray(node.children) && node.children.length) {
				node.children.forEach(dfs);
			}
		};
		dfs(VNode);

		return VNode;
	}

	/**
	 * 倒计时组件使用的格式化代码
	 * @param format
	 * @param currentTime
	 * @returns {*}
	 */
	function parseFormat(format, currentTime) {
		var days = currentTime.days;
		var hours = currentTime.hours,
			minutes = currentTime.minutes,
			seconds = currentTime.seconds,
			milliseconds = currentTime.milliseconds;

		if (format.indexOf("DD") === -1) {
			hours += days * 24;
		} else {
			format = format.replace("DD", padZero(days));
		}

		if (format.indexOf("HH") === -1) {
			minutes += hours * 60;
		} else {
			format = format.replace("HH", padZero(hours));
		}

		if (format.indexOf("mm") === -1) {
			seconds += minutes * 60;
		} else {
			format = format.replace("mm", padZero(minutes));
		}

		if (format.indexOf("ss") === -1) {
			milliseconds += seconds * 1000;
		} else {
			format = format.replace("ss", padZero(seconds));
		}

		if (format.indexOf("S") !== -1) {
			var ms = padZero(milliseconds, 3);

			if (format.indexOf("SSS") !== -1) {
				format = format.replace("SSS", ms);
			} else if (format.indexOf("SS") !== -1) {
				format = format.replace("SS", ms.slice(0, 2));
			} else {
				format = format.replace("S", ms.charAt(0));
			}
		}

		return format;
	}

	/**
	 * 补齐 0
	 * @param num
	 * @param targetLength
	 * @returns {string}
	 */
	function padZero(num, targetLength) {
		if (targetLength === void 0) {
			targetLength = 2;
		}
		var str = num + "";
		while (str.length < targetLength) {
			str = "0" + str;
		}
		return str;
	}

	var SECOND = 1000;
	var MINUTE = 60 * SECOND;
	var HOUR = 60 * MINUTE;
	var DAY = 24 * HOUR;

	/**
	 * 转换时间
	 * @param time
	 * @returns {{milliseconds: number, total, hours: number, seconds: number, minutes: number, days: number}}
	 */
	function parseTime(time) {
		var days = Math.floor(time / DAY);
		var hours = Math.floor((time % DAY) / HOUR);
		var minutes = Math.floor((time % HOUR) / MINUTE);
		var seconds = Math.floor((time % MINUTE) / SECOND);
		var milliseconds = Math.floor(time % SECOND);

		return {
			total: time,
			days: days,
			hours: hours,
			minutes: minutes,
			seconds: seconds,
			milliseconds: milliseconds
		};
	}

	var ACountDown = /*@__PURE__*/ (function(Component) {
		function ACountDown(props) {
			Component.call(this, props);
			this.data = {
				time: 0,
				nowTime: null
			};
			this.compute = {
				timeStr: function() {
					var format = this.props.format || "HH:mm:ss";
					return parseFormat(format, this.timeData);
				},
				timeData: function() {
					if (this.data.nowTime != this.props.time) {
						//修改时间时重置
						this.data.nowTime = this.props.time || 0;
						this.data.time = this.props.time || 0;
					}
					return parseTime(this.data.time);
				}
			};
		}

		if (Component) ACountDown.__proto__ = Component;
		ACountDown.prototype = Object.create(Component && Component.prototype);
		ACountDown.prototype.constructor = ACountDown;
		ACountDown.prototype.install = function() {
			var this$1 = this;
			this.render = function(props) {
				var h = apivm.h;
				return slotSupport(
					h(
						"view",
						{
							class: "a-count-down " + (props.class || ""),
							style: "" + (props.style || ""),
							_slot: "default"
						} // h('text', {
						//			 class: 'a-count-down__text'
						//		 },
						//		 this.timeStr
						// )
					),
					props.children,
					this$1
				);
			};
		};
		ACountDown.prototype.installed = function() {
			this.data.nowTime = this.props.time || 0;
			this.data.time = this.props.time || 0;
			if (this.props["auto-start"] !== false) {
				this.start();
			}
		};
		ACountDown.prototype.start = function() {
			var this$1 = this;

			if (this.timer || this.data.time === 0) {
				return;
			}
			var step = this.props.millisecond ? 1 : 1000;

			var _run = function() {
				this$1.data.time -= step;
				if (this$1.data.time > step) {
					this$1.timer = setTimeout(_run, step);
					this$1.fire("change", this$1.timeData);
				} else {
					this$1.pause();
					this$1.fire("finish");
				}
			};

			_run();
		};
		ACountDown.prototype.pause = function() {
			clearTimeout(this.timer);
			this.timer = false;
		};
		ACountDown.prototype.reset = function() {
			this.pause();
			this.data.time = this.props.time || 0;
		};
		ACountDown.prototype.render = function() {
			return;
		};

		return ACountDown;
	})(Component);
	ACountDown.css = {".a-count-down": {flexFlow: "row nowrap"}};
	apivm.define("a-count-down", ACountDown);

	var Item31 = /*@__PURE__*/ (function(Component) {
		function Item31(props) {
			Component.call(this, props);
		}

		if (Component) Item31.__proto__ = Component;
		Item31.prototype = Object.create(Component && Component.prototype);
		Item31.prototype.constructor = Item31;
		Item31.prototype.openDetail = function(e) {
			openWin_study(this.props.item);
			stopBubble$1(e);
		};
		Item31.prototype.showDot = function() {
			return (
				(this.props.item.isRedDot == 1 || this.props.item.hasComplete === 0) &&
				!this.props.item.isDelete
			);
		};
		Item31.prototype.downFinish = function() {
			this.fire("refresh");
		};
		Item31.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: "itemlist_box"},
				apivm.h(
					"view",
					{
						class: "itemlist_warp",
						style:
							"background-color: rgba(255, 255, 255, " +
							(G.watermark ? 0.6 : 1) +
							");",
						onClick: this.openDetail
					},
					apivm.h(
						"view",
						null,
						this.showDot() &&
							apivm.h("view", {
								style:
									loadConfigurationSize(-6) +
									"position:absolute;top: -5px;right: -3px;background: #f92323;border-radius: 50%;"
							})
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "flex-wrap: wrap;"},
						apivm.h("a-iconfont", {
							name: "suoyouwenjian",
							style: "margin-right:5px;",
							color: G.appTheme,
							size: G.appFontSize + 4
						}),
						this.props.item.title.split("").map(function(nItem, nIndex) {
							return (
								nIndex < Math.floor((G.pageWidth - 72) / (G.appFontSize + 2)) * 2 &&
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(1) +
											"margin:2px 0;font-weight: 600;color: " +
											(this$1.props.search &&
											this$1.props.search.result &&
											this$1.props.search.result.indexOf(nItem) > -1
												? G.appTheme
												: "#333") +
											";"
									},
									nIndex == Math.floor((G.pageWidth - 72) / (G.appFontSize + 2)) * 2 - 1
										? "..."
										: nItem
								)
							);
						})
					),
					apivm.h(
						"view",
						{style: "margin-top:10px;"},
						apivm.h(
							"view",
							null,
							(this.props.item.state == "未开始" ||
								this.props.item.state == "已截止") &&
								apivm.h(
									"text",
									{style: loadConfiguration(-2) + "font-weight: 600;color: #333333;"},
									"考试时间",
									this.props.item.state
								)
						),
						apivm.h(
							"view",
							null,
							this.props.item.state != "未开始" &&
								this.props.item.state != "已截止" &&
								apivm.h(
									"view",
									{class: "flex_row"},
									apivm.h("a-iconfont", {
										name: "daojishi",
										color: "#333",
										size: G.appFontSize
									}),
									apivm.h(
										"text",
										{
											style:
												loadConfiguration(-2) +
												"color:#333;font-weight: 600;margin-left:5px;"
										},
										"截止时间："
									),
									apivm.h(
										"a-count-down",
										{
											class: "flex_row",
											onFinish: this.downFinish,
											time: dayjs(this.props.item.endTime).valueOf() - dayjs().valueOf()
										},
										apivm.h(
											"view",
											{
												class: "countdown_time",
												style: "background:" + colorRgba(G.appTheme, 0.05)
											},
											apivm.h(
												"text",
												{style: loadConfiguration(-2) + "color:" + G.appTheme},
												"[[timeData.days]]"
											)
										),
										apivm.h(
											"view",
											{style: "width:auto;"},
											apivm.h("text", {style: loadConfiguration(-2) + "color:#333"}, "天")
										),
										apivm.h(
											"view",
											{
												class: "countdown_time",
												style: "background:" + colorRgba(G.appTheme, 0.05)
											},
											apivm.h(
												"text",
												{style: loadConfiguration(-2) + "color:" + G.appTheme},
												"[[timeData.hours]]"
											)
										),
										apivm.h(
											"view",
											{style: "width:auto;"},
											apivm.h(
												"text",
												{
													style:
														loadConfiguration(-2) +
														"color:#333;width:" +
														G.appFontSize * 2 +
														"px;"
												},
												"小时"
											)
										),
										apivm.h(
											"view",
											{
												class: "countdown_time",
												style: "background:" + colorRgba(G.appTheme, 0.05)
											},
											apivm.h(
												"text",
												{style: loadConfiguration(-2) + "color:" + G.appTheme},
												"[[timeData.minutes]]"
											)
										),
										apivm.h(
											"view",
											{style: "width:auto;"},
											apivm.h("text", {style: loadConfiguration(-2) + "color:#333"}, "分")
										)
									)
								)
						)
					),
					apivm.h(
						"text",
						{style: loadConfiguration(-2) + "color: #333333;margin-top:6px;"},
						"考试完成时限：",
						this.props.item.limitCount
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "margin-top:6px;"},
						apivm.h(
							"text",
							{style: loadConfiguration(-2) + "color: #333333;"},
							this.props.item.addText
						),
						this.props.item.showscore &&
							apivm.h(
								"text",
								{style: loadConfiguration(-2) + "color: " + G.appTheme + ";"},
								this.props.item.score,
								"分"
							)
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "margin-top:10px;"},
						apivm.h(
							"text",
							{style: loadConfiguration() + "color: #999;flex:1;"},
							this.props.item.joinCount,
							"人已参与"
						),
						apivm.h(
							"view",
							{style: "margin-left:10px;"},
							apivm.h("z-button", {
								onClick: function() {
									return this$1.openDetail();
								},
								disabled: this.props.item.appState == 4,
								plain: this.props.item.appState == 0 || this.props.item.appState == 1,
								round: true,
								style: "min-width:180px;padding:7px 15px;",
								color: G.appTheme,
								text: this.props.item.btnText
							})
						)
					)
				)
			);
		};

		return Item31;
	})(Component);
	Item31.css = {
		".itemlist_box": {padding: "8px 16px"},
		".itemlist_warp": {
			boxShadow: "0px 2px 10px 1px rgba(24,64,118,0.08)",
			borderTopLeftRadius: "4px",
			borderTopRightRadius: "4px",
			borderBottomLeftRadius: "4px",
			borderBottomRightRadius: "4px",
			padding: "15px 10px"
		},
		".countdown_time": {
			minWidth: "20px",
			minHeight: "20px",
			borderRadius: "1px 1px 1px 1px",
			margin: "0 2px",
			justifyContent: "center",
			alignItems: "center",
			padding: "0 3px"
		}
	};
	apivm.define("item31", Item31);

	var Item22 = /*@__PURE__*/ (function(Component) {
		function Item22(props) {
			Component.call(this, props);
		}

		if (Component) Item22.__proto__ = Component;
		Item22.prototype = Object.create(Component && Component.prototype);
		Item22.prototype.constructor = Item22;
		Item22.prototype.openDetail = function(e) {
			openWin_notice(this.props.item);
			stopBubble$1(e);
		};
		Item22.prototype.showDot = function() {
			return (
				(this.props.item.isRedDot == 1 || this.props.item.hasComplete === 0) &&
				!this.props.item.isDelete
			);
		};
		Item22.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: "itemlist_box"},
				apivm.h(
					"text",
					{
						style: loadConfiguration(-2) + "color:#999;margin:8px;text-align: center;"
					},
					dayjs(this.props.item.time).format("YYYY-MM-DD HH:mm")
				),
				apivm.h(
					"view",
					{
						class: "itemlist_warp",
						style:
							"background-color: rgba(255, 255, 255, " +
							(G.watermark ? 0.6 : 1) +
							");",
						onClick: this.openDetail
					},
					apivm.h(
						"view",
						null,
						this.showDot() &&
							apivm.h("view", {
								style:
									loadConfigurationSize(-6) +
									"position:absolute;top: -5px;right: -3px;background: #f92323;border-radius: 50%;"
							})
					),
					apivm.h(
						"view",
						{class: "flex_row"},
						apivm.h("a-iconfont", {
							name: "shengyin1",
							color: G.appTheme,
							size: "" + (G.appFontSize + 4)
						}),
						apivm.h(
							"text",
							{style: loadConfiguration(-2) + "color:#333;margin-left:8px;"},
							this.props.item.channelId
						),
						this.props.item.urgentLevel &&
							apivm.h(
								"z-tag",
								{
									style: "margin-left:5px;",
									size: -2,
									type: "3",
									color: this.props.item.urgentLevel.color
								},
								this.props.item.urgentLevel.text
							)
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "margin-top:10px;flex-wrap: wrap;"},
						this.props.item.title.split("").map(function(nItem, nIndex) {
							return (
								nIndex < Math.floor((G.pageWidth - 52) / (G.appFontSize + 2)) * 2 &&
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(1) +
											"margin:2px 0;font-weight: 600;color: " +
											(this$1.props.search &&
											this$1.props.search.result &&
											this$1.props.search.result.indexOf(nItem) > -1
												? G.appTheme
												: "#333") +
											";"
									},
									nIndex == Math.floor((G.pageWidth - 52) / (G.appFontSize + 2)) * 2 - 1
										? "..."
										: nItem
								)
							);
						})
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "margin-top:10px;flex-wrap: wrap;"},
						this.props.item.content.split("").map(function(nItem, nIndex) {
							return (
								nIndex < Math.floor((G.pageWidth - 52) / G.appFontSize) * 2 &&
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(-2) +
											"margin:1px 0;color: " +
											(this$1.props.search &&
											this$1.props.search.result &&
											this$1.props.search.result.indexOf(nItem) > -1
												? G.appTheme
												: "#666") +
											";"
									},
									nIndex == Math.floor((G.pageWidth - 52) / G.appFontSize) * 2 - 1
										? "..."
										: nItem
								)
							);
						})
					),
					apivm.h(
						"view",
						{class: "flex_row"},
						this.props.item.isTop == 1 &&
							apivm.h(
								"z-tag",
								{
									style: "padding: 0px 8px;border-radius:9px;",
									class: "itemlist_tag",
									size: -4,
									type: "1",
									color: "#666"
								},
								"置顶"
							),
						this.props.item.isReceipt == 1 &&
							apivm.h(
								"z-tag",
								{
									style: "padding: 0px 8px;border-radius:9px;",
									class: "itemlist_tag",
									size: -4,
									type: "2",
									color: "#F6931C"
								},
								"回执"
							),
						this.props.item.attachmentIds != "" &&
							apivm.h(
								"z-tag",
								{
									style: "padding: 0px 8px;border-radius:9px;",
									class: "itemlist_tag",
									size: -4,
									type: "3",
									color: "#1A74DA"
								},
								"附件"
							)
					)
				)
			);
		};

		return Item22;
	})(Component);
	Item22.css = {
		".itemlist_box": {padding: "8px 16px"},
		".itemlist_warp": {
			boxShadow: "0px 2px 10px 1px rgba(24,64,118,0.08)",
			borderTopLeftRadius: "4px",
			borderTopRightRadius: "4px",
			borderBottomLeftRadius: "4px",
			borderBottomRightRadius: "4px",
			padding: "15px 10px"
		},
		".itemlist_tag": {marginTop: "8px", marginRight: "10px"}
	};
	apivm.define("item22", Item22);

	var Item14 = /*@__PURE__*/ (function(Component) {
		function Item14(props) {
			Component.call(this, props);
		}

		if (Component) Item14.__proto__ = Component;
		Item14.prototype = Object.create(Component && Component.prototype);
		Item14.prototype.constructor = Item14;
		Item14.prototype.handClick = function(e) {
			stopBubble(e);
		};
		Item14.prototype.openDetail = function() {
			var item = this.props.item;
			console.log(JSON.stringify(item));
			if (item.isDelete) {
				toast("已删除");
				return;
			}
			this.openPage(item.moduleCode);
		};
		Item14.prototype.openPage = function(_name) {
			var item = this.props.item;
			switch (_name) {
				case "stationActivity": //活动
					openWin_workstation_activity({id: item.id});
					break;
				case "stationLetter": //留言
					openWin_workstation_letter({id: item.id, option: 1});
					break;
				case "station_notice": //任务接收
					openWin_workstation_task({id: item.id});
					break;
				case "station_meeting": //视频会议
					openWin_workstation_video({id: item.id});
					break;
				case "stationDutiesRoom": //补录审核
					openWin_workstation_dutieshelp_review({id: item.id});
					break;
				case "contactStation": //联络站总体
					this.openPage(item.mobileRedirectUrl);
					return;
				case "opinioncollect": //意见征集
					openWin_opinioncollect({id: item.id});
					break;
				case "LegislationBusinessCode": //立法征询
					openWin_legislative({id: item.id});
					break;
				case "pan": //云盘共享
					openWin_pan({defaultType: "share"});
					break;
				case "message": //通知公告
					openWin_notice({id: item.id});
					break;
				case "study_publish": //学习培训
					openWin_study({id: item.id});
					break;
				case "npcSuggestion": //建议
					openWin_suggestion({id: item.id});
					break;
				case "npcProposal": //提案
					openWin_proposal({id: item.id});
					break;
				case "performduties": //履职大厅
					openWin_performancehall({id: item.id});
					break;
				case "LegislationBusinessCode": //立法征询
					openWin_legislative({id: item.id});
					break;
				case "servantActivity": //履职活动
					openWin_activity({id: item.id});
					break;
				case "npcMotion": //议案管理
					openWin_motion({id: item.id});
					break;
				case "npcMemberManager": //代表信息审核
				case "cppccMemberManager": //委员信息审核
					openWin_npcinfo_review({id: item.id});
					break;
				case "MicroAdviceBusinessCode": //微建议
					openWin_microAdvice({id: item.id});
					break;
				default:
					toast("请使用电脑登录系统进行处理");
					return;
			}

			if (item.isRedDot == 1) {
				addRedDot({businessCode: item.oldCode, id: item.oldId});
				item.isRedDot = "0";
			}
		};
		Item14.prototype.showDot = function() {
			return this.props.item.isRedDot == 1 && !this.props.item.isDelete;
		};
		Item14.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: "itemlist_box"},
				apivm.h(
					"view",
					{
						class: "itemlist_warp",
						style:
							"background-color: rgba(255, 255, 255, " +
							(G.watermark ? 0.6 : 1) +
							");",
						onClick: this.openDetail
					},
					apivm.h(
						"view",
						null,
						this.showDot() &&
							apivm.h("view", {
								style:
									loadConfigurationSize(-6) +
									"position:absolute;top: -5px;right: -3px;background: #f92323;border-radius: 50%;"
							})
					),
					apivm.h(
						"view",
						{style: "flex-direction:row; align-items: flex-start;"},
						apivm.h(
							"view",
							{
								class: "xy_center",
								style:
									"margin:8px 8px 0 0;width:8px;height:8px;background:" +
									G.appTheme +
									";border-radius:50%;"
							},
							apivm.h("view", {
								style: "width:6px;height:6px;background:#FFF;border-radius:50%;"
							})
						),
						apivm.h(
							"view",
							{class: "flex_w"},
							apivm.h(
								"view",
								{style: "flex-direction:row; align-items: flex-start;"},
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(1) +
											"color:#333;margin-right:14px;flex:1;font-weight: 600;"
									},
									this.props.item.businessName
								),
								apivm.h(
									"text",
									{style: loadConfiguration(-2) + "color:#999;"},
									this.props.item.time
								)
							)
						)
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "flex-wrap: wrap;margin-top:10px;"},
						this.props.item.title.split("").map(function(nItem, nIndex) {
							return apivm.h(
								"text",
								{
									style:
										loadConfiguration(1) +
										"width:" +
										(nItem == " " ? "8px" : "auto") +
										";margin:2px 0;color: " +
										(this$1.props.search &&
										this$1.props.search.result &&
										this$1.props.search.result.indexOf(nItem) > -1
											? G.appTheme
											: "#666") +
										";"
								},
								nItem
							);
						})
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "margin-top:6px;"},
						this.props.item.isDelete
							? apivm.h(
									"text",
									{style: loadConfiguration(-2) + "color:" + G.appTheme + ";"},
									"已删除"
							  )
							: apivm.h(
									"view",
									{class: "flex_row"},
									apivm.h(
										"text",
										{style: loadConfiguration(-2) + "color:" + G.appTheme + ";"},
										"点击前往"
									),
									apivm.h("a-iconfont", {
										name: "xiangxia1",
										style: "transform: rotate(-90deg);",
										color: G.appTheme,
										size: G.appFontSize
									}),
									apivm.h("a-iconfont", {
										name: "xiangxia1",
										style: "transform: rotate(-90deg);margin-left:-10px;",
										color: G.appTheme,
										size: G.appFontSize
									})
							  )
					)
				)
			);
		};

		return Item14;
	})(Component);
	Item14.css = {
		".itemlist_box": {padding: "8px 16px"},
		".itemlist_warp": {
			boxShadow: "0px 2px 10px 1px rgba(24,64,118,0.08)",
			borderTopLeftRadius: "4px",
			borderTopRightRadius: "4px",
			borderBottomLeftRadius: "4px",
			borderBottomRightRadius: "4px",
			padding: "10px"
		}
	};
	apivm.define("item14", Item14);

	var Item10 = /*@__PURE__*/ (function(Component) {
		function Item10(props) {
			Component.call(this, props);
		}

		if (Component) Item10.__proto__ = Component;
		Item10.prototype = Object.create(Component && Component.prototype);
		Item10.prototype.constructor = Item10;
		Item10.prototype.openDetail = function(e) {
			openWin_activity(this.props.item);
			stopBubble$1(e);
		};
		Item10.prototype.getTagColor = function(_state) {
			var cs = {
				签到中: "#50C614",
				报名中: "#F6931C",
				进行中: "#F6931C",
				未开始: "#F6631C",
				请假通过: "#50C614",
				请假待审批: "#F6931C",
				请假中: "#F6931C"
			}[_state];
			return cs || "#666";
		};
		Item10.prototype.downFinish = function() {
			this.fire("refresh");
		};
		Item10.prototype.showDot = function() {
			return (
				(this.props.item.isRedDot == 1 || this.props.item.hasComplete === 0) &&
				!this.props.item.isDelete
			);
		};
		Item10.prototype.btnclick = function() {
			var this$1 = this;

			var item = this.props.item;
			if (item.state == "签到中") {
				actionSheet(
					{
						title: "提示",
						buttons: ["扫二维码签到", "签到口令签到"]
					},
					function(ret) {
						if (ret.buttonIndex == 1) {
							openScan(item, function(ret) {
								if (ret && ret.code == 200) {
									this$1.downFinish();
								}
							});
						} else if (ret.buttonIndex == 2) {
							G.numInputPop = {
								show: true,
								title: "签到口令",
								value: "",
								codeLength: 4,
								mask: true,
								callback: function(ret) {
									item.signInCommand = G.numInputPop.value;
									activityOption(item, function(ret) {
										if (ret) {
											if (ret.code == 200) {
												setTimeout(function() {
													G.numInputPop.show = false;
													this$1.downFinish();
												}, 300);
											} else {
												G.numInputPop.value = "";
											}
										}
									});
								}
							};
						}
					}
				);
			} else {
				activityOption(item, function(ret) {
					if (ret && ret.code == 200) {
						setTimeout(function() {
							this$1.downFinish();
						}, 300);
					}
				});
			}
		};
		Item10.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: "itemlist_box"},
				apivm.h(
					"view",
					{
						class: "itemlist_warp",
						style:
							"background-color: rgba(255, 255, 255, " +
							(G.watermark ? 0.6 : 1) +
							");",
						onClick: this.openDetail
					},
					apivm.h(
						"view",
						null,
						this.showDot() &&
							apivm.h("view", {
								style:
									loadConfigurationSize(-6) +
									"position:absolute;top: -5px;right: -3px;background: #f92323;border-radius: 50%;"
							})
					),
					apivm.h(
						"view",
						{class: "flex_row"},
						apivm.h(
							"view",
							null,
							this.props.item.state &&
								apivm.h("z-tag", {
									style: "" + loadConfiguration(-2),
									color: this.getTagColor(this.props.item.state),
									text: this.props.item.state
								})
						),
						apivm.h("view", {style: "flex:1;"}),
						apivm.h(
							"view",
							null,
							this.props.item.rightState &&
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(-2) +
											"color: " +
											this.getTagColor(this.props.item.rightState) +
											";"
									},
									this.props.item.rightState
								)
						)
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "flex-wrap: wrap;margin:10px 0;"},
						this.props.item.title.split("").map(function(nItem, nIndex) {
							return (
								nIndex < Math.floor((G.pageWidth - 52) / (G.appFontSize + 2)) * 2 &&
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(1) +
											"margin:2px 0;font-weight: 600;color: " +
											(this$1.props.search &&
											this$1.props.search.result &&
											this$1.props.search.result.indexOf(nItem) > -1
												? G.appTheme
												: "#333") +
											";"
									},
									nIndex == Math.floor((G.pageWidth - 52) / (G.appFontSize + 2)) * 2 - 1
										? "..."
										: nItem
								)
							);
						})
					),
					apivm.h(
						"view",
						null,
						this.props.item.state == "未开始" &&
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(-2) +
										"font-weight: 600;color: #333333;margin-bottom:6px;"
								},
								"报名截止时间：",
								dayjs(this.props.item.signupStopTime).format("YYYY-MM-DD HH:mm")
							)
					),
					apivm.h(
						"view",
						null,
						this.props.item.state == "已结束" &&
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(-2) +
										"font-weight: 600;color: #333333;margin-bottom:6px;"
								},
								"活动已结束"
							)
					),
					apivm.h(
						"view",
						null,
						(this.props.item.state == "签到中" ||
							this.props.item.state == "报名中" ||
							this.props.item.state == "进行中") &&
							apivm.h(
								"view",
								{class: "flex_row", style: "margin-bottom:6px;"},
								apivm.h("a-iconfont", {
									name: "daojishi",
									color: "#333",
									size: G.appFontSize
								}),
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(-2) +
											"color:#333;font-weight: 600;margin-left:5px;"
									},
									this.props.item.state == "签到中"
										? "签到"
										: this.props.item.state == "报名中"
										? "报名"
										: "活动",
									"剩余："
								),
								apivm.h(
									"a-count-down",
									{
										class: "flex_row",
										onFinish: this.downFinish,
										time:
											dayjs(
												this.props.item.state == "签到中"
													? this.props.item.joinEndTime
													: this.props.item.state == "报名中"
													? this.props.item.signupStopTime
													: this.props.item.endTime
											).valueOf() - dayjs().valueOf()
									},
									apivm.h(
										"view",
										{
											class: "countdown_time",
											style: "background:" + colorRgba(G.appTheme, 0.05)
										},
										apivm.h(
											"text",
											{style: loadConfiguration(-2) + "color:" + G.appTheme},
											"[[timeData.days]]"
										)
									),
									apivm.h(
										"view",
										{style: "width:auto;"},
										apivm.h("text", {style: loadConfiguration(-2) + "color:#333"}, "天")
									),
									apivm.h(
										"view",
										{
											class: "countdown_time",
											style: "background:" + colorRgba(G.appTheme, 0.05)
										},
										apivm.h(
											"text",
											{style: loadConfiguration(-2) + "color:" + G.appTheme},
											"[[timeData.hours]]"
										)
									),
									apivm.h(
										"view",
										{style: "width:auto;"},
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(-2) +
													"color:#333;width:" +
													G.appFontSize * 2 +
													"px;"
											},
											"小时"
										)
									),
									apivm.h(
										"view",
										{
											class: "countdown_time",
											style: "background:" + colorRgba(G.appTheme, 0.05)
										},
										apivm.h(
											"text",
											{style: loadConfiguration(-2) + "color:" + G.appTheme},
											"[[timeData.minutes]]"
										)
									),
									apivm.h(
										"view",
										{style: "width:auto;"},
										apivm.h("text", {style: loadConfiguration(-2) + "color:#333"}, "分")
									)
								)
							)
					),
					apivm.h(
						"text",
						{style: loadConfiguration(-2) + "color: #333333;margin-bottom:6px;"},
						"活动地点：",
						this.props.item.place
					),
					apivm.h(
						"text",
						{style: loadConfiguration(-2) + "color: #333333;margin-bottom:5px;"},
						"活动时间：",
						dayjs(this.props.item.startTime).format("YYYY-MM-DD HH:mm"),
						" 至 ",
						dayjs(this.props.item.startTime).year() !=
							dayjs(this.props.item.endTime).year()
							? dayjs(this.props.item.endTime).format("YYYY-MM-DD HH:mm")
							: dayjs(this.props.item.startTime).month() !=
									dayjs(this.props.item.endTime).month() ||
							  dayjs(this.props.item.startTime).date() !=
									dayjs(this.props.item.endTime).date()
							? dayjs(this.props.item.endTime).format("MM-DD HH:mm")
							: dayjs(this.props.item.endTime).format("HH:mm")
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "margin-top:10px;"},
						apivm.h(
							"text",
							{
								style: loadConfiguration() + "color: #333333;flex:1;text-align: center;"
							},
							"查看详情"
						),
						apivm.h(
							"view",
							null,
							this.props.item.canSign == 1 &&
								((this.props.item.state == "报名中" &&
									(this.props.item.joinStatus == "sign" ||
										this.props.item.joinStatus == "nosign")) ||
									(this.props.item.state == "签到中" &&
										(this.props.item.joinStatus == "sign" ||
											this.props.item.joinStatus == "join"))) &&
								apivm.h(
									"view",
									{style: "margin-left:10px;"},
									apivm.h("z-button", {
										onClick: function() {
											return this$1.btnclick(1);
										},
										disabled: this.props.item.joinStatus == "join",
										plain:
											this.props.item.state == "报名中" &&
											this.props.item.joinStatus == "sign",
										round: true,
										style: "min-width:210px;padding:7px 15px;",
										color: G.appTheme,
										text:
											this.props.item.state == "签到中" ||
											this.props.item.joinStatus == "join"
												? this.props.item.joinStatus == "join"
													? "已签到"
													: "立即签到"
												: this.props.item.joinStatus == "sign"
												? "取消报名"
												: "一键报名"
									})
								)
						)
					)
				)
			);
		};

		return Item10;
	})(Component);
	Item10.css = {
		".itemlist_box": {padding: "8px 16px"},
		".itemlist_warp": {
			boxShadow: "0px 2px 10px 1px rgba(24,64,118,0.08)",
			borderTopLeftRadius: "4px",
			borderTopRightRadius: "4px",
			borderBottomLeftRadius: "4px",
			borderBottomRightRadius: "4px",
			padding: "15px 10px"
		},
		".countdown_time": {
			minWidth: "20px",
			minHeight: "20px",
			borderRadius: "1px 1px 1px 1px",
			margin: "0 2px",
			justifyContent: "center",
			alignItems: "center",
			padding: "0 3px"
		}
	};
	apivm.define("item10", Item10);

	var NumberKeyboard = /*@__PURE__*/ (function(Component) {
		function NumberKeyboard(props) {
			Component.call(this, props);
			this.data = {
				numbers: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "0"],
				closeBase64:
					"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAAAtCAYAAAA5reyyAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAABmJLR0QAAAAAAAD5Q7t/AAAACXBIWXMAAC4jAAAuIwF4pT92AAADLElEQVRo3u2Zz0tVQRTHP/lMreiXEEREPKIg29gmSJIQWgYVoQhtSqJalv0D0SKQdq0KClpXUNaupE0JZoZWZhL9cNNvkS5mlpbaYs7tnZ7vao83b+67Mh+43PO+9zh35jhnftwBj8fj8Xg8Hg+wHfgAzJTo1QWsjztIc9FfAkGa7+q22eBFlgM4AVQAAXBDKlxm+R35Mi3tPACsAqaA8pjrFMkoJmh9cVckB31St1GbhdruHdNyr3ARkTwJ6zRdUClZuOjKm4GDYt8CnojdBGzF9Ijz0rB1wFF53o8ZBgDqgd1iXwbeY/75J4AVwAvgujzfBuwT+x7Q6aCN1ggwaTKgtCYyA/gppfcovUq0BqU9Vr5tSm8QrUppPcq3VekXlD4gWmCzwS4G+F8R9o8cvjq9JpU9EeGTq6yvyv5S7Ma5SOFHwHFgMXBX6aeBWsy6MQzQIHAMqMSkZcg1YFj8BkWbAJoxaf/UQTucEDA7hV1zmEwKn1F6YlPYNR9dvsxFCm8AGjFj1wxmUVsG3ASGxKcZ2Ai8Ba6KtgnYL3Y78Fr5poEx4AowjlmitGCGiTqXAbRNwOwUbiT3luqI8hkWbURpLcq3RekjSk+LVh3xjrPq7xKbwt//Q/8s908Rz7WtfX7LfUou5+1zkcIPgJ1i6xTuVz7NwFoygQToAHaJ/TzLdw0mqKH/N2AHZm0YBrKCTNonhoD4Z+EoEpvCCxqXs3AKuAM8E30PUEPusSsfyoBXwG35XQ0cwszI94GHDtpojYC5Z2G9F+6mOB9Ja5V+UemJTeFxZY8pe9jiO3RZer8dFLtxLlK4E/M5qpx/e+ZJ4Bzws8DyqzD76ZAhzIy8FHjpoH1WCfCzcEGk5D5ZUCnFIaxTqqBSsrCdwkvkngYuUVqHSmn5XYrHDX/pIP5jy/mu3riDNBfVmLVX3EGKut5gzmGsYftcOKQOWEn+i+Qp4B3RJ2erMWe7+ZLC7J27yHyAsEKxAlgINZiPBDNZ+nLMGUehy54FTSVmSxaVgvVxVzAp9DI7eHvjrlSSWIY52wiD1xp3hZLIFkzw2uOuSJKpw/RGj8fj8XgWHn8A4dKLb2WSDzAAAAAASUVORK5CYII=",
				backBase64:
					"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAAAtCAYAAAA5reyyAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAABmJLR0QAAAAAAAD5Q7t/AAAACXBIWXMAAC4jAAAuIwF4pT92AAAC/UlEQVRo3u2aO2gUURSGv1k1PlFXUHwUQZEYlUACPiM2KbQSKxEkBEtbHyDxAWqjoK2gqIjYWJpC0gQLQUWjoBJUtBOMiWJMwJiYiGtxdtlzx1lndr07w4X7wcBm59xzcv655752wOPxeDwej8dJggxjtwGHgHagsQ7+fwHvgJvA1QzzrAv7gUKK12NgQdZJ22J3yuKVruf1SCbtEu4Cbqm/nyEl9hKYBGZYivMbyCMP66j6/gxwNuWcrXEMs0fcSynuKRXzY9Yi1EoHpnivUoydRyaUAvApayFqYQtSUiXxPgPLUoyfA0aKsYeyFqNaOjHFewisSNCuHRm7ZiaM0wQcB1ZH3JsNfMXBHniAv5cSSQgo95gBYH6M/XJggso9bI7y54yAzZjijZB8xg+AMZKNlw3Aa2U7HGHjnIAbgO8qqTFgY5U+9mI+gNsRNguBF5jirYuwc0rAFsozXqkEV9boaxvmg3iAjGcArcCguvcIWFLBj1MCvlVJvQHm/qe/9cC48nkf2E55zCsAH2J8OCPgVsyyW2PJbwuVt2fjwNqY9s4IeBpzuWKTDmSM0+L1A6sStK2rgDmLvvLq86Dl/7MfGVs1ozi8NYuik3Lv+GbR7yxErKgSfkL8AYQzJbwolNxFCz4D4KnyOQQcDsXpjfHhjIAAR7AnYiNSuiVf75FZGWSXM63u3fiHH6cEBBFNi3i3Bh9NmLuRAWBeyKYNmMKcuKLK2TkBAS5hininyvYDqu0UsuuIohX4qWz7ImycFBDkwFSL2F1F21KbaWBzjG34rDGMswICXA4ldyVhuz2I4M0J7XcBJ4GdEfecFhDgPKaIPSnHD4AvOHqgWiI8Jl5LMXYD8oOV0wKCrNe0iCdSitulYjpZwppwOfcCO5BeEiDLEBtXDlgMHMRc6ly3nVAWr3acQw4eNMPFRG3tzQuIgPpthB/I4cNoBjlb5wLpvpUwAWyqRyK23gSolj7kxGYpUgUB0gNtXpPFGD3APuS3E4/H4/F4PB6Pxwp/AGH7f53PGS6/AAAAAElFTkSuQmCC"
			};
		}

		if (Component) NumberKeyboard.__proto__ = Component;
		NumberKeyboard.prototype = Object.create(Component && Component.prototype);
		NumberKeyboard.prototype.constructor = NumberKeyboard;
		NumberKeyboard.prototype.installed = function() {
			if (this.props.dataMore.isRandom) {
				this.data.numbers.sort(this.randomsort);
			}
		};
		NumberKeyboard.prototype.randomsort = function(a, b) {
			return Math.random() > 0.5 ? -1 : 1;
		};
		NumberKeyboard.prototype.closeBoard = function(e) {
			this.fire("close");
		};
		NumberKeyboard.prototype.getNumber = function(_item) {
			if (_item == -1) {
				_item = this.data.numbers[this.data.numbers.length - 1];
			}
			if (this.props.dataMore.value.length < this.props.dataMore.codeLength) {
				this.props.dataMore.value += _item;
			}
			if (this.props.dataMore.value.length == this.props.dataMore.codeLength) {
				this.fire("finish");
			}
		};
		NumberKeyboard.prototype.delNumber = function(e) {
			if (this.props.dataMore.value) {
				this.props.dataMore.value = this.props.dataMore.value.substring(
					0,
					this.props.dataMore.value.length - 1
				);
			}
		};
		NumberKeyboard.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: "nkb"},
				apivm.h(
					"view",
					{class: "watermark_box"},
					G.watermark &&
						apivm.h("image", {
							class: "xy_100",
							src: G.watermark,
							mode: "aspectFill",
							thumbnail: "false"
						})
				),
				apivm.h(
					"view",
					{class: "nkb-i-c"},
					(Array.isArray(this.data.numbers)
						? this.data.numbers
						: Object.values(this.data.numbers)
					).map(function(item$1, index$1) {
						return apivm.h(
							"view",
							{
								class: "nkb-i",
								style:
									"display:" +
									(index$1 != this$1.data.numbers.length - 1 ? "block" : "none")
							},
							apivm.h(
								"view",
								{
									class: "nkb-i-l",
									onClick: function() {
										return this$1.getNumber(item$1);
									}
								},
								apivm.h("text", {style: "font-size:28px;"}, item$1)
							)
						);
					}),
					apivm.h(
						"view",
						{class: "nkb-i"},
						apivm.h(
							"view",
							{class: "nkb-i-l", onClick: this.closeBoard},
							apivm.h("image", {
								class: "nkb-i-i",
								src: this.data.closeBase64,
								mode: "widthFix"
							})
						)
					),
					apivm.h(
						"view",
						{class: "nkb-i"},
						apivm.h(
							"view",
							{
								class: "nkb-i-l",
								onClick: function() {
									return this$1.getNumber(-1);
								}
							},
							apivm.h(
								"text",
								{style: "font-size:28px;"},
								this.data.numbers[this.data.numbers.length - 1]
							)
						)
					),
					apivm.h(
						"view",
						{class: "nkb-i"},
						apivm.h(
							"view",
							{class: "nkb-i-l", onClick: this.delNumber},
							apivm.h("image", {
								class: "nkb-i-i",
								src: this.data.backBase64,
								mode: "widthFix"
							})
						)
					)
				)
			);
		};

		return NumberKeyboard;
	})(Component);
	NumberKeyboard.css = {
		".nkb": {alignItems: "center", width: "100%", backgroundColor: "#FFF"},
		".nkb-i-c": {
			flexFlow: "row wrap",
			justifyContent: "space-around",
			alignItems: "center",
			padding: "10px"
		},
		".nkb-i": {flexBasis: "33%", boxSizing: "border-box", padding: "5px"},
		".nkb-i-l": {
			display: "flex",
			padding: "5px",
			borderRadius: "5px",
			width: "100%",
			height: "48px",
			alignItems: "center",
			justifyContent: "center"
		},
		".nkb-i-i": {width: "60px"}
	};
	apivm.define("number-keyboard", NumberKeyboard);

	var PasswordInput = /*@__PURE__*/ (function(Component) {
		function PasswordInput(props) {
			Component.call(this, props);
			this.data = {
				cursorIndex: false,
				cursorTask: null
			};
			this.compute = {
				codeArr: function() {
					var aar = [];
					for (var index = 0; index < this.props.dataMore.codeLength; index++) {
						aar[index] = null;
					}
					return aar;
				}
			};
		}

		if (Component) PasswordInput.__proto__ = Component;
		PasswordInput.prototype = Object.create(Component && Component.prototype);
		PasswordInput.prototype.constructor = PasswordInput;
		PasswordInput.prototype.installed = function() {
			this.startCursor();
		};
		PasswordInput.prototype.codeInput = function(e) {
			this.fire("codeClick", "");
		};
		PasswordInput.prototype.startCursor = function() {
			var this$1 = this;

			this.data.cursorIndex = !this.data.cursorIndex;
			this.data.cursorTask && clearTimeout(this.data.cursorTask);
			this.data.cursorTask = setTimeout(function() {
				this$1.startCursor();
			}, 600);
		};
		PasswordInput.prototype.getCursorShow = function(index) {
			return this.data.cursorIndex && index == this.props.dataMore.value.length;
		};
		PasswordInput.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: "password-input_security"},
				(Array.isArray(this.codeArr)
					? this.codeArr
					: Object.values(this.codeArr)
				).map(function(item$1, index$1) {
					return apivm.h(
						"view",
						{
							class: "password-input_item",
							style: "margin-left:" + (index$1 ? "16" : "0") + "px;",
							onClick: this$1.codeInput
						},
						this$1.props.dataMore.value[index$1] && !this$1.props.dataMore.mask
							? apivm.h("text", {class: "password-input_item-word--hidden"})
							: null,
						this$1.props.dataMore.value[index$1] && this$1.props.dataMore.mask
							? apivm.h(
									"text",
									{
										class: "password-input_item-word--mask",
										style: loadConfiguration(4) + "color: #333333;font-weight: 600;"
									},
									this$1.props.dataMore.value[index$1]
							  )
							: null,
						this$1.getCursorShow(index$1) &&
							apivm.h("view", {
								class: "password-input_item-cursor",
								style: "height: " + (G.appFontSize + 10) + "px;"
							})
					);
				})
			);
		};

		return PasswordInput;
	})(Component);
	PasswordInput.css = {
		".password-input_security": {flexFlow: "row", justifyContent: "center"},
		".password-input_item": {
			width: "48px",
			height: "48px",
			borderRadius: "2px",
			background: "#F8F8F8",
			alignItems: "center",
			justifyContent: "center"
		},
		".password-input_item-word--hidden": {
			width: "10px",
			height: "10px",
			backgroundColor: "#000",
			borderRadius: "100%"
		},
		".password-input_item-word--mask": {textAlign: "center"},
		".password-input_item-cursor": {
			position: "absolute",
			width: "1px",
			left: "24px",
			top: "9px",
			background: "#666"
		}
	};
	apivm.define("password-input", PasswordInput);

	var InputNum = /*@__PURE__*/ (function(Component) {
		function InputNum(props) {
			Component.call(this, props);
		}

		if (Component) InputNum.__proto__ = Component;
		InputNum.prototype = Object.create(Component && Component.prototype);
		InputNum.prototype.constructor = InputNum;
		InputNum.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		InputNum.prototype.finish = function() {
			G.numInputPop.callback();
		};
		InputNum.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				this.props.dataMore.show && [
					apivm.h(
						"view",
						{
							style:
								"flex:1;height:1px;align-items: center; justify-content: center;width:100%;"
						},
						apivm.h(
							"view",
							{class: "numInput_warp xy_center"},
							apivm.h(
								"view",
								{class: "watermark_box"},
								G.watermark &&
									apivm.h("image", {
										class: "xy_100",
										src: G.watermark,
										mode: "aspectFill",
										thumbnail: "false"
									})
							),
							apivm.h(
								"view",
								{class: "numInput_title flex_row"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "numInput_close xy_center"
									},
									apivm.h("a-iconfont", {
										name: "cuohao",
										color: "#666666",
										size: G.appFontSize + 6
									})
								),
								apivm.h(
									"text",
									{style: loadConfiguration(1) + "color: #333;font-weight: 600;"},
									this.props.dataMore.title
								)
							),
							apivm.h(
								"view",
								{style: "width:100%;padding:9px 9px 25px;"},
								apivm.h("password-input", {dataMore: this.props.dataMore})
							)
						)
					),
					apivm.h("number-keyboard", {
						dataMore: this.props.dataMore,
						onFinish: this.finish,
						onClose: function() {
							return this$1.closePage();
						}
					}),
					apivm.h("view", {
						style: "background:#FFF;padding-bottom:" + safeArea().bottom + "px;"
					})
				]
			);
		};

		return InputNum;
	})(Component);
	InputNum.css = {
		".numInput_warp": {
			background: "#FFF",
			borderRadius: "10px",
			minWidth: "320px"
		},
		".numInput_title": {
			justifyContent: "center",
			height: "54px",
			width: "100%",
			minWidth: "320px"
		},
		".numInput_close": {
			position: "absolute",
			right: "0",
			height: "54px",
			width: "54px"
		}
	};
	apivm.define("input-num", InputNum);

	var ZTabsThree = /*@__PURE__*/ (function(Component) {
		function ZTabsThree(props) {
			Component.call(this, props);
		}

		if (Component) ZTabsThree.__proto__ = Component;
		ZTabsThree.prototype = Object.create(Component && Component.prototype);
		ZTabsThree.prototype.constructor = ZTabsThree;
		ZTabsThree.prototype.tabClick = function(_item) {
			if (_item.readonly) {
				this.fire("readonly", _item);
				return;
			}
			if (this.props.dataMore.key == _item.key) {
				return;
			}
			this.props.dataMore.key = _item.key;
			this.fire("change", {key: this.props.dataMore.key});
		};
		ZTabsThree.prototype.nTouchmove = function() {
			touchmove();
		};
		ZTabsThree.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					id: "" + (this.props.id || ""),
					style: "" + (this.props.style || ""),
					class: "tabs_three_box flex_row " + (this.props.class || "")
				},
				(this.props.dataMore.data || []).map(function(item, index, list) {
					return [
						apivm.h(
							"view",
							{
								style:
									"background:" +
									(this$1.props.dataMore.key == item.key
										? this$1.props.color || G.appTheme
										: "transparent") +
									";",
								onClick: function() {
									return this$1.tabClick(item);
								},
								onTouchStart: this$1.nTouchmove,
								onTouchMove: this$1.nTouchmove,
								onTouchEnd: this$1.nTouchmove
							},
							apivm.h("view", {
								class: "tabs_three_line",
								style: {display: index ? "flex" : "none"}
							}),
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(this$1.props.size + 1) +
										";margin:2px 12px;font-weight: 600;color:#" +
										(this$1.props.dataMore.key == item.key ? "fff" : "333") +
										";"
								},
								item.value
							)
						)
					];
				})
			);
		};

		return ZTabsThree;
	})(Component);
	ZTabsThree.css = {
		".tabs_three_box": {
			border: "1px solid #333",
			borderRadius: "2px",
			overflow: "hidden"
		},
		".tabs_three_line": {
			position: "absolute",
			top: "0",
			bottom: "0",
			width: "1px",
			background: "#333"
		}
	};
	apivm.define("z-tabs-three", ZTabsThree);

	var ZAlert = /*@__PURE__*/ (function(Component) {
		function ZAlert(props) {
			Component.call(this, props);
			this.data = {
				show: false,

				inputBox: {
					dotIcon: true,
					value: "",
					placeholder: "",
					autoFocus: true,
					cleanFocus: true,
					height: 150,
					confirmType: ""
				},

				inputBoxPass: {
					dotIcon: true,
					value: "",
					placeholder: "",
					inputType: "password",
					autoFocus: false,
					cleanFocus: true,
					height: 150,
					confirmType: ""
				},

				marginBottom: 0,
				timeout: 0
			};
			this.compute = {
				monitor: function() {
					var dm = this.props.dataMore;
					if (dm.show != this.data.show) {
						this.data.inputBox.autoFocus = true;
						this.data.show = dm.show;
						this.data.marginBottom = 0;
						if (this.data.show) {
							this.data.timeout = dm.timeout || 0;
							if (this.data.timeout > 0) {
								this.startTime();
							}
							if (
								this.props.dataMore.type == "input" ||
								this.props.dataMore.type == "textarea"
							) {
								this.data.inputBox.value = dm.content;
								this.data.inputBox.placeholder = dm.placeholder;
								this.data.inputBox.confirmType = dm.confirmType || "done";

								this.data.inputBoxPass.value = dm.content2;
								this.data.inputBoxPass.placeholder = dm.placeholder2;
								this.data.inputBoxPass.confirmType = dm.confirmType2 || "done";
							}
						}
					}
				}
			};
		}

		if (Component) ZAlert.__proto__ = Component;
		ZAlert.prototype = Object.create(Component && Component.prototype);
		ZAlert.prototype.constructor = ZAlert;
		ZAlert.prototype.closePage = function(_type) {
			this.props.dataMore.show = false;
			if (!_type) {
				G.alertPop.callback({buttonIndex: 2});
			}
		};
		ZAlert.prototype.closeStop = function(e) {
			stopBubble$1(e);
		};
		ZAlert.prototype.inputFocus = function(e) {
			if (platform() == "app" && api.systemType == "ios") {
				this.data.marginBottom = e.detail.height;
			}
		};
		ZAlert.prototype.inputBlur = function(e) {
			this.data.marginBottom = 0;
		};
		ZAlert.prototype.itemClick = function() {
			if (this.data.timeout > 0) {
				return;
			}
			if (
				this.props.dataMore.type == "input" ||
				this.props.dataMore.type == "textarea"
			) {
				this.props.dataMore.content = this.data.inputBox.value;
				this.props.dataMore.content2 = this.data.inputBoxPass.value;
			}
			this.props.dataMore.buttonIndex = 1;
			G.alertPop.callback(this.props.dataMore);
			this.closePage(1);
		};
		ZAlert.prototype.startTime = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.data.timeout--;
				if (this$1.data.timeout >= 0) {
					this$1.startTime();
				} else if (this$1.props.dataMore.autoClose) {
					this$1.itemClick();
				}
			}, 1000);
		};
		ZAlert.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box xy_center",
					onClick: this.closeStop,
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);z-index:1001;"
				},
				this.data.show && [
					apivm.h(
						"view",
						{
							class: "alert_warp",
							style: "margin-bottom:" + this.data.marginBottom + "px;",
							onClick: this.closeStop
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							null,
							(this.props.dataMore.title || this.props.dataMore.closeType == "2") &&
								apivm.h(
									"view",
									{style: "padding: 20px 20px 0;"},
									this.props.dataMore.title &&
										apivm.h(
											"text",
											{class: "alert_title", style: "" + loadConfiguration(4)},
											this.props.dataMore.title
										),
									apivm.h(
										"view",
										{
											style:
												"display:" +
												(this.props.dataMore.closeType == "2" ? "flex" : "none") +
												";position:absolute;right:0;top:0;"
										},
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.closePage();
												},
												style: "padding:20px 20px 10px 10px;"
											},
											apivm.h("a-iconfont", {
												name: "cuohao",
												color: "#666",
												size: G.appFontSize + 4
											})
										)
									)
								)
						),
						apivm.h(
							"scroll-view",
							{class: "alert_content_box", "scroll-y": true},
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "input" &&
									apivm.h("z-input", {
										id: "input",
										dataMore: this.data.inputBox,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.inputPassword &&
									apivm.h("z-input", {
										id: "password",
										style: "margin-top:15px;",
										dataMore: this.data.inputBoxPass,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "textarea" &&
									apivm.h("z-textarea", {
										class: "alert_textarea",
										dataMore: this.data.inputBox,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "richText" &&
									apivm.h("z-rich-text", {
										detail: true,
										nodes: this.props.dataMore.content
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "text" &&
									apivm.h(
										"text",
										{class: "alert_content", style: "" + loadConfiguration(1)},
										this.props.dataMore.content
									)
							)
						),
						apivm.h(
							"view",
							null,
							this.props.dataMore.closeType == "1" && [
								apivm.h(
									"view",
									{style: "width:100%;height:1px;padding:0 15px;flex-shrink: 0;"},
									apivm.h("view", {style: "height:1px;background: #F6F6F6;"})
								),
								apivm.h(
									"view",
									{class: "alert_btn_box"},
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.closePage();
											},
											class: "alert_btn_item",
											style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}
										},
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color:" +
													(this.props.dataMore.cancel.color == "appTheme"
														? G.appTheme
														: this.props.dataMore.cancel.color) +
													";"
											},
											this.props.dataMore.cancel.text
										)
									),
									apivm.h(
										"view",
										{style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}},
										apivm.h("view", {style: "width:1px;height:30px;background:#F6F6F6;"})
									),
									apivm.h(
										"view",
										{
											onClick: this.itemClick,
											class: "alert_btn_item",
											style: {display: this.props.dataMore.sure.show ? "flex" : "none"}
										},
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color:" +
													(this.props.dataMore.sure.color == "appTheme"
														? G.appTheme
														: this.props.dataMore.sure.color) +
													";opacity:" +
													(this.data.timeout > 0 ? "0.5" : "1") +
													";"
											},
											this.props.dataMore.sure.text,
											this.data.timeout > 0 ? "(" + this.data.timeout + ")" : ""
										)
									)
								)
							]
						),
						apivm.h(
							"view",
							null,
							this.props.dataMore.closeType != "1" &&
								this.props.dataMore.sure.show &&
								apivm.h(
									"view",
									{style: "padding-bottom:15px;", class: "alert_btn_box"},
									apivm.h(
										"z-button",
										{
											style: "width:210px;",
											disabled: this.data.timeout > 0,
											color:
												this.props.dataMore.sure.color == "appTheme"
													? G.appTheme
													: this.props.dataMore.sure.color,
											round: true,
											onClick: this.itemClick
										},
										this.props.dataMore.sure.text,
										this.data.timeout > 0 ? "(" + this.data.timeout + ")" : ""
									)
								)
						)
					),
					apivm.h(
						"view",
						null,
						this.props.dataMore.closeType == "3" &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.closePage();
									},
									style: "margin-top:15px;"
								},
								apivm.h("a-iconfont", {
									style: "transform: rotate(45deg);",
									name: "gengduo1",
									color: "#FFF",
									size: G.appFontSize + 26
								})
							)
					)
				]
			);
		};

		return ZAlert;
	})(Component);
	ZAlert.css = {
		".alert_warp": {background: "#FFF", borderRadius: "10px", width: "320px"},
		".alert_content_box": {
			margin: "20px 15px",
			maxHeight: "385px",
			width: "auto"
		},
		".alert_title": {color: "#333333", fontWeight: "bold", textAlign: "center"},
		".alert_content": {
			width: "100%",
			textAlign: "center",
			color: "#333333",
			wordWrap: "break-word"
		},
		".alert_btn_box": {
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_btn_item": {
			flex: "1",
			padding: "10px",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_textarea": {
			borderColor: "#ccc !important",
			borderRadius: "10px",
			padding: "8px",
			width: "100%"
		}
	};
	apivm.define("z-alert", ZAlert);

	var Root = /*@__PURE__*/ (function(Component) {
		function Root(props) {
			Component.call(this, props);
			this.data = {
				pageParam: {},
				pageType: "",
				sStyle: "",
				dTitle: "",
				MG: !this.props.dataMore ? G : null,
				emptyBox: {
					type: "load",
					text: ""
				},

				pageNo: 1,
				pageSize: 15,
				refreshPageSize: 0,
				listData: [],

				tabBox: {key: "", type: "1", average: false, data: []},

				inputBox: {
					show: false,
					value: "",
					placeholder: "请输入关键词"
				},

				listUnread: {show: false, code: "", data: []},

				rightTextBtn: {
					show: false,
					text: "",
					type: ""
				},

				titleBox: {
					show: false,
					key: "",
					type: "",
					style: "",
					data: [],
					min: 0
				},

				showOtherPage: {
					news: {
						show: false,
						closeH: true,
						pageParam: {module: "6"}
					}
				},

				filters: {
					show: false,
					data: []
				},

				suspendedBtn: {
					//右下角悬浮按钮
					show: true,
					data: []
				},

				titleHint: {
					show: false
				}
			};
		}

		if (Component) Root.__proto__ = Component;
		Root.prototype = Object.create(Component && Component.prototype);
		Root.prototype.constructor = Root;
		Root.prototype.onShow = function() {
			G.onShowNum++;
		};
		Root.prototype.baseInit = function(ref) {
			var detail = ref.detail;

			this.code = this.data.pageParam.code || "50_4";
			var gray = "background:rgba(0,0,0,0.03);";
			switch (this.code) {
				case "10":
					this.data.tabBox.data = [
						{key: "0", value: "全部", badge: 0},
						{key: "2", value: "进行中", badge: 0},
						{key: "1", value: "未开始", badge: 0},
						{key: "3", value: "已结束", badge: 0},
						{key: "4", value: "我的", badge: 0}
					];

					this.data.tabBox.key = "0";
					this.data.sStyle = gray;
					break;
				case "14":
					this.data.rightTextBtn.show = true;
					this.data.rightTextBtn.text = "全部已读";
					this.data.rightTextBtn.type = "allRead";
					this.data.rightTextBtn.code = module14.businessCode;
					this.data.sStyle = gray;
					break;
				case "22":
					this.data.rightTextBtn.show = true;
					this.data.rightTextBtn.text = "全部已读";
					this.data.rightTextBtn.type = "allRead";
					this.data.rightTextBtn.code = module22.businessCode;
					this.data.listUnread.show = true;
					this.data.listUnread.code = module22.businessCode;
					this.data.tabBox.type = "2";
					this.data.tabBox.key = null;
					this.data.sStyle = gray;
					break;
				case "31":
					this.data.rightTextBtn.show = true;
					this.data.rightTextBtn.text = "成绩单";
					this.data.rightTextBtn.type = "transcript";
					this.data.tabBox.average = true;
					this.data.titleBox.show = true;
					this.data.titleBox.data = [
						{key: "1", value: "学习资料", badge: 0},
						{key: "0", value: "培训考试", badge: 0}
					];

					this.data.titleBox.key = this.data.pageParam.titleKey || "1";
					this.tabChange(3);
					this.data.titleBox.style = "margin-left:-25px;";
					this.data.sStyle = gray;
					break;
				case "50_2":
					var data = [
						{key: "0", value: "所有", badge: 0},
						{key: "2", value: "未回复", badge: 0},
						{key: "3", value: "已回复", badge: 0},
						{key: "4", value: "已评价", badge: 0}
					];

					// if(!this.pageParam.mine && (G.isAdmin || getItemForKey('station_admin',G.specialRoleKeys) || getItemForKey('station_worker',G.specialRoleKeys))){//有管理身份
					// 	data.splice(1,0,{key: "1", value: "待审核",badge:0});
					// }
					this.data.tabBox.data = data;
					this.data.tabBox.key = "0";
					this.data.sStyle = gray;
					break;
				case "50_3":
					this.data.tabBox.data = [
						{key: "0", value: "所有", badge: 0},
						{key: "2", value: "进行中", badge: 0},
						{key: "1", value: "未开始", badge: 0},
						{key: "3", value: "已结束", badge: 0}
					];

					this.data.tabBox.key = "0";
					this.data.sStyle = gray;
					break;
				case "50_4":
					this.data.tabBox.key = null;
					this.data.tabBox.data = [
						{key: null, value: "全部", badge: 0},
						{key: "unread", value: "未读", badge: 0}
					];
					this.data.tabBox.type = "2";
					this.data.titleBox.min = 1;
					break;
				case "50_6":
					var admin =
						G.isAdmin ||
						getItemForKey("station_worker", G.specialRoleKeys) ||
						getItemForKey("station_admin", G.specialRoleKeys);
					if (admin) {
						this.data.tabBox.data = [
							{key: "myPublish", value: "我发起的", badge: 0},
							{key: "myJoin", value: "我加入的", badge: 0}
						];

						this.data.tabBox.average = true;
						this.data.tabBox.key = "myPublish";
					} else {
						this.data.tabBox.key = "myJoin";
					}
					this.data.sStyle = gray;
					break;
				case "50_7":
					this.isAdmin =
						G.isAdmin ||
						getItemForKey("station_worker", G.specialRoleKeys) ||
						getItemForKey("station_admin", G.specialRoleKeys);
					this.isMember =
						getItemForKey(
							(G.sysSign == "rd" ? "npc" : "cppcc") + "_member",
							G.specialRoleKeys
						) ||
						getItemForKey(
							(G.sysSign == "rd" ? "npc" : "cppcc") + "_nation_member",
							G.specialRoleKeys
						) ||
						getItemForKey("station_member", G.specialRoleKeys);
					if (this.isMember) {
						this.data.suspendedBtn.data = [
							{
								show: true,
								key: "add",
								value: "",
								type: "icon",
								src: "xinzeng",
								size: 5,
								addParam: {paramType: "addDutieshelp", title: "dTitle"}
							}
						];

						this.data.tabBox.data = [
							{key: "all", value: "所有", badge: 0},
							{key: "my", value: "我的", badge: 0}
						];

						this.data.tabBox.average = true;
						this.data.tabBox.key = "all";
					}
					break;
				case "50_7_re":
					this.data.tabBox.data = [
						{key: null, value: "全部", badge: 0},
						{key: "0", value: "待审核", badge: 0},
						{key: "1", value: "已通过", badge: 0},
						{key: "2", value: "未通过", badge: 0}
					];

					this.data.tabBox.average = true;
					this.data.tabBox.key = null;
					break;
				case "54":
					this.data.suspendedBtn.data = [
						{show: true, key: "join", value: "加入", type: "text", src: "", size: 5},
						{
							show: true,
							key: "add",
							value: "发起",
							type: "text",
							src: "",
							size: 5,
							addParam: {paramType: "addVideoconference", title: "发起会议"}
						}
					];

					break;
			}

			this.data.inputBox.show = true;
			this.pageRefresh();
		};
		Root.prototype.pageRefresh = function() {
			this.getData(0);
		};
		Root.prototype.close = function() {
			if (this.props.dataMore) {
				this.props.dataMore.show = false;
			} else {
				closeWin();
			}
		};
		Root.prototype.getModuleLists = function(_param, _callback, _again) {
			var this$1 = this;

			this.data.filters.data.forEach(function(_eItem) {
				if (isNumber(_eItem.value) || _eItem.value) {
					_param.param.query[_eItem.key] = _eItem.value;
				}
			});
			switch (_param.code) {
				case "10":
					_param.param.activityStatus = this.data.tabBox.key;
					getList10(_param, _callback);
					break;
				case "14":
					this.data.dTitle = "消息";
					getList14(_param, _callback);
					break;
				case "22":
					if (_param.param.pageNo == 1) {
						getColumn22({}, function(ret) {
							if (ret && ret.dealWith) {
								this$1.data.tabBox.data = [
									{key: null, value: "全部", badge: 0},
									{key: "unread", value: "未读", badge: 0}
								].concat(ret.dealWith);
							}
						});
					}
					_param.param.query.channelId = this.data.tabBox.key;
					if (this.data.tabBox.key === "unread") {
						_param.param.hasRead = 0;
						_param.param.query.channelId = null;
					}
					getList22(_param, _callback);
					break;
				case "31":
					if (_param.param.pageNo == 1 && !_again) {
						getColumn31({}, function(ret) {
							var data = ret ? ret.dealWith || [] : [];
							if (!isArray(data) || !data.length) {
								dealData(0, this$1, ret);
								return;
							}
							this$1.data.tabBox.data = data;
							if (
								!this$1.data.tabBox.key ||
								!getItemForKey(this$1.data.tabBox.key, this$1.data.tabBox.data)
							) {
								this$1.data.tabBox.key = this$1.data.tabBox.data[0].key;
								this$1.getModuleLists(_param, _callback, true);
							}
						});
						if (!this.data.tabBox.data.length) {
							return;
						}
					}
					_param.param.examineStatus = this.data.tabBox.key;
					getList31(_param, _callback);
					break;
				case "31_1":
					getList31_1(_param, _callback);
					break;
				case "apptext":
					this.data.inputBox.show = false;
					_param.pt = this.data.pageParam.pt || "qtbz";
					getListApptext(_param, _callback);
					break;
				case "50_1":
					_param.param.query.columnId = this.data.tabBox.key || null;
					_param.param.query.businessCode =
						this.data.pageParam.businessCode || "pin";
					_param.param.query.stationId =
						this.data.pageParam.stationId || "1729690690503467009";
					_param.param.tableId = "id_station_" + _param.param.query.businessCode;
					if (_param.param.pageNo == 1) {
						getColumn50_1(_param, function(ret) {
							if (ret && ret.dealWith) {
								this$1.data.tabBox.data = [{key: "", value: "全部", badge: 0}].concat(
									ret.dealWith
								);
							}
						});
					}
					getList50_1(_param, _callback);
					break;
				case "50_2":
					this.data.dTitle = this.data.pageParam.mine ? "我的留言" : "民情民意";
					_param.param.status = this.data.tabBox.key;
					_param.url =
						appUrl() +
						"stationLetter/list" +
						(this.data.pageParam.mine ? "mine" : "app");
					getList50_2(_param, _callback);
					break;
				case "50_3":
					this.data.dTitle = "活动";
					_param.param.activityStatus = this.data.tabBox.key;
					_param.url = appUrl() + "stationActivity/listmine";
					getList50_3(_param, _callback);
					break;
				case "50_4":
					this.data.dTitle = "任务接收";
					if (!this.workstations && !this.hasReq) {
						getWorkerStation({}, function(ret) {
							var data = ret ? ret.dealWith || [] : [];
							if (isArray(data) && data.length) {
								this$1.workstations = {
									show: true,
									type: "select",
									sType: "line",
									open: false,
									key: "stationId",
									title: "所属" + (G.sysSign == "rd" ? "站点" : "工作室"),
									value: "",
									defaultValue: "",
									data: data
								};
								this$1.data.filters.data = [this$1.workstations];
							} else {
								this$1.hasReq = true;
							}
							this$1.getModuleLists(_param, _callback);
						});
						return;
					}
					_param.param.stationId = _param.param.query.stationId || null;
					delete _param.param.query.stationId;
					if (this.data.tabBox.key === "unread") {
						_param.param.hasRead = 0;
					}
					getList50_4(_param, _callback);
					break;
				case "50_6":
					this.data.dTitle = "视频会议";
					_param.param.optionType = this.data.tabBox.key;
					getList50_6(_param, _callback);
					break;
				case "50_7":
					this.data.dTitle = "履职补录";
					_param.url =
						appUrl() +
						"stationdutiesroom/" +
						(this.data.tabBox.key == "my" ? "my" : "") +
						"listapp";
					getList50_7(_param, _callback);
					if (this.isAdmin) {
						ajax(
							{u: appUrl() + "stationdutiesroom/nopasscount"},
							"nopasscount",
							function(ret, err) {
								var num = ret ? ret.data || 0 : 0;
								if (isNumber(num) && num > 0) {
									this$1.data.titleHint = {
										show: true,
										text: "存在 " + num + " 条履职动态待审核！",
										key: "review_50_7"
									};
								} else {
									this$1.data.titleHint.show = false;
								}
							},
							"待审核数量",
							"post",
							{
								body: JSON.stringify({})
							}
						);
					}
					break;
				case "50_7_re":
					this.data.dTitle = "履职动态审核";
					_param.param.query.passStatus = this.data.tabBox.key || null;
					getList50_7_re(_param, _callback);
					break;
				case "53":
					this.data.dTitle = "反馈历史";
					getList53(_param, _callback);
					break;
				case "54":
					this.data.dTitle = "视频会议";
					getList54(_param, _callback);
					break;
			}

			if (this.data.listUnread.show && _param.param.pageNo == 1) {
				getListUnread(this.data.listUnread, function(ret) {
					this$1.setUnread();
				});
			}
		};
		Root.prototype.getData = function(_type) {
			var this$1 = this;

			if (_type == -1) {
				this.data.emptyBox.type = "load";
				this.data.emptyBox.text = "";
				this.data.listData = [];
				this.data.inputBox.value = "";
				_type = 0;
			}
			var postParam = {
				pageNo: !_type ? 1 : this.data.pageNo,
				pageSize:
					!_type && this.data.refreshPageSize > this.data.pageSize
						? this.data.refreshPageSize
						: this.data.pageSize,
				keyword: this.data.inputBox.value,
				query: {}
			};

			this.getModuleLists(
				{code: this.code, param: postParam, pageParam: this.data.pageParam},
				function(ret, err) {
					this$1.data.inputBox.result = this$1.data.inputBox.value;
					var data = ret ? ret.dealWith || [] : [];
					if (!isArray(data) || !data.length) {
						dealData(_type, this$1, ret);
						return;
					}
					if (!_type) {
						this$1.data.listData = data;
					} else {
						this$1.data.listData = this$1.data.listData.concat(data);
					}
					this$1.setUnread();
					this$1.data.emptyBox.type = "";
					this$1.data.emptyBox.text =
						data.length >= postParam.pageSize ? LOAD_MORE : LOAD_ALL;
					this$1.data.pageNo =
						Math.ceil(this$1.data.listData.length / this$1.data.pageSize) + 1;
					this$1.data.refreshPageSize =
						Math.ceil(this$1.data.listData.length / this$1.data.pageSize) *
						this$1.data.pageSize;
				}
			);
		};
		Root.prototype.loadMore = function() {
			if (
				(this.data.emptyBox.text == LOAD_MORE ||
					this.data.emptyBox.text == NET_ERR$1) &&
				this.data.pageNo != 1
			) {
				this.data.emptyBox.text = LOAD_ING;
				this.getData(this.data.listData.length ? 1 : 0);
			}
		};
		Root.prototype.setUnread = function() {
			var this$1 = this;

			if (this.data.listUnread.data.length && this.data.listData.length) {
				var newList = this.data.listData.concat([]);
				newList.forEach(function(_item) {
					_item.isRedDot = getItemForKey(_item.id, this$1.data.listUnread.data)
						? "1"
						: "";
				});
				this.data.listData = newList;
			}
		};
		Root.prototype.tabChange = function(_type) {
			if (_type == 3) {
				switch (this.code) {
					case "31":
						this.data.showOtherPage.news.show = this.data.titleBox.key == "1";
						this.update();
						break;
				}

				return;
			}
			this.getData(-1);
		};
		Root.prototype.rightTextClick = function() {
			var this$1 = this;

			switch (this.data.rightTextBtn.type) {
				case "allRead":
					setListUnread(this.data.rightTextBtn, function(ret) {
						if (ret && ret.code == 200) {
							this$1.pageRefresh();
						}
					});
					break;
				case "transcript":
					openWin_listn({code: "31_1", title: "成绩单"});
					break;
			}
		};
		Root.prototype.filtersShow = function() {
			var change = false;
			this.data.filters.data.forEach(function(_eItem) {
				if (!_eItem.sType) {
					change = true;
				}
			});
			return change;
		};
		Root.prototype.filtersChange = function() {
			var change = false;
			this.data.filters.data.forEach(function(_eItem) {
				if (_eItem.sType) {
					return;
				}
				if (JSON.stringify(_eItem.value) != JSON.stringify(_eItem.defaultValue)) {
					change = true;
				}
			});
			return change;
		};
		Root.prototype.keyCallback = function(ref) {
			var this$1 = this;
			var detail = ref.detail;

			switch (detail.key) {
				case "add":
					if (detail.addParam.title == "dTitle") {
						detail.addParam.title = G.headTitle || this.data.dTitle;
					}
					openWin_add_n(detail.addParam);
					break;
				case "review_50_7":
					openWin_workstation_dutieshelp_review({});
					break;
				case "join":
					alert(
						{
							title: "加入视频会议",
							content: this.meetingNumber || "",
							type: "input",
							placeholder: "请输入会议室号",
							buttons: ["确定", "取消"],
							otherParam: {
								inputPassword: true,
								content2: this.meetingPassword || "",
								placeholder2: "请输入入会密码(没有不填)"
							}
						},

						function(ret) {
							if (ret.buttonIndex == 1) {
								this$1.meetingNumber = ret.content || "";
								this$1.meetingPassword = ret.content2 || "";
								if (!this$1.meetingNumber || !trim(this$1.meetingNumber)) {
									toast("请输入会议室号");
									return;
								}
								joinMeeting({
									meetingNumber: this$1.meetingNumber,
									meetingPassword: this$1.meetingPassword
								});
							}
						}
					);
					break;
			}
		};
		Root.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"y-base-page",
				{
					_this: this,
					dataMore: this.props.dataMore,
					titleBox: this.data.titleBox.show,
					more: this.data.rightTextBtn.show
				},
				apivm.h("view", null),
				apivm.h(
					"view",
					{style: "height:100%;"},
					this.data.rightTextBtn.show &&
						apivm.h(
							"view",
							{
								onClick: function() {
									return this$1.rightTextClick();
								},
								class: "header_btn"
							},
							apivm.h(
								"text",
								{
									style: loadConfiguration(1) + "color:" + G.headColor + ";margin:0 4px;"
								},
								this.data.rightTextBtn.text
							)
						)
				),
				apivm.h(
					"view",
					{class: "flex_w xy_center", style: "height:100%;"},
					apivm.h("z-tabs-three", {
						style: this.data.titleBox.style,
						dataMore: this.data.titleBox,
						onChange: function() {
							return this$1.tabChange(3);
						}
					})
				),
				apivm.h(
					"view",
					{class: "flex_h"},
					apivm.h(
						"view",
						null,
						this.data.titleHint.show &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.keyCallback({detail: this$1.data.titleHint});
									},
									class: "flex_row",
									style: "padding:6px 16px;background: rgba(0,0,0,0.01);"
								},
								apivm.h(
									"text",
									{style: loadConfiguration(-2) + "flex:1;color: #333;"},
									this.data.titleHint.text
								),
								apivm.h(
									"view",
									{class: "flex_row"},
									apivm.h(
										"text",
										{
											style:
												loadConfiguration(-2) +
												"font-weight: 600;color: " +
												G.appTheme +
												";margin-right:2px;"
										},
										"立即前往"
									),
									apivm.h("a-iconfont", {
										name: "jiantou_xiangyouliangci",
										color: G.appTheme,
										size: G.appFontSize + 2
									})
								)
							)
					),
					apivm.h(
						"view",
						{style: "padding:0 7px;"},
						this.data.tabBox.type == "1" &&
							this.data.tabBox.data.length > 0 &&
							apivm.h("z-tabs-one", {
								dataMore: this.data.tabBox,
								average: this.data.tabBox.average,
								onChange: function() {
									return this$1.tabChange();
								}
							})
					),
					apivm.h(
						"view",
						null,
						this.data.inputBox.show &&
							apivm.h(
								"view",
								{class: "flex_row", style: "padding: 4px 16px"},
								apivm.h(
									"view",
									{class: "flex_w"},
									apivm.h("z-input", {
										dataMore: this.data.inputBox,
										onConfirm: function() {
											return this$1.getData(0);
										},
										onClean: function() {
											return this$1.getData(0);
										}
									})
								),
								this.filtersShow() &&
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.openFilters();
											},
											class: "flex_row",
											style: "margin-left:10px;"
										},
										apivm.h("a-iconfont", {
											name: "shaixuan",
											color: this.filtersChange() ? G.appTheme : "#333",
											size: G.appFontSize + 4
										}),
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color:" +
													(this.filtersChange() ? G.appTheme : "#333") +
													";margin-left:2px;"
											},
											"筛选"
										)
									)
							)
					),
					apivm.h(
						"view",
						{style: "padding:0 11px;"},
						apivm.h("y-filters-line", {
							dataMore: this.data.filters,
							onSure: function() {
								return this$1.getData(0);
							}
						})
					),
					apivm.h(
						"view",
						{style: "padding:0 11px;"},
						this.data.tabBox.type == "2" &&
							this.data.tabBox.data.length > this.data.titleBox.min &&
							apivm.h("z-tabs-two", {
								dataMore: this.data.tabBox,
								onChange: function() {
									return this$1.tabChange();
								}
							})
					),
					apivm.h(
						"y-scroll-view",
						{
							_this: this,
							refresh: true,
							style: "overflow-y: scroll;" + this.data.sStyle
						},
						apivm.h(
							"view",
							null,
							!this.data.emptyBox.type &&
								this.data.listData.map(function(item, index) {
									return [
										item.code == "10"
											? [
													apivm.h("item10", {
														search: this$1.data.inputBox,
														_this: this$1,
														item: item,
														onRefresh: function() {
															return this$1.getData(0);
														}
													})
											  ]
											: item.code == "14"
											? [
													apivm.h("item14", {
														search: this$1.data.inputBox,
														_this: this$1,
														item: item,
														onRefresh: function() {
															return this$1.getData(0);
														}
													})
											  ]
											: item.code == "22"
											? [
													apivm.h("item22", {
														search: this$1.data.inputBox,
														_this: this$1,
														item: item,
														onRefresh: function() {
															return this$1.getData(0);
														}
													})
											  ]
											: item.code == "31"
											? [
													apivm.h("item31", {
														search: this$1.data.inputBox,
														_this: this$1,
														item: item,
														onRefresh: function() {
															return this$1.getData(0);
														}
													})
											  ]
											: item.code == "31_1"
											? [
													apivm.h("item31-1", {
														search: this$1.data.inputBox,
														_this: this$1,
														item: item,
														onRefresh: function() {
															return this$1.getData(0);
														}
													})
											  ]
											: item.code == "apptext"
											? [
													apivm.h("item-apptext", {
														search: this$1.data.inputBox,
														_this: this$1,
														item: item,
														onRefresh: function() {
															return this$1.getData(0);
														}
													})
											  ]
											: item.code == "50_1"
											? [
													apivm.h("item50-1", {
														search: this$1.data.inputBox,
														_this: this$1,
														item: item,
														index: index,
														onRefresh: function() {
															return this$1.getData(0);
														}
													})
											  ]
											: item.code == "50_2"
											? [
													apivm.h("item50-2-1", {
														search: this$1.data.inputBox,
														_this: this$1,
														item: item,
														index: index,
														onRefresh: function() {
															return this$1.getData(0);
														}
													})
											  ]
											: item.code == "50_3"
											? [
													apivm.h("item50-3-1", {
														search: this$1.data.inputBox,
														_this: this$1,
														item: item,
														index: index,
														onRefresh: function() {
															return this$1.getData(0);
														}
													})
											  ]
											: item.code == "50_4"
											? [
													apivm.h("item50-4", {
														search: this$1.data.inputBox,
														_this: this$1,
														item: item,
														index: index,
														onRefresh: function() {
															return this$1.getData(0);
														}
													})
											  ]
											: item.code == "50_6"
											? [
													apivm.h("item50-6", {
														search: this$1.data.inputBox,
														_this: this$1,
														item: item,
														index: index,
														onRefresh: function() {
															return this$1.getData(0);
														}
													})
											  ]
											: item.code == "50_7"
											? [
													apivm.h("item50-7", {
														search: this$1.data.inputBox,
														_this: this$1,
														item: item,
														index: index,
														onRefresh: function() {
															return this$1.getData(0);
														}
													})
											  ]
											: item.code == "50_7_re"
											? [
													apivm.h("item50-7-re", {
														search: this$1.data.inputBox,
														_this: this$1,
														item: item,
														index: index,
														onRefresh: function() {
															return this$1.getData(0);
														}
													})
											  ]
											: item.code == "53"
											? [
													apivm.h("item53", {
														search: this$1.data.inputBox,
														_this: this$1,
														item: item,
														index: index,
														onRefresh: function() {
															return this$1.getData(0);
														}
													})
											  ]
											: item.code == "54"
											? [
													apivm.h("item54", {
														search: this$1.data.inputBox,
														_this: this$1,
														item: item,
														index: index,
														onRefresh: function() {
															return this$1.getData(0);
														}
													})
											  ]
											: []
									];
								})
						),
						apivm.h(
							"view",
							null,
							this.data.emptyBox.type == "load" && apivm.h("z-skeleton", null)
						),
						apivm.h("z-empty", {
							_this: this,
							dataMore: this.data.emptyBox,
							onRefresh: this.pageRefresh
						})
					),
					apivm.h("y-filters", {
						dataMore: this.data.filters,
						onSure: function() {
							return this$1.getData(0);
						}
					}),
					apivm.h("mo-news", {dataMore: this.data.showOtherPage["news"]})
				),

				apivm.h("input-num", {dataMore: G.numInputPop}),

				apivm.h("y-suspended-btns", {
					dataMore: this.data.suspendedBtn,
					onClick: this.keyCallback
				}),

				apivm.h("qrcode", {dataMore: G.qrcodePop})
			);
		};

		return Root;
	})(Component);
	apivm.define("root", Root);
	apivm.render(apivm.h("root", null), "body");
})();
