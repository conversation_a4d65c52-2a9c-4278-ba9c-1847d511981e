(function() {
	var NET_ERR = "用户您好，系统正在更新，请稍后再试。";

	//参数是否为空
	function isParameters(_obj) {
		return _obj != null && _obj != undefined;
	}
	//是否数组
	function isArray(_obj) {
		return isParameters(_obj) && toString.apply(_obj) === "[object Array]";
	}
	//是否对象
	function isObject(_obj) {
		return isParameters(_obj) && typeof _obj === "object";
	}
	//是否方法
	function isFunction(_obj) {
		return isParameters(_obj) && typeof _obj === "function";
	}

	//获取随机数
	function getNum() {
		return Math.floor(Math.random() * 100000000);
	}

	//合并json
	function setNewJSON(_obj, _newobj, _dotReplace) {
		_obj = _obj || {};
		_newobj = _newobj || {};
		var returnObj = {};
		for (var key in _obj) {
			returnObj[key] = _obj[key];
		}
		for (var key in _newobj) {
			if (
				(_dotReplace && isParameters(returnObj[key])) ||
				isFunction(isParameters(returnObj[key]))
			)
				continue;
			returnObj[key] =
				isArray(returnObj[key]) || !isObject(returnObj[key])
					? _newobj[key]
					: setNewJSON(returnObj[key], _newobj[key]);
		}
		return returnObj;
	}

	//移除字符串所有标签
	function removeTag(str) {
		if (!str) return str;
		return decodeCharacter(
			str
				.replace(/<!--[\w\W\r\n]*?-->/gim, "")
				.replace(/(<[^\s\/>]+)\b[^>]*>/gi, "$1>")
				.replace(/<[^>]+>/g, "")
				.replace(/\s*/g, "")
		);
	}

	//转义字符串
	function decodeCharacter(str) {
		if (!str) return str;
		return str
			.replace(/&amp;/g, "&")
			.replace(/(&nbsp;|&ensp;|&emsp;)/g, " ")
			.replace(/&mdash;/g, "—")
			.replace(/&ldquo;/g, "“")
			.replace(/&rsquo;/g, "’")
			.replace(/&lsquo;/g, "‘")
			.replace(/&rdquo;/g, "”")
			.replace(/&middot;/g, "·")
			.replace(/&hellip;/g, "…")
			.replace(/&quot;/g, '"')
			.replace(/&lt;/g, "<")
			.replace(/&gt;/g, ">");
	}

	//获取平台类型
	function platform() {
		return api.platform;
	}

	//app设置状态栏
	function setStatusBarStyle(_param) {
		try {
			api.setStatusBarStyle(_param);
		} catch (e) {}
	}

	//区域参数
	function safeArea() {
		try {
			return api.safeArea;
		} catch (e) {
			return {top: 0, left: 0, bottom: 0, right: 0};
		}
	}

	//页面参数对象
	function pageParam(_this) {
		try {
			var pageParam =
				(_this && _this.props
					? _this.props.pageParam || (_this.props.dataMore || {}).pageParam
					: null) ||
				api.pageParam ||
				{};
			if (pageParam.paramSaveKey) {
				pageParam = JSON.parse(getPrefs(pageParam.paramSaveKey) || "{}");
			}
			if (platform() == "web" && JSON.stringify(pageParam) == "{}") {
				pageParam = getOtherParam(window.location.href);
			}
			return pageParam;
		} catch (e) {
			return {};
		}
	}

	//获取缓存
	function getPrefs(key) {
		try {
			return api.getPrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				return window.parent[key];
			} else {
				return window[key];
			}
		}
	}

	//设置缓存
	function setPrefs(key, value) {
		if (!isParameters(value)) {
			removePrefs(key);
			return;
		}
		try {
			api.setPrefs({sync: true, key: key, value: value});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				window.parent[key] = value;
			} else {
				window[key] = value;
			}
		}
	}

	//删除缓存
	function removePrefs(key) {
		try {
			api.removePrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				delete window.parent[key];
			} else {
				delete window[key];
			}
		}
	}

	//添加监听
	function addEventListener(name, callback) {
		var keyback = function keyback(ret, err) {
			isFunction(callback) && callback(ret, err);
		};
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				if (!window.baseEventList) window.baseEventList = [];
				if (getItemForKey(name, window.baseEventList)) {
					delItemForKey(name, window.baseEventList);
				}
				window.baseEventList.push({key: name, value: keyback});
			} else {
				api.addEventListener({name: name}, keyback);
			}
		} catch (e) {}
	}
	//移除监听
	function removeEventListener(name) {
		if (
			platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			delItemForKey(name, window.baseEventList);
		} else {
			try {
				api.removeEventListener({name: name});
			} catch (e) {}
		}
	}
	//发送监听
	function sendEvent(name, extra) {
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				var pageframes = window.parent.document.getElementsByTagName("iframe");
				for (var i = 0; i < pageframes.length; i++) {
					if (isArray(pageframes[i].contentWindow.baseEventList)) {
						var sendItem = getItemForKey(
							isObject(name) ? name.name : name,
							pageframes[i].contentWindow.baseEventList
						);
						if (sendItem)
							sendItem.value({value: isObject(name) ? name.extra : extra});
					}
				}
			} else {
				api.sendEvent(isObject(name) ? name : {name: name, extra: extra});
			}
		} catch (e) {}
	}
	//加载框
	function showProgress(_param, modal) {
		var o = {
			style: "default",
			animationType: "fade",
			title: "加载中",
			text: "请稍候...",
			modal: true //是否模态，模态时整个页面将不可交互
		};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.title = isParameters(_param) ? _param : "";
			o.modal = !modal; //是否可以交互 反过来了
		}
		o.title = o.title.toString();
		api.showProgress(o);
	}

	//隐藏加载框
	function hideProgress() {
		api.hideProgress();
	}

	//处理app链接 可以不带app会拼接上 或者需要带上特定参数
	function handleSYSLink(_link) {
		if (!_link) return;
		_link = _link.replace("{{tomcatAddress}}", tomcatAddress());
		_link = _link.replace("{{shareAddress}}", shareAddress());
		_link = _link.replace("{{token}}", encodeURIComponent(getPrefs("sys_token")));
		_link = _link.replace("{{sysUrl}}", appUrl());
		_link = _link.replace("{{areaId}}", areaId()); //当前跳转页面的地区id，例如：430000
		_link = _link.replace("{{userId}}", G.userId); //当前用户id，例如：1
		_link = _link.replace("{{iszx}}", G.sysSign == "zx"); //当前系统类型，例如：true (true为政协，flase为人大)
		_link = _link.replace("{{appTheme}}", G.appTheme); //当前app主题颜色，例如：#3657C0
		_link = _link.replace("{{careMode}}", G.careMode); //当前是否为关怀模式：例如：true (关怀模式下字体大4px)
		if (_link.indexOf("?ndata=") != -1) {
			if (_link.indexOf("sysUrl-zy-") == -1) _link += "-zyz-sysUrl-zy-" + appUrl();
			if (_link.indexOf("sysAreaId-zy-") == -1)
				_link += "-zyz-sysAreaId-zy-" + areaId();
			if (_link.indexOf("iszx-zy-") == -1)
				_link += "-zyz-iszx-zy-" + (G.sysSign == "zx");
			if (_link.indexOf("appTheme-zy-") == -1)
				_link += "-zyz-appTheme-zy-" + G.appTheme;
			if (_link.indexOf("careMode-zy-") == -1)
				_link += "-zyz-careMode-zy-" + G.careMode;
		}
		return _link;
	}

	//打开新页面
	function openWin(name, url, pageParam, _more) {
		url = handleSYSLink(url); //先处理跳转链接
		if (url.indexOf("http") != 0) {
			url =
				platform() == "web"
					? url.substring(url.lastIndexOf("/") + 1)
					: url.indexOf("..") != 0
					? "../" + url.split(".")[0] + "/" + url
					: url;
		}
		var o = {
			name: name,
			url: url,
			pageParam: pageParam || {},
			bounces: false,
			bgColor: "#FFF",
			slidBackEnabled: false, //ios滑动返回
			vScrollBarEnabled: true,
			hScrollBarEnabled: true,
			scaleEnabled: true,
			animation: {
				type: "push",
				subType: "from_right",
				duration: 300
			},

			reload: true, // 去除设置
			allowEdit: true, //去除设置 默认都可以复制粘贴
			delay: 0,
			overScrollMode: "scrolls",
			defaultRefreshHeader: "swipe"
		};

		if (isObject(_more)) {
			o = setNewJSON(o, _more);
		}
		if (G.headTheme != getPrefs("headTheme") && G.headTheme != "transparent") {
			o.pageParam.headTheme = G.headTheme;
		}
		if (
			G.appTheme != getPrefs("appTheme" + G.sysSign) &&
			G.appTheme != (G.sysSign == "rd" ? "#C61414" : "#3088FE")
		) {
			o.pageParam.appTheme = G.appTheme;
		}
		if (
			o.pageParam.areaId != areaId() ||
			(areaId() != getPrefs("sys_aresId") && areaId() != getPrefs("sys_platform"))
		) {
			o.pageParam.areaId = o.pageParam.areaId || areaId();
		}
		if (o.pageParam.paramSaveKey) {
			setPrefs(o.pageParam.paramSaveKey, JSON.stringify(o.pageParam));
			o.pageParam = {paramSaveKey: o.pageParam.paramSaveKey};
		}
		videoPlayRemoves();
		api.openWin(o);
	}

	function closeWin(_param) {
		var o = {};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.name = _param;
		}
		try {
			if (api.pageParam.paramSaveKey) {
				removePrefs(api.pageParam.paramSaveKey);
			}
			videoPlayRemoves();
			api.closeWin(o);
		} catch (e) {}
	}

	function toast(_param, location, global) {
		var o = {
			msg: "",
			duration: 2000,
			location: location || "middle",
			global: global ? true : false
		};

		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.msg = isParameters(_param) ? _param : "";
		}
		o.msg = o.msg.toString();
		api.toast(o);
	}

	//弹出alert
	function alert(_param, _callback) {
		var o = {title: "", msg: "", buttons: ["确定"]};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.msg = (isParameters(_param) ? _param : "").toString();
		}
		var alertBox = {
			title: o.title,
			content: (o.msg || o.content || "").toString(),
			placeholder: o.placeholder,
			closeType: o.closeType || "1",
			type: o.type || "text",
			timeout: o.timeout || 0,
			autoClose: o.autoClose,
			cancel: {
				show: o.buttons.length > 1 || o.closeType == "2" || o.closeType == "3",
				text: o.buttons[1],
				color: "#333333"
			},

			sure: {
				show: o.buttons.length > 0,
				text: o.buttons[0],
				color: "appTheme"
			},

			show: true,
			callback: function callback(ret) {
				_callback && _callback(ret);
			}
		};

		alertBox = setNewJSON(alertBox, _param.otherParam);
		G.alertPop = alertBox;
	}

	//网络请求
	function ajax(url, tag, callback, logText, method, data, header) {
		var getUrl = url,
			dataType = "json",
			cacheType = "",
			paramData = {},
			aId = areaId(),
			isWeb = "";
		if (isObject(url)) {
			getUrl = url.u;
			dataType = url.dt || "json";
			cacheType = url.t || "";
			paramData = url.paramData || {};
			aId = url.areaId || areaId();
			isWeb = url.web || "";
		}
		var o = {
			url: getUrl,
			tag: tag,
			method: method || "get",
			cache: false,
			timeout: 120,
			dataType: dataType,
			data: isObject(data) ? data : {},
			headers: setNewJSON(
				{
					"u-login-areaId": aId,
					Authorization: getPrefs("sys_token") || "",
					"content-type": "application/json",
					"u-terminal": G.terminal || "APP"
				},
				header || {}
			)
		};

		o = setNewJSON(o, paramData);
		if (o.url.indexOf("push/rongCloud") != -1) {
			if (o.method == "get") {
				o.url +=
					(o.url.indexOf("?") != -1 ? "&" : "?") +
					"environment=" +
					chatEnvironment();
			} else if (o.data.values) {
				o.data.values.environment = chatEnvironment();
			}
		}
		if (isWeb) {
			(o.headers.Authorization =
				(header || {}).Authorization || getPrefs("public_token") || ""),
				(o.headers["u-terminal"] = "PUBLIC");
		}
		var oldContentType = o.headers["content-type"];
		if (oldContentType == "file") {
			delete o.headers["content-type"];
		}
		if (platform() == "app" && logText) {
			console.log(logText + o.method + "：" + JSON.stringify(o));
		}
		var cbFun = function cbFun(ret, err) {
			if (isFunction(callback)) {
				// if(isObject(err)){
				// 	try{
				// 		ret = JSON.parse(err.msg);
				// 	}catch(e){
				// 		ret = JSON.parse(JSON.stringify(err));
				// 	}
				// 	err = null;
				// }
				if (platform() == "app" && logText) {
					console.log(
						"得到" + logText + "返回结果：" + JSON.stringify(ret ? ret : err)
					);
				}
				if (isObject(ret)) {
					ret.message = ret.message || ret.msg || "";
					var errcode = ret.code || "";
					if ((errcode == 302 || errcode == 2) && cacheType != "login") {
						cleanAllMsg();
						sendEvent({
							name: "index",
							extra: {type: "verificationToken", errmsg: ret.message}
						});
					}
					// if(!isObject(ret.data)){
					// 	ret.data = "";
					// }
				}
				callback(ret, err);
			}
		};
		if (platform() == "web") {
			var xhr = new XMLHttpRequest();
			xhr.open(o.method, o.url);
			for (var header in o.headers) {
				xhr.setRequestHeader(header, o.headers[header]);
			}
			var sendValue = "";
			if (oldContentType.indexOf("x-www-form-urlencoded") != -1) {
				var dValue = o.data.values || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue +=
						(!sendValue ? "" : "&") + vItem + "=" + encodeURIComponent(dValue[vItem]);
				}
			} else if (oldContentType.indexOf("file") != -1) {
				sendValue = new FormData();
				var dValue = o.data.values || {};
				var fileValue = o.data.files || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue.append(vItem, encodeURIComponent(dValue[vItem]));
				}
				for (var vItem in fileValue) {
					sendValue.append(vItem, fileValue[vItem]);
				}
			} else {
				sendValue = o.data.body || JSON.stringify(o.data.values);
			}
			xhr.onreadystatechange = function() {
				if (xhr.readyState === XMLHttpRequest.DONE) {
					var ret, err;
					if (!xhr.responseText) {
						err = {};
					} else {
						var response = this.responseText;
						if (o.dataType == "json") {
							try {
								ret = JSON.parse(response);
							} catch (e) {
								err = {msg: response};
							}
						} else {
							ret = response;
						}
					}
					cbFun(ret, err);
				}
			};
			xhr.send(sendValue);
		} else {
			api.cancelAjax({tag: tag});
			api.ajax(o, cbFun);
		}
	}

	//人大政协标识  rd人大 zx政协
	function sysSign() {
		return getPrefs("sys_sign") || "rd";
	}

	//配置地址
	function appUrl() {
		var prot = sysSign() == "rd" ? "20169" : "20170";
		return (
			getPrefs("sys_appUrl") || "https://productpc.cszysoft.com:" + prot + "/lzt/"
		);
	}
	//tomcat配置地址
	function tomcatAddress() {
		return (
			getPrefs("sys_tomcatAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http://cszysoft.com:9090/"
				: "https://cszysoft.com:9091/")
		);
	}
	//分享地址
	function shareAddress(_type) {
		if (_type == 1 && platform() != "mp") return "../../";
		return (
			getPrefs("sys_shareAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http:"
				: "https:") +
				"//cszysoft.com/appShare/" +
				(sysSign() == "rd" ? "platform5rd/" : "platform5zx/")
		);
	}
	//融云唯一前缀
	function chatHeader() {
		return getPrefs("sys_chatHeader") || "platform5" + sysSign();
	}
	//融云正测
	function chatEnvironment() {
		return getPrefs("sys_chatEnvironment") || "1";
	}
	//当前页面地区id
	function areaId() {
		return (
			(G._this && G._this.data.area ? G._this.data.area.key : "") ||
			pageParam().areaId ||
			getPrefs("sys_aresId") ||
			getPrefs("sys_platform") ||
			""
		);
	}

	var SECONDS_A_MINUTE = 60;
	var SECONDS_A_HOUR = SECONDS_A_MINUTE * 60;
	var SECONDS_A_DAY = SECONDS_A_HOUR * 24;
	var SECONDS_A_WEEK = SECONDS_A_DAY * 7;
	var MILLISECONDS_A_SECOND = 1e3;
	var MILLISECONDS_A_MINUTE = SECONDS_A_MINUTE * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_HOUR = SECONDS_A_HOUR * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_DAY = SECONDS_A_DAY * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_WEEK = SECONDS_A_WEEK * MILLISECONDS_A_SECOND; // English locales

	var MS = "millisecond";
	var S = "second";
	var MIN = "minute";
	var H = "hour";
	var D = "day";
	var W = "week";
	var M = "month";
	var Q = "quarter";
	var Y = "year";
	var DATE = "date";
	var FORMAT_DEFAULT = "YYYY-MM-DDTHH:mm:ssZ";
	var INVALID_DATE_STRING = "Invalid Date"; // regex

	var REGEX_PARSE = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/;
	var REGEX_FORMAT = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;

	var en = {
		name: "en",
		weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split(
			"_"
		),
		months: "January_February_March_April_May_June_July_August_September_October_November_December".split(
			"_"
		)
	};

	var padStart = function padStart(string, length, pad) {
		var s = String(string);
		if (!s || s.length >= length) return string;
		return "" + Array(length + 1 - s.length).join(pad) + string;
	};
	var padZoneStr = function padZoneStr(instance) {
		var negMinutes = -instance.utcOffset();
		var minutes = Math.abs(negMinutes);
		var hourOffset = Math.floor(minutes / 60);
		var minuteOffset = minutes % 60;
		return (
			"" +
			(negMinutes <= 0 ? "+" : "-") +
			padStart(hourOffset, 2, "0") +
			":" +
			padStart(minuteOffset, 2, "0")
		);
	};
	var monthDiff = function monthDiff(a, b) {
		if (a.date() < b.date()) return -monthDiff(b, a);
		var wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month());
		var anchor = a.clone().add(wholeMonthDiff, M);
		var c = b - anchor < 0;
		var anchor2 = a.clone().add(wholeMonthDiff + (c ? -1 : 1), M);
		return +(
			-(
				wholeMonthDiff +
				(b - anchor) / (c ? anchor - anchor2 : anchor2 - anchor)
			) || 0
		);
	};
	var absFloor = function absFloor(n) {
		return n < 0 ? Math.ceil(n) || 0 : Math.floor(n);
	};
	var prettyUnit = function prettyUnit(u) {
		var special = {
			M: M,
			y: Y,
			w: W,
			d: D,
			D: DATE,
			h: H,
			m: MIN,
			s: S,
			ms: MS,
			Q: Q
		};

		return (
			special[u] ||
			String(u || "")
				.toLowerCase()
				.replace(/s$/, "")
		);
	};
	var isUndefined = function isUndefined(s) {
		return s === undefined;
	};
	var U = {
		s: padStart,
		z: padZoneStr,
		m: monthDiff,
		a: absFloor,
		p: prettyUnit,
		u: isUndefined
	};

	var L = "en";
	var Ls = {};
	Ls[L] = en;
	var isDayjs = function isDayjs(d) {
		return d instanceof Dayjs;
	};
	var parseLocale = function parseLocale(preset, object, isLocal) {
		var l;
		if (!preset) return L;
		if (typeof preset === "string") {
			var presetLower = preset.toLowerCase();
			if (Ls[presetLower]) {
				l = presetLower;
			}
			if (object) {
				Ls[presetLower] = object;
				l = presetLower;
			}
			var presetSplit = preset.split("-");
			if (!l && presetSplit.length > 1) {
				return parseLocale(presetSplit[0]);
			}
		} else {
			var name = preset.name;
			Ls[name] = preset;
			l = name;
		}
		if (!isLocal && l) L = l;
		return l || (!isLocal && L);
	};
	var dayjs = function dayjs(date, c) {
		if (isDayjs(date)) {
			return date.clone();
		}
		var cfg = typeof c === "object" ? c : {};
		cfg.date = date;
		cfg.args = arguments;
		return new Dayjs(cfg);
	};
	var wrapper = function wrapper(date, instance) {
		return dayjs(date, {
			locale: instance.$L,
			utc: instance.$u,
			x: instance.$x,
			$offset: instance.$offset
		});
	};
	var Utils = U;
	Utils.l = parseLocale;
	Utils.i = isDayjs;
	Utils.w = wrapper;
	var parseDate = function parseDate(cfg) {
		var date = cfg.date,
			utc = cfg.utc;
		if (date === null) return new Date(NaN);
		if (Utils.u(date)) return new Date();
		if (date instanceof Date) return new Date(date);
		if (typeof date === "string" && !/Z$/i.test(date)) {
			var d = date.match(REGEX_PARSE);
			if (d) {
				var m = d[2] - 1 || 0;
				var ms = (d[7] || "0").substring(0, 3);
				if (utc) {
					return new Date(
						Date.UTC(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms)
					);
				}
				return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);
			}
		}
		return new Date(date);
	};
	var Dayjs = (function() {
		function Dayjs(cfg) {
			this.$L = parseLocale(cfg.locale, null, true);
			this.parse(cfg);
		}
		var _proto = Dayjs.prototype;
		_proto.parse = function parse(cfg) {
			this.$d = parseDate(cfg);
			this.$x = cfg.x || {};
			this.init();
		};
		_proto.init = function init() {
			var $d = this.$d;
			this.$y = $d.getFullYear();
			this.$M = $d.getMonth();
			this.$D = $d.getDate();
			this.$W = $d.getDay();
			this.$H = $d.getHours();
			this.$m = $d.getMinutes();
			this.$s = $d.getSeconds();
			this.$ms = $d.getMilliseconds();
		};
		_proto.$utils = function $utils() {
			return Utils;
		};
		_proto.isValid = function isValid() {
			return !(this.$d.toString() === INVALID_DATE_STRING);
		};
		_proto.isSame = function isSame(that, units) {
			var other = dayjs(that);
			return this.startOf(units) <= other && other <= this.endOf(units);
		};
		_proto.isAfter = function isAfter(that, units) {
			return dayjs(that) < this.startOf(units);
		};
		_proto.isBefore = function isBefore(that, units) {
			return this.endOf(units) < dayjs(that);
		};
		_proto.$g = function $g(input, get, set) {
			if (Utils.u(input)) return this[get];
			return this.set(set, input);
		};
		_proto.unix = function unix() {
			return Math.floor(this.valueOf() / 1000);
		};
		_proto.valueOf = function valueOf() {
			return this.$d.getTime();
		};
		_proto.startOf = function startOf(units, _startOf) {
			var _this = this;
			var isStartOf = !Utils.u(_startOf) ? _startOf : true;
			var unit = Utils.p(units);
			var instanceFactory = function instanceFactory(d, m) {
				var ins = Utils.w(
					_this.$u ? Date.UTC(_this.$y, m, d) : new Date(_this.$y, m, d),
					_this
				);
				return isStartOf ? ins : ins.endOf(D);
			};
			var instanceFactorySet = function instanceFactorySet(method, slice) {
				var argumentStart = [0, 0, 0, 0];
				var argumentEnd = [23, 59, 59, 999];
				return Utils.w(
					_this
						.toDate()
						[method].apply(
							_this.toDate("s"),
							(isStartOf ? argumentStart : argumentEnd).slice(slice)
						),
					_this
				);
			};
			var $W = this.$W,
				$M = this.$M,
				$D = this.$D;
			var utcPad = "set" + (this.$u ? "UTC" : "");
			switch (unit) {
				case Y:
					return isStartOf ? instanceFactory(1, 0) : instanceFactory(31, 11);
				case M:
					return isStartOf ? instanceFactory(1, $M) : instanceFactory(0, $M + 1);
				case W: {
					var weekStart = this.$locale().weekStart || 0;
					var gap = ($W < weekStart ? $W + 7 : $W) - weekStart;
					return instanceFactory(isStartOf ? $D - gap : $D + (6 - gap), $M);
				}
				case D:
				case DATE:
					return instanceFactorySet(utcPad + "Hours", 0);
				case H:
					return instanceFactorySet(utcPad + "Minutes", 1);
				case MIN:
					return instanceFactorySet(utcPad + "Seconds", 2);
				case S:
					return instanceFactorySet(utcPad + "Milliseconds", 3);
				default:
					return this.clone();
			}
		};
		_proto.endOf = function endOf(arg) {
			return this.startOf(arg, false);
		};
		_proto.$set = function $set(units, _int) {
			var _C$D$C$DATE$C$M$C$Y$C;
			var unit = Utils.p(units);
			var utcPad = "set" + (this.$u ? "UTC" : "");
			var name = ((_C$D$C$DATE$C$M$C$Y$C = {}),
			(_C$D$C$DATE$C$M$C$Y$C[D] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[DATE] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[M] = utcPad + "Month"),
			(_C$D$C$DATE$C$M$C$Y$C[Y] = utcPad + "FullYear"),
			(_C$D$C$DATE$C$M$C$Y$C[H] = utcPad + "Hours"),
			(_C$D$C$DATE$C$M$C$Y$C[MIN] = utcPad + "Minutes"),
			(_C$D$C$DATE$C$M$C$Y$C[S] = utcPad + "Seconds"),
			(_C$D$C$DATE$C$M$C$Y$C[MS] = utcPad + "Milliseconds"),
			_C$D$C$DATE$C$M$C$Y$C)[unit];
			var arg = unit === D ? this.$D + (_int - this.$W) : _int;
			if (unit === M || unit === Y) {
				var date = this.clone().set(DATE, 1);
				date.$d[name](arg);
				date.init();
				this.$d = date.set(DATE, Math.min(this.$D, date.daysInMonth())).$d;
			} else if (name) this.$d[name](arg);
			this.init();
			return this;
		};
		_proto.set = function set(string, _int2) {
			return this.clone().$set(string, _int2);
		};
		_proto.get = function get(unit) {
			return this[Utils.p(unit)]();
		};
		_proto.add = function add(number, units) {
			var _this2 = this,
				_C$MIN$C$H$C$S$unit;
			number = Number(number);
			var unit = Utils.p(units);
			var instanceFactorySet = function instanceFactorySet(n) {
				var d = dayjs(_this2);
				return Utils.w(d.date(d.date() + Math.round(n * number)), _this2);
			};
			if (unit === M) {
				return this.set(M, this.$M + number);
			}
			if (unit === Y) {
				return this.set(Y, this.$y + number);
			}
			if (unit === D) {
				return instanceFactorySet(1);
			}
			if (unit === W) {
				return instanceFactorySet(7);
			}
			var step =
				((_C$MIN$C$H$C$S$unit = {}),
				(_C$MIN$C$H$C$S$unit[MIN] = MILLISECONDS_A_MINUTE),
				(_C$MIN$C$H$C$S$unit[H] = MILLISECONDS_A_HOUR),
				(_C$MIN$C$H$C$S$unit[S] = MILLISECONDS_A_SECOND),
				_C$MIN$C$H$C$S$unit)[unit] || 1;
			var nextTimeStamp = this.$d.getTime() + number * step;
			return Utils.w(nextTimeStamp, this);
		};
		_proto.subtract = function subtract(number, string) {
			return this.add(number * -1, string);
		};
		_proto.format = function format(formatStr) {
			var _this3 = this;
			var locale = this.$locale();
			if (!this.isValid()) return locale.invalidDate || INVALID_DATE_STRING;
			var str = formatStr || FORMAT_DEFAULT;
			var zoneStr = Utils.z(this);
			var $H = this.$H,
				$m = this.$m,
				$M = this.$M;
			var weekdays = locale.weekdays,
				months = locale.months,
				meridiem = locale.meridiem;
			var getShort = function getShort(arr, index, full, length) {
				return (
					(arr && (arr[index] || arr(_this3, str))) || full[index].slice(0, length)
				);
			};
			var get$H = function get$H(num) {
				return Utils.s($H % 12 || 12, num, "0");
			};
			var meridiemFunc =
				meridiem ||
				function(hour, minute, isLowercase) {
					var m = hour < 12 ? "AM" : "PM";
					return isLowercase ? m.toLowerCase() : m;
				};
			var matches = {
				YY: String(this.$y).slice(-2),
				YYYY: this.$y,
				M: $M + 1,
				MM: Utils.s($M + 1, 2, "0"),
				MMM: getShort(locale.monthsShort, $M, months, 3),
				MMMM: getShort(months, $M),
				D: this.$D,
				DD: Utils.s(this.$D, 2, "0"),
				d: String(this.$W),
				dd: getShort(locale.weekdaysMin, this.$W, weekdays, 2),
				ddd: getShort(locale.weekdaysShort, this.$W, weekdays, 3),
				dddd: weekdays[this.$W],
				H: String($H),
				HH: Utils.s($H, 2, "0"),
				h: get$H(1),
				hh: get$H(2),
				a: meridiemFunc($H, $m, true),
				A: meridiemFunc($H, $m, false),
				m: String($m),
				mm: Utils.s($m, 2, "0"),
				s: String(this.$s),
				ss: Utils.s(this.$s, 2, "0"),
				SSS: Utils.s(this.$ms, 3, "0"),
				Z: zoneStr
			};

			return str.replace(REGEX_FORMAT, function(match, $1) {
				return $1 || matches[match] || zoneStr.replace(":", "");
			});
		};
		_proto.utcOffset = function utcOffset() {
			return -Math.round(this.$d.getTimezoneOffset() / 15) * 15;
		};
		_proto.diff = function diff(input, units, _float) {
			var _C$Y$C$M$C$Q$C$W$C$D$;
			var unit = Utils.p(units);
			var that = dayjs(input);
			var zoneDelta =
				(that.utcOffset() - this.utcOffset()) * MILLISECONDS_A_MINUTE;
			var diff = this - that;
			var result = Utils.m(this, that);
			result =
				((_C$Y$C$M$C$Q$C$W$C$D$ = {}),
				(_C$Y$C$M$C$Q$C$W$C$D$[Y] = result / 12),
				(_C$Y$C$M$C$Q$C$W$C$D$[M] = result),
				(_C$Y$C$M$C$Q$C$W$C$D$[Q] = result / 3),
				(_C$Y$C$M$C$Q$C$W$C$D$[W] = (diff - zoneDelta) / MILLISECONDS_A_WEEK),
				(_C$Y$C$M$C$Q$C$W$C$D$[D] = (diff - zoneDelta) / MILLISECONDS_A_DAY),
				(_C$Y$C$M$C$Q$C$W$C$D$[H] = diff / MILLISECONDS_A_HOUR),
				(_C$Y$C$M$C$Q$C$W$C$D$[MIN] = diff / MILLISECONDS_A_MINUTE),
				(_C$Y$C$M$C$Q$C$W$C$D$[S] = diff / MILLISECONDS_A_SECOND),
				_C$Y$C$M$C$Q$C$W$C$D$)[unit] || diff;
			return _float ? result : Utils.a(result);
		};
		_proto.daysInMonth = function daysInMonth() {
			return this.endOf(M).$D;
		};
		_proto.$locale = function $locale() {
			return Ls[this.$L];
		};
		_proto.locale = function locale(preset, object) {
			if (!preset) return this.$L;
			var that = this.clone();
			var nextLocaleName = parseLocale(preset, object, true);
			if (nextLocaleName) that.$L = nextLocaleName;
			return that;
		};
		_proto.clone = function clone() {
			return Utils.w(this.$d, this);
		};
		_proto.toDate = function toDate() {
			return new Date(this.valueOf());
		};
		_proto.toJSON = function toJSON() {
			return this.isValid() ? this.toISOString() : null;
		};
		_proto.toISOString = function toISOString() {
			return this.$d.toISOString();
		};
		_proto.toString = function toString() {
			return this.$d.toUTCString();
		};
		return Dayjs;
	})();
	var proto = Dayjs.prototype;
	dayjs.prototype = proto;
	[
		["$ms", MS],
		["$s", S],
		["$m", MIN],
		["$H", H],
		["$W", D],
		["$M", M],
		["$y", Y],
		["$D", DATE]
	].forEach(function(g) {
		proto[g[1]] = function(input) {
			return this.$g(input, g[0], g[1]);
		};
	});
	dayjs.extend = function(plugin, option) {
		if (!plugin.$i) {
			plugin(option, Dayjs, dayjs);
			plugin.$i = true;
		}
		return dayjs;
	};
	dayjs.locale = parseLocale;
	dayjs.isDayjs = isDayjs;
	dayjs.unix = function(timestamp) {
		return dayjs(timestamp * 1e3);
	};
	dayjs.en = Ls[L];
	dayjs.Ls = Ls;
	dayjs.p = {};

	//获取群成员列表
	function getGroupListUser(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "chatGroupMember/list",
				param: setNewJSON(
					{pageNo: 1, pageSize: 9999, query: {chatGroupId: ""}},
					_param.param
				),
				tag: "chatGroupMember",
				name: "群成员"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.id = _eItem.accountId || "";
						item.targetId = _eItem.accountId || "";
						item.name = _eItem.userName || "";
						item.url = _eItem.headImg || _eItem.photo || "";
						item.isOwner = _eItem.isOwner;
						nowList.push(item);
					});
					setAllChatInfo(nowList);
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取登录信息
	function getLoginInfo(_param, _callback) {
		ajax(
			{u: appUrl() + "login/user"},
			"login/user",
			function(ret, err) {
				if (ret && ret.code == 200 && ret.data.id != "anonymous") {
					saveLogin(ret.data);
				}
				_callback && _callback(ret, err);
			},
			"用户信息",
			"post",
			{
				values: _param.param
			},
			_param.header
		);
	}

	//请求
	function ajaxProcess(_param, _callback) {
		if (_param.toast) showProgress(_param.toast);
		ajax(
			{u: _param.url, areaId: _param.areaId, web: _param.web},
			_param.tag || "ajaxProcess",
			function(ret, err) {
				hideProgress();
				if (_param.toast) {
					toast(ret ? ret.message || ret.data : NET_ERR);
				}
				_callback && _callback(ret, err);
			},
			_param.name || "\u64CD\u4F5C",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	//获取融云群或用户信息
	function getBaseChatInfos(_item, callback) {
		var _id = _item.targetId;
		var _type = _item.conversationType;
		var _force = _item.force;
		if (_id.indexOf(chatHeader()) == 0) {
			_id = _id.split(chatHeader())[1];
		}
		var nTask = getItemForKey("task_" + _id, G.chatInfoTask);
		if (nTask) {
			nTask.callback.push(callback);
			return;
		}
		if (!_item.index) {
			getAllChatInfo();
		}
		var nowItem = getItemForKey(_id, G.chatInfos, "id");
		if (!nowItem || _force) {
			nTask = {key: "task_" + _id, callback: []};
			G.chatInfoTask.push(nTask);
			nTask.callback.push(callback);
			ajax(
				{
					u:
						appUrl() + (_type == "PRIVATE" ? "user/infoByAccount" : "chatGroup/info")
				},
				"infoByChat" + _id + "_" + _id,
				function(ret, err) {
					var item = null;
					if (ret && ret.code == "200") {
						if (_type == "PRIVATE") {
							var data = ret.data || [];
							var headImg = "",
								photo = "";
							data.forEach(function(_nItem) {
								if (_nItem.headImg && !headImg) {
									headImg = _nItem.headImg;
								}
								if (_nItem.photo && !photo) {
									photo = _nItem.photo;
								}
							});
							data = data[0];
							item = {
								id: data.accountId,
								name: data.userName,
								url: headImg || photo || ""
							};
						} else {
							var data = ret.data || {};
							item = {
								id: data.id,
								businessCode: data.businessCode,
								name: data.groupName,
								url: data.groupImg || "",
								memberUserIds: data.memberUserIds || []
							};
						}
						setAllChatInfo(item);
					}
					nTask = getItemForKey("task_" + _id, G.chatInfoTask);
					for (var i = 0; i < nTask.callback.length; i++) {
						var call = nTask.callback[i];
						call && call(item, ret, err);
					}
					delItemForKey("task_" + _id, G.chatInfoTask);
				},
				"\u83B7\u53D6\u4FE1\u606F" + _id,
				"post",
				{
					body: JSON.stringify({accountId: _id, detailId: _id, isSelectList: 1})
				}
			);
		} else {
			callback && callback(nowItem);
		}
	}

	function getAllChatInfo() {
		var chatInfos = JSON.parse(getPrefs("chatInfos") || "[]"),
			addInfo = [];
		chatInfos.forEach(function(_eItem) {
			if (_eItem.id) {
				if (/^(?!h|(\.)|(\/)).*/.test(_eItem.url)) {
					_eItem.url = showImg(_eItem.url);
				}
				addInfo.push(_eItem);
			}
		});
		G.chatInfos = addInfo;
		setPrefs("chatInfos", JSON.stringify(G.chatInfos));
		return G.chatInfos;
	}

	function setAllChatInfo(_item) {
		getAllChatInfo();
		var nowInfo = JSON.parse(JSON.stringify(G.chatInfos));
		if (isArray(_item)) {
			_item.forEach(function(_eItem) {
				if (_eItem.id) {
					delItemForKey(_eItem.id, nowInfo, "id");
					nowInfo.push(_eItem);
				}
			});
		} else {
			if (!_item.id) {
				return;
			}
			delItemForKey(_item.id, nowInfo, "id");
			nowInfo.push(_item);
		}
		setPrefs("chatInfos", JSON.stringify(nowInfo));
		setRongChatInfo();
	}

	function setRongChatInfo() {
		if (platform() == "app" && api.require("zyRongCloudRTC")) {
			getAllChatInfo();
			var setUser = {
				apply: 1,
				users: G.chatInfos.map(function(obj) {
					return {id: chatHeader() + obj.id, name: obj.name, url: obj.url};
				}),
				groupUser: JSON.parse(getPrefs("rongGroupUser") || "[]"), //当前通话的群组成员列表id集合
				totalNumber: 10 //语音或视频最大人数
			};
			// console.log("融云设置信息："+JSON.stringify(setUser));
			api.require("zyRongCloudRTC").setUsers(setUser, function(ret) {
				removePrefs("rongGroupUser");
				setRongChatInfo();
				setTimeout(
					function() {
						sendEvent({name: "chat_refresh"});
					},
					api.systemType == "ios" ? 600 : 1500
				);
			});
		}
	}

	function dealVideoId(_id) {
		return _id.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, "");
	}

	//当前页面播放视频集合
	function videoPlayPush(_id) {
		if (!G.playVideos) {
			G.playVideos = [];
		}
		if (getItemForKey(_id, G.playVideos)) {
			return;
		}
		videoPlayRemoves();
		G.playVideos.push(_id);
		console.log("当前播放：" + JSON.stringify(G.playVideos));
	}

	//去除某个视频播放
	function videoPlayRemove(_id) {
		delItemForKey(_id, G.playVideos);
	}

	//去除所有播放
	function videoPlayRemoves() {
		(G.playVideos || []).forEach(function(_id) {
			document.getElementById(_id) && document.getElementById(_id).pause();
			videoPlayRemove(_id);
		});
	}

	//全局页面引用变量
	var G = {
		sysSign: sysSign(), //人大政协标识
		pageWidth: "", //页面总宽度
		onShowNum: -1, //当前页面展示次数 1为首次
		appName: "", //app名字
		appFont: "", //app全局字体
		appFontSize: 0, //app全局字体大小
		appTheme: "", //app全局主题色
		headTheme: "", //head全局主题色
		headColor: "", //标题前景色
		headTitle: "", //标题栏文字
		loginInfo: "",

		careMode: false, //是否启用了关怀模式
		systemtTypeIsPlatform: false, //系统类型是否是平台版
		isAppReview: false, //app是否上架期间 隐藏和显示部分功能
		v: "", //缓存版本号

		uId: "", //普通用户id
		userId: "", //当前用户id 账号id
		userName: "", //当前用户名字
		userImg: "", //当前用户头像
		areaId: "", //当前地区id
		specialRoleKeys: [], //当前用户角色集合
		isAdmin: false, //是否管理员 拥有所有权限
		grayscale: "", //全局置灰
		watermark: "", //全局水印

		// chatInfos:[],
		// chatInfoTask:[],

		alertPop: {
			// alert提示框
			show: false
		},

		actionSheetPop: {
			//actionSheet弹出框
			show: false
		},

		imgPreviewerPop: {
			//图片预览
			show: false
		},

		areasPop: {
			// 全局地区弹窗
			show: false
		},

		numInputPop: {
			// 全局数字弹窗
			show: false
		},

		favoritePop: {
			//收藏弹窗
			show: false
		},

		sharePosterPop: {
			//分享海报
			show: false
		},

		sharePop: {
			//分享
			show: false
		},

		identifyAudioPop: {
			//语音输入
			show: false
		},

		selectPop: {
			//单多选
			show: false
		},

		qrcodePop: {
			//h5扫码
			show: false
		},

		addressBookPop: {
			//通讯录
			show: false
		},

		fileListPop: {
			//选择文件
			show: false
		}
	};

	//默认情况下是否展示标题栏
	function showHeader() {
		return platform() == "app";
	}

	//计算头部离顶上距离
	function headerTop(_type) {
		if (platform() == "mp" && _type) {
			var wxArea = wx.getMenuButtonBoundingClientRect();
			return wxArea.top + (_type == 2 ? wxArea.height : 0);
		}
		return showHeader() ? safeArea().top : 0;
	}

	//动态加载字体和大小
	function loadConfiguration(size) {
		return (
			"font-size:" +
			((G.appFontSize || 0) + (size || 0)) +
			"px;" +
			(G.appFont != "heitiSimplified" ? "font-family:" + G.appFont + ";" : "")
		);
	}

	//动态宽度配置
	function loadConfigurationSize(size, _who) {
		var changeSize = size || 0,
			cssWidth,
			cssHeight;
		if (isArray(size)) {
			cssWidth = "width:" + (G.appFontSize + (size[0] || 0)) + "px;";
			cssHeight = "height:" + (G.appFontSize + (size[1] || 0)) + "px;";
		} else {
			cssWidth = "width:" + (G.appFontSize + changeSize) + "px;";
			cssHeight = "height:" + (G.appFontSize + changeSize) + "px;";
		}
		if (!_who) {
			return cssWidth + cssHeight;
		} else {
			return _who == "w" ? cssWidth : cssHeight;
		}
	}

	//动态计算
	function dataForNum(_num) {
		return Array.from({length: _num || 0}).fill("");
	}

	//获取item参数
	function getItemForKey(_value, _list, _key, _child) {
		if (!isParameters(_list)) return;
		var hasChild = false,
			listLength = _list.length;
		for (var i = 0; i < listLength; i++) {
			var listItem = _list[i];
			if (isArray(listItem)) {
				hasChild = true;
				var result = getItemForKey(_value, listItem, _key, true);
				if (result) return result;
			} else {
				if (!isObject(listItem)) {
					if (listItem === _value) return listItem;
				} else {
					var listItemKey = listItem[_key || "key"];
					if (isArray(listItemKey)) {
						hasChild = true;
						var result = getItemForKey(_value, listItemKey, _key, true);
						if (result) {
							return listItem;
						}
					} else if (!isObject(listItemKey) && listItemKey === _value) {
						listItem["_i"] = i;
						return listItem;
					}
				}
				if (isObject(listItem) && isArray(listItem.children)) {
					hasChild = true;
					var result = getItemForKey(_value, listItem.children, _key, true);
					if (result) return result;
				}
			}
		}
		if (!_child && !hasChild) return false;
	}

	//删除item中的元素
	function delItemForKey(_obj, _list, _key) {
		var contrastObj = !isObject(_obj) ? _obj : _obj[_key || "key"];
		for (var i = 0; i < _list.length; i++) {
			if (
				(!isObject(_list[i]) ? _list[i] : _list[i][_key || "key"]) === contrastObj
			) {
				_list.splice(i, 1);
				delItemForKey(_obj, _list, _key);
				break;
			}
		}
	}

	//web和小程序阻止底部事件
	function stopBubble(e) {
		if (!e) return;
		if (platform() == "web") {
			e.preventDefault();
			e.stopPropagation();
		} else if (platform() == "mp") {
			e.$_canBubble = false;
		}
	}

	//移动事件 不左滑关闭屏幕
	function touchmove() {
		G.nTouchmove = true;
		clearTimeout(G.touchmoveTask);
		G.touchmoveTask = setTimeout(function() {
			G.nTouchmove = false;
		}, 1000);
	}

	//适配图片
	function showImg(_item, _add) {
		if (_add === void 0) {
			_add = "";
		}
		var baseUrl = isObject(_item) ? _item.url || "" : _item || "";
		if (!baseUrl) return;
		if (/^(?!h|(\.)|(\/)).*/.test(baseUrl)) {
			baseUrl =
				appUrl() +
				"image/" +
				_add +
				(baseUrl.indexOf("-compress-") > -1
					? baseUrl.split("-compress-")[1]
					: baseUrl);
		}
		return baseUrl + (baseUrl.indexOf("?") != -1 ? "&" : "?") + "v=" + G.v;
	}

	//获取元素位置宽高
	function getBoundingClientRect(_id, _callback) {
		if (!document.getElementById(_id)) return;
		if (platform() == "mp") {
			document
				.getElementById(_id)
				.$$getBoundingClientRect()
				.then(function(res) {
					return _callback(res);
				});
		} else {
			return _callback(document.getElementById(_id).getBoundingClientRect());
		}
	}

	//颜色转成rgba
	function colorRgba(_color, _alpha) {
		if (!_color) return;
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		var color = _color.toLowerCase();
		if (reg.test(color)) {
			if (color.length === 4) {
				var colorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
				}
				color = colorNew;
			}
			var colorChange = [];
			for (var i = 1; i < 7; i += 2) {
				colorChange.push(parseInt("0x" + color.slice(i, i + 2)));
			}
			return (
				"rgba(" +
				colorChange.join(",") +
				"," +
				(isParameters(_alpha) ? _alpha : "1") +
				")"
			);
		} else {
			return color;
		}
	}

	//删除所有缓存
	function cleanAllMsg() {
		var exitPrefs = [
			"isAutoLogin",
			"loginPassword",
			"sys_token",
			"sys_Id",
			"sys_UserID",
			"sys_UserName",
			"sys_AppPhoto",
			"sys_Mobile",
			"sys_Position",
			"sys_aresId",
			"sys_OfficeId",
			"sys_SpecialRoleKeys",
			"sdt_signin_phone",
			"public_token",
			"last14Msg",
			"sys_unread"
		];
		exitPrefs.forEach(function(_eItem) {
			removePrefs(_eItem);
		});
	}

	//保存登录信息
	function saveLogin(_info) {
		var infolist = _info || {};
		setPrefs("sys_Id", infolist.id);
		setPrefs("sys_UserID", infolist.accountId);
		setPrefs("sys_UserName", infolist.userName);
		setPrefs("sys_AppPhoto", infolist.headImg || infolist.photo); //headImg用户头像 photo代表头像
		setPrefs("sys_Mobile", infolist.mobile);
		setPrefs("sys_Position", infolist.position);
		if (!getPrefs("sys_aresId")) {
			setPrefs("sys_aresId", infolist.areaId);
		}
		setPrefs("sys_OfficeId", infolist.officeId);
		setPrefs(
			"sys_SpecialRoleKeys",
			JSON.stringify(infolist.specialRoleKeys || [])
		);
		if (infolist.id) {
			// //修改头像后保存下缓存数据
			setAllChatInfo({
				id: infolist.accountId,
				name: infolist.userName,
				url: infolist.headImg || infolist.photo
			});
		}
		sendEvent({name: "sys_refresh"});
	}

	//获取链接中参数
	function getOtherParam(_url) {
		if (_url.indexOf("?") != -1) {
			_url = _url.substring(_url.indexOf("?") + 1);
		}
		var params = _url.split("&"),
			rp = {};
		for (var j = 0; j < params.length; j++) {
			if (params[j]) {
				var data_key = params[j].substring(0, params[j].indexOf("="));
				if (!data_key) {
					continue;
				}
				var data_value = decodeURIComponent(
					params[j].substring(params[j].indexOf("=") + 1)
				);
				if (data_value.indexOf("{") == 0 || data_value.indexOf("[") == 0)
					data_value = JSON.parse(data_value);
				rp[data_key] = data_value;
			}
		}
		return rp;
	}

	//通用上传附件 item格式  url本地地址路径 uploadId上传后的id	state状态【1上传中2完成3失败】
	function uploadFile(_item, callback) {
		if (_item._fileAjax || _item.module == "-noUpload") return;
		_item._fileAjax = true;
		_item.state = 1;
		if (_item.showToast) {
			showProgress("上传中");
		}
		var nCallack = function nCallack(ret, err) {
			hideProgress();
			var code = ret ? ret.code : "";
			if (code == 200) {
				var data = ret.data || {};
				_item.state = 2;
				_item.uploadId = data.id;
				_item.otherInfo = data;
			} else {
				_item.state = 3;
				_item.error = ret ? ret.message || ret.data : err.data || err.body || "";
			}
			callback && callback(_item);
		};
		if (platform() == "mp") {
			wx.uploadFile({
				url: appUrl() + "file/upload",
				filePath: _item.url.path,
				name: "file",
				header: {
					"Content-Type": "multipart/form-data",
					"u-login-areaId": areaId(),
					Authorization: getPrefs("sys_token") || ""
				},

				success: function success(res) {
					nCallack(JSON.parse(res.data), null);
				},
				fail: function fail(err) {
					nCallack(null, JSON.parse(err.data));
				}
			});
		} else {
			ajax(
				{u: appUrl() + "file/upload", web: _item.web},
				"file/upload" + _item.url,
				nCallack,
				"上传附件",
				"post",
				{
					files: {file: _item.url}
				},
				{"content-type": "file"}
			);
		}
	}

	//展示省略文字
	function showTextSize(_text, _size, _middle) {
		if (_size && _text) {
			if (_text.length > _size) {
				if (_middle) {
					var mSize = _size / 2;
					var nLast = getSizeText(_text, mSize);
					var nNext = getSizeText(_text, mSize, 1);
					if (nLast.length + nNext.length < _text.length) {
						_text = nLast + "..." + nNext;
					}
				} else {
					var nText = getSizeText(_text, _size);
					_text = nText + (nText.length < _text.length ? "..." : "");
				}
			}
		}
		return _text;
	}

	function getSizeText(_text, _size, _next) {
		var texts = _text.split("");
		var nowSize = 0,
			nowLength = 0;
		if (_next) {
			for (var i = texts.length - 1; i >= 0; i--) {
				nowSize += /[\u4E00-\u9FA5]|[\u0800-\u4E00]/.test(texts[i]) ? 1 : 0.7;
				nowLength++;
				if (nowSize >= _size) {
					break;
				}
			}
			return _text.substring(texts.length - nowLength);
		} else {
			for (var i = 0; i < texts.length; i++) {
				nowSize += /[\u4E00-\u9FA5]|[\u0800-\u4E00]/.test(texts[i]) ? 1 : 0.7;
				nowLength++;
				if (nowSize >= _size) {
					break;
				}
			}
			return _text.substring(0, nowLength);
		}
	}

	// 判断是否为微信环境
	function isWeChat() {
		return /micromessenger/i.test(window.navigator.userAgent);
	}

	// 判断是否为小程序环境
	function isMiniProgram() {
		// 通过wx对象存在与否进行判断
		if (typeof wx === "object" && typeof wx.miniprogram === "object") {
			return true;
		} else {
			return false;
		}
	}

	var module6 = {
		name: sysSign() == "rd" ? "意见征集" : "网络议政",
		code: "6",
		businessCode: "opinioncollect"
	};
	var module9 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息",
		code: "9",
		businessCode: "cppcc_member"
	};
	var module9_1 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息变更申请",
		code: "9_1",
		businessCode: "cppccMemberCheckPrepare"
	};
	var module12 = {
		name: sysSign() == "rd" ? "圈子" : "委员说",
		code: "12",
		businessCode: "styleCircle"
	};

	//打开链接
	function openWin_url(_param) {
		_param.url = handleSYSLink(_param.url);
		if (
			(_param.wopen || _param.url.indexOf("wopen") != -1) &&
			platform() == "web"
		) {
			window.open(_param.url);
		} else {
			openWin("mo_details_url", "../mo_details_url/mo_details_url.stml", _param);
		}
	}

	//打开搜索
	function openWin_search(_param) {
		openWin("mo_search", "../mo_search/mo_search.stml", _param);
	}

	//打开通用新增
	function openWin_add(_param) {
		openWin("mo_add", "../mo_add/mo_add.stml", _param);
	}

	//打开图片预览
	function openWin_imgPreviewer(_param) {
		G.imgPreviewerPop = {
			show: true,
			index: _param.index,
			imgs: _param.imgs
		};

		console.log(JSON.stringify(G.imgPreviewerPop));
	}

	//打开聊天转发
	function openWin_chat_share(_param, _callback) {
		_param.title = _param.title || "发送给";
		_param.callback = _param.callback || "chat_share_callback";
		addEventListener(_param.callback, function(ret) {
			_callback && _callback(ret);
		});
		openWin("mo_chat_share", "../mo_chat_share/mo_chat_share.stml", _param);
	}

	//打开聊天用户相关
	function openWin_chat_user(_param, _callback) {
		_param.callback = _param.callback || "chat_user_callback";
		addEventListener(_param.callback, function(ret) {
			_callback && _callback(ret);
		});
		openWin("mo_chat_user", "../mo_chat_user/mo_chat_user.stml", _param);
	}

	//打开代表信息
	function openWin_npcinfo(_item) {
		var openPage = _item.id ? "mo_npcinfo_details" : "mo_npcinfo_list";
		var param = {};
		param.code = module9.code;
		param = setNewJSON(param, _item);
		openWin(
			openPage + (_item.id || module9.code),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
	}

	//区域截图
	function screenshotIntoPic(_id, _callback, _dm) {
		switch (platform()) {
			case "app":
				api.screenCapture(
					{
						region: _id
					},
					function(ret) {
						_callback(ret ? ret.savePath || "" : "");
					}
				);
				break;
			case "web":
				var imgData = _dm.shareImg || document.querySelector(_id).toDataURL() || "";
				_dm.shareImg = imgData;
				_callback(imgData);
				break;
			case "mp":
				document
					.querySelector(_id)
					.$$getNodesRef()
					.then(function(nodesRef) {
						nodesRef
							.node(function(res) {
								var canvas = res.node;
								wx.canvasToTempFilePath({
									canvas: canvas,
									success: function success(res) {
										_callback(res.tempFilePath);
									},
									fail: function fail() {
										_callback("");
									}
								});
							})
							.exec();
					});
				break;
		}
	}

	//图片转成file
	function dataURLtoFile(_path) {
		var data = _path.split(",");
		var type = data[0].match(/:(.*?);/)[1];
		var suffix = type.split("/")[1];
		var bstr = window.atob(data[1]);
		var n = bstr.length;
		var u8Arr = new Uint8Array(n);
		while (n--) {
			u8Arr[n] = bstr.charCodeAt(n);
		}
		return new File([u8Arr], new Date().valueOf() + "." + suffix, {type: type});
	}

	//图片保存到相册
	function picInfoAlbum(_path, _callback) {
		switch (platform()) {
			case "app":
				api.saveMediaToAlbum(
					{
						path: _path
					},
					function(ret) {
						_callback(ret && ret.status ? "已保存到手机相册" : "");
					}
				);
				break;
			case "web":
				try {
					var base64 = _path.split(",")[1];
					var imgName = new Date().valueOf() + ".png";
					var trans = window.trans || window.parent.trans;
					if (trans) {
						trans.saveImage(
							{
								base64Str: base64,
								album: true,
								imgPath: "fs://file_temporary/",
								imgName: imgName
							},
							function(ret, err) {
								if (ret) {
									_callback(ret && ret.status ? "已保存到手机相册" : "");
								} else {
									_callback(false, err.msg);
								}
							}
						);
						return;
					}
					if (isWeChat() || isMiniProgram()) {
						toast("请长按图片保存或转发");
						return;
					}
					var bytes = atob(base64);
					var ab = new ArrayBuffer(bytes.length);
					var ia = new Uint8Array(ab);
					for (var i = 0; i < bytes.length; i++) {
						ia[i] = bytes.charCodeAt(i);
					}
					var blob = new Blob([ab], {type: "application/octet-stream"});
					var url = URL.createObjectURL(blob);
					var a = document.createElement("a");
					a.href = url;
					a.download = imgName;
					var e = document.createEvent("MouseEvents");
					e.initMouseEvent(
						"click",
						true,
						false,
						window,
						0,
						0,
						0,
						0,
						0,
						false,
						false,
						false,
						false,
						0,
						null
					);
					a.dispatchEvent(e);
					URL.revokeObjectURL(url);
					_callback("已保存");
				} catch (e) {
					_callback(false);
				}
				break;
			case "mp":
				wx.saveImageToPhotosAlbum({
					filePath: _path,
					success: function success() {
						_callback("已保存到手机相册");
					},
					fail: function fail() {
						_callback(false);
					}
				});

				break;
		}
	}

	//点
	function canvasPoint(x, y) {
		return {x: x, y: y};
	}

	//圆角：区域、角度、ctx对象
	function canvasRoundedRect(rect, r, ctx) {
		var lt, lb, rt, rb;
		if (isArray(r)) {
			lt = r[0];
			rt = r[1];
			rb = r[2];
			lb = r[3];
		} else {
			lt = r;
			rt = r;
			rb = r;
			lb = r;
		}
		var ptA = canvasPoint(rect.x + lt, rect.y);
		var ptB = canvasPoint(rect.x + rect.width, rect.y);
		var ptC = canvasPoint(rect.x + rect.width, rect.y + rect.height);
		var ptD = canvasPoint(rect.x, rect.y + rect.height);
		var ptE = canvasPoint(rect.x, rect.y);
		ctx.beginPath();
		ctx.moveTo(ptA.x, ptA.y);
		ctx.arcTo(ptB.x, ptB.y, ptC.x, ptC.y, rt);
		ctx.arcTo(ptC.x, ptC.y, ptD.x, ptD.y, rb);
		ctx.arcTo(ptD.x, ptD.y, ptE.x, ptE.y, lb);
		ctx.arcTo(ptE.x, ptE.y, ptA.x, ptA.y, lt);
		ctx.closePath();
	}

	//计算文字地区
	function canvasCalcText(_text, _width, _left, _top, _margin, ctx) {
		_text = _text + "";
		if (ctx.measureText(_text).width < _width) {
			ctx.fillText(_text, _left, _top);
			return;
		}
		var nowText = "",
			nowList = _text.split("");
		for (var i = 0; i < nowList.length; i++) {
			nowText += nowList[i];
			if (ctx.measureText(nowText).width > _width + 5) {
				nowText = nowText.substring(0, nowText.length - 1);
				break;
			}
		}
		ctx.fillText(nowText, _left, _top);
		if (nowText != _text) {
			canvasCalcText(
				_text.substring(nowText.length),
				_width,
				_left,
				_top + _margin,
				_margin,
				ctx
			);
		}
	}

	//展示文字
	function canvasShowText(_param, ctx) {
		getBoundingClientRect(_param.id, function(ret) {
			ctx.save();
			ctx.textBaseline = _param.tLine || "top";
			ctx.textAlign = _param.tAlign || "left";
			ctx.fillStyle = _param.color || "#333333";
			ctx.font =
				(_param.weight || "400") +
				" " +
				(G.appFontSize + (_param.size || 0)) +
				"px Arial";
			canvasCalcText(
				_param.text,
				ret.width,
				ret.left - _param.bRect.left,
				ret.top - _param.bRect.top + (_param.spacing || 5) / 2 + 2,
				G.appFontSize + (_param.size || 0) + (_param.spacing || 5),
				ctx
			);
			ctx.restore();
		});
	}

	//展示图片 type:none|cover|contain
	function canvasShowImg(_param, ctx, canvas) {
		getBoundingClientRect(_param.id, function(ret) {
			var image = platform() == "mp" ? canvas.createImage() : new Image();
			image.crossOrigin = "anonymous";
			image.onload = function() {
				ctx.save();
				var imgLeft = ret.left - _param.bRect.left,
					imgTop = ret.top - _param.bRect.top;
				canvasRoundedRect(
					{x: imgLeft, y: imgTop, width: ret.width, height: ret.height},
					_param.round || 0,
					ctx
				);
				if (_param.bg) {
					ctx.fillStyle = _param.bg;
					ctx.fill();
				}
				ctx.clip();
				if (_param.type == "cover" || _param.type == "contain") {
					var sx, sy, sw, sh, imgRatio, canvasRatio;
					canvasRatio = ret.width / ret.height;
					imgRatio = image.width / image.height;
					if (imgRatio <= canvasRatio) {
						sw = {cover: image.width, contain: imgRatio * ret.width}[_param.type];
						sh = {cover: image.width / canvasRatio, contain: ret.height}[_param.type];
						sx = {cover: 0, contain: (ret.width - imgRatio * ret.width) / 2}[
							_param.type
						];
						sy = {cover: (image.height - image.width / canvasRatio) / 2, contain: 0}[
							_param.type
						];
					} else {
						sw = {cover: image.height * canvasRatio, contain: ret.width}[_param.type];
						sh = {cover: image.height, contain: ret.width / imgRatio}[_param.type];
						sx = {cover: (image.width - image.height * canvasRatio) / 2, contain: 0}[
							_param.type
						];
						sy = {cover: 0, contain: (ret.height - ret.width / imgRatio) / 2}[
							_param.type
						];
					}
					if (_param.type == "cover") {
						ctx.drawImage(
							this,
							sx,
							sy,
							sw,
							sh,
							imgLeft,
							imgTop,
							ret.width,
							ret.height
						);
					} else {
						ctx.drawImage(this, imgLeft + sx, imgTop + sy, sw, sh);
					}
				} else {
					ctx.drawImage(this, imgLeft, imgTop, ret.width, ret.height);
				}
				ctx.restore();
				_param.callback && _param.callback(true);
			};
			image.onerror = function(e) {
				_param.callback && _param.callback(false);
			};
			image.src = _param.src || "";
		});
	}

	//画布中加载图片回调
	function canvasImgLoad(_dm) {
		if (_dm.canvasImg <= 0) {
			if (platform() == "web" && (isWeChat() || isMiniProgram())) {
				toast("请长按图片保存或转发");
			}
			setTimeout(function() {
				hideProgress();
				if (platform() == "web") {
					screenshotIntoPic("#sharePosterCanvas", function(_path) {}, _dm);
				}
			}, 50);
		}
	}

	var SharePoster = /*@__PURE__*/ (function(Component) {
		function SharePoster(props) {
			Component.call(this, props);
			this.data = {
				show: false,

				btns: [
					{
						show: true,
						key: "platform",
						value: "平台好友",
						type: "img",
						src: appUrl() + "pageImg/open/logo"
					},
					// {show:true,key:"session",value:"微信好友",type:"icon",bg:"#50C614",color:"#FFF",src:"changyonglogo28",size:16},
					// {show:true,key:"timeline",value:"朋友圈",type:"icon",bg:"#50C614",color:"#FFF",src:"pengyouquan",size:13},
					{
						show: true,
						key: "QFriend",
						value: "QQ",
						type: "icon",
						bg: "#3AA2F3",
						color: "#FFF",
						src: "QQ",
						size: 13
					},
					{
						show: true,
						key: "QZone",
						value: "QQ空间",
						type: "icon",
						bg: "#3AA2F3",
						color: "#FFF",
						src: "pengyouquan",
						size: 13
					},
					{
						show: true,
						key: "save",
						value: "保存图片",
						type: "icon",
						bg: "",
						color: "",
						src: "zhixiangxia",
						size: 13
					}
				]
			};
			this.compute = {
				monitor: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.baseInit();
						}
					}
				}
			};
		}

		if (Component) SharePoster.__proto__ = Component;
		SharePoster.prototype = Object.create(Component && Component.prototype);
		SharePoster.prototype.constructor = SharePoster;
		SharePoster.prototype.baseInit = function() {
			var showBtns = this.props.dataMore.btns || [];
			this.data.btns.forEach(function(_item) {
				_item.show = !showBtns.length || getItemForKey(_item.key, showBtns);
				if (_item.key != "save" && _item.key != "platform") {
					_item.show = _item.show && platform() == "app";
				}
			});
		};
		SharePoster.prototype.penetrate = function() {};
		SharePoster.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		SharePoster.prototype.shareBtn = function(_item) {
			var this$1 = this;

			switch (_item.key) {
				case "platform":
					var openCallback = function(_path) {
						openWin_chat_share(
							{
								fType: "sendImageMessage",
								fImagePath: _path
							},
							function() {
								this$1.closePage();
								G.sharePop.show = false;
							}
						);
					};
					var savecallback = function(_path) {
						if (_path) {
							if (platform() != "web") {
								openCallback(_path);
							} else {
								showProgress("分享中");
								uploadFile({url: dataURLtoFile(_path)}, function(ret) {
									hideProgress();
									if (ret.state == 2) {
										openCallback(appUrl() + "image/" + ret.otherInfo.newFileName);
									} else {
										toast(ret.error);
									}
								});
							}
						} else {
							toast("保存失败");
						}
					};
					screenshotIntoPic(
						platform() == "app" ? "#sharePoster" : "#sharePosterCanvas",
						savecallback,
						this.props.dataMore
					);
					break;
				case "save":
					var savecallback = function(_path) {
						if (_path) {
							picInfoAlbum(_path, function(ret, err) {
								toast(ret || err || "保存失败");
								if (ret) {
									setTimeout(function() {
										this$1.closePage();
										G.sharePop.show = false;
									}, 500);
								}
							});
						} else {
							toast("保存失败");
						}
					};
					screenshotIntoPic(
						platform() == "app" ? "#sharePoster" : "#sharePosterCanvas",
						savecallback,
						this.props.dataMore
					);
					break;
				case "QFriend":
				case "QZone":
					var qq = api.require("QQPlus");
					if (!qq) {
						toast("未绑定模块，请联系管理员");
						return;
					}
					qq.setIsPermissionGranted({granted: true});
					qq.installed(function(ret, err) {
						if (!ret.status) {
							toast("当前设备未安装QQ客户端");
							return;
						}
						api.screenCapture(
							{
								region: "#sharePoster"
							},
							function(ret, err) {
								var param = {
									imgPath: ret.savePath,
									type: _item.key
								};

								console.log(JSON.stringify(param));
								qq.shareImage(param, function(ret, err) {
									toast(ret.status ? "分享成功！" : JSON.stringify(err));
								});
							}
						);
					});
					break;
				case "session":
				case "timeline":
					var wx = api.require("wxPlus");
					if (!wx) {
						toast("未绑定模块，请联系管理员");
						return;
					}
					wx.isInstalled(function(ret, err) {
						if (!ret.installed) {
							toast("当前设备未安装wx客户端");
							return;
						}
						api.screenCapture(
							{
								region: "#sharePoster"
							},
							function(ret, err) {
								var param = {
									contentUrl: ret.savePath,
									scene: _item.key
								};

								console.log(JSON.stringify(param));
								wx.shareImage(param, function(ret, err) {
									toast(ret.status ? "分享成功！" : JSON.stringify(err));
								});
							}
						);
					});
					break;
				default:
					toast("分享到" + _item.value);
					break;
			}
		};
		SharePoster.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.data.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				this.data.show && [
					apivm.h(
						"view",
						{class: "flex_h xy_center"},
						apivm.h(
							"view",
							{class: "poster_base"},
							this.props.dataMore.shareImg
								? apivm.h("image", {
										class: "poster_box",
										style: this.props.canvasStyle,
										src: this.props.dataMore.shareImg,
										mode: "aspectFill",
										thumbnail: "false"
								  })
								: [
										apivm.h("canvas", {
											type: "2d",
											id: "sharePosterCanvas",
											class: "canvas",
											style:
												"display:" +
												(platform() != "app" ? "flex" : "none") +
												";" +
												this.props.canvasStyle
										}),
										this.props.children
								  ]
						)
					),
					apivm.h(
						"view",
						{
							class: "share_warp",
							onClick: function() {
								return this$1.penetrate();
							}
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							{class: "share_btn_box"},
							this.data.btns.map(function(item, index, list) {
								return (
									(isParameters(item.show) ? item.show : true) &&
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.shareBtn(item);
											},
											class: "share_btn_item"
										},
										item.type == "img"
											? apivm.h("image", {
													style: "" + loadConfigurationSize(34),
													class: "share_btn_icon",
													src: item.src
											  })
											: apivm.h(
													"view",
													{
														style:
															loadConfigurationSize(34) +
															"background:" +
															(item.bg || "#F4F5F7") +
															";",
														class: "share_btn_icon"
													},
													apivm.h("a-iconfont", {
														name: item.src,
														color: item.color || "#333",
														size:
															G.appFontSize + (isParameters(item.size) ? Number(item.size) : 8)
													})
											  ),
										apivm.h(
											"text",
											{style: loadConfiguration(-2) + "color:#333;margin-top:8px;"},
											item.value
										)
									)
								);
							})
						),
						apivm.h(
							"view",
							null,
							!this.props.dataMore.dotCancel &&
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										style:
											"padding:18px;justify-content:center;border-top:10px solid rgba(0,0,0,0.03);"
									},
									apivm.h(
										"text",
										{style: loadConfiguration() + ";color:#333;text-align: center;"},
										"取消"
									)
								)
						),
						apivm.h("view", {style: "padding-bottom:" + safeArea().bottom + "px;"})
					)
				]
			);
		};

		return SharePoster;
	})(Component);
	SharePoster.css = {
		".poster_base": {width: "320px"},
		".canvas": {position: "absolute", left: "0", right: "0", zIndex: "11"},
		".poster_box": {
			background: "#FFFFFF",
			borderRadius: "10px",
			overflow: "hidden"
		},
		".share_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px",
			flexShrink: "0",
			maxHeight: "80%"
		},
		".share_btn_box": {padding: "10px 0", flexDirection: "row", flexWrap: "wrap"},
		".share_btn_item": {
			padding: "10px 5px",
			width: "25%",
			alignItems: "center",
			justifyContent: "center"
		},
		".share_btn_icon": {
			borderRadius: "50%",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("share-poster", SharePoster);

	var ZImage = /*@__PURE__*/ (function(Component) {
		function ZImage(props) {
			Component.call(this, props);
			this.data = {
				imgId: "img_" + getNum(),
				imgMode: this.props.mode || "aspectFill",
				imgThumbnail: isParameters(this.props.thumbnail)
					? this.props.thumbnail
					: true,
				showFilter: false,
				show: true
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					var scrollBox = this.props.scrollBox;
					if (scrollBox) {
						getBoundingClientRect("box_" + this.data.imgId, function(ret) {
							// console.log("scrollBox:----"+JSON.stringify(scrollBox) + "--" + api.winHeight);
							// console.log("img:-------"+JSON.stringify(ret));
							this$1.data.show = !(
								ret.top + ret.height < -150 || ret.top - 150 > api.winHeight
							);
						});
					}
				}
			};
		}

		if (Component) ZImage.__proto__ = Component;
		ZImage.prototype = Object.create(Component && Component.prototype);
		ZImage.prototype.constructor = ZImage;
		ZImage.prototype.load = function(e) {
			if (!this.props.src || !document.getElementById(this.data.imgId)) {
				return;
			}
			var nowProportion = 0;
			var imgWidth =
				platform() == "app"
					? e.detail.width
					: document.getElementById(this.data.imgId).naturalWidth;
			var imgHeight =
				platform() == "app"
					? e.detail.height
					: document.getElementById(this.data.imgId).naturalHeight;
			if (imgWidth && imgHeight) {
				nowProportion = Number((imgWidth / imgHeight).toFixed(2));
			}
			if (nowProportion && this.props.proportionMin && this.props.proportionMax) {
				if (
					nowProportion < Number(this.props.proportionMin) ||
					nowProportion > Number(this.props.proportionMax)
				) {
					this.data.showFilter = true;
					this.data.imgMode = "aspectFit";
				} else {
					this.data.showFilter = false;
					this.data.imgMode = this.props.mode || "aspectFill";
				}
			}
		};
		ZImage.prototype.error = function() {
			this.fire("error");
		};
		ZImage.prototype.render = function() {
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					class: "" + (this.props.class || ""),
					style:
						"overflow:hidden;border-radius: " +
						(this.props.round ? "50%" : "0px") +
						";width:100%;height:100%;" +
						(this.props.style || "")
				},
				this.data.showFilter &&
					this.data.show &&
					apivm.h("image", {
						class: "z_imageFilter",
						mode: "aspectFill",
						src: this.props.src,
						thumbnail: true
					}),
				apivm.h(
					"view",
					{class: "z_image", id: "box_" + this.data.imgId},
					this.data.show &&
						apivm.h("image", {
							id: this.data.imgId,
							class: "xy_100",
							mode: this.data.imgMode,
							src: this.props.src,
							thumbnail: this.data.imgThumbnail,
							onLoad: this.load,
							onError: this.error
						})
				)
			);
		};

		return ZImage;
	})(Component);
	ZImage.css = {
		".z_image": {
			width: "100%",
			height: "100%",
			filter: "none",
			opacity: "1",
			position: "absolute",
			left: "0",
			top: "0"
		},
		".z_imageFilter": {
			width: "100%",
			height: "100%",
			filter: "blur(4px)",
			opacity: "0.7",
			position: "relative",
			left: "0",
			top: "0"
		}
	};
	apivm.define("z-image", ZImage);

	var ZAvatar = /*@__PURE__*/ (function(Component) {
		function ZAvatar(props) {
			Component.call(this, props);
			this.data = {
				esrc: appUrl() + "img/default_user_head.jpg",
				asrc: null,
				bsrc: ""
			};
			this.compute = {
				monitor: function() {
					var url = isObject(this.props.src) ? this.props.src.url : this.props.src;
					if (this.data.asrc != url || !this.data.asrc) {
						this.data.asrc = url || this.data.esrc;
						this.data.bsrc = "";
					}
				}
			};
		}

		if (Component) ZAvatar.__proto__ = Component;
		ZAvatar.prototype = Object.create(Component && Component.prototype);
		ZAvatar.prototype.constructor = ZAvatar;
		ZAvatar.prototype.error = function() {
			this.data.bsrc = this.data.esrc;
		};
		ZAvatar.prototype.render = function() {
			return apivm.h(
				"view",
				{s: this.monitor, class: "xy_100"},
				apivm.h("z-image", {
					round: true,
					mode: "scaleToFill",
					thumbnail: "false",
					src: showImg(this.data.bsrc || this.data.asrc, "150x150-compress-"),
					onError: this.error
				})
			);
		};

		return ZAvatar;
	})(Component);
	apivm.define("z-avatar", ZAvatar);

	var SharePosterGroup = /*@__PURE__*/ (function(Component) {
		function SharePosterGroup(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				canvasStyle: ""
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show && platform() != "app") {
							setTimeout(function() {
								this$1.baseInit();
							}, 0);
						}
					}
				}
			};
		}

		if (Component) SharePosterGroup.__proto__ = Component;
		SharePosterGroup.prototype = Object.create(Component && Component.prototype);
		SharePosterGroup.prototype.constructor = SharePosterGroup;
		SharePosterGroup.prototype.baseInit = function() {
			var this$1 = this;

			var dm = this.props.dataMore;
			var dmd = this.props.dataMore.data;
			var createPic = function(canvas) {
				showProgress();
				var ctx;
				var bRect = null;

				dm.canvasImg = 0;
				dm.canvasErr = "";

				//设置画布大小背景圆角
				getBoundingClientRect("sharePoster", function(ret) {
					bRect = ret;
					this$1.data.canvasStyle =
						"width:" + bRect.width + "px;height:" + bRect.height + "px;";
					var dpr =
						platform() == "mp"
							? wx.getSystemInfoSync().pixelRatio
							: window.devicePixelRatio;
					canvas.width = bRect.width * dpr;
					canvas.height = bRect.height * dpr;
					ctx = canvas.getContext("2d");
					ctx.scale(dpr, dpr);
					canvasRoundedRect(
						{x: 0, y: 0, width: bRect.width, height: bRect.height},
						10,
						ctx
					);
					ctx.fillStyle = "white";
					ctx.fill();
					ctx.restore();
					var loadOtherElement = function() {
						// 头像
						dm.canvasImg++;
						canvasShowImg(
							{
								id: "posterImg",
								src: showImg(dmd.url),
								round: 15,
								bRect: bRect,
								callback: function(ret) {
									dm.canvasImg--;
									if (!ret) {
										dm.canvasErr = true;
									}
									canvasImgLoad(dm);
								}
							},
							ctx,
							canvas
						);
						//群名
						canvasShowText(
							{
								id: "posterName",
								text: dmd.name,
								color: "#333",
								weight: 600,
								size: 1,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);

						//二维码
						dm.canvasImg++;
						canvasShowImg(
							{
								id: "posterQr",
								src: showImg(tomcatAddress() + "utils/qr?text=" + dmd.shareUrl),
								round: 0,
								bRect: bRect,
								callback: function(ret) {
									dm.canvasImg--;
									if (!ret) {
										dm.canvasErr = true;
									}
									canvasImgLoad(dm);
								}
							},
							ctx,
							canvas
						);
						canvasImgLoad(dm);
					};
					//有水印
					if (G.watermark) {
						canvasShowImg(
							{
								id: "sharePoster",
								src: G.watermark,
								round: 10,
								bRect: bRect,
								callback: function(ret) {
									loadOtherElement();
								}
							},
							ctx,
							canvas
						);
					} else {
						loadOtherElement();
					}
				});
			};
			var _id = document.querySelector("#sharePosterCanvas");
			if (!_id) {
				return;
			}
			switch (platform()) {
				case "web":
					createPic(_id);
					break;
				case "mp":
					_id.$$getNodesRef().then(function(nodesRef) {
						nodesRef
							.node(function(res) {
								createPic(res.node);
							})
							.exec();
					});
					break;
			}
		};
		SharePosterGroup.prototype.render = function() {
			return apivm.h(
				"share-poster",
				{
					s: this.monitor,
					dataMore: this.props.dataMore,
					canvasStyle: this.data.canvasStyle
				},
				this.props.dataMore.show && [
					apivm.h(
						"view",
						{id: "sharePoster", class: "poster_box"},
						apivm.h("view", {
							class: "watermark_box",
							style:
								"display:" +
								(platform() != "app" ? "flex" : "none") +
								";z-index:2;background:#FFF;"
						}),
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							{style: "padding:15px;"},
							apivm.h(
								"view",
								{class: "flex_row", style: "margin-bottom:20px;"},
								apivm.h(
									"view",
									{id: "posterImg", style: loadConfigurationSize(29)},
									apivm.h("z-avatar", {src: showImg(this.props.dataMore.data)})
								),
								apivm.h(
									"text",
									{
										id: "posterName",
										style:
											loadConfiguration(1) +
											"flex:1;color:#333;font-weight: 600;margin-left:6px;"
									},
									this.props.dataMore.data.name
								)
							),
							apivm.h(
								"view",
								{class: "xy_center"},
								apivm.h("image", {
									id: "posterQr",
									style: "" + loadConfigurationSize(234),
									src:
										tomcatAddress() +
										"utils/qr?text=" +
										this.props.dataMore.data.shareUrl,
									mode: "aspectFill"
								})
							)
						)
					)
				]
			);
		};

		return SharePosterGroup;
	})(Component);
	SharePosterGroup.css = {
		".poster_top": {padding: "20px 20px", width: "100%", minHeight: "94px"}
	};
	apivm.define("share-poster-group", SharePosterGroup);

	var SECONDS_A_MINUTE$1 = 60;
	var SECONDS_A_HOUR$1 = SECONDS_A_MINUTE$1 * 60;
	var SECONDS_A_DAY$1 = SECONDS_A_HOUR$1 * 24;
	var SECONDS_A_WEEK$1 = SECONDS_A_DAY$1 * 7;
	var MILLISECONDS_A_SECOND$1 = 1e3;
	var MILLISECONDS_A_MINUTE$1 = SECONDS_A_MINUTE$1 * MILLISECONDS_A_SECOND$1;
	var MILLISECONDS_A_HOUR$1 = SECONDS_A_HOUR$1 * MILLISECONDS_A_SECOND$1;
	var MILLISECONDS_A_DAY$1 = SECONDS_A_DAY$1 * MILLISECONDS_A_SECOND$1;
	var MILLISECONDS_A_WEEK$1 = SECONDS_A_WEEK$1 * MILLISECONDS_A_SECOND$1; // English locales

	var MS$1 = "millisecond";
	var S$1 = "second";
	var MIN$1 = "minute";
	var H$1 = "hour";
	var D$1 = "day";
	var W$1 = "week";
	var M$1 = "month";
	var Q$1 = "quarter";
	var Y$1 = "year";
	var DATE$1 = "date";
	var FORMAT_DEFAULT$1 = "YYYY-MM-DDTHH:mm:ssZ";
	var INVALID_DATE_STRING$1 = "Invalid Date"; // regex

	var REGEX_PARSE$1 = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/;
	var REGEX_FORMAT$1 = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;

	var en$1 = {
		name: "en",
		weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split(
			"_"
		),
		months: "January_February_March_April_May_June_July_August_September_October_November_December".split(
			"_"
		)
	};

	var padStart$1 = function padStart(string, length, pad) {
		var s = String(string);
		if (!s || s.length >= length) return string;
		return "" + Array(length + 1 - s.length).join(pad) + string;
	};
	var padZoneStr$1 = function padZoneStr(instance) {
		var negMinutes = -instance.utcOffset();
		var minutes = Math.abs(negMinutes);
		var hourOffset = Math.floor(minutes / 60);
		var minuteOffset = minutes % 60;
		return (
			"" +
			(negMinutes <= 0 ? "+" : "-") +
			padStart$1(hourOffset, 2, "0") +
			":" +
			padStart$1(minuteOffset, 2, "0")
		);
	};
	var monthDiff$1 = function monthDiff(a, b) {
		if (a.date() < b.date()) return -monthDiff(b, a);
		var wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month());
		var anchor = a.clone().add(wholeMonthDiff, M$1);
		var c = b - anchor < 0;
		var anchor2 = a.clone().add(wholeMonthDiff + (c ? -1 : 1), M$1);
		return +(
			-(
				wholeMonthDiff +
				(b - anchor) / (c ? anchor - anchor2 : anchor2 - anchor)
			) || 0
		);
	};
	var absFloor$1 = function absFloor(n) {
		return n < 0 ? Math.ceil(n) || 0 : Math.floor(n);
	};
	var prettyUnit$1 = function prettyUnit(u) {
		var special = {
			M: M$1,
			y: Y$1,
			w: W$1,
			d: D$1,
			D: DATE$1,
			h: H$1,
			m: MIN$1,
			s: S$1,
			ms: MS$1,
			Q: Q$1
		};

		return (
			special[u] ||
			String(u || "")
				.toLowerCase()
				.replace(/s$/, "")
		);
	};
	var isUndefined$1 = function isUndefined(s) {
		return s === undefined;
	};
	var U$1 = {
		s: padStart$1,
		z: padZoneStr$1,
		m: monthDiff$1,
		a: absFloor$1,
		p: prettyUnit$1,
		u: isUndefined$1
	};

	var L$1 = "en";
	var Ls$1 = {};
	Ls$1[L$1] = en$1;
	var isDayjs$1 = function isDayjs(d) {
		return d instanceof Dayjs$1;
	};
	var parseLocale$1 = function parseLocale(preset, object, isLocal) {
		var l;
		if (!preset) return L$1;
		if (typeof preset === "string") {
			var presetLower = preset.toLowerCase();
			if (Ls$1[presetLower]) {
				l = presetLower;
			}
			if (object) {
				Ls$1[presetLower] = object;
				l = presetLower;
			}
			var presetSplit = preset.split("-");
			if (!l && presetSplit.length > 1) {
				return parseLocale(presetSplit[0]);
			}
		} else {
			var name = preset.name;
			Ls$1[name] = preset;
			l = name;
		}
		if (!isLocal && l) L$1 = l;
		return l || (!isLocal && L$1);
	};
	var dayjs$1 = function dayjs(date, c) {
		if (isDayjs$1(date)) {
			return date.clone();
		}
		var cfg = typeof c === "object" ? c : {};
		cfg.date = date;
		cfg.args = arguments;
		return new Dayjs$1(cfg);
	};
	var wrapper$1 = function wrapper(date, instance) {
		return dayjs$1(date, {
			locale: instance.$L,
			utc: instance.$u,
			x: instance.$x,
			$offset: instance.$offset
		});
	};
	var Utils$1 = U$1;
	Utils$1.l = parseLocale$1;
	Utils$1.i = isDayjs$1;
	Utils$1.w = wrapper$1;
	var parseDate$1 = function parseDate(cfg) {
		var date = cfg.date,
			utc = cfg.utc;
		if (date === null) return new Date(NaN);
		if (Utils$1.u(date)) return new Date();
		if (date instanceof Date) return new Date(date);
		if (typeof date === "string" && !/Z$/i.test(date)) {
			var d = date.match(REGEX_PARSE$1);
			if (d) {
				var m = d[2] - 1 || 0;
				var ms = (d[7] || "0").substring(0, 3);
				if (utc) {
					return new Date(
						Date.UTC(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms)
					);
				}
				return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);
			}
		}
		return new Date(date);
	};
	var Dayjs$1 = (function() {
		function Dayjs(cfg) {
			this.$L = parseLocale$1(cfg.locale, null, true);
			this.parse(cfg);
		}
		var _proto = Dayjs.prototype;
		_proto.parse = function parse(cfg) {
			this.$d = parseDate$1(cfg);
			this.$x = cfg.x || {};
			this.init();
		};
		_proto.init = function init() {
			var $d = this.$d;
			this.$y = $d.getFullYear();
			this.$M = $d.getMonth();
			this.$D = $d.getDate();
			this.$W = $d.getDay();
			this.$H = $d.getHours();
			this.$m = $d.getMinutes();
			this.$s = $d.getSeconds();
			this.$ms = $d.getMilliseconds();
		};
		_proto.$utils = function $utils() {
			return Utils$1;
		};
		_proto.isValid = function isValid() {
			return !(this.$d.toString() === INVALID_DATE_STRING$1);
		};
		_proto.isSame = function isSame(that, units) {
			var other = dayjs$1(that);
			return this.startOf(units) <= other && other <= this.endOf(units);
		};
		_proto.isAfter = function isAfter(that, units) {
			return dayjs$1(that) < this.startOf(units);
		};
		_proto.isBefore = function isBefore(that, units) {
			return this.endOf(units) < dayjs$1(that);
		};
		_proto.$g = function $g(input, get, set) {
			if (Utils$1.u(input)) return this[get];
			return this.set(set, input);
		};
		_proto.unix = function unix() {
			return Math.floor(this.valueOf() / 1000);
		};
		_proto.valueOf = function valueOf() {
			return this.$d.getTime();
		};
		_proto.startOf = function startOf(units, _startOf) {
			var _this = this;
			var isStartOf = !Utils$1.u(_startOf) ? _startOf : true;
			var unit = Utils$1.p(units);
			var instanceFactory = function instanceFactory(d, m) {
				var ins = Utils$1.w(
					_this.$u ? Date.UTC(_this.$y, m, d) : new Date(_this.$y, m, d),
					_this
				);
				return isStartOf ? ins : ins.endOf(D$1);
			};
			var instanceFactorySet = function instanceFactorySet(method, slice) {
				var argumentStart = [0, 0, 0, 0];
				var argumentEnd = [23, 59, 59, 999];
				return Utils$1.w(
					_this
						.toDate()
						[method].apply(
							_this.toDate("s"),
							(isStartOf ? argumentStart : argumentEnd).slice(slice)
						),
					_this
				);
			};
			var $W = this.$W,
				$M = this.$M,
				$D = this.$D;
			var utcPad = "set" + (this.$u ? "UTC" : "");
			switch (unit) {
				case Y$1:
					return isStartOf ? instanceFactory(1, 0) : instanceFactory(31, 11);
				case M$1:
					return isStartOf ? instanceFactory(1, $M) : instanceFactory(0, $M + 1);
				case W$1: {
					var weekStart = this.$locale().weekStart || 0;
					var gap = ($W < weekStart ? $W + 7 : $W) - weekStart;
					return instanceFactory(isStartOf ? $D - gap : $D + (6 - gap), $M);
				}
				case D$1:
				case DATE$1:
					return instanceFactorySet(utcPad + "Hours", 0);
				case H$1:
					return instanceFactorySet(utcPad + "Minutes", 1);
				case MIN$1:
					return instanceFactorySet(utcPad + "Seconds", 2);
				case S$1:
					return instanceFactorySet(utcPad + "Milliseconds", 3);
				default:
					return this.clone();
			}
		};
		_proto.endOf = function endOf(arg) {
			return this.startOf(arg, false);
		};
		_proto.$set = function $set(units, _int) {
			var _C$D$C$DATE$C$M$C$Y$C;
			var unit = Utils$1.p(units);
			var utcPad = "set" + (this.$u ? "UTC" : "");
			var name = ((_C$D$C$DATE$C$M$C$Y$C = {}),
			(_C$D$C$DATE$C$M$C$Y$C[D$1] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[DATE$1] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[M$1] = utcPad + "Month"),
			(_C$D$C$DATE$C$M$C$Y$C[Y$1] = utcPad + "FullYear"),
			(_C$D$C$DATE$C$M$C$Y$C[H$1] = utcPad + "Hours"),
			(_C$D$C$DATE$C$M$C$Y$C[MIN$1] = utcPad + "Minutes"),
			(_C$D$C$DATE$C$M$C$Y$C[S$1] = utcPad + "Seconds"),
			(_C$D$C$DATE$C$M$C$Y$C[MS$1] = utcPad + "Milliseconds"),
			_C$D$C$DATE$C$M$C$Y$C)[unit];
			var arg = unit === D$1 ? this.$D + (_int - this.$W) : _int;
			if (unit === M$1 || unit === Y$1) {
				var date = this.clone().set(DATE$1, 1);
				date.$d[name](arg);
				date.init();
				this.$d = date.set(DATE$1, Math.min(this.$D, date.daysInMonth())).$d;
			} else if (name) this.$d[name](arg);
			this.init();
			return this;
		};
		_proto.set = function set(string, _int2) {
			return this.clone().$set(string, _int2);
		};
		_proto.get = function get(unit) {
			return this[Utils$1.p(unit)]();
		};
		_proto.add = function add(number, units) {
			var _this2 = this,
				_C$MIN$C$H$C$S$unit;
			number = Number(number);
			var unit = Utils$1.p(units);
			var instanceFactorySet = function instanceFactorySet(n) {
				var d = dayjs$1(_this2);
				return Utils$1.w(d.date(d.date() + Math.round(n * number)), _this2);
			};
			if (unit === M$1) {
				return this.set(M$1, this.$M + number);
			}
			if (unit === Y$1) {
				return this.set(Y$1, this.$y + number);
			}
			if (unit === D$1) {
				return instanceFactorySet(1);
			}
			if (unit === W$1) {
				return instanceFactorySet(7);
			}
			var step =
				((_C$MIN$C$H$C$S$unit = {}),
				(_C$MIN$C$H$C$S$unit[MIN$1] = MILLISECONDS_A_MINUTE$1),
				(_C$MIN$C$H$C$S$unit[H$1] = MILLISECONDS_A_HOUR$1),
				(_C$MIN$C$H$C$S$unit[S$1] = MILLISECONDS_A_SECOND$1),
				_C$MIN$C$H$C$S$unit)[unit] || 1;
			var nextTimeStamp = this.$d.getTime() + number * step;
			return Utils$1.w(nextTimeStamp, this);
		};
		_proto.subtract = function subtract(number, string) {
			return this.add(number * -1, string);
		};
		_proto.format = function format(formatStr) {
			var _this3 = this;
			var locale = this.$locale();
			if (!this.isValid()) return locale.invalidDate || INVALID_DATE_STRING$1;
			var str = formatStr || FORMAT_DEFAULT$1;
			var zoneStr = Utils$1.z(this);
			var $H = this.$H,
				$m = this.$m,
				$M = this.$M;
			var weekdays = locale.weekdays,
				months = locale.months,
				meridiem = locale.meridiem;
			var getShort = function getShort(arr, index, full, length) {
				return (
					(arr && (arr[index] || arr(_this3, str))) || full[index].slice(0, length)
				);
			};
			var get$H = function get$H(num) {
				return Utils$1.s($H % 12 || 12, num, "0");
			};
			var meridiemFunc =
				meridiem ||
				function(hour, minute, isLowercase) {
					var m = hour < 12 ? "AM" : "PM";
					return isLowercase ? m.toLowerCase() : m;
				};
			var matches = {
				YY: String(this.$y).slice(-2),
				YYYY: this.$y,
				M: $M + 1,
				MM: Utils$1.s($M + 1, 2, "0"),
				MMM: getShort(locale.monthsShort, $M, months, 3),
				MMMM: getShort(months, $M),
				D: this.$D,
				DD: Utils$1.s(this.$D, 2, "0"),
				d: String(this.$W),
				dd: getShort(locale.weekdaysMin, this.$W, weekdays, 2),
				ddd: getShort(locale.weekdaysShort, this.$W, weekdays, 3),
				dddd: weekdays[this.$W],
				H: String($H),
				HH: Utils$1.s($H, 2, "0"),
				h: get$H(1),
				hh: get$H(2),
				a: meridiemFunc($H, $m, true),
				A: meridiemFunc($H, $m, false),
				m: String($m),
				mm: Utils$1.s($m, 2, "0"),
				s: String(this.$s),
				ss: Utils$1.s(this.$s, 2, "0"),
				SSS: Utils$1.s(this.$ms, 3, "0"),
				Z: zoneStr
			};

			return str.replace(REGEX_FORMAT$1, function(match, $1) {
				return $1 || matches[match] || zoneStr.replace(":", "");
			});
		};
		_proto.utcOffset = function utcOffset() {
			return -Math.round(this.$d.getTimezoneOffset() / 15) * 15;
		};
		_proto.diff = function diff(input, units, _float) {
			var _C$Y$C$M$C$Q$C$W$C$D$;
			var unit = Utils$1.p(units);
			var that = dayjs$1(input);
			var zoneDelta =
				(that.utcOffset() - this.utcOffset()) * MILLISECONDS_A_MINUTE$1;
			var diff = this - that;
			var result = Utils$1.m(this, that);
			result =
				((_C$Y$C$M$C$Q$C$W$C$D$ = {}),
				(_C$Y$C$M$C$Q$C$W$C$D$[Y$1] = result / 12),
				(_C$Y$C$M$C$Q$C$W$C$D$[M$1] = result),
				(_C$Y$C$M$C$Q$C$W$C$D$[Q$1] = result / 3),
				(_C$Y$C$M$C$Q$C$W$C$D$[W$1] = (diff - zoneDelta) / MILLISECONDS_A_WEEK$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[D$1] = (diff - zoneDelta) / MILLISECONDS_A_DAY$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[H$1] = diff / MILLISECONDS_A_HOUR$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[MIN$1] = diff / MILLISECONDS_A_MINUTE$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[S$1] = diff / MILLISECONDS_A_SECOND$1),
				_C$Y$C$M$C$Q$C$W$C$D$)[unit] || diff;
			return _float ? result : Utils$1.a(result);
		};
		_proto.daysInMonth = function daysInMonth() {
			return this.endOf(M$1).$D;
		};
		_proto.$locale = function $locale() {
			return Ls$1[this.$L];
		};
		_proto.locale = function locale(preset, object) {
			if (!preset) return this.$L;
			var that = this.clone();
			var nextLocaleName = parseLocale$1(preset, object, true);
			if (nextLocaleName) that.$L = nextLocaleName;
			return that;
		};
		_proto.clone = function clone() {
			return Utils$1.w(this.$d, this);
		};
		_proto.toDate = function toDate() {
			return new Date(this.valueOf());
		};
		_proto.toJSON = function toJSON() {
			return this.isValid() ? this.toISOString() : null;
		};
		_proto.toISOString = function toISOString() {
			return this.$d.toISOString();
		};
		_proto.toString = function toString() {
			return this.$d.toUTCString();
		};
		return Dayjs;
	})();
	var proto$1 = Dayjs$1.prototype;
	dayjs$1.prototype = proto$1;
	[
		["$ms", MS$1],
		["$s", S$1],
		["$m", MIN$1],
		["$H", H$1],
		["$W", D$1],
		["$M", M$1],
		["$y", Y$1],
		["$D", DATE$1]
	].forEach(function(g) {
		proto$1[g[1]] = function(input) {
			return this.$g(input, g[0], g[1]);
		};
	});
	dayjs$1.extend = function(plugin, option) {
		if (!plugin.$i) {
			plugin(option, Dayjs$1, dayjs$1);
			plugin.$i = true;
		}
		return dayjs$1;
	};
	dayjs$1.locale = parseLocale$1;
	dayjs$1.isDayjs = isDayjs$1;
	dayjs$1.unix = function(timestamp) {
		return dayjs$1(timestamp * 1e3);
	};
	dayjs$1.en = Ls$1[L$1];
	dayjs$1.Ls = Ls$1;
	dayjs$1.p = {};

	var myjs = {};

	//修改时 需同步修改_zy/myjs.js
	myjs.iszx = false;

	myjs.proxy = false;

	myjs.appName = (myjs.iszx ? "政协" : "人大") + "平台版";

	myjs.appUrl = function() {
		// if(T.platform() == "web"){//适配正式测试 不用重复切换
		// 	switch(location.hostname){
		// 		case "*************":
		// 			return "http://*************:810/lzt/";
		// 		case "***************":
		// 			return "http://***************:54386/lzt/";
		// 	}
		// }
		return (
			T.getPrefs("sys_appUrl") ||
			(myjs.iszx
				? "https://productpc.cszysoft.com:20170/lzt/"
				: "https://productpc.cszysoft.com:20169/lzt/")
		);
	};

	myjs.chatHeader = function() {
		return (
			T.getPrefs("sys_chatHeader") || "platform5" + (myjs.iszx ? "zx" : "rd")
		);
	};

	myjs.chatEnvironment = function() {
		return T.getPrefs("sys_chatEnvironment") || "1";
	};

	myjs.tomcatAddress = function() {
		return (
			T.getPrefs("sys_tomcatAddress") ||
			(T.platform() == "web"
				? window.location.protocol == "https:"
					? "https://cszysoft.com:9091/"
					: "http://cszysoft.com:9090/"
				: "https://cszysoft.com:9091/")
		); //http://**********:8080/ http://cszysoft.com:9090/ https://cszysoft.com:9091/
	};

	myjs.shareAddress = function(_type) {
		if (_type == 1 && T.platform() != "mp") {
			return "../../";
		}
		return (
			T.getPrefs("sys_shareAddress") ||
			(T.platform() == "web"
				? window.location.protocol.indexOf("http") == 0
					? window.location.protocol
					: "http:"
				: "https:") +
				"//cszysoft.com/appShare/" +
				(myjs.iszx ? "platform5zx/" : "platform5rd/")
		);
	};

	//默认当前页面 area下的地区 无则传参地区
	(myjs.areaId = function(_this) {
		return (
			(_this && _this.data && _this.data.area ? _this.data.area.key || "" : "") ||
			T.pageParam(_this).areaId ||
			T.getPrefs("sys_aresId") ||
			T.getPrefs("sys_platform") ||
			""
		);
	}),
		//系统类型：（平台版：platform）（标准版：standard）
		(myjs.systemType = function(_this) {
			return (
				T.pageParam(_this).platform || T.getPrefs("sys_systemType") || "platform"
			);
		});

	myjs.clientId = "zyrdV5TestAccount";

	myjs.clientSecret = "zyrdV5TestPassword";

	myjs.hw_project_id = "0611d8333100251b2fc1c01937b8e6d9";

	myjs.hw_bucket = "zy-soft";

	myjs.hw_header = "ZY";

	// import dayjs from "./dayjs";
	// import { MD5 } from './crypto-ts.js';
	/**
	 * 封装和适配 api相关所有接口 和一些别的
	 */
	var T = {};
	T.NET_ERR = "网络不小心断开了";
	T.NET_OK = "操作成功";
	T.NET_NO = "操作失败，请重试";
	T.JK_ERR = "接口异常，请联系技术";
	T.LOAD_ING = "加载中，请稍候...";
	T.LOAD_MORE = "点击加载更多";
	T.LOAD_ALL = "已加载完";
	T.LOAD_NO = "暂无数据";
	T.LOAD_NOT = "页面尚未加载完成，请刷新重试";

	T.isParameters = function(obj) {
		return obj != null && obj != undefined;
	};
	T.isTargetType = function(obj, typeString) {
		return typeof obj === typeString;
	};
	T.isNumber = function(obj) {
		return T.isParameters(obj) && T.isTargetType(obj, "number");
	};
	T.isObject = function(obj) {
		return T.isParameters(obj) && T.isTargetType(obj, "object");
	};
	T.isArray = function(obj) {
		return T.isParameters(obj) && toString.apply(obj) === "[object Array]";
	};
	T.isFunction = function(obj) {
		return T.isParameters(obj) && T.isTargetType(obj, "function");
	};

	T.setNewJSON = function(obj, newobj, _ifReplace) {
		obj = obj || {};
		newobj = newobj || {};
		var returnObj = {};
		for (var key in obj) {
			returnObj[key] = obj[key];
		}
		for (var key in newobj) {
			if (_ifReplace && returnObj.hasOwnProperty(key)) {
				//是否不替换前者 默认替换
				continue;
			}
			returnObj[key] = newobj[key];
		}
		return returnObj;
	};

	T.getNum = function() {
		return Math.round(Math.random() * 1000000000);
	};

	T.trim = function(str) {
		if (String.prototype.trim) {
			return str == null ? "" : String.prototype.trim.call(str);
		} else {
			return str.replace(/(^\s*)|(\s*$)/g, "");
		}
	};
	T.trimAll = function(str) {
		return str.replace(/\s*/g, "");
	};
	T.removeTag = function(str) {
		if (!str) return str;
		return T.decodeCharacter(
			str
				.replace(/<!--[\w\W\r\n]*?-->/gim, "")
				.replace(/(<[^\s\/>]+)\b[^>]*>/gi, "$1>")
				.replace(/<[^>]+>/g, "")
				.replace(/\s*/g, "")
		);
	};
	T.decodeCharacter = function(str) {
		if (!str) return str;
		return str
			.replace(/&amp;/g, "&")
			.replace(/(&nbsp;|&ensp;)/g, " ")
			.replace(/&mdash;/g, "—")
			.replace(/&ldquo;/g, "“")
			.replace(/&rsquo;/g, "’")
			.replace(/&lsquo;/g, "‘")
			.replace(/&rdquo;/g, "”")
			.replace(/&middot;/g, "·")
			.replace(/&hellip;/g, "…")
			.replace(/&quot;/g, '"')
			.replace(/&lt;/g, "<")
			.replace(/&gt;/g, ">");
	};

	T.platform = function() {
		try {
			return api.platform || "";
		} catch (e) {
			return "";
		}
	};

	T.rebootApp = function() {
		if (T.platform() == "web") {
			window.location.reload();
		} else if (T.platform() == "mp") {
			wx.reLaunch({url: "/pages/index/index"});
		} else {
			api.rebootApp();
		}
	};

	T.appName = function() {
		try {
			return myjs.appName || "";
		} catch (e) {
			return "";
		}
	};

	T.systemType = function() {
		try {
			return api.systemType;
		} catch (e) {
			return "";
		}
	};

	T.pageParam = function(_this) {
		try {
			var pageParam =
				(_this && _this.props ? _this.props.pageParam : null) ||
				api.pageParam ||
				{};
			if (pageParam.paramSaveKey) {
				pageParam = JSON.parse(T.getPrefs(pageParam.paramSaveKey) || "{}");
			}
			if (T.platform() == "web" && JSON.stringify(pageParam) == "{}") {
				pageParam = getOtherParam(window.location.href);
			}
			return pageParam;
		} catch (e) {
			return {};
		}
	};

	T.safeArea = function() {
		try {
			return api.safeArea;
		} catch (e) {
			return {top: 0, left: 0, bottom: 0, right: 0};
		}
	};

	T.setPrefs = function(key, value) {
		if (!T.isParameters(value)) {
			T.removePrefs(key);
			return;
		}
		try {
			api.setPrefs({sync: true, key: key, value: value});
		} catch (e) {}
	};

	T.getPrefs = function(key) {
		try {
			return api.getPrefs({sync: true, key: key});
		} catch (e) {
			return "";
		}
	};

	T.removePrefs = function(key) {
		try {
			api.removePrefs({sync: true, key: key});
		} catch (e) {}
	};

	T.addEventListener = function(name, callback) {
		var keyback = function keyback(ret, err) {
			T.isFunction(callback) && callback(ret, err);
		};
		if (
			T.platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			if (!window.baseEventList) {
				window.baseEventList = [];
			}
			if (G$1.getItemForKey(name, window.baseEventList)) {
				G$1.delItemForKey(name, window.baseEventList);
			}
			window.baseEventList.push({
				key: name,
				value: keyback
			});
		} else {
			api.addEventListener({name: name}, keyback);
		}
	};

	T.removeEventListener = function(name) {
		if (
			T.platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			G$1.delItemForKey(name, window.baseEventList);
		} else {
			api.removeEventListener({name: name});
		}
	};

	T.sendEvent = function(name, extra) {
		if (
			T.platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			var pageframes = window.parent.document.getElementsByTagName("iframe");
			for (var i = 0; i < pageframes.length; i++) {
				if (T.isArray(pageframes[i].contentWindow.baseEventList)) {
					var sendItem = G$1.getItemForKey(
						T.isObject(name) ? name.name : name,
						pageframes[i].contentWindow.baseEventList
					);
					if (sendItem) {
						sendItem.value({value: T.isObject(name) ? name.extra : extra});
					}
				}
			}
		} else {
			try {
				api.sendEvent(T.isObject(name) ? name : {name: name, extra: extra});
			} catch (e) {}
		}
	};

	T.removeLaunchView = function() {
		try {
			api.removeLaunchView();
		} catch (e) {}
	};

	T.setScreenOrientation = function(orientation) {
		try {
			api.setScreenOrientation({orientation: orientation});
		} catch (e) {}
	};

	T.cancelAjax = function(name) {
		try {
			api.cancelAjax({tag: name});
		} catch (e) {}
	};

	T.ajax = function(url, tag, callback, logText, method, data, header) {
		if (header === void 0) {
			header = {};
		}
		T.cancelAjax(tag);
		var getUrl = url; //请求链接
		var frequency = 0; //网络异常 重复请求次数
		var dataType = "json"; //返回数据类型
		var cacheType = ""; //请求类型
		var paramData = {};
		var areaId = "";
		var timeout = 0;
		var isWeb = "";
		if (T.isObject(url)) {
			getUrl = url.u; //请求链接
			dataType = url.dt || "json"; //
			cacheType = url.t || ""; //
			frequency = url.frequency || 0;
			paramData = url.paramData || {};
			areaId = url.areaId || myjs.areaId(url._this);
			timeout = url.timeout || 0;
			isWeb = url.web || "";
		}
		var o = {
			url: getUrl,
			tag: tag,
			method: method ? method : "get",
			cache: false,
			timeout: 50,
			dataType: dataType,
			data: T.isObject(data) ? data : {},
			headers: T.setNewJSON(
				{
					"u-login-areaId": areaId,
					Authorization: T.getPrefs("sys_token") || "",
					"content-type": "application/json",
					"u-terminal": "APP"
				},
				header
			)
		};

		o = T.setNewJSON(o, paramData);
		if (T.platform() == "web") {
			delete o.tag;
		}
		if (o.url.indexOf("push/rongCloud") != -1) {
			//融云接口
			if (o.method == "get") {
				o.url +=
					(o.url.indexOf("?") != -1 ? "&" : "?") +
					"environment=" +
					myjs.chatEnvironment();
			} else if (o.data.values) {
				o.data.values.environment = myjs.chatEnvironment();
			}
		}
		if (
			isWeb &&
			JSON.stringify(o.data) != "{}" &&
			(o.data.body || JSON.stringify(o.data.values) != "{}")
		) {
			//公众端通用 过期时间不传token且置空public_token
			var webToken = "";
			if (
				T.getPrefs("tokenEndTime") &&
				new Date().getTime() < T.getPrefs("tokenEndTime")
			) {
				webToken = T.getPrefs("public_token") || "";
			} else {
				T.removePrefs("public_token");
			}
			(o.headers.Authorization = header.Authorization || webToken || ""),
				(o.headers["u-terminal"] = "PUBLIC");
			// var isBody = o.data.body?true:false;
			// var postParam = isBody?JSON.parse(o.data.body):o.data.values;
			// var signParam = {};
			// function getParam(_obj){
			// 	// console.log(JSON.stringify(_obj));
			// 	for (var key in _obj) {
			// 		var kValue = _obj[key];
			// 		if(T.isObject(kValue) && !T.isArray(kValue)){
			// 			getParam(kValue);
			// 		}else{
			// 			kValue = T.isArray(kValue)?kValue.join(","):kValue;
			// 			if (signParam.hasOwnProperty(key)) {
			// 				signParam[key] += (signParam[key]?',':'') + kValue;
			// 			}else{
			// 				signParam[key] = kValue;
			// 			}
			// 		}
			// 	}
			// }
			// getParam(postParam);
			// var signStr = T.sort_ascii(signParam,"#");
			// postParam.clientId = M.clientId;
			// postParam.token = isWeb;
			// postParam.timestamp = dayjs().valueOf();
			// postParam.nonce = "zyrd";
			// postParam.sign = MD5(signStr + "#" + M.clientId + isWeb + postParam.timestamp + postParam.nonce).toString().toUpperCase()
			// if(isBody){
			// 	o.data.body = JSON.stringify(postParam);
			// }
		}
		var oldContentType = o.headers["content-type"];
		if (myjs.proxy && T.platform() != "app" && o.url.indexOf("https") != 0) {
			//小程序 使用代理 T.platform() == "mp" &&
			var oldUrl = o.url;
			var proxyUrl = myjs.tomcatAddress() + "utils/proxy";
			if (oldUrl.indexOf("?") != -1) {
				o.url = proxyUrl + oldUrl.substring(oldUrl.indexOf("?"));
				oldUrl = oldUrl.substring(0, oldUrl.indexOf("?"));
			} else {
				o.url = proxyUrl;
			}
			o.url +=
				(o.url.indexOf("?") != -1
					? o.url.substring(o.url.indexOf("?")) == "?"
						? ""
						: "&"
					: "?") +
				"BASE_URL=" +
				oldUrl;
			o.url += "&BASE_TYPE=" + oldContentType;
			o.headers["content-type"] = "application/x-www-form-urlencoded";
		}
		if (oldContentType == "file") {
			delete o.headers["content-type"];
		}
		if (T.platform() == "app" && logText) {
			if (o.method == "post") {
				console.log(logText + "post【" + frequency + "】：" + JSON.stringify(o));
			} else {
				console.log(logText + "get【" + frequency + "】：" + JSON.stringify(o));
			}
		}
		try {
			var cbFun = function cbFun(ret, err) {
				// if(T.isObject(url) && T.isParameters(url._this.ajax)){
				// 	url._this.ajax = true;
				// }
				if (T.isFunction(callback)) {
					if (err) {
						try {
							ret = JSON.parse(err.msg);
							err = null;
						} catch (e) {
							ret = JSON.parse(JSON.stringify(err));
							err = null;
						}
					}
					if (err) {
						// if (frequency > 0) {
						// 	var frequencyUrl = url;
						// 	frequencyUrl.frequency--;
						// 	T.ajax(frequencyUrl, tag, callback, logText, method, data, header);
						// 	return;
						// }
					}
					if (T.platform() == "app" && logText) {
						if (ret)
							console.log("得到" + logText + "返回结果ret：" + JSON.stringify(ret));
						if (err)
							console.log("得到" + logText + "返回结果err：" + JSON.stringify(err));
					}
					if (ret) {
						ret.message = ret.message || ret.msg || "";
						var errcode = ret.code || "";
						if ((errcode == 302 || errcode == 2) && cacheType != "login") {
							//令牌失效
							T.hideProgress();
							T.sendEvent({
								name: "index",
								extra: {type: "verificationToken", errmsg: ret.message}
							});
							// return;
						}
					}
					callback(ret, err, true);
				}
			};
			setTimeout(function() {
				if (T.platform() == "web") {
					var xhr = new XMLHttpRequest();
					xhr.open(o.method, o.url);
					for (var header in o.headers) {
						xhr.setRequestHeader(header, o.headers[header]);
					}
					var sendValue = "";
					if (oldContentType.indexOf("x-www-form-urlencoded") != -1) {
						var dValue = o.data.values || {};
						var sendBody = o.data.body || "";
						if (sendBody) {
							dValue = JSON.parse(sendBody);
						}
						for (var vItem in dValue) {
							sendValue +=
								(!sendValue ? "" : "&") +
								vItem +
								"=" +
								encodeURIComponent(dValue[vItem]);
						}
					} else if (oldContentType.indexOf("file") != -1) {
						sendValue = new FormData();
						var dValue = o.data.values || {};
						var fileValue = o.data.files || {};
						var sendBody = o.data.body || "";
						if (sendBody) {
							dValue = JSON.parse(sendBody);
						}
						for (var vItem in dValue) {
							sendValue.append(vItem, encodeURIComponent(dValue[vItem]));
						}
						for (var vItem in fileValue) {
							sendValue.append(vItem, fileValue[vItem]);
						}
					} else {
						sendValue = o.data.body || JSON.stringify(o.data.values); //encodeURIComponent web加了之后 不能传递json了
					}
					xhr.send(sendValue);
					xhr.onreadystatechange = function() {
						if (xhr.readyState === XMLHttpRequest.DONE) {
							if (xhr.responseText) {
								var response = this.responseText;
								if (o.dataType == "json") {
									var isJSON = false;
									try {
										response = JSON.parse(response);
										isJSON = true;
									} catch (e) {
										isJSON = false;
									}
									if (isJSON) {
										cbFun(response, null);
									} else {
										cbFun(null, response);
									}
								} else {
									cbFun(response, null);
								}
							} else {
								cbFun(null, {});
							}
						}
					};
				} else {
					api.ajax(o, cbFun);
				}
			}, timeout);
		} catch (e) {
			console.log(e);
		}
	};

	T.openWin = function(name, url, pageParam, _this, allowEdit, _more) {
		var delay = 0;
		url = T.handleSYSLink(url, _this); //先处理跳转链接
		var o = {
			name: name,
			url: T.platform() == "web" ? url.substring(url.lastIndexOf("/") + 1) : url,
			pageParam: pageParam ? pageParam : {},
			bounces: false,
			bgColor: "#FFF",
			slidBackEnabled: false, //ios滑动返回
			vScrollBarEnabled: true,
			hScrollBarEnabled: true,
			scaleEnabled: true,
			animation: {
				type: "push",
				subType: "from_right",
				duration: 300
			},

			reload: true, // 去除设置
			allowEdit: true, //去除设置 默认都可以复制粘贴
			delay: delay,
			overScrollMode: "scrolls",
			defaultRefreshHeader: "swipe"
		};

		if (T.isObject(_more)) {
			o = T.setNewJSON(o, _more);
		}
		o.pageParam.headTheme =
			(_this && _this.data && _this.data.headTheme
				? _this.data.headTheme || ""
				: "") ||
			G$1.headTheme ||
			"";
		o.pageParam.appTheme = G$1.appTheme || "";
		o.pageParam.areaId = o.pageParam.areaId || myjs.areaId(_this);
		o.pageParam.v = G$1.v;
		if (o.pageParam.paramSaveKey) {
			T.setPrefs(o.pageParam.paramSaveKey, JSON.stringify(o.pageParam));
			o.pageParam = {paramSaveKey: o.pageParam.paramSaveKey};
		}
		api.openWin(o);
	};

	T.closeWin = function(_param) {
		var o = {};
		if (T.isObject(_param)) {
			o = T.setNewJSON(o, _param);
		} else {
			o.name = _param;
		}
		try {
			if (api.pageParam.paramSaveKey) {
				T.removePrefs(api.pageParam.paramSaveKey);
			}
			api.closeWin(o);
		} catch (e) {}
	};

	T.clearCache = function(callback) {
		var o = {};
		try {
			api.clearCache(o, function(ret, err) {
				T.isFunction(callback) && callback(ret, err);
			});
		} catch (e) {}
	};

	T.toast = function(_param, location, global) {
		var o = {
			msg: "",
			duration: 2000,
			location: "middle",
			global: false
		};

		if (T.isObject(_param)) {
			o = T.setNewJSON(o, _param);
		} else {
			o.msg = T.isParameters(_param) ? _param : "";
			o.location = location || "middle";
			o.global = global;
		}
		o.msg = o.msg.toString();
		try {
			api.toast(o);
		} catch (e) {}
	};

	T.showProgress = function(_param, modal) {
		var o = {
			style: "default",
			animationType: "fade",
			title: "加载中",
			text: "请稍候...",
			modal: true //是否模态，模态时整个页面将不可交互
		};
		if (T.isObject(_param)) {
			o = T.setNewJSON(o, _param);
		} else {
			o.title = T.isParameters(_param) ? _param : "";
			o.modal = !modal; //是否可以交互 反过来了
		}
		o.title = o.title.toString();
		try {
			api.showProgress(o);
		} catch (e) {}
	};

	T.hideProgress = function() {
		try {
			api.hideProgress();
		} catch (e) {}
	};

	T.alert = function(_param, callback) {
		G$1.alert(_param, callback);
		// var o = {
		// 	title: '提示',
		// 	msg: "",
		// 	buttons: ["确定"]
		// };
		// if (T.isObject(_param)) {
		// 	o = T.setNewJSON(o, _param);
		// } else {
		// 	o.msg = T.isParameters(_param)?_param:"";
		// }
		// o.msg = o.msg.toString();
		// try{
		// 	api.alert(o, (ret, err)=> {
		// 		T.isFunction(callback) && callback(ret, err);
		// 	});
		// }catch(e){}
	};

	T.confirm = function(_param, callback) {
		G$1.alert(_param, callback);
		// var o = {
		// 	title: '提示',
		// 	msg: "",
		// 	buttons: ['确定', '取消']
		// };
		// if (T.isObject(_param)) {
		// 	o = T.setNewJSON(o, _param);
		// } else {
		// 	o.msg = _param;
		// }
		// o = T.setNewJSON(o, _param);
		// try{
		// 	api.confirm(o, (ret, err)=> {
		// 		T.isFunction(callback) && callback(ret, err);
		// 	});
		// }catch(e){}
	};

	T.actionSheet = function(_param, _callback) {
		G$1.actionSheet(_param, _callback);
		// var o = {
		// 	title: '请选择',
		// 	cancelTitle: '取消',
		// 	destructiveTitle: "",
		// };
		// o = T.setNewJSON(o, _param);
		// try{
		// 	api.actionSheet(o, (ret, err)=> {
		// 		T.isFunction(callback) && callback(ret, err);
		// 	});
		// }catch(e){}
	};

	T.getPicture = function(_param, callback) {
		if (!callback) {
			T.toast("请先设置callback");
			return;
		}
		var o = {
			sourceType: "camera",
			encodingType: "jpg",
			mediaValue: "pic",
			targetWidth: 720,
			count: 1,
			max: 0
		};

		o = T.setNewJSON(o, _param);
		try {
			if (T.platform() == "web") {
				var h5Input = document.createElement("input");
				h5Input.style = "display: none;";
				h5Input.type = "file";
				h5Input.accept = "image/*";
				var ua = navigator.userAgent.toLowerCase();
				var version = "";
				if (ua.indexOf("android") > 0) {
					var reg = /android [\d._]+/gi;
					var v_info = ua.match(reg);
					version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, ".");
					version = parseInt(version.split(".")[0]);
				}
				if (!version || Number(version) <= 13) {
					h5Input.multiple = "multiple";
				}
				h5Input.onchange = function() {
					var listLength =
						o.max != 0 && h5Input.files.length > o.max ? o.max : h5Input.files.length;
					for (var i = 0; i < listLength; i++) {
						callback({data: h5Input.files[i]}, null);
					}
				};
				//ios拍照需要加到真实dom能才进onchange
				document.body.appendChild(h5Input);
				h5Input.click();
			} else if (T.platform() == "mp") {
				wx.chooseImage({
					count: o.max != 0 ? o.max : 9,
					sourceType: [o.sourceType == "camera" ? "camera" : "album"],
					success: function success(res) {
						for (var i = 0; i < res.tempFiles.length; i++) {
							callback({data: res.tempFiles[i]}, null);
						}
					}
				});
			} else if (T.platform() == "app") {
				var preName = o.sourceType == "camera" ? "camera" : "photos";
				if (
					!T.confirmPer(
						preName,
						"getPicture",
						o.reason || "用于上传并使用图片的功能，若取消将无法使用图片功能"
					)
				) {
					//相机相册权限
					T.addEventListener(preName + "Per_" + "getPicture", function(ret, err) {
						T.removeEventListener(preName + "Per_" + "getPicture");
						if (ret.value.granted) {
							T.getPicture(_param, callback);
						}
					});
					return;
				}
				if (o.sourceType == "camera" || o.userOne) {
					api.getPicture(o, function(ret, err) {
						T.isFunction(callback) && callback(ret, err);
					});
				} else {
					api.require("WXPhotoPicker").open(
						{
							max: o.max != 0 ? o.max : 9,
							styles: {
								mark: {checked: G$1.appTheme},
								bottomTabBar: {sendText: "确定", sendBgColor: G$1.appTheme}
							},
							type: "image"
						},
						function(ret) {
							var eventType = ret ? ret.eventType : "cancel";
							if (eventType == "cancel") return;
							if (eventType == "confirm" && ret.list.length != 0) {
								for (var i = 0; i < ret.list.length; i++) {
									callback({data: ret.list[i].path}, null);
								}
							}
						}
					);
				}
			}
		} catch (e) {}
	};

	T.hasPermission = function(one_per) {
		if (T.platform() == "app") {
			if (!one_per) return;
			var rets = api.hasPermission({list: one_per.split(",")});
			if (one_per.indexOf(",") != -1) {
				//判断一堆时 就自己看	一般是一个一个判断
				T.alert("判断结果：" + JSON.stringify(rets));
				return;
			} else {
				return rets;
			}
		}
	};

	T.requestPermission = function(one_per, callback, _fName) {
		if (T.platform() == "app") {
			if (!one_per) return;
			api.requestPermission({list: one_per.split(",")}, function(ret) {
				console.log(JSON.stringify(ret));
				//把结果 发监听过去
				ret.list.forEach(function(_eItem, _eIndex, _eArr) {
					T.sendEvent(_eItem.name + "Per_" + _fName, {granted: _eItem.granted});
				});
				T.isFunction(callback) && callback(ret, err);
			});
		}
	};

	T.confirmPer = function(perm, _fName, _reason) {
		if (T.platform() == "app") {
			var has = T.hasPermission(perm);
			if (!has || !has[0] || !has[0].granted) {
				var hintWord = {
					camera: "相机",
					storage: "存储",
					photos: "照片",
					microphone: "麦克风",
					location: "位置",
					phone: "电话",
					"phone-r": "通话状态",
					"phone-r-log": "通话记录"
				};

				var iosHint =
					"请在" +
					(api.uiMode == "phone" ? "iPhone" : "iPad") +
					"的“设置-隐私-" +
					hintWord[perm] +
					"”中请允许访问" +
					hintWord[perm] +
					(_reason ? "，" + _reason : "。");
				var androidHint =
					"使用该功能需要" +
					hintWord[perm] +
					"权限，" +
					(_reason || "请前往系统设置开启权限。");
				T.confirm(
					{
						title: "无法使用" + hintWord[perm],
						msg: T.systemType() == "ios" ? iosHint : androidHint,
						buttons: ["下一步", "取消"]
					},
					function(ret, err) {
						if (1 == ret.buttonIndex) {
							T.requestPermission(perm, null, _fName);
						} else {
							T.sendEvent(perm + "Per_" + _fName, {granted: false});
						}
					}
				);
				return false;
			}
			return true;
		}
		return true;
	};

	//处理app链接 可以不带app会拼接上 或者需要带上特定参数
	T.handleSYSLink = function(_link, _this, myParam) {
		if (_link === void 0) {
			_link = "";
		}
		// if(_link.indexOf("http") != 0){
		// 	return _link;
		// }
		myParam = myParam || {};
		//index.html?token={{token}}&userId={{userId}}
		_link = _link.replace("{{tomcatAddress}}", myjs.tomcatAddress());
		_link = _link.replace("{{shareAddress}}", myjs.shareAddress());
		_link = _link.replace(
			"{{token}}",
			encodeURIComponent(T.getPrefs("sys_token"))
		); //当前app登录用户的token，例如：bearer eyJhbGciOiJ...
		_link = _link.replace("{{sysUrl}}", myjs.appUrl()); //当前app请求系统地址，例如：http://**************:54386/lzt/
		_link = _link.replace("{{areaId}}", myParam.areaId || myjs.areaId(_this)); //当前跳转页面的地区id，例如：430000
		_link = _link.replace("{{userId}}", G$1.userId); //当前用户id，例如：1
		_link = _link.replace("{{iszx}}", myjs.iszx); //当前系统类型，例如：true (true为政协，flase为人大)
		_link = _link.replace("{{appTheme}}", G$1.appTheme); //当前app主题颜色，例如：#3657C0
		_link = _link.replace("{{careMode}}", G$1.careMode); //当前是否为关怀模式：例如：true (关怀模式下字体大4px)
		if (_link.indexOf("?ndata=") != -1) {
			//是app内页面 带上特有参数如果没有
			if (_link.indexOf("sysUrl-zy-") == -1) {
				//没有带地址
				_link += "-zyz-sysUrl-zy-" + myjs.appUrl();
			}
			if (_link.indexOf("sysAreaId-zy-") == -1) {
				//没有带地区
				_link += "-zyz-sysAreaId-zy-" + (myParam.areaId || myjs.areaId(_this));
			}
			if (_link.indexOf("iszx-zy-") == -1) {
				//没有带人大政协判断
				_link += "-zyz-iszx-zy-" + myjs.iszx;
			}
			if (_link.indexOf("appTheme-zy-") == -1) {
				//没有带主题
				_link += "-zyz-appTheme-zy-" + G$1.appTheme;
			}
			if (_link.indexOf("careMode-zy-") == -1) {
				//没有唯一标识
				_link += "-zyz-careMode-zy-" + G$1.careMode;
			}
		}
		return _link;
	};

	//按照ascii 排序
	T.sort_ascii = function(obj, _default) {
		var arr = [];
		var num = 0;
		for (var i in obj) {
			arr[num] = i;
			num++;
		}
		var sortArr = arr.sort();
		var str = "";
		for (var _i = 0; _i < sortArr.length; _i++) {
			var sValue = obj[sortArr[_i]];
			str +=
				sortArr[_i] +
				"=" +
				(T.isTargetType(sValue, "number")
					? sValue
					: T.isObject(sValue)
					? JSON.stringify(sValue)
					: sValue || _default) +
				"&";
		}
		var char = "&";
		str = str.replace(new RegExp("^\\" + char + "+|\\" + char + "+$", "g"), "");
		return str;
	};

	/** 判断颜色属于深色还是浅色*/
	T.isColorDarkOrLight = function(hexcolor) {
		try {
			var colorrgb = T.colorRgb(hexcolor);
			var colors = colorrgb.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
			var red = colors[1];
			var green = colors[2];
			var blue = colors[3];
			var brightness;
			brightness = red * 299 + green * 587 + blue * 114;
			brightness = brightness / 255000;
			if (brightness >= 0.5) {
				return "light";
			} else {
				return "dark";
			}
		} catch (e) {
			return "";
		}
	};

	//16进制颜色转化为RGB颜色
	T.colorRgb = function(str) {
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		var sColor = str.toLowerCase();
		if (sColor && reg.test(sColor)) {
			if (sColor.length === 4) {
				var sColorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1));
				}
				sColor = sColorNew;
			}
			var sColorChange = [];
			for (var i = 1; i < 7; i += 2) {
				sColorChange.push(parseInt("0x" + sColor.slice(i, i + 2)));
			}
			return "rgb(" + sColorChange.join(",") + ")";
		} else {
			return sColor;
		}
	};

	/** 16进制颜色 转换成rgba颜色	可设置透明 */
	T.colorRgba = function(_color, _alpha) {
		if (!_color) return;
		// 16进制颜色值的正则
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		// 把颜色值变成小写
		var color = _color.toLowerCase();
		if (reg.test(color)) {
			if (color.length === 4) {
				var colorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
				}
				color = colorNew;
			}
			var colorChange = [];
			for (var i = 1; i < 7; i += 2) {
				colorChange.push(parseInt("0x" + color.slice(i, i + 2)));
			}
			return (
				"rgba(" +
				colorChange.join(",") +
				"," +
				(T.isParameters(_alpha) ? _alpha : "1") +
				")"
			);
		} else {
			return color;
		}
	};

	var G$1 = {
		pageWidth:
			api.platform == "web"
				? api.winWidth > api.winHeight
					? 600
					: api.winWidth
				: api.winWidth,
		refreshPageSize: 0, //返回当前页刷新列表的条数
		dotRefsresh: false, // 返回当前页是否不刷新
		showSkeleton: true, //是否展示骨架屏
		seachText: "", //搜索词
		seachPlaceholder: "请输入搜索内容", //搜索提示
		firstAjax: false, //首次网络请求是否成功
		dotCloseListener: false, //当前页面不要划动返回
		hasCloseListener: false, //不管其它页面 直接添加关闭监听

		appName: "",
		appFont: "", //app全局字体
		appFontSize: 0, //app全局字体大小
		appTheme: "", //app全局主题色
		headTheme: "", //head全局主题色
		careMode: false, //是否启用了关怀模式
		htmlStyle: "", //html级别设置style 置灰等操作
		htmlClass: "", //html级别设置class
		systemtTypeIsPlatform: false, //系统类型是否是平台版
		v: "", //缓存版本号

		uId: "", //普通用户id
		userId: "", //当前用户id 账号id
		userName: "", //当前用户名字
		userImg: "", //当前用户头像
		areaId: "", //当前地区id
		specialRoleKeys: [], //当前用户角色集合
		isAdmin: false, //是否管理员 拥有所有权限
		viewappearFrist: true, //是否首次进入页面

		touchmoveTask: null, //划动元素时 禁用页面划动返回事件
		nTouchmove: false,

		isAppReview: false, //app是否上架期间 隐藏和显示部分功能

		touchmove: function touchmove() {
			G$1.nTouchmove = true;
			G$1.touchmoveTask && clearTimeout(G$1.touchmoveTask);
			G$1.touchmoveTask = setTimeout(function() {
				G$1.nTouchmove = false;
			}, 1000);
		},
		//通用组件 start=====================================================

		imagePreviewer: {
			//全局图片预览组件
			show: false,
			imgs: [],
			activeIndex: 0,
			type: 1
		},

		openImgPreviewer: function openImgPreviewer(_param) {
			if (_param === void 0) {
				_param = {};
			}
			if ((_param.imgs || []).length <= 0) {
				return;
			}
			G$1.imagePreviewer.activeIndex = _param.index || 0;
			G$1.imagePreviewer.imgs = _param.imgs;
			G$1.imagePreviewer.show = true;
			T.sendEvent("updatePage");
		},

		areasBox: {
			//地区切换弹窗组件
			show: false,
			type: "half", //full全屏打开  half半屏打开
			pageParam: null
		},

		openAreas: function openAreas(_param, _callback) {
			if (_param === void 0) {
				_param = {};
			}
			G$1.areasBox.pageParam = _param;
			G$1.areasBox.show = true;
			T.addEventListener("base_areas_callback", function(ret) {
				_callback && _callback(ret.value);
				T.removeEventListener("base_areas_callback");
			});
			T.sendEvent("updatePage");
		},

		alertBox: {
			// 确认提示框
			show: false,
			title: "",
			content: "",
			richText: false,
			input: false,
			textarea: false,
			placeholder: "",
			cancel: {show: false, text: "取消", color: "#333333"},
			sure: {show: true, text: "确定", color: "appTheme"}
		},

		alert: function alert(_param, _callback) {
			var o = {title: "", msg: "", buttons: ["确定"]};
			if (T.isObject(_param)) {
				o = T.setNewJSON(o, _param);
			} else {
				o.msg = T.isParameters(_param) ? _param : "";
			}
			G$1.alertBox.title = o.title;
			G$1.alertBox.content = (o.msg || o.content || "").toString();
			G$1.alertBox.input = o.input;
			G$1.alertBox.textarea = o.textarea;
			G$1.alertBox.placeholder = o.placeholder;
			G$1.alertBox.richText = o.richText;
			G$1.alertBox.cancel.show = o.buttons.length > 1;
			G$1.alertBox.cancel.text = o.buttons[1];
			G$1.alertBox.sure.text = o.buttons[0];
			G$1.alertBox.show = true;
			T.addEventListener("base_alert_callback", function(ret) {
				_callback && _callback(ret.value);
				T.removeEventListener("base_alert_callback");
			});
			T.sendEvent("updatePage");
		},

		actionSheetBox: {
			show: false,
			cancel: false,
			title: "",
			active: null,
			data: []
		},

		actionSheet: function actionSheet(_param, _callback) {
			var o = {title: "", cancelTitle: "取消", destructiveTitle: ""};
			o = T.setNewJSON(o, _param);
			G$1.actionSheetBox.title = o.title;
			G$1.actionSheetBox.cancel = o.cancelTitle;
			var oldButton = o.buttons || [],
				newButton = [];
			oldButton.forEach(function(item) {
				newButton.push(T.isObject(item) ? item : {value: item});
			});
			G$1.actionSheetBox.data = newButton;
			G$1.actionSheetBox.active = _param.active;
			G$1.actionSheetBox.dotClose = _param.dotClose;
			G$1.actionSheetBox.show = true;
			T.addEventListener("base_actionSheet_callback", function(ret) {
				_callback && _callback(ret.value);
				T.removeEventListener("base_actionSheet_callback");
			});
			T.sendEvent("updatePage");
		},
		//通用组件 end=====================================================

		installed: function installed(_this) {
			var _this2 = this;
			if (_this.props && _this.props.dataMore);
			else {
				G$1.fitWidth();
				G$1.changeConfiguration(_this);
				G$1.appGrayscale();
				G$1.initOther();
				T.addEventListener("index_login_ok", function(ret, err) {
					G$1.initOther();
				});
				//字体刷新
				T.addEventListener("changeConfiguration", function(ret, err) {
					G$1.changeConfiguration(_this);
				});
				//地区刷新监听
				T.addEventListener("areaChange", function(ret, err) {
					var notifas = ["module", "news", "my", "negotiable", "area"];
					notifas.forEach(function(_eItem) {
						T.sendEvent({name: "areaChange_" + _eItem, extra: ret.value});
					});
				});
				//红点刷新
				T.addEventListener("unreadChange", function(ret, err) {
					var notifas = ["module", "my"];
					notifas.forEach(function(_eItem) {
						T.sendEvent({name: "unreadChange_" + _eItem, extra: ret.value});
					});
				});
				if (
					T.isFunction(_this.close) &&
					!T.isParameters(_this.props.pageParam) &&
					!T.isParameters(_this.props.dataMore)
				) {
					T.addEventListener("keyback", function(ret, err) {
						if (G$1.imagePreviewer.show) {
							G$1.imagePreviewer.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G$1.areasBox.show) {
							G$1.areasBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G$1.alertBox.show) {
							if (G$1.alertBox.cancel.show) {
								G$1.alertBox.show = false;
								T.sendEvent("updatePage");
							}
							return;
						}
						if (G$1.actionSheetBox.show) {
							if (G$1.actionSheetBox.cancel) {
								G$1.actionSheetBox.show = false;
								T.sendEvent("updatePage");
							}
							return;
						}
						_this2.close(_this);
					});
					T.addEventListener("swiperight", function(ret, err) {
						if (G$1.nTouchmove) {
							return;
						}
						if (G$1.imagePreviewer.show) {
							return;
						}
						if (G$1.areasBox.show) {
							G$1.areasBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G$1.alertBox.show && G$1.alertBox.cancel.show) {
							G$1.alertBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G$1.actionSheetBox.show) {
							G$1.actionSheetBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						_this2.close(_this);
					});
				}
				setTimeout(function() {
					_this2.setHeader(_this);
				}, 10);
			}
			try {
				_this.init();
			} catch (e) {
				console.log(e);
			}
		},
		close: function close(_this) {
			_this.close();
		},
		setHeader: function setHeader(_this) {
			var title = _this.data.title;
			// console.log("=================="+title);
			// console.log(_this.props);
			// console.log(_this.data);
			if (!title) {
				return;
			}
			if (T.platform() == "web") {
				if (window.parent) {
					window.parent.document.title = title;
				} else {
					document.title = title;
				}
			} else if (T.platform() == "mp") {
				wx.setNavigationBarTitle({
					title: title
				});
			}
		},
		//多端页面显示回调 app、h5、小程序
		onShow: function onShow(_this) {
			var _this3 = this;
			if (_this.props.dataMore) {
				return;
			}
			if (G$1.viewappearFrist) {
				G$1.viewappearFrist = false;
				return;
			}
			console.log("返回了当前页面：");
			T.sendEvent({name: "changeConfiguration"});
			if (G$1.areaId != T.getPrefs("sys_aresId")) {
				G$1.areaId = T.getPrefs("sys_aresId") || "";
				T.sendEvent({name: "areaChange", extra: {key: G$1.areaId}});
			}
			setTimeout(function() {
				_this3.setHeader(_this);
			}, 10);
			if (_this.getData) {
				//返回刷新一下
				_this.getData(false, {refresh: 1});
			}
		},
		//初始化后其它配置
		initOther: function initOther() {
			G$1.areaId = T.getPrefs("sys_aresId") || "";
			G$1.systemtTypeIsPlatform = T.getPrefs("sys_systemType") == "platform"; //系统类型是否是平台版
			G$1.appName = T.getPrefs("sys_systemName") || "";
			G$1.uId = T.getPrefs("sys_Id") || "";
			G$1.userId = T.getPrefs("sys_UserID") || "";
			G$1.userName = T.getPrefs("sys_UserName") || "";
			G$1.userImg = T.getPrefs("sys_AppPhoto") || "";
			G$1.specialRoleKeys = JSON.parse(T.getPrefs("sys_SpecialRoleKeys") || "[]");
			G$1.isAdmin =
				G$1.userId == "1" ||
				G$1.getItemForKey("dc_admin", G$1.specialRoleKeys) ||
				G$1.getItemForKey("admin", G$1.specialRoleKeys);
			G$1.v = T.getPrefs("sys_appVersion") || "";
			if (T.platform() == "app") {
				G$1.isAppReview =
					JSON.parse(T.getPrefs("sys_appReviewVersion") || "{}")[api.systemType] ==
					api.appVersion;
			}
		},
		//全局配置
		changeConfiguration: function changeConfiguration(_this) {
			G$1.appFont =
				T.getPrefs("appFont") && T.getPrefs("appFont") != "0"
					? T.getPrefs("appFont")
					: "heitiSimplified";
			G$1.appFontSize = Number(
				T.getPrefs("appFontSize") && T.getPrefs("appFontSize") != "0"
					? T.getPrefs("appFontSize")
					: "16"
			);
			G$1.appTheme =
				T.pageParam(_this).appTheme ||
				T.getPrefs("appTheme" + (myjs.iszx ? "zx" : "rd")) ||
				(myjs.iszx ? "#3088FE" : "#C61414");
			var headTheme =
				_this.data.headTheme ||
				T.pageParam(_this).headTheme ||
				T.getPrefs("headTheme") ||
				"#FFF";
			G$1.headTheme = headTheme == "appTheme" ? G$1.appTheme : headTheme;
			G$1.careMode = parseInt(G$1.appFontSize) > 16;
			if (T.platform() == "web") {
				var fontStyleId = "fontStyle";
				if (document.getElementById(fontStyleId)) {
					//存在的时候先删除
					document
						.getElementById(fontStyleId)
						.parentNode.removeChild(document.getElementById(fontStyleId));
				}
				var fontStyle = document.createElement("style");
				fontStyle.id = fontStyleId;
				switch (G$1.appFont) {
					case "shusongSimplified":
						fontStyle.innerText =
							"@font-face{font-family: shusongSimplified;src: url('../../res/fz_shusong_simplified.ttf')}";
						break;
					case "kaitiSimplified":
						fontStyle.innerText =
							"@font-face{font-family: kaitiSimplified;src: url('../../res/fz_kaiti_simplified.ttf')}";
						break;
					case "heitiTraditional":
						fontStyle.innerText =
							"@font-face{font-family: heitiTraditional;src: url('../../res/fz_heiti_traditional.ttf')}";
						break;
				}

				document.getElementsByTagName("head")[0].appendChild(fontStyle);
			}
			_this.update();
			T.sendEvent("updatePage");
		},
		//是否全局置灰
		appGrayscale: function appGrayscale() {
			var appGrayscale = T.getPrefs("appGrayscale") || "0";
			if (T.platform() == "app");
			else {
				// G.htmlStyle = "filter:"+(appGrayscale == 1?'grayscale(1)':'none')+";";//小程序不知道为啥style没用
				G$1.htmlClass = appGrayscale == 1 ? "filterGray" : "filterNone";
			}
		},
		//展示图片
		showImg: function showImg(_item) {
			var baseUrl = T.isObject(_item) ? _item.url || "" : _item || "";
			baseUrl = G$1.showAllSystemImg(baseUrl); //先显示系统图片
			if (
				baseUrl.indexOf("http") == 0 &&
				baseUrl.indexOf("http://127.0.0.1") != 0 &&
				baseUrl.indexOf(myjs.tomcatAddress()) != 0
			) {
				//是链接 不是小程序本地链接 不是处理过的链接
				if (myjs.proxy && T.platform() != "app" && baseUrl.indexOf("https") != 0) {
					baseUrl = myjs.tomcatAddress() + "utils/proxyPic?" + baseUrl;
				}
			}
			return baseUrl + (baseUrl.indexOf("?") != -1 ? "&" : "?") + "v=" + G$1.v;
		},
		showAllSystemImg: function showAllSystemImg(_url) {
			return !_url ||
				_url.indexOf("http") == 0 ||
				_url.indexOf("/") == 0 ||
				_url.indexOf("../") == 0
				? _url
				: myjs.appUrl() + "image/" + _url;
		},
		//图片处理
		cacheImg: function cacheImg(_item, _thumbnail, _url, _priority) {
			if (!T.isObject(_item) || !T.isParameters(_item.url)) return; //没有传对象 或者没有url的时候不处理
			var baseUrl = _item.webImg || _url || _item.url || ""; //存储当前缓存地址
		},
		//字体配置
		loadConfiguration: function loadConfiguration(_changeSize) {
			return (
				"font-size:" +
				((G$1.appFontSize || 0) + (_changeSize || 0)) +
				"px;font-family:" +
				G$1.appFont +
				";"
			);
		},
		//宽度配置
		loadConfigurationSize: function loadConfigurationSize(_changeSize, _who) {
			var changeSize = _changeSize || 0,
				returnCss = "",
				cssWidth,
				cssHeight;
			if (T.isArray(_changeSize)) {
				cssWidth = "width:" + (G$1.appFontSize + (_changeSize[0] || 0)) + "px;";
				cssHeight = "height:" + (G$1.appFontSize + (_changeSize[1] || 0)) + "px;";
			} else {
				cssWidth = "width:" + (G$1.appFontSize + changeSize) + "px;";
				cssHeight = "height:" + (G$1.appFontSize + changeSize) + "px;";
			}
			if (!_who) {
				returnCss = cssWidth + cssHeight;
			} else {
				returnCss = _who == "w" ? cssWidth : cssHeight;
			}
			return returnCss;
		},
		//获取item	只有一层级的时候 会返回 当前index	_i
		getItemForKey: function getItemForKey(_value, _list, _key, _child) {
			var hasChild = false;
			if (!T.isParameters(_list)) return;
			var listLength = _list.length;
			for (var i = 0; i < listLength; i++) {
				var listItem = _list[i];
				if (T.isArray(listItem)) {
					hasChild = true;
					var result = G$1.getItemForKey(_value, listItem, _key, true);
					if (result) return result;
				} else {
					if (!T.isObject(listItem)) {
						if (listItem === _value) {
							return listItem;
						}
					} else {
						if (T.isArray(listItem[_key || "key"])) {
							hasChild = true;
							var result = G$1.getItemForKey(
								_value,
								listItem[_key || "key"],
								_key,
								true
							);
							if (result) {
								listItem["_i"] = i;
								return listItem;
							}
						} else if (listItem[_key || "key"] === _value) {
							listItem["_i"] = i;
							return listItem;
						}
					}
					if (
						T.isObject(listItem) &&
						listItem.children &&
						T.isArray(listItem.children)
					) {
						hasChild = true;
						var result = G$1.getItemForKey(_value, listItem.children, _key, true);
						if (result) return result;
					}
				}
			}
			if (!_child && !hasChild) return false;
		},
		//在集合中删除第一个参数obj和index都可以或对比字符串	第二个传入集合	第三个为对比key
		delItemForKey: function delItemForKey(_obj, _list, _key) {
			if (T.isTargetType(_obj, "number") && _obj < _list.length) {
				_list.splice(_obj, 1);
			} else {
				var contrastObj = !T.isObject(_obj) ? _obj : _obj[_key || "key"];
				for (var i = 0; i < _list.length; i++) {
					if (
						(!T.isObject(_list[i]) ? _list[i] : _list[i][_key || "key"]) ==
						contrastObj
					) {
						_list.splice(i, 1);
						G$1.delItemForKey(_obj, _list, _key);
						break;
					}
				}
			}
		},
		//是否显示顶部
		showHeader: function showHeader(_this) {
			return T.platform() == "app" || T.pageParam(_this).showHeader;
		},
		//适配多端状态栏
		headerTop: function headerTop() {
			return T.platform() == "app" ? T.safeArea().top : 0;
		},
		//底部可视区域
		footerBottom: function footerBottom(_bottom) {
			return _bottom ? T.safeArea().bottom : 0;
		},
		//适配pc页打开宽度
		fitWidth: function fitWidth() {
			if (
				T.platform() == "web" &&
				document.documentElement.clientWidth > document.documentElement.clientHeight
			) {
				$("body").style.width = "100%";
				$("body").style.maxWidth = "600px";
				$("body").style.minWidth = "300px";
				$("body").style.margin = "auto";
				$("body").style.position = "relative";
			}
		},
		//获取当前主题色相对应前景色
		getHeadThemeRelatively: function getHeadThemeRelatively(_this) {
			var theme =
				(_this && _this.data && _this.data.headTheme
					? _this.data.headTheme || ""
					: "") || G$1.headTheme;
			return theme && T.isColorDarkOrLight(theme) == "dark" ? "#FFF" : "#333";
		},
		//转换成html格式
		convertRichText: function convertRichText(value) {
			value = (T.isParameters(value) ? value : "") + "";
			if (T.isObject(value) || T.isArray(value)) {
				value = JSON.stringify(value);
			}
			var textList = value.split("\n");
			var str = "";
			for (var i = 0; i < textList.length; i++) {
				var addText = textList[i].replace(/&amp;/g, "&").replace(/ /g, "&nbsp;");
				if (addText) {
					str += "<p>" + addText + "</p>";
				}
			}
			return str;
		},
		//清空html格式
		clearRichText: function clearRichText(value) {
			value = (T.isParameters(value) ? value : "") + "";
			if (T.isObject(value) || T.isArray(value)) {
				value = JSON.stringify(value);
			}
			//坑爹的后台管理了 & 符号
			value = value.replace(/&amp;/g, "&");
			// 空格处理
			value = value.replace(/(&nbsp;)/g, " ");
			// 换行处理
			value = value.replace(/<br\/?[^>]*>/g, "\n");

			value = value.replace(/<\/[p|div|h1|h2|h3|h4|h5|h6]>/g, "\n");
			value = value.replace(/<\/?[^>]*>/g, "");
			return value;
		},
		//阻止冒泡事件
		stopBubble: function stopBubble(e) {
			if (!e) return;
			if (T.platform() == "web") {
				e.preventDefault();
				e.stopPropagation();
			} else if (T.platform() == "mp") {
				e.$_canBubble = false;
			}
		},
		getTagColor: function getTagColor(_key) {
			if (_key === void 0) {
				_key = "";
			}
			var tagColors = [
				{key: "未处理", value: "#F6631C"},
				{key: "未开始", value: "#F6631C"},

				{key: "签到中", value: "#F6931C"},
				{key: "报名中", value: "#F6931C"},
				{key: "进行中", value: "#F6931C"},

				{key: "请假通过", value: "#50C614"},
				{key: "请假待审批", value: "#F6931C"},
				{key: "请假中", value: "#F6931C"},
				{key: "已参与", value: G$1.appTheme},
				{key: "待审核", value: "#F6931C"},
				{key: "已通过", value: "#50C614"},
				{key: "有效", value: G$1.appTheme},
				{key: "待审查", value: "#F6931C"},
				{key: "人大交办中", value: "#F6931C"},
				{key: "政协交办中", value: "#F6931C"},
				{key: "政府交办中", value: "#F6931C"},
				{key: "党委交办中", value: "#F6931C"},
				{key: "两院交办中", value: "#F6931C"},
				{key: "法院交办中", value: "#F6931C"},
				{key: "检察院交办中", value: "#F6931C"},
				{key: "转参阅件", value: "#C61414"},
				{key: "办理中", value: "#F6931C"},
				{key: "重新办理", value: "#F6931C"},
				{key: "已答复", value: "#50C614"},
				{key: "已办结", value: "#559FFF"},
				{key: "A类", value: "#F6931C"},
				{key: "B类", value: "#1A74DA"},
				{key: "待受理", value: "#F6931C"},
				{key: "已受理", value: "#50C614"},
				{key: "已回复", value: "#50C614"},
				{key: "待交付审议", value: "#F6931C"},
				{key: "专委会审议中", value: "#F6931C"},
				{key: "已上传相关资料", value: "#50C614"},
				{key: "留存", value: "#F6931C"},
				{key: "采用", value: "#50C614"}
			];

			var tagColor = G$1.getItemForKey(_key, tagColors);
			return tagColor ? tagColor.value : "#666666";
		},
		//获取文件类型 并返回数据
		getFileInfo: function getFileInfo(_name) {
			if (_name === void 0) {
				_name = "";
			}
			var name = _name.toLocaleLowerCase(),
				fileInfo = {name: "file-unknow-fill", color: "#bccbd7", type: "unknown"};
			try {
				if (name.indexOf(".") != -1)
					name = name.split(".")[name.split(".").length - 1];
				switch (name) {
					case "xlsx":
					case "xlsm":
					case "xlsb":
					case "xltx":
					case "xltm":
					case "xls":
					case "xlt":
					case "et":
					case "csv":
					case "uos": //excel格式
						fileInfo.name = "file-excel-fill";
						fileInfo.color = "#00bd76";
						fileInfo.type = "excel";
						fileInfo.convertType = "0";
						break;
					case "doc":
					case "docx":
					case "docm":
					case "dotx":
					case "dotm":
					case "dot":
					case "xps":
					case "rtf":
					case "wps":
					case "wpt":
					case "uot": //word格式
						fileInfo.name = "file-word-fill";
						fileInfo.color = "#387efa";
						fileInfo.type = "word";
						fileInfo.convertType = "0";
						break;
					case "pdf": //pdf格式
						fileInfo.name = "file-pdf-fill";
						fileInfo.color = "#e9494a";
						fileInfo.type = "pdf";
						fileInfo.convertType = "20";
						break;
					case "ppt":
					case "pptx":
					case "pps":
					case "pot":
					case "pptm":
					case "potx":
					case "potm":
					case "ppsx":
					case "ppsm":
					case "ppa":
					case "ppam":
					case "dps":
					case "dpt":
					case "uop": //ppt
						fileInfo.name = "file-ppt-fill";
						fileInfo.color = "#ff7440";
						fileInfo.type = "ppt";
						fileInfo.convertType = "0";
						break;
					case "bmp":
					case "gif":
					case "jpg":
					case "pic":
					case "png":
					case "tif":
					case "jpeg":
					case "jpe":
					case "icon":
					case "jfif":
					case "dib": //图片格式 case 'webp':
						fileInfo.name = "file-text-fill";
						fileInfo.color = "#ff7440";
						fileInfo.type = "image";
						fileInfo.convertType = "440";
						break;
					case "txt": //文本
						fileInfo.name = "file-text-fill";
						fileInfo.color = "#2696ff";
						fileInfo.type = "txt";
						fileInfo.convertType = "0";
						break;
					case "rar":
					case "zip":
					case "7z":
					case "tar":
					case "gz":
					case "jar":
					case "ios": //压缩格式
						fileInfo.name = "file-zip-fill";
						fileInfo.color = "#a5b0c0";
						fileInfo.type = "compression";
						fileInfo.convertType = "19";
						break;
					case "mp4":
					case "avi":
					case "flv":
					case "f4v":
					case "webm":
					case "m4v":
					case "mov":
					case "3gp":
					case "rm":
					case "rmvb":
					case "mkv":
					case "mpeg":
					case "wmv": //视频格式
						fileInfo.name = "file-music-fill";
						fileInfo.color = "#e14a4a";
						fileInfo.type = "video";
						fileInfo.convertType = "450";
						break;
					case "mp3":
					case "m4a":
					case "amr":
					case "pcm":
					case "wav":
					case "aiff":
					case "aac":
					case "ogg":
					case "wma":
					case "flac":
					case "alac":
					case "wma":
					case "cda": //音频格式
						fileInfo.name = "file-music-fill";
						fileInfo.color = "#8043ff";
						fileInfo.type = "voice";
						fileInfo.convertType = "660";
						break;
					case "folder": //文件夹
						fileInfo.name = "folder-2-fill";
						fileInfo.color = "#ffd977";
						fileInfo.type = "folder";
						break;
				}
			} catch (e) {
				console.log(e.message);
			}
			return fileInfo;
		},
		//获取文件大小
		getFileSize: function getFileSize(_fileSize) {
			if (!_fileSize && _fileSize != 0) return "";
			try {
				var size1 = parseFloat((_fileSize / 1024 / 1024).toFixed(1));
				var size2 = parseFloat((_fileSize / 1024).toFixed(1));
				if (size1 >= 1) {
					return size1 + "MB";
				} else if (size2 >= 1) {
					return size2 + "KB";
				} else {
					return parseInt(_fileSize) + "B";
				}
			} catch (e) {
				return _fileSize;
			}
		},
		//选择文件并上传
		chooseFile: function chooseFile(_this, _item, callback) {
			var max = T.isNumber(_item.max) ? _item.max : 0;
			if (T.platform() == "app") {
				if (T.systemType() == "ios") {
					if (!T.confirmPer("storage", "chooseFile")) {
						//存储权限
						T.addEventListener("storage" + "Per_" + "chooseFile", function(ret, err) {
							T.removeEventListener("storage" + "Per_" + "chooseFile");
							if (ret.value.granted) {
								G$1.chooseFile(_this, _item, callback);
							}
						});
						return;
					}
				} else {
					if (!api.require("zyRongCloud").hasAllFilesPermission()) {
						T.alert(
							{
								title: "提示",
								msg: "选择本机文件需要您授权访问所有文件权限，是否继续?",
								buttons: ["确定", "取消"]
							},
							function(ret) {
								if (ret.buttonIndex == "1") {
									api.require("zyRongCloud").requestAllFilesPermission(function(ret) {
										if (ret.status) {
											G$1.chooseFile(_this, _item, callback);
										}
									});
								}
							}
						);
						return;
					}
				}
				var fileBrowser = api.require("fileBrowser");
				fileBrowser.open({}, function(ret, err) {
					fileBrowser.close();
					setTimeout(function() {
						_item.url = ret.url;
						G$1.uploadFile(_this, _item, function(ret) {
							callback && callback(ret);
						});
					}, 500);
				});
			} else if (T.platform() == "web") {
				var h5Input = document.createElement("input");
				h5Input.type = "file";
				h5Input.accept = "";
				var ua = navigator.userAgent.toLowerCase();
				var version = "";
				if (ua.indexOf("android") > 0) {
					var reg = /android [\d._]+/gi;
					var v_info = ua.match(reg);
					version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, ".");
					version = parseInt(version.split(".")[0]);
				}
				if (!version || Number(version) <= 13) {
					h5Input.multiple = "multiple";
				}
				h5Input.click();
				h5Input.onchange = function() {
					var listLength =
						max != 0 && h5Input.files.length > max ? max : h5Input.files.length;
					for (var i = 0; i < listLength; i++) {
						(function(j) {
							var nItem = JSON.parse(JSON.stringify(_item));
							nItem.url = h5Input.files[j];
							G$1.uploadFile(_this, nItem, function(ret) {
								callback && callback(ret);
							});
						})(i);
					}
				};
			} else if (T.platform() == "mp") {
				wx.chooseMessageFile({
					count: max != 0 ? max : 9,
					type: "file",
					success: function success(res) {
						for (var i = 0; i < res.tempFiles.length; i++) {
							(function(j) {
								var nItem = JSON.parse(JSON.stringify(_item));
								nItem.url = res.tempFiles[j];
								G$1.uploadFile(_this, nItem, function(ret) {
									callback && callback(ret);
								});
							})(i);
						}
					}
				});
			}
		},
		//通用上传附件 item格式  url本地地址路径 uploadId上传后的id	state状态【1上传中2完成3失败】
		uploadFile: function uploadFile(_this, _item, callback) {
			if (_item._fileAjax || _item.module == "-noUpload")
				//有传过 或者明确不传
				return;
			_item._fileAjax = true; //是否请求过	有就不再请求
			_item.state = 1;
			if (_item.showToast) {
				T.showProgress("上传中");
			}
			var nCallack = function nCallack(ret, err) {
				T.hideProgress();
				var code = ret ? ret.code : "";
				if (code == 200) {
					var data = ret.data || {};
					_item.state = 2;
					_item.uploadId = data.id;
					_item.otherInfo = data;
				} else {
					_item.state = 3;
					_item.error = ret ? ret.message || ret.data : err.data || "";
				}
				callback && callback(_item);
			};
			if (T.platform() == "mp") {
				wx.uploadFile({
					url: myjs.tomcatAddress() + "utils/proxy",
					filePath: _item.url.path,
					name: "file",
					header: {
						"Content-Type": "multipart/form-data",
						"u-login-areaId": myjs.areaId(_this),
						Authorization: T.getPrefs("sys_token") || ""
					},

					formData: {
						BASE_URL: myjs.appUrl() + "file/upload",
						BASE_TYPE: "file",
						fileName:
							_item.url.name ||
							_item.url.path.substring(_item.url.path.lastIndexOf("/") + 1)
					},

					success: function success(res) {
						nCallack(JSON.parse(res.data), null);
					},
					fail: function fail(err) {
						nCallack(null, JSON.parse(err.data));
					}
				});
			} else {
				T.ajax(
					{u: myjs.appUrl() + "file/upload", _this: _this, web: _item.web},
					"file/upload" + _item.url,
					nCallack,
					"上传附件",
					"post",
					{
						files: {file: _item.url},
						values: {a: 1}
					},
					{
						"content-type": "file" //安卓附件不能传 得用默认的
					}
				);
			}
		},
		getAreaForKey: function getAreaForKey(_key, _dotAll) {
			var rItem = null;
			var areas = JSON.parse(T.getPrefs("sys_areas") || "[]");
			if (!_dotAll || !areas.length) {
				areas = JSON.parse(T.getPrefs("sys_allAreas") || "[]");
			}
			if (areas.length) {
				rItem = G$1.getItemForKey(_key, areas, "id");
				if (rItem) {
					rItem.name =
						rItem.name.length > 4 ? rItem.name.substring(0, 4) + "..." : rItem.name;
				}
			}
			return rItem || {};
		},
		showTextSize: function showTextSize(_text, _size, _middle) {
			if (_size && _text) {
				_text =
					_text.length > _size
						? _middle
							? _text.substring(0, _size / 2) +
							  "..." +
							  _text.substring(_text.length - _size / 2)
							: _text.substring(0, _size) + "..."
						: _text;
			}
			return _text;
		},
		ajaxAlert: function ajaxAlert(_param, _this, _callback) {
			var _this4 = this;
			var param = {
				title: "提示",
				msg: _param.msg || "",
				buttons: _param.buttons || ["确定", "取消"]
			};

			if (_param.alertParam) {
				param = T.setNewJSON(param, _param.alertParam);
			}
			T.alert(param, function(ret) {
				if (ret.buttonIndex == "1") {
					_this4.ajaxProcess(_param, _this, _callback);
				}
			});
		},
		ajaxProcess: function ajaxProcess(_param, _this, _callback) {
			if (!_param.dotProgress) T.showProgress(_param.toast);
			T.ajax(
				{u: _param.url, _this: _this},
				"ajaxProcess",
				function(ret) {
					if (!_param.dotProgress) T.hideProgress();
					if (!_param.dotToast) T.toast(ret ? ret.message || ret.data : T.NET_ERR);
					if (ret && ret.code == "200") {
						_callback && _callback(ret);
					}
				},
				"\u64CD\u4F5C",
				"post",
				{
					body: JSON.stringify(_param.param)
				}
			);
		}
	};

	var YChooseBook = /*@__PURE__*/ (function(Component) {
		function YChooseBook(props) {
			Component.call(this, props);
			this.data = {};
		}

		if (Component) YChooseBook.__proto__ = Component;
		YChooseBook.prototype = Object.create(Component && Component.prototype);
		YChooseBook.prototype.constructor = YChooseBook;
		YChooseBook.prototype.cleanUser = function(_item, _index, e) {
			if (this.props.noClean) {
				return;
			}
			G$1.delItemForKey(_index, this.props.listSelect);
			G$1.stopBubble(e);
		};
		YChooseBook.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: "page"},
				apivm.h(
					"scroll-view",
					{
						style: "padding:10px 3px;",
						class: "selected_notes_scroll_view",
						"scroll-x": true,
						"scroll-y": "false"
					},
					this.props.listSelect &&
						apivm.h(
							"view",
							{class: "flex-row"},
							this.props.showAddBook &&
								apivm.h(
									"view",
									{class: "addBook"},
									apivm.h("a-iconfont", {
										name: "xinzeng",
										color: "#7F7F7F",
										size: G$1.appFontSize + 20
									})
								),
							(Array.isArray(this.props.listSelect)
								? this.props.listSelect
								: Object.values(this.props.listSelect)
							).map(function(item$1, _eindex) {
								return apivm.h(
									"view",
									{style: "width:90px;margin-right:10px;"},
									apivm.h("image", {
										style: "width:90px;height:100px;",
										src: item$1.coverImgUrl.includes("http")
											? item$1.coverImgUrl
											: appUrl() + "image/" + item$1.coverImgUrl,
										mode: "aspectFill"
									}),
									apivm.h(
										"text",
										{
											class: "text_one" + (T.platform() == "app" ? "" : "2"),
											style: G$1.loadConfiguration(-2) + ";"
										},
										item$1.bookName
									),
									!this$1.props.noClean &&
										apivm.h(
											"view",
											{
												class: "select_clean",
												onClick: function(e) {
													return this$1.cleanUser(item$1, _eindex, e);
												}
											},
											apivm.h("a-iconfont", {
												name: "qingkong",
												color: "#7F7F7F",
												size: G$1.appFontSize + 4
											})
										)
								);
							})
						)
				)
			);
		};

		return YChooseBook;
	})(Component);
	YChooseBook.css = {
		".page": {maxHeight: "180px", height: "100%"},
		".select_clean": {
			position: "absolute",
			zIndex: "999",
			right: "-10px",
			top: "-10px"
		},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden"
		},
		".text_one2": {
			WebkitLineClamp: "1",
			WebkitBoxOrient: "vertical",
			display: "-webkit-box",
			overflow: "hidden"
		},
		".flex-row": {flexDirection: "row"},
		".addBook": {
			width: "90px",
			height: "120px",
			marginRight: "10px",
			border: "0.5px dashed lightgray",
			justifyContent: "center",
			alignItems: "center"
		}
	};
	apivm.define("y-choose-book", YChooseBook);

	var PreviewerImg = /*@__PURE__*/ (function(Component) {
		function PreviewerImg(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				frameName: "previewerImg"
			};
			this.compute = {
				monitor: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.props.dataMore.watermark = G.watermark;
							this.baseInit();
						} else {
							if (platform() == "app" && this.UIPhotoViewer) {
								this.UIPhotoViewer.close();
							}
						}
					}
					if (this.data.show) {
						if (this.nowParam != JSON.stringify(this.props.dataMore)) {
							this.nowParam = JSON.stringify(this.props.dataMore);
							this.sendMessage();
						}
					}
				}
			};
		}

		if (Component) PreviewerImg.__proto__ = Component;
		PreviewerImg.prototype = Object.create(Component && Component.prototype);
		PreviewerImg.prototype.constructor = PreviewerImg;
		PreviewerImg.prototype.baseInit = function() {
			var this$1 = this;

			var data = this.props.dataMore;
			switch (platform()) {
				case "app":
					if (G.watermark) {
						//有水印使用web
						addEventListener(this.data.frameName + "_msg", function() {
							this$1.closePage();
						});
						api.setFrameClient({frameName: this.data.frameName}, function(ret, err) {
							if (ret.state == 2) {
								this$1.isLoading = true;
								setTimeout(function() {
									this$1.sendMessage();
								}, 400);
							}
						});
						return;
					}
					this.UIPhotoViewer = api.require("UIPhotoViewer");
					this.UIPhotoViewer.open(
						{
							images: data.imgs,
							activeIndex: data.index,
							gestureClose: true,
							bgColor: "#000"
						},
						function(ret, err) {
							switch (ret.eventType) {
								case "click":
									this$1.closePage();
									break;
								case "gestureColse":
									this$1.UIPhotoViewer = null;
									this$1.closePage();
									break;
								case "longPress":
									var nowImg = data.imgs[ret.index];
									api.actionSheet(
										{
											buttons: ["保存到相册"],
											cancelTitle: "取消"
										},
										function(ret) {
											switch (ret.buttonIndex) {
												case 1:
													api.saveMediaToAlbum(
														{
															path: nowImg
														},
														function(ret) {
															toast(ret && ret.status ? "已保存到手机相册" : "保存失败");
														}
													);
													break;
											}
										}
									);
									break;
							}
						}
					);
					break;
				case "mp":
					wx.previewImage({
						urls: data.imgs,
						current: data.imgs[data.index],
						complete: function() {
							this$1.closePage();
						}
					});

					break;
				case "web":
					window.addEventListener("message", function(event) {
						this$1.closePage();
					});
					document
						.getElementById(this.data.frameName)
						.addEventListener("load", function() {
							this$1.isLoading = true;
							this$1.sendMessage();
						});
					break;
			}
		};
		PreviewerImg.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		PreviewerImg.prototype.sendMessage = function() {
			if (this.isLoading && this.props.dataMore.show) {
				if (platform() == "web") {
					if (!document.getElementById(this.data.frameName)) {
						return;
					}
					var targetWindow = document.getElementById(this.data.frameName)
						.contentWindow;
					targetWindow.postMessage(this.nowParam, "*");
				} else if (platform() == "app") {
					sendEvent(this.data.frameName + "_open", this.props.dataMore);
				}
			}
		};
		PreviewerImg.prototype.render = function() {
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0);"
				},
				this.props.dataMore.show &&
					(platform() == "web" || G.watermark) &&
					apivm.h("frame", {
						id: this.data.frameName,
						class: "xy_100",
						name: this.data.frameName,
						url: shareAddress() + "html/previewerImg.html",
						pageParam: {name: this.data.frameName},
						useWKWebView: true,
						scaleEnabled: true,
						allowEdit: true
					})
			);
		};

		return PreviewerImg;
	})(Component);
	apivm.define("previewer-img", PreviewerImg);

	var ZVideo = /*@__PURE__*/ (function(Component) {
		function ZVideo(props) {
			Component.call(this, props);
			this.data = {
				controls: true
			};
			this.compute = {
				getSrc: function() {
					var src = this.props.src;
					if (src.indexOf("http") != 0) {
						src = appUrl() + "file/preview/" + src;
					}
					return src;
				}
			};
		}

		if (Component) ZVideo.__proto__ = Component;
		ZVideo.prototype = Object.create(Component && Component.prototype);
		ZVideo.prototype.constructor = ZVideo;
		ZVideo.prototype.firstPause = function() {
			var this$1 = this;

			if (!this.isPause) {
				this.isPause = true;
				if (platform() == "app" && !this.props.poster) {
					this.data.controls = false;
					setTimeout(function() {
						if (!this$1.props.autoplay) {
							document.getElementById(dealVideoId(this$1.props.src)) &&
								document.getElementById(dealVideoId(this$1.props.src)).pause();
						}
						this$1.data.controls = true;
					}, 50);
				} else {
					this.data.controls = true;
				}
			}
		};
		ZVideo.prototype.loadedmetadata = function(e) {
			if (api.systemType != "ios") {
				this.isPause = false;
				this.firstPause();
			}
		};
		ZVideo.prototype.play = function() {
			videoPlayPush(dealVideoId(this.props.src));
		};
		ZVideo.prototype.pause = function() {
			videoPlayRemove(dealVideoId(this.props.src));
		};
		ZVideo.prototype.render = function() {
			return apivm.h("video", {
				id: dealVideoId(this.props.src),
				style: "width:100%;height:" + G.pageWidth * 0.56 + "px;",
				controls: this.data.controls,
				autoplay:
					(platform() == "app" && !this.props.poster) || this.props.autoplay,
				onLoadedmetadata: this.loadedmetadata,
				onWaiting: this.firstPause,
				onPlay: this.play,
				onPause: this.pause,
				onEnded: this.pause,
				src: this.getSrc,
				poster: showImg(this.props.poster)
			});
		};

		return ZVideo;
	})(Component);
	apivm.define("z-video", ZVideo);

	var ZRichText = /*@__PURE__*/ (function(Component) {
		function ZRichText(props) {
			Component.call(this, props);
			this.data = {
				showText: null,
				listData: [],
				hasExpand: false, //是否有展开
				isExpand: false, //是否展开了
				appDetailsStyle: ""
			};
			this.compute = {
				monitor: function() {
					if (this.props.nodes != this.data.showText) {
						this.data.showText = this.props.nodes;
						this.dealWithCon();
					}
				}
			};
		}

		if (Component) ZRichText.__proto__ = Component;
		ZRichText.prototype = Object.create(Component && Component.prototype);
		ZRichText.prototype.constructor = ZRichText;
		ZRichText.prototype.expandShow = function(e) {
			stopBubble(e);
			this.data.isExpand = !this.data.isExpand;
			this.dealWithCon();
		};
		ZRichText.prototype.dealWithCon = function() {
			var this$1 = this;

			var expText =
				(isParameters(this.data.showText) ? this.data.showText : "") + "";
			if (isObject(expText) || isArray(expText)) {
				expText = JSON.stringify(expText);
			}
			this.data.appDetailsStyle = getPrefs("appDetailsStyle");
			if (this.data.appDetailsStyle == "edit") {
				this.data.showText = expText;
				return;
			}

			var notTagText = removeTag(expText);
			this.data.hasExpand =
				isParameters(this.props.expand) && notTagText.length > this.props.expand;
			expText =
				this.data.hasExpand && !this.data.isExpand
					? notTagText.substring(0, this.props.expand) + "..."
					: expText;

			expText = expText.replace(
				/(<style(.*?)<\/style>|<link(.*?)<\/link>|<script(.*?)<\/script>|<!--[\w\W\r\n]*?-->|^\s*|\s*$)/gi,
				""
			);
			var reLabel = function(_bel) {
				var oldText = expText;
				expText = expText.replace(
					new RegExp("<" + _bel + "[^>]*>(.*?)</" + _bel + ">", "gi"),
					"$1"
				);
				if (expText != oldText && expText.indexOf(_bel) != -1) {
					reLabel(_bel);
				}
			};
			["span"].forEach(function(item) {
				reLabel(item);
			});
			expText = expText.replace(/<\s*\/?\s*br\s*\/?\s*>/gi, "</br>");
			expText = expText.replace(/\n/gi, "</br>");
			expText = decodeCharacter(expText);
			expText = expText.replace(/<ins(.*?)>(.*?)<\/ins>/gi, "$2");
			if (!expText.startsWith("<") || !expText.endsWith(">")) {
				expText = "<div>" + expText + "</div>";
			}
			// console.log("解析一："+expText);
			var newText = expText;
			//清空表格td中所有的标签
			var rdRegex = /<td(.*?)>(.*?)<\/td>/gi;
			var match;
			while ((match = rdRegex.exec(expText)) !== null) {
				var nText = removeTag(match[2]);
				newText = newText.replace(match[2], nText);
			}
			expText = newText;
			// console.log("解析二："+expText);
			var strRegex = /<\/?\w+[^>]*>/gi;
			var nowIndexs = [],
				viewIndexs = [];
			var startIndex = [];
			expText.replace(strRegex, function(item, index) {
				//循环对应的内容和角标
				var nlabel = item.match(/<\/*([^> ]+)[^>]*>/)[1].toLocaleLowerCase();
				var styleMatch = /style\s*=\s*['"]([^'"]*)['"]/i.exec(item);
				var nStyle = styleMatch ? styleMatch[1] : "";
				if (nlabel == "img") {
					nStyle = nStyle
						.replace("width: auto;", "width:100%;")
						.replace("width:auto;", "width:100%;");
				}
				var nowItem = {label: nlabel, index: index, text: item, style: nStyle};
				viewIndexs.push(nowItem);
				// console.log(index + "-" + nlabel + "-" + item);
				if (/^<(p|div|span).*/i.test(item)) {
					var nowItems = {
						index: nowIndexs.length,
						endIndex: 0,
						label: nlabel,
						start: nowItem,
						end: null,
						style: nStyle
					};
					startIndex.push(nowItems);
					nowIndexs.push(nowItems);
				} else if (/^<\/(p|div|span)>/i.test(item)) {
					if (startIndex.length) {
						nowIndexs[startIndex[startIndex.length - 1].index].end = nowItem;
						nowIndexs[startIndex[startIndex.length - 1].index].endIndex =
							nowIndexs.length - 1;
						startIndex.pop();
					}
				} else if (/^<.*?>$/.test(item)) {
					nowIndexs.push({
						index: nowIndexs.length,
						endIndex: nowIndexs.length,
						label: nlabel,
						start: nowItem,
						end: nowItem,
						style: nStyle
					});
				}
			});
			var showTexts = [];
			var tableData = null,
				isTable = false,
				tableItem = null;
			viewIndexs.forEach(function(item, index) {
				var minIndex = item.index + item.text.length;
				var maxIndex =
					index != viewIndexs.length - 1
						? viewIndexs[index + 1].index
						: expText.length;
				var viewText = expText.substring(minIndex, maxIndex);
				if (item.label == "table") {
					if (!isTable) {
						isTable = true;
						tableData = {
							label: "table",
							widths: [],
							data: [],
							index: minIndex,
							style: item.style
						};
					} else {
						isTable = false;
						showTexts.push(tableData);
					}
					return;
				}
				if (isTable) {
					if (item.text == "</tr>") {
						tableData.data.push(tableItem);
					} else if (item.label == "tr") {
						tableItem = {label: "tr", data: [], index: minIndex, style: item.style};
					} else if (item.text != "</td>" && item.label == "td") {
						var widthMatch = item.text.match(/width="([^"]*)"/);
						var tdWidth = widthMatch ? widthMatch[1] + "px" : null;
						if (!tdWidth) {
							tdWidth = this$1.getStyle(item.style, "width") || "150px";
						}
						if (!tableData.data.length) {
							if (tdWidth.indexOf("%") != -1) {
								//是百分比宽度
								var nW = Number(tdWidth.replace(/%/g, ""));
								tdWidth = ((nW < 30 ? 30 : nW) * G.pageWidth) / 100 + "px";
							}
							tableData.widths.push(tdWidth);
						}
						var showStyle = "";
						showStyle +=
							"border:" +
							(this$1.getStyle(item.style, "border") || "1px solid #ccc") +
							";";
						showStyle +=
							"border-left:" + this$1.getStyle(item.style, "border-left") + ";";
						showStyle +=
							"border-right:" + this$1.getStyle(item.style, "border-right") + ";";
						showStyle +=
							"border-top:" + this$1.getStyle(item.style, "border-top") + ";";
						showStyle +=
							"border-bottom:" + this$1.getStyle(item.style, "border-bottom") + ";";
						showStyle +=
							"background:" + this$1.getStyle(item.style, "background") + ";";
						showStyle += "color:" + this$1.getStyle(item.style, "color") + ";";
						tableItem.data.push({
							label: "td",
							index: minIndex,
							text: viewText,
							style: showStyle
						});
					}
					return;
				}
				if (/^(?!p|div|span).*$/i.test(item.label)) {
					if (
						item.label == "br" &&
						!item.text &&
						showTexts.length &&
						showTexts[showTexts.length - 1].label == "br"
					) {
						return;
					}
					var addItem = {label: item.label, index: item.index, style: item.style};
					minIndex = minIndex + item.text.length;
					var srcMatch = /src\s*=\s*['"]([^'"]*)['"]/i.exec(item.text);
					if (srcMatch) {
						addItem.src = srcMatch[1];
					}
					var hrefMatch = /href\s*=\s*['"]([^'"]*)['"]/i.exec(item.text);
					if (hrefMatch) {
						addItem.href = hrefMatch[1];
					}
					if (addItem.label == "img" && !addItem.src);
					else {
						showTexts.push(addItem);
					}
				}
				if (viewText.replace(/^\s*|\s*$/, "")) {
					var showText = {
						label: "text",
						index: minIndex,
						text: viewText,
						style: item.style
					};
					if (item.label == "a") {
						showText.label = "text_a";
						showText.href = showTexts.length
							? showTexts[showTexts.length - 1].href
							: "";
					}
					showTexts.push(showText);
				}
			});
			// console.log(JSON.stringify(nowIndexs));
			// console.log(JSON.stringify(viewIndexs));
			// console.log(JSON.stringify(showTexts));
			this.data.listData = showTexts;
		};
		ZRichText.prototype.openImages = function(e, _item, _index) {
			stopBubble(e);
			var imgs = this.data.listData.filter(function(item, index) {
				return item.label == "img";
			});
			openWin_imgPreviewer({
				index: getItemForKey(_item.index, imgs, "index")._i,
				imgs: imgs.map(function(obj) {
					return obj.src;
				})
			});
		};
		ZRichText.prototype.openHrefs = function(e, _item, _index) {
			stopBubble(e);
			if (!_item.href) {
				return;
			}
			if (platform() == "web") {
				window.open(_item.href);
				return;
			}
			openWin_url({url: _item.href});
		};
		ZRichText.prototype.getStyle = function(_text, _item) {
			var match = new RegExp('[;"]s*' + _item + "s*:s*([^;]+);").exec(_text);
			if (match) {
				var matchText = match[1].replace(/pt/g, "px");
				return matchText;
			}
			return "";
		};
		ZRichText.prototype.nTouchmove = function() {
			touchmove();
		};
		ZRichText.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					style: "" + (this.props.style || ""),
					class: "" + (this.props.class || "")
				},
				apivm.h(
					"view",
					null,
					this.data.appDetailsStyle != "edit" &&
						this.data.listData.map(function(item, index) {
							return [
								apivm.h(
									"view",
									null,
									item.label == "text" &&
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(this$1.props.size || 0) +
													"line-height:" +
													(G.appFontSize + (this$1.props.size || 0)) * 1.8 +
													"px;" +
													(this$1.props.detail &&
													((platform() == "mp" && item.text.indexOf("　") != 0) ||
														(platform() == "web" &&
															item.text.indexOf(" ") != 0 &&
															item.text.indexOf("　") != 0))
														? "text-indent:2em;"
														: ""),
												class: "richText"
											},
											this$1.props.detail &&
												item.text.indexOf(" ") != 0 &&
												item.text.indexOf("　") != 0
												? api.systemType == "android"
													? "					"
													: api.systemType == "ios"
													? "	 "
													: ""
												: "",
											item.text
										)
								),
								apivm.h(
									"view",
									null,
									item.label == "text_a" &&
										apivm.h(
											"view",
											{
												onClick: function(e) {
													return this$1.openHrefs(e, item, index);
												}
											},
											apivm.h(
												"text",
												{
													style: loadConfiguration(this$1.props.size || 0) + "color: blue;",
													class: "richText"
												},
												item.text
											)
										)
								),
								apivm.h(
									"view",
									null,
									item.label == "br" &&
										apivm.h("view", {
											style:
												"height:" + (G.appFontSize - 6 + (this$1.props.size || 0)) + "px;"
										})
								),
								apivm.h(
									"view",
									null,
									item.label == "img" &&
										apivm.h(
											"view",
											{
												class: "richImgBox",
												onClick: function(e) {
													return this$1.openImages(e, item, index);
												}
											},
											apivm.h("image", {
												class: "richImg",
												style: item.style || "",
												mode: "widthFix",
												thumbnail: "false",
												src: item.src
											})
										)
								),
								apivm.h(
									"view",
									null,
									(item.label == "video" || item.label == "source") &&
										apivm.h(
											"view",
											{class: "richImgBox"},
											item.src && apivm.h("z-video", {src: item.src})
										)
								),
								apivm.h(
									"view",
									null,
									item.label == "table" &&
										apivm.h(
											"scroll-view",
											{"scroll-x": true, "scroll-y": false},
											apivm.h(
												"view",
												{
													onTouchStart: this$1.nTouchmove,
													onTouchMove: this$1.nTouchmove,
													onTouchEnd: this$1.nTouchmove
												},
												(item.data || []).map(function(nItem, nIndex) {
													return apivm.h(
														"view",
														{
															class: "richTable_item",
															style: "margin-top:" + (nIndex ? -1 : 0) + "px;"
														},
														(nItem.data || []).map(function(uItem, uIndex) {
															return apivm.h(
																"view",
																{
																	class: "richTable_item_td",
																	style:
																		uItem.style +
																		"width:" +
																		item.widths[uIndex] +
																		";margin-left:" +
																		(uIndex ? -1 : 0) +
																		"px;"
																},
																apivm.h(
																	"text",
																	{
																		style:
																			loadConfiguration(this$1.props.size || 0) +
																			"text-align: center;",
																		class: "richText"
																	},
																	uItem.text
																)
															);
														})
													);
												})
											)
										)
								)
							];
						})
				),
				apivm.h(
					"view",
					null,
					this.data.appDetailsStyle == "edit" &&
						apivm.h("rich-text", {nodes: this.data.showText})
				),
				apivm.h(
					"view",
					null,
					this.data.hasExpand &&
						apivm.h(
							"view",
							{
								style:
									"padding: 7px 0;flex-direction:row; align-items: center;justify-content: flex-start;",
								onClick: this.expandShow
							},
							apivm.h(
								"text",
								{style: loadConfiguration((this.props.size || 0) - 2) + "color:#999"},
								this.data.isExpand ? "收起" : "展开"
							),
							apivm.h("a-iconfont", {
								name: "xiangxiagengduo",
								style:
									"margin-left:5px;transform: rotate(" +
									(this.data.isExpand ? "180" : "0") +
									"deg);",
								color: "#999",
								size: G.appFontSize - 3
							})
						)
				)
			);
		};

		return ZRichText;
	})(Component);
	ZRichText.css = {
		".richImgBox": {
			alignItems: "center",
			justifyContent: "center",
			margin: "10px 0"
		},
		".richImg": {maxWidth: "100% !important"},
		".richTable_item": {flexFlow: "row nowrap"},
		".richTable_item_td": {
			alignItems: "center",
			justifyContent: "center",
			flexShrink: "0",
			padding: "5px"
		},
		".richText": {wordWrap: "break-word", wordBreak: "break-all"}
	};
	apivm.define("z-rich-text", ZRichText);

	var ZInput = /*@__PURE__*/ (function(Component) {
		function ZInput(props) {
			Component.call(this, props);
			this.data = {
				inputId: this.props.id || "z_input" + getNum()
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.autoFocus) {
						this.props.dataMore.autoFocus = false;
						this.props.dataMore.inputId = this.data.inputId;
						setTimeout(function() {
							document.getElementById(this$1.data.inputId).focus();
						}, 500);
					}
					if (this.props.dataMore.inputId != this.data.inputId) {
						this.props.dataMore.inputId = this.data.inputId;
					}
				}
			};
		}

		if (Component) ZInput.__proto__ = Component;
		ZInput.prototype = Object.create(Component && Component.prototype);
		ZInput.prototype.constructor = ZInput;
		ZInput.prototype.inputConfirm = function(e) {
			document.getElementById(this.data.inputId).blur();
			this.fire("confirm", e.detail);
		};
		ZInput.prototype.inputIng = function(e) {
			var nValue = (e.detail || {}).value;
			if (!nValue) {
				//解决安卓上清空输入无效的问题
				if (!this.i) {
					this.props.dataMore.value = "";
					this.i = 1;
				} else {
					this.props.dataMore.value = " ".repeat(this.i++ % 2);
					this.props.dataMore.value = " ".repeat(this.i++ % 2);
				}
			} else {
				this.props.dataMore.value = nValue;
			}
			if (this.props.dataMore.number) {
				if (!this.props.dataMore.value) {
					return;
				}
				if (/[^-?\d+(\.\d+)?$]/.test(this.props.dataMore.value)) {
					this.props.dataMore.value = this.props.dataMore.value.replace(
						/[^-?\d+(\.\d+)?$]/g,
						""
					);
					toast("请输入数字！");
					return;
				}
			}
			if (this.props.dataMore.expression) {
				//有正则表达示
				this.props.dataMore.value = this.props.dataMore.value.replace(
					new RegExp(this.props.dataMore.expression, "g"),
					""
				);
			}
			this.fire("input", e.detail);
		};
		ZInput.prototype.inputBlur = function(e) {
			this.fire("blur", e.detail);
		};
		ZInput.prototype.inputFocus = function(e) {
			document.getElementById(this.data.inputId).focus();
			this.fire("focus", e.detail);
		};
		ZInput.prototype.clean = function() {
			var this$1 = this;

			this.inputIng({detail: {value: ""}});
			this.fire("clean");
			if (this.props.dataMore.cleanFocus) {
				setTimeout(function() {
					document.getElementById(this$1.data.inputId).focus();
				}, 150);
			}
		};
		ZInput.prototype.switchLook = function() {
			this.props.dataMore.isLook = !this.props.dataMore.isLook;
			this.props.dataMore.inputType = this.props.dataMore.isLook
				? "text"
				: "password";
		};
		ZInput.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "z_input_box " + (this.props.class || ""),
					style:
						"border-radius:" +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;background:" +
						(this.props.bg || "rgba(0,0,0,0.05)") +
						";justify-content: " +
						(this.props.justify || "flex-start") +
						";" +
						(this.props.style || "")
				},
				apivm.h(
					"view",
					null,
					this.props.children.length >= 1 && this.props.children[0]
				),
				apivm.h(
					"view",
					null,
					!this.props.dataMore.dotIcon &&
						!this.props.dotIcon &&
						apivm.h(
							"view",
							{onClick: this.inputConfirm, style: "padding: 5px;marign-right:5px;"},
							apivm.h("a-iconfont", {
								name: this.props.dataMore.icon || "sousuo",
								color: "#999",
								size: G.appFontSize + (this.props.dataMore.iconSize || 0)
							})
						)
				),
				this.props.type == 2
					? apivm.h(
							"text",
							{
								style:
									"line-height:" +
									(G.appFontSize + 14) +
									"px;color:#999;" +
									loadConfiguration()
							},
							this.props.dataMore.placeholder || this.props.placeholder
					  )
					: apivm.h("input", {
							id: this.data.inputId,
							style:
								loadConfiguration() +
								"height:" +
								(G.appFontSize + 14) +
								"px;" +
								(this.props.dataMore.inputStyle || ""),
							"placeholder-style": "color:#ccc;",
							class: "z_input_input flex_w",
							type: this.props.dataMore.inputType || "text",
							placeholder:
								this.props.dataMore.placeholder ||
								this.props.dataMore.hint ||
								this.props.placeholder ||
								"请输入" + (this.props.dataMore.title || ""),
							onInput: function(e) {
								if (typeof this$1 != "undefined") {
									this$1.props.dataMore.value = e.target.value;
								} else {
									this$1.data.this.props.dataMore.value = e.target.value;
								}
								this$1.inputIng(e);
							},
							maxlength: this.props.dataMore.maxlength || this.props.dataMore.max,
							disabled:
								(isParameters(this.props.disabled) ? this.props.disabled : false) ||
								isParameters(this.props.readonly)
									? this.props.readonly
									: false,
							"confirm-type":
								this.props.confirmType || this.props.dataMore.confirmType || "search",
							"keyboard-type": this.props.dataMore.keyboardType || "default",
							onConfirm: this.inputConfirm,
							onBlur: this.inputBlur,
							onFocus: this.inputFocus,
							value:
								typeof this == "undefined"
									? this.data.this.props.dataMore.value
									: this.props.dataMore.value
					  }),
				apivm.h(
					"view",
					null,
					this.props.dataMore.value &&
						!this.props.dataMore.dotCleanIcon &&
						apivm.h(
							"view",
							{onClick: this.clean, style: "padding: 5px;"},
							apivm.h("a-iconfont", {
								name: "qingkong",
								color: "#666",
								size: G.appFontSize
							})
						)
				),
				apivm.h(
					"view",
					null,
					isParameters(this.props.dataMore.isLook) &&
						this.props.dataMore.value &&
						apivm.h(
							"view",
							{
								onClick: function() {
									return this$1.switchLook();
								},
								style: "padding:5px 10px 5px 3px;"
							},
							apivm.h("a-iconfont", {
								name: this.props.dataMore.isLook ? "kejian" : "bukejian",
								color: "#919191",
								size: G.appFontSize + 4
							})
						)
				),
				apivm.h(
					"view",
					null,
					this.props.children.length >= 2 && this.props.children[1]
				)
			);
		};

		return ZInput;
	})(Component);
	ZInput.css = {
		".z_input_box": {
			width: "100%",
			flexDirection: "row",
			padding: "2px 5px 2px 8px",
			alignItems: "center"
		},
		".z_input_input": {
			background: "transparent",
			borderColor: "transparent",
			color: "#333",
			paddingRight: "5px"
		},
		".z_input_input::placeholder": {color: "#ccc"}
	};
	apivm.define("z-input", ZInput);

	var ZTextarea = /*@__PURE__*/ (function(Component) {
		function ZTextarea(props) {
			Component.call(this, props);
			this.data = {
				textareaId: this.props.id || "z_textarea" + getNum()
			};
		}

		if (Component) ZTextarea.__proto__ = Component;
		ZTextarea.prototype = Object.create(Component && Component.prototype);
		ZTextarea.prototype.constructor = ZTextarea;
		ZTextarea.prototype.installed = function() {
			var this$1 = this;

			this.props.dataMore.textareaId = this.data.textareaId;
			if (this.props.dataMore.autoFocus) {
				setTimeout(function() {
					document.getElementById(this$1.data.textareaId).focus();
				}, 500);
			}
		};
		ZTextarea.prototype.input = function(e) {
			var value = e.detail.value;
			if (this.props.dataMore.expression) {
				value = value.replace(new RegExp(this.props.dataMore.expression, "g"), "");
			}
			this.props.dataMore.value = value;
			this.fire("input", e.detail);
		};
		ZTextarea.prototype.blur = function(e) {
			this.fire("blur", e.detail);
		};
		ZTextarea.prototype.focus = function(e) {
			document.getElementById(this.data.textareaId).focus();
			this.fire("focus", e.detail);
		};
		ZTextarea.prototype.render = function() {
			return apivm.h("textarea", {
				id: this.data.textareaId,
				style:
					"" +
					loadConfiguration() +
					(api.systemType == "android" ? "min-" : "") +
					"height: " +
					(this.props.dataMore.height || "250") +
					"px;" +
					(this.props.style || ""),
				class: "z_textarea " + (this.props.class || ""),
				placeholder:
					this.props.dataMore.replyPlaceholder ||
					this.props.dataMore.placeholder ||
					this.props.dataMore.hint ||
					"请输入" + (this.props.dataMore.title || ""),
				"placeholder-style": "color:#ccc;",
				"auto-height": api.systemType == "android",
				value: this.props.dataMore.value,
				onInput: this.input,
				onBlur: this.blur,
				onFocus: this.focus,
				maxlength: this.props.dataMore.maxlength || this.props.dataMore.max
			});
		};

		return ZTextarea;
	})(Component);
	ZTextarea.css = {
		".z_textarea": {
			background: "transparent",
			borderColor: "transparent",
			color: "#333",
			padding: "0",
			width: "100%",
			fontFamily: "none"
		},
		".z_textarea::placeholder": {color: "#ccc"}
	};
	apivm.define("z-textarea", ZTextarea);

	var ZAlert = /*@__PURE__*/ (function(Component) {
		function ZAlert(props) {
			Component.call(this, props);
			this.data = {
				show: false,

				inputBox: {
					dotIcon: true,
					value: "",
					placeholder: "",
					autoFocus: true,
					cleanFocus: true,
					height: 150,
					confirmType: ""
				},

				inputBoxPass: {
					dotIcon: true,
					value: "",
					placeholder: "",
					inputType: "password",
					autoFocus: false,
					cleanFocus: true,
					height: 150,
					confirmType: ""
				},

				marginBottom: 0,
				timeout: 0
			};
			this.compute = {
				monitor: function() {
					var dm = this.props.dataMore;
					if (dm.show != this.data.show) {
						this.data.inputBox.autoFocus = true;
						this.data.show = dm.show;
						this.data.marginBottom = 0;
						if (this.data.show) {
							this.data.timeout = dm.timeout || 0;
							if (this.data.timeout > 0) {
								this.startTime();
							}
							if (
								this.props.dataMore.type == "input" ||
								this.props.dataMore.type == "textarea"
							) {
								this.data.inputBox.value = dm.content;
								this.data.inputBox.placeholder = dm.placeholder;
								this.data.inputBox.confirmType = dm.confirmType || "done";

								this.data.inputBoxPass.value = dm.content2;
								this.data.inputBoxPass.placeholder = dm.placeholder2;
								this.data.inputBoxPass.confirmType = dm.confirmType2 || "done";
							}
						}
					}
				}
			};
		}

		if (Component) ZAlert.__proto__ = Component;
		ZAlert.prototype = Object.create(Component && Component.prototype);
		ZAlert.prototype.constructor = ZAlert;
		ZAlert.prototype.closePage = function(_type) {
			this.props.dataMore.show = false;
			if (!_type) {
				G.alertPop.callback({buttonIndex: 2});
			}
		};
		ZAlert.prototype.closeStop = function(e) {
			stopBubble(e);
		};
		ZAlert.prototype.inputFocus = function(e) {
			if (platform() == "app" && api.systemType == "ios") {
				this.data.marginBottom = e.detail.height;
			}
		};
		ZAlert.prototype.inputBlur = function(e) {
			this.data.marginBottom = 0;
		};
		ZAlert.prototype.itemClick = function() {
			if (this.data.timeout > 0) {
				return;
			}
			if (
				this.props.dataMore.type == "input" ||
				this.props.dataMore.type == "textarea"
			) {
				this.props.dataMore.content = this.data.inputBox.value;
				this.props.dataMore.content2 = this.data.inputBoxPass.value;
			}
			this.props.dataMore.buttonIndex = 1;
			G.alertPop.callback(this.props.dataMore);
			this.closePage(1);
		};
		ZAlert.prototype.startTime = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.data.timeout--;
				if (this$1.data.timeout >= 0) {
					this$1.startTime();
				} else if (this$1.props.dataMore.autoClose) {
					this$1.itemClick();
				}
			}, 1000);
		};
		ZAlert.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box xy_center",
					onClick: this.closeStop,
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);z-index:1001;"
				},
				this.data.show && [
					apivm.h(
						"view",
						{
							class: "alert_warp",
							style: "margin-bottom:" + this.data.marginBottom + "px;",
							onClick: this.closeStop
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							null,
							(this.props.dataMore.title || this.props.dataMore.closeType == "2") &&
								apivm.h(
									"view",
									{style: "padding: 20px 20px 0;"},
									this.props.dataMore.title &&
										apivm.h(
											"text",
											{class: "alert_title", style: "" + loadConfiguration(4)},
											this.props.dataMore.title
										),
									apivm.h(
										"view",
										{
											style:
												"display:" +
												(this.props.dataMore.closeType == "2" ? "flex" : "none") +
												";position:absolute;right:0;top:0;"
										},
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.closePage();
												},
												style: "padding:20px 20px 10px 10px;"
											},
											apivm.h("a-iconfont", {
												name: "cuohao",
												color: "#666",
												size: G.appFontSize + 4
											})
										)
									)
								)
						),
						apivm.h(
							"scroll-view",
							{class: "alert_content_box", "scroll-y": true},
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "input" &&
									apivm.h("z-input", {
										id: "input",
										dataMore: this.data.inputBox,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.inputPassword &&
									apivm.h("z-input", {
										id: "password",
										style: "margin-top:15px;",
										dataMore: this.data.inputBoxPass,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "textarea" &&
									apivm.h("z-textarea", {
										class: "alert_textarea",
										dataMore: this.data.inputBox,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "richText" &&
									apivm.h("z-rich-text", {
										detail: true,
										nodes: this.props.dataMore.content
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "text" &&
									apivm.h(
										"text",
										{class: "alert_content", style: "" + loadConfiguration(1)},
										this.props.dataMore.content
									)
							)
						),
						apivm.h(
							"view",
							null,
							this.props.dataMore.closeType == "1" && [
								apivm.h(
									"view",
									{style: "width:100%;height:1px;padding:0 15px;flex-shrink: 0;"},
									apivm.h("view", {style: "height:1px;background: #F6F6F6;"})
								),
								apivm.h(
									"view",
									{class: "alert_btn_box"},
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.closePage();
											},
											class: "alert_btn_item",
											style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}
										},
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color:" +
													(this.props.dataMore.cancel.color == "appTheme"
														? G.appTheme
														: this.props.dataMore.cancel.color) +
													";"
											},
											this.props.dataMore.cancel.text
										)
									),
									apivm.h(
										"view",
										{style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}},
										apivm.h("view", {style: "width:1px;height:30px;background:#F6F6F6;"})
									),
									apivm.h(
										"view",
										{
											onClick: this.itemClick,
											class: "alert_btn_item",
											style: {display: this.props.dataMore.sure.show ? "flex" : "none"}
										},
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color:" +
													(this.props.dataMore.sure.color == "appTheme"
														? G.appTheme
														: this.props.dataMore.sure.color) +
													";opacity:" +
													(this.data.timeout > 0 ? "0.5" : "1") +
													";"
											},
											this.props.dataMore.sure.text,
											this.data.timeout > 0 ? "(" + this.data.timeout + ")" : ""
										)
									)
								)
							]
						),
						apivm.h(
							"view",
							null,
							this.props.dataMore.closeType != "1" &&
								this.props.dataMore.sure.show &&
								apivm.h(
									"view",
									{style: "padding-bottom:15px;", class: "alert_btn_box"},
									apivm.h(
										"z-button",
										{
											style: "width:210px;",
											disabled: this.data.timeout > 0,
											color:
												this.props.dataMore.sure.color == "appTheme"
													? G.appTheme
													: this.props.dataMore.sure.color,
											round: true,
											onClick: this.itemClick
										},
										this.props.dataMore.sure.text,
										this.data.timeout > 0 ? "(" + this.data.timeout + ")" : ""
									)
								)
						)
					),
					apivm.h(
						"view",
						null,
						this.props.dataMore.closeType == "3" &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.closePage();
									},
									style: "margin-top:15px;"
								},
								apivm.h("a-iconfont", {
									style: "transform: rotate(45deg);",
									name: "gengduo1",
									color: "#FFF",
									size: G.appFontSize + 26
								})
							)
					)
				]
			);
		};

		return ZAlert;
	})(Component);
	ZAlert.css = {
		".alert_warp": {background: "#FFF", borderRadius: "10px", width: "320px"},
		".alert_content_box": {
			margin: "20px 15px",
			maxHeight: "385px",
			width: "auto"
		},
		".alert_title": {color: "#333333", fontWeight: "bold", textAlign: "center"},
		".alert_content": {
			width: "100%",
			textAlign: "center",
			color: "#333333",
			wordWrap: "break-word"
		},
		".alert_btn_box": {
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_btn_item": {
			flex: "1",
			padding: "10px",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_textarea": {
			borderColor: "#ccc !important",
			borderRadius: "10px",
			padding: "8px",
			width: "100%"
		}
	};
	apivm.define("z-alert", ZAlert);

	var ZSwitch = /*@__PURE__*/ (function(Component) {
		function ZSwitch(props) {
			Component.call(this, props);
		}

		if (Component) ZSwitch.__proto__ = Component;
		ZSwitch.prototype = Object.create(Component && Component.prototype);
		ZSwitch.prototype.constructor = ZSwitch;
		ZSwitch.prototype.change = function(e) {
			this.props.dataMore.value = e.detail.value;
			this.fire("change", this.props.dataMore);
		};
		ZSwitch.prototype.render = function() {
			return apivm.h("switch", {
				id: true,
				checked: this.props.dataMore.value,
				disabled: this.props.disabled || false,
				color: this.props.color || G.appTheme,
				onChange: this.change
			});
		};

		return ZSwitch;
	})(Component);
	apivm.define("z-switch", ZSwitch);

	var ZButton = /*@__PURE__*/ (function(Component) {
		function ZButton(props) {
			Component.call(this, props);
		}

		if (Component) ZButton.__proto__ = Component;
		ZButton.prototype = Object.create(Component && Component.prototype);
		ZButton.prototype.constructor = ZButton;
		ZButton.prototype.componentsClick = function(e) {
			stopBubble(e);
			if (!this.props.disabled && !this.props.readonly) {
				this.fire("click", {});
			}
		};
		ZButton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					id: "" + (this.props.id || ""),
					class: "z_button " + (this.props.class || ""),
					style:
						"border-radius:" +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;border-color:" +
						(this.props.color || G.appTheme) +
						";background:" +
						(this.props.plain ? "#FFF" : this.props.color || G.appTheme) +
						";opacity:" +
						(this.props.disabled ? "0.5" : "1") +
						";" +
						(this.props.style || ""),
					onClick: function(e) {
						return this$1.componentsClick(e);
					}
				},
				this.props.icon &&
					apivm.h("a-iconfont", {
						name: this.props.icon,
						style: "margin-right:5px;",
						color: this.props.plain ? this.props.color || G.appTheme : "#FFF",
						size: G.appFontSize - 1 + (this.props.size || 0)
					}),
				apivm.h(
					"text",
					{
						style:
							"color:" +
							(this.props.plain ? this.props.color || G.appTheme : "#FFF") +
							";" +
							loadConfiguration(this.props.size || 0)
					},
					this.props.text || this.props.children
				)
			);
		};

		return ZButton;
	})(Component);
	ZButton.css = {
		".z_button": {
			padding: "7px 12px",
			borderWidth: "1px",
			borderStyle: "solid",
			boxSizing: "border-box",
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("z-button", ZButton);

	var ZEmpty = /*@__PURE__*/ (function(Component) {
		function ZEmpty(props) {
			Component.call(this, props);
		}

		if (Component) ZEmpty.__proto__ = Component;
		ZEmpty.prototype = Object.create(Component && Component.prototype);
		ZEmpty.prototype.constructor = ZEmpty;
		ZEmpty.prototype.getShowImg = function() {
			if (this.props.dataMore.type == 1 || this.props.dataMore.type == 2) {
				return showImg(
					shareAddress(1) + "image/icon_empty_" + this.props.dataMore.type + ".png"
				);
			}
			return showImg(this.props.dataMore.type);
		};
		ZEmpty.prototype.getShowText = function() {
			return (
				this.props.dataMore.text ||
				(this.props.dataMore.type == 1
					? "暂无数据"
					: this.props.dataMore.type == 2
					? "网络异常，请点击重试"
					: "")
			);
		};
		ZEmpty.prototype.refresh = function() {
			showProgress("刷新中");
			this.fire("refresh");
			setTimeout(function() {
				hideProgress();
			}, 2500);
		};
		ZEmpty.prototype.up = function() {
			if (this.props._this && isFunction(this.props._this.loadMore)) {
				this.props._this.loadMore({detail: {}});
			} else {
				this.fire("up", {});
			}
		};
		ZEmpty.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"view",
					null,
					this.props.dataMore.type &&
						this.props.dataMore.type != "load" &&
						apivm.h(
							"view",
							{
								id: "" + (this.props.id || ""),
								style: "margin:100px 0;" + (this.props.style || ""),
								class: "xy_center " + (this.props.class || "")
							},
							apivm.h("image", {
								style: "width:200px;height:200px;",
								src: this.getShowImg(),
								mode: "aspectFill"
							}),
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(this.props.size || 0) +
										"color:#666;padding:10px 20px;text-align: center;"
								},
								this.getShowText()
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == 2 &&
									apivm.h(
										"z-button",
										{
											style: "width:132px;margin-top:20px;",
											color: G.appTheme,
											round: true,
											onClick: this.refresh
										},
										"刷新"
									)
							),
							this.props.children
						)
				),
				apivm.h(
					"view",
					null,
					!this.props.dataMore.type &&
						this.props.dataMore.text &&
						apivm.h(
							"view",
							{class: "xy_center", onClick: this.up},
							apivm.h(
								"text",
								{style: loadConfiguration(-4) + "color:#bbb;padding:15px;"},
								this.props.dataMore.text
							)
						)
				)
			);
		};

		return ZEmpty;
	})(Component);
	apivm.define("z-empty", ZEmpty);

	var ZSkeleton = /*@__PURE__*/ (function(Component) {
		function ZSkeleton(props) {
			Component.call(this, props);
		}

		if (Component) ZSkeleton.__proto__ = Component;
		ZSkeleton.prototype = Object.create(Component && Component.prototype);
		ZSkeleton.prototype.constructor = ZSkeleton;
		ZSkeleton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{style: "width:100%;"},
				(Array.isArray(dataForNum(this.props.cell || 3))
					? dataForNum(this.props.cell || 3)
					: Object.values(dataForNum(this.props.cell || 3))
				).map(function(item$1, index$1, list) {
					return apivm.h(
						"view",
						{class: "z_skeleton"},
						apivm.h(
							"view",
							null,
							(Array.isArray(dataForNum(this$1.props.row || 4))
								? dataForNum(this$1.props.row || 4)
								: Object.values(dataForNum(this$1.props.row || 4))
							).map(function(item$1, index$1, list) {
								return apivm.h("view", {
									class: "z_skeleton_row",
									style:
										"width:" +
										(!index$1 ? 40 : index$1 == list.length - 1 ? 60 : 100) +
										"%;"
								});
							})
						)
					);
				})
			);
		};

		return ZSkeleton;
	})(Component);
	ZSkeleton.css = {
		".z_skeleton": {width: "100%", padding: "10px 16px"},
		".z_skeleton_row": {
			marginTop: "12px",
			height: "16px",
			backgroundColor: "#f2f3f5"
		}
	};
	apivm.define("z-skeleton", ZSkeleton);

	var YScrollView = /*@__PURE__*/ (function(Component) {
		function YScrollView(props) {
			Component.call(this, props);
			this.data = {
				mId: "",
				scroll_view: "", //滚动到id
				scroll_animation: true, //滚动动画
				refreshEd: false
			};
			this.compute = {
				monitor: function() {
					if (!this.data.mId) {
						var nowId = this.props.id || "scroll_view" + getNum();
						this.data.mId = !document.getElementById(nowId)
							? nowId
							: "scroll_view" + getNum();
						(this.props.dataMore || {}).id = this.data.mId;
					}
					var dataMore = this.props.dataMore || {};
					if (this.data.scroll_view != dataMore.scroll_view) {
						this.data.scroll_animation = isParameters(dataMore.scroll_animation)
							? dataMore.scroll_animation
							: true;
						this.data.scroll_view = dataMore.scroll_view || "";
						if (this.data.scroll_view) {
							this.scrollTo(this.data.scroll_view);
						}
					}
				}
			};
		}

		if (Component) YScrollView.__proto__ = Component;
		YScrollView.prototype = Object.create(Component && Component.prototype);
		YScrollView.prototype.constructor = YScrollView;
		YScrollView.prototype.onrefresherrefresh = function(e) {
			var this$1 = this;

			if (this.props._this && isFunction(this.props._this.pageRefresh)) {
				this.props._this.pageRefresh({detail: {}});
			} else {
				this.fire("lower", {});
			}
			this.data.refreshEd = true;
			setTimeout(function() {
				this$1.data.refreshEd = false;
			}, 800);
		};
		YScrollView.prototype.onscrolltolower = function(e) {
			if (this.props._this && isFunction(this.props._this.loadMore)) {
				this.props._this.loadMore({detail: {}});
			} else {
				this.fire("up", {});
			}
		};
		YScrollView.prototype.onscroll = function(ref) {
			var detail = ref.detail;

			if (this.props._this && isFunction(this.props._this.pageScroll)) {
				this.props._this.pageScroll({detail: detail});
			} else {
				this.fire("scroll", detail);
			}
		};
		YScrollView.prototype.scrollTo = function(nowView) {
			var this$1 = this;

			var _animated = this.data.scroll_animation;
			if (document.getElementById(this.data.mId) && platform() != "mp") {
				var scrollTo =
					platform() == "app"
						? {view: nowView, animated: _animated}
						: this.props.dataMore.direction == "horizontal"
						? {
								left: this.getOffestValue(document.getElementById(nowView)).left,
								behavior: _animated ? "smooth" : "instant"
						  }
						: {
								top: this.getOffestValue(document.getElementById(nowView)).top,
								behavior: _animated ? "smooth" : "instant"
						  };
				document.getElementById(this.data.mId).scrollTo(scrollTo);
			}
			setTimeout(function() {
				this$1.props.dataMore.scroll_view = "";
			}, 500);
		};
		YScrollView.prototype.getOffestValue = function(elem) {
			var Far = null;
			var topValue = elem && elem.offsetTop;
			var leftValue = elem && elem.offsetLeft;
			var offsetFar = elem && elem.offsetParent;
			while (offsetFar) {
				if (offsetFar.id == this.data.mId) {
					break;
				}
				topValue += offsetFar.offsetTop;
				leftValue += offsetFar.offsetLeft;
				Far = offsetFar;
				offsetFar = offsetFar.offsetParent;
				if (offsetFar.id == this.data.mId) {
					break;
				}
			}
			return {top: topValue, left: leftValue, Far: Far};
		};
		YScrollView.prototype.render = function() {
			return apivm.h(
				"scroll-view",
				{
					s: this.monitor,
					id: "" + this.data.mId,
					style: "flex:1;" + (this.props.style || ""),
					class: "" + (this.props.class || ""),
					"refresher-background": "rgba(0,0,0,0)",
					"scroll-x": isParameters(this.props["scroll-x"])
						? this.props["scroll-x"]
						: false,
					"scroll-y": isParameters(this.props["scroll-y"])
						? this.props["scroll-y"]
						: true,
					bounces: isParameters(this.props["bounces"])
						? this.props["bounces"]
						: false,
					"scroll-into-view": platform() == "mp" ? this.data.scroll_view : null,
					"scroll-with-animation":
						platform() == "mp" ? this.data.scroll_animation : null,
					"refresher-enabled": isParameters(this.props["refresh"])
						? this.props["refresh"] &&
						  (platform() != "app" ? !this.props._this.props.dataMore : true)
						: false,
					"refresher-triggered": this.data.refreshEd,
					onScrolltolower: this.onscrolltolower,
					onRefresherrefresh: this.onrefresherrefresh,
					onScroll: this.onscroll
				},
				this.props.children
			);
		};

		return YScrollView;
	})(Component);
	apivm.define("y-scroll-view", YScrollView);

	var baoguo_hezi_o = "";
	var fuzhilianjiexian = "";
	var fuzhilianjiemian = "";
	var wenjian = "";
	var tianjia = "";
	var tianjiawenjian = "";
	var sousuowenjian = "";
	var wenjianshangchuan = "";
	var dalou = "";
	var wucan = "";
	var zengjia1 = "";
	var xinsui = "";
	var aixin = "";
	var jianshao2 = "";
	var yishoucang = "";
	var yuyinshibie = "";
	var shuruxiangce = "";
	var tupianshibie = "";
	var shuruwenjian = "";
	var shurupaizhao = "";
	var tongji4 = "";
	var pinglun1 = "";
	var gerentongxunlu = "";
	var fenxiang2 = "";
	var dianzan = "";
	var shoucang1 = "";
	var sousuo2 = "";
	var jiancexinbanben = "";
	var shezhi1 = "";
	var guanhuaimoshi = "";
	var xiugaimima = "";
	var anquantuichu = "";
	var dianhua = "";
	var shipintianchong = "";
	var xiangxiagengduo = "";
	var mubiao = "";
	var jianshao1 = "";
	var biaoqian = "";
	var duigou = "";
	var jinengbiaoqian = "";
	var jiantou_xiangyouliangci = "";
	var toupiao = "";
	var riqi = "";
	var gonggao = "";
	var tongji3 = "";
	var paihang = "";
	var mn_paiming_fill = "";
	var shaixuan = "";
	var envelope = "";
	var nvshangjia = "";
	var nan = "";
	var tongji1 = "";
	var tuceng = "";
	var tongji2 = "";
	var biaoqianlan_shouye = "";
	var shengyinjingyin = "";
	var shengyin = "";
	var shengyin1 = "";
	var tianxie = "";
	var erweima = "";
	var huanyuan = "";
	var lianjie = "";
	var changyonglogo28 = "";
	var pengyouquan = "";
	var zhixiangxia = "";
	var QQ = "";
	var quanxian = "";
	var baocun = "";
	var suoyouwenjian = "";
	var xiangxia1 = "";
	var xinzeng = "";
	var ziyuanxhdpi = "";
	var xiazai = "";
	var zhongmingming = "";
	var yunpan = "";
	var tongji = "";
	var kefu = "";
	var weibiaoti1 = "";
	var tongxunlu = "";
	var xinyongqia = "";
	var tuichu = "";
	var aixinxiantiao = "";
	var shezhi = "";
	var jianchaxinbanben = "";
	var yuyin = "";
	var shanchu = "";
	var remen = "";
	var tupian = "";
	var xiangji = "";
	var zan = "";
	var zan1 = "";
	var like = "";
	var unlike = "";
	var pinglun = "";
	var jushoucang = "";
	var jushoucanggift = "";
	var share = "";
	var shoucangfill = "";
	var fenxiang1 = "";
	var shoucang = "";
	var bofang = "";
	var daojishi = "";
	var cuohao = "";
	var duihao = "";
	var chakan = "";
	var shoujihaoma = "";
	var mima1 = "";
	var zhanghao1 = "";
	var yanzhengma1 = "";
	var kejian = "";
	var bukejian = "";
	var gengduo1 = "";
	var gengduogongneng = "";
	var gengduo2 = "";
	var gengduo3 = "";
	var fanhui1 = "";
	var fanhui2 = "";
	var sousuo = "";
	var sousuo1 = "";
	var saoyisao1 = "";
	var xiangxia = "";
	var xiangzuo = "";
	var fangxingweixuanzhong = "";
	var fangxingxuanzhongfill = "";
	var fangxingxuanzhong = "";
	var yuanxingweixuanzhong = "";
	var yuanxingxuanzhong = "";
	var yuanxingxuanzhongfill = "";
	var danxuan_xuanzhong = "";
	var danxuan_weixuanzhong = "";
	var gengduo = "";
	var fenxiang = "";
	var zhuanfa00 = "";
	var dingwei = "";
	var dingwei1 = "";
	var jianshao = "";
	var zengjia = "";
	var qingkong = "";
	var mima = "";
	var zhanghao = "";
	var yanzhengma = "";
	var icons = {
		baoguo_hezi_o: baoguo_hezi_o,
		fuzhilianjiexian: fuzhilianjiexian,
		fuzhilianjiemian: fuzhilianjiemian,
		wenjian: wenjian,
		tianjia: tianjia,
		tianjiawenjian: tianjiawenjian,
		sousuowenjian: sousuowenjian,
		wenjianshangchuan: wenjianshangchuan,
		"zuanshi-L": "",
		dalou: dalou,
		wucan: wucan,
		zengjia1: zengjia1,
		xinsui: xinsui,
		aixin: aixin,
		jianshao2: jianshao2,
		yishoucang: yishoucang,
		yuyinshibie: yuyinshibie,
		shuruxiangce: shuruxiangce,
		tupianshibie: tupianshibie,
		shuruwenjian: shuruwenjian,
		shurupaizhao: shurupaizhao,
		tongji4: tongji4,
		pinglun1: pinglun1,
		gerentongxunlu: gerentongxunlu,
		fenxiang2: fenxiang2,
		dianzan: dianzan,
		shoucang1: shoucang1,
		sousuo2: sousuo2,
		jiancexinbanben: jiancexinbanben,
		shezhi1: shezhi1,
		guanhuaimoshi: guanhuaimoshi,
		xiugaimima: xiugaimima,
		anquantuichu: anquantuichu,
		dianhua: dianhua,
		shipintianchong: shipintianchong,
		"31dianhua": "",
		xiangxiagengduo: xiangxiagengduo,
		mubiao: mubiao,
		jianshao1: jianshao1,
		biaoqian: biaoqian,
		"gantanhao-xianxingyuankuang": "",
		duigou: duigou,
		jinengbiaoqian: jinengbiaoqian,
		jiantou_xiangyouliangci: jiantou_xiangyouliangci,
		toupiao: toupiao,
		riqi: riqi,
		gonggao: gonggao,
		tongji3: tongji3,
		paihang: paihang,
		mn_paiming_fill: mn_paiming_fill,
		"line-084": "",
		"line-085": "",
		shaixuan: shaixuan,
		envelope: envelope,
		"envelope-open": "",
		"a-4_huaban1": "",
		nvshangjia: nvshangjia,
		nan: nan,
		tongji1: tongji1,
		"weibiaoti--": "",
		tuceng: tuceng,
		tongji2: tongji2,
		biaoqianlan_shouye: biaoqianlan_shouye,
		shengyinjingyin: shengyinjingyin,
		shengyin: shengyin,
		shengyin1: shengyin1,
		tianxie: tianxie,
		"file-download-fill": "",
		"file-damage-fill": "",
		"file-excel-fill": "",
		"file-copy-fill": "",
		"file-edit-fill": "",
		"file-music-fill": "",
		"file-gif-fill": "",
		"file-history-fill": "",
		"file-lock-fill": "",
		"file-info-fill": "",
		"file-pdf-fill": "",
		"file-text-fill": "",
		"file-ppt-fill": "",
		"file-upload-fill": "",
		"file-unknow-fill": "",
		"file-word-fill": "",
		"file-search-fill": "",
		"file-transfer-fill": "",
		"file-zip-fill": "",
		"folder-2-fill": "",
		"file-warning-fill": "",
		"folder-add-fill": "",
		erweima: erweima,
		"file-reduce-fill": "",
		huanyuan: huanyuan,
		lianjie: lianjie,
		changyonglogo28: changyonglogo28,
		pengyouquan: pengyouquan,
		zhixiangxia: zhixiangxia,
		QQ: QQ,
		quanxian: quanxian,
		baocun: baocun,
		suoyouwenjian: suoyouwenjian,
		xiangxia1: xiangxia1,
		"wj-gxwj": "",
		xinzeng: xinzeng,
		ziyuanxhdpi: ziyuanxhdpi,
		xiazai: xiazai,
		zhongmingming: zhongmingming,
		yunpan: yunpan,
		"file-code-fill": "",
		"file-add-fill": "",
		tongji: tongji,
		kefu: kefu,
		weibiaoti1: weibiaoti1,
		tongxunlu: tongxunlu,
		xinyongqia: xinyongqia,
		tuichu: tuichu,
		aixinxiantiao: aixinxiantiao,
		shezhi: shezhi,
		jianchaxinbanben: jianchaxinbanben,
		yuyin: yuyin,
		shanchu: shanchu,
		remen: remen,
		tupian: tupian,
		xiangji: xiangji,
		zan: zan,
		zan1: zan1,
		like: like,
		unlike: unlike,
		"like-fill": "",
		"unlike-fill": "",
		pinglun: pinglun,
		jushoucang: jushoucang,
		jushoucanggift: jushoucanggift,
		share: share,
		shoucangfill: shoucangfill,
		fenxiang1: fenxiang1,
		shoucang: shoucang,
		bofang: bofang,
		"bell-off": "",
		daojishi: daojishi,
		cuohao: cuohao,
		duihao: duihao,
		chakan: chakan,
		shoujihaoma: shoujihaoma,
		mima1: mima1,
		zhanghao1: zhanghao1,
		yanzhengma1: yanzhengma1,
		kejian: kejian,
		bukejian: bukejian,
		gengduo1: gengduo1,
		gengduogongneng: gengduogongneng,
		gengduo2: gengduo2,
		gengduo3: gengduo3,
		"a-14Bshanchu": "",
		fanhui1: fanhui1,
		fanhui2: fanhui2,
		sousuo: sousuo,
		sousuo1: sousuo1,
		saoyisao1: saoyisao1,
		xiangxia: xiangxia,
		xiangzuo: xiangzuo,
		fangxingweixuanzhong: fangxingweixuanzhong,
		fangxingxuanzhongfill: fangxingxuanzhongfill,
		fangxingxuanzhong: fangxingxuanzhong,
		yuanxingweixuanzhong: yuanxingweixuanzhong,
		yuanxingxuanzhong: yuanxingxuanzhong,
		yuanxingxuanzhongfill: yuanxingxuanzhongfill,
		danxuan_xuanzhong: danxuan_xuanzhong,
		danxuan_weixuanzhong: danxuan_weixuanzhong,
		gengduo: gengduo,
		fenxiang: fenxiang,
		zhuanfa00: zhuanfa00,
		dingwei: dingwei,
		dingwei1: dingwei1,
		jianshao: jianshao,
		zengjia: zengjia,
		qingkong: qingkong,
		mima: mima,
		zhanghao: zhanghao,
		yanzhengma: yanzhengma
	};

	var AMpcss = /*@__PURE__*/ (function(Component) {
		function AMpcss(props) {
			Component.call(this, props);
		}

		if (Component) AMpcss.__proto__ = Component;
		AMpcss.prototype = Object.create(Component && Component.prototype);
		AMpcss.prototype.constructor = AMpcss;
		AMpcss.prototype.render = function() {
			return;
		};

		return AMpcss;
	})(Component);
	AMpcss.css = {
		"@font-face": {
			fontFamily: '"iconfont_mp"',
			src:
				"url('https://at.alicdn.com/t/c/font_3560231_mff0m4knr.ttf') format('truetype')"
		}
	};

	apivm.define("a-mpcss", AMpcss);

	var AIconfont = /*@__PURE__*/ (function(Component) {
		function AIconfont(props) {
			Component.call(this, props);
		}

		if (Component) AIconfont.__proto__ = Component;
		AIconfont.prototype = Object.create(Component && Component.prototype);
		AIconfont.prototype.constructor = AIconfont;
		AIconfont.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"text",
					{
						style:
							"font-size:" +
							(this.props.size || 16) +
							"px;color:" +
							(this.props.color || "#999") +
							";\n\t\tfont-family: " +
							(api.platform == "mp" ? "iconfont_mp" : "iconfont;") +
							";\n\t\t" +
							(this.props.style || "") +
							";font-weight:400;",
						class: "" + (this.props.class || "")
					},
					icons[this.props.name + ""]
				),
				api.platform == "mp" && apivm.h("a-mpcss", null)
			);
		};

		return AIconfont;
	})(Component);
	AIconfont.css = {
		"@font-face": {
			fontFamily: '"iconfont"',
			src:
				"url('../../components/act/a-iconfont/fonts/iconfont.ttf') format('truetype')"
		}
	};
	apivm.define("a-iconfont", AIconfont);

	var YBasePage = /*@__PURE__*/ (function(Component) {
		function YBasePage(props) {
			Component.call(this, props);
			this.data = {
				show: false, //为组件时显示隐藏
				initFrist: true, //首次初始化
				title: "" //标题栏
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					if (!this.props.dataMore) {
						if (this.headTitle != G.headTitle) {
							this.headTitle = G.headTitle;
							this.setHeader();
						}
						if (this.headTheme != (G.showHeadTheme || G.headTheme)) {
							this.headTheme = G.showHeadTheme || G.headTheme;
							this.setHeadTheme();
						}
					} else {
						if (this.props.dataMore.show != this.data.show) {
							this.data.show = this.props.dataMore.show;
							videoPlayRemoves();
							if (this.data.show) {
								if (this.data.initFrist) {
									setTimeout(function() {
										this$1.baseInit();
									}, 110);
								} else {
									this.pageRefresh();
								}
							} else {
								if (this.props._this && isFunction(this.props._this.baseClose)) {
									this.props._this.baseClose({detail: {}});
								} else {
									this.fire("baseClose");
								}
							}
						}
					}
					if (
						(!this.props.dataMore || this.data.show) &&
						G.onShowNum != this.onShowNum
					) {
						this.onShowNum = G.onShowNum;
						if (this.isShow && this.onShowNum > 0) {
							this.pageRefresh();
							if (!this.props.dataMore) {
								console.log(
									(api.winName || "") +
										"第" +
										G.onShowNum +
										"次返回：" +
										JSON.stringify(this.props._this.data.pageParam)
								);
							}
						}
						this.isShow = true;
					}
				},
				detail: function() {}
			};
		}

		if (Component) YBasePage.__proto__ = Component;
		YBasePage.prototype = Object.create(Component && Component.prototype);
		YBasePage.prototype.constructor = YBasePage;
		YBasePage.prototype.installed = function() {
			var this$1 = this;
			var that = this.props._this;
			that.data.pageParam = pageParam(that);
			that.data.pageType = that.data.pageParam.pageType || "page";
			clearTimeout(that.data.oneTask);
			that.data.oneTask = setTimeout(function() {
				console.log("当前页面参数：" + JSON.stringify(that.data.pageParam));
				G.chatInfos = [];
				G.chatInfoTask = [];
				setTimeout(function() {
					if (!this$1.props.dataMore) {
						if (!G.headTitle) {
							G.headTitle = that.data.pageParam.title || that.data.dTitle || "";
						}
					} else {
						this$1.data.title = that.data.pageParam.title || that.data.dTitle || "";
					}
				}, 400);
				if (!this$1.props.dataMore) {
					G._this = that;
					if (that.data.pageParam.token) {
						setPrefs("sys_token", decodeURIComponent(that.data.pageParam.token));
						if (!getPrefs("sys_Mobile")) {
							getLoginInfo({header: {"u-login-areaId": ""}}, function(ret, err) {});
						}
					}
					if (that.data.pageParam.areaId) {
						setPrefs("sys_aresId", that.data.pageParam.areaId);
					}
					addEventListener("sys_refresh", function(ret) {
						this$1.initConfiguration();
					});
					var traverseList = function(_obj, _option) {
						for (var key in _obj) {
							var item = _obj[key];
							if (
								isObject(item) &&
								!isArray(item) &&
								key.endsWith("Pop") &&
								isParameters(item.show) &&
								item.show
							) {
								if (_option) {
									item.show = false;
								}
								return false;
							}
						}
						return true;
					};
					addEventListener("keyback", function(ret, err) {
						if (G.alertPop.show) {
							if (G.alertPop.cancel.show) {
								G.alertPop.show = false;
							}
							return;
						}
						if (!traverseList(G, true) || !traverseList(that.data, true)) {
							return;
						}
						this$1.close();
					});
					addEventListener("swiperight", function(ret) {
						if (G.nTouchmove) {
							return;
						}
						if (!traverseList(G, false) || !traverseList(that.data, false)) {
							return;
						}
						this$1.close(1);
					});
					this$1.initConfiguration();
					this$1.baseInit();
				}
			}, 50);
		};
		YBasePage.prototype.baseInit = function() {
			var this$1 = this;

			setTimeout(
				function() {
					var detail = {first: this$1.data.initFrist};
					if (this$1.props._this && isFunction(this$1.props._this.baseInit)) {
						this$1.props._this.baseInit({detail: detail});
					} else {
						this$1.fire("init", detail);
					}
					this$1.data.initFrist = false;
				},
				!this.props.dataMore ? 300 : 0
			);
		};
		YBasePage.prototype.close = function(_type) {
			if (this.props._this && isFunction(this.props._this.close)) {
				this.props._this.close({detail: {type: _type}});
			} else {
				this.fire("close", {type: _type});
			}
		};
		YBasePage.prototype.cTitle = function() {
			this.fire("cTitle");
		};
		YBasePage.prototype.pageRefresh = function(_frist) {
			if (!this.props.dataMore) {
				this.setHeadTheme();
			}
			if (!_frist) {
				if (this.props._this && isFunction(this.props._this.pageRefresh)) {
					this.props._this.pageRefresh({detail: {back: true}});
				} else {
					this.fire("pageRefresh", {back: true});
				}
			}
		};
		YBasePage.prototype.initConfiguration = function() {
			G.pageWidth =
				platform() == "web"
					? api.winWidth > api.winHeight
						? 600
						: api.winWidth
					: api.winWidth;
			G.showCaptcha = getPrefs("enableShortAuth") == "true";
			G.appName = getPrefs("sys_systemName") || "";
			G.terminal = getPrefs("terminal") || "";
			G.appFont = getPrefs("appFont") || "heitiSimplified";
			G.appFontSize = Number(getPrefs("appFontSize") || "16");
			G.sysSign = sysSign();
			G.appTheme =
				this.props._this.data.pageParam.appTheme ||
				getPrefs("appTheme" + G.sysSign) ||
				(G.sysSign == "rd" ? "#C61414" : "#3088FE");
			var headTheme =
				this.props._this.data.headTheme ||
				this.props._this.data.pageParam.headTheme ||
				getPrefs("headTheme") ||
				"transparent";
			G.headTheme = headTheme == "appTheme" ? G.appTheme : headTheme;
			G.careMode = parseInt(G.appFontSize) > 16;
			G.systemtTypeIsPlatform = getPrefs("sys_systemType") == "platform"; //系统类型是否是平台版
			G.loginInfo = getPrefs("sys_systemLoginContact") || "";
			if (platform() == "app") {
				G.isAppReview =
					JSON.parse(getPrefs("sys_appReviewVersion") || "{}")[api.systemType] ==
					api.appVersion;
			}
			G.v = getPrefs("sys_appVersion") || "";

			G.uId = getPrefs("sys_Id") || "";
			G.userId = getPrefs("sys_UserID") || "";
			G.userName = getPrefs("sys_UserName") || "";
			G.userImg = getPrefs("sys_AppPhoto") || "";
			G.areaId = getPrefs("sys_aresId") || "";
			G.specialRoleKeys = JSON.parse(getPrefs("sys_SpecialRoleKeys") || "[]");
			G.isAdmin =
				G.userId == "1" ||
				getItemForKey("dc_admin", G.specialRoleKeys) ||
				getItemForKey("admin", G.specialRoleKeys);
			G.grayscale = getPrefs("sys_grayscale") || "";
			var watermark = getPrefs("sys_watermark") || "";
			if (platform() == "app") {
				api.screenLayerFilter({
					region: "window",
					sat: G.grayscale == "true" ? 0 : 1
				});
			}
			if (watermark && watermark != "false") {
				var t = watermark
					.replace("user", G.userName)
					.replace("phone", getPrefs("sys_Mobile") || "")
					.replace("true", G.appName);
				if (t) {
					G.watermark =
						tomcatAddress() +
						"utils2/watermark?s=" +
						G.appFontSize +
						"&t=" +
						t +
						"&w=" +
						G.pageWidth +
						"&h=" +
						api.winHeight;
				}
			} else {
				G.watermark = false;
			}

			this.pageRefresh(true);
			if (platform() == "web") {
				var fontStyleId = "fontStyle";
				if (document.getElementById(fontStyleId)) {
					//存在的时候先删除
					document
						.getElementById(fontStyleId)
						.parentNode.removeChild(document.getElementById(fontStyleId));
				}
				var fontStyle = document.createElement("style");
				fontStyle.id = fontStyleId;
				switch (G.appFont) {
					case "shusongSimplified":
						fontStyle.innerText =
							"@font-face{font-family: shusongSimplified;src: url('../../res/fz_shusong_simplified.ttf')}";
						break;
					case "kaitiSimplified":
						fontStyle.innerText =
							"@font-face{font-family: kaitiSimplified;src: url('../../res/fz_kaiti_simplified.ttf')}";
						break;
					case "heitiTraditional":
						fontStyle.innerText =
							"@font-face{font-family: heitiTraditional;src: url('../../res/fz_heiti_traditional.ttf')}";
						break;
				}

				document.getElementsByTagName("head")[0].appendChild(fontStyle);
				this.fitWidth();
			}
		};
		YBasePage.prototype.fitWidth = function() {
			if (platform() == "web") {
				$("body").style.width = "100%";
				$("body").style.maxWidth = G.pageWidth + "px";
				$("body").style.minWidth = "300px";
				$("body").style.margin = "auto";
				$("body").style.position = "relative";
			}
		};
		YBasePage.prototype.isColorDarkOrLight = function(hexcolor) {
			try {
				var colors = colorRgba(hexcolor).match(/\d+\.?\d*/g);
				var red = colors[1],
					green = colors[2],
					blue = colors[3],
					brightness;
				brightness = (red * 299 + green * 587 + blue * 114) / 255000;
				return brightness >= 0.5 ? "light" : "dark";
			} catch (e) {
				return "";
			}
		};
		YBasePage.prototype.setHeadTheme = function() {
			var colorDarkOrLight = this.isColorDarkOrLight(
				this.headTheme == "transparent" ? "#FFF" : this.headTheme
			);
			G.headColor = colorDarkOrLight == "dark" ? "#FFF" : "#333";
			setStatusBarStyle({
				style: colorDarkOrLight == "light" ? "dark" : "light",
				color: "rgba(0,0,0,0)"
			});
		};
		YBasePage.prototype.setHeader = function() {
			if (this.props.dataMore) {
				return;
			}
			this.data.title = G.headTitle;
			if (platform() == "web") {
				if (window.parent) {
					window.parent.document.title = this.data.title;
				} else {
					document.title = this.data.title;
				}
			} else if (platform() == "mp") {
				wx.setNavigationBarTitle({title: this.data.title});
			}
		};
		YBasePage.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "page_box",
					class:
						"page_box page_bg " +
						(G.grayscale == "true" ? "filterGray" : "filterNone"),
					style: {display: !this.props.dataMore || this.data.show ? "flex" : "none"}
				},
				apivm.h(
					"view",
					{class: "watermark_box"},
					G.watermark &&
						apivm.h("image", {
							class: "xy_100",
							src: G.watermark,
							mode: "aspectFill",
							thumbnail: "false"
						})
				),
				G.appTheme && [
					apivm.h(
						"view",
						{class: "xy_100"},

						apivm.h(
							"view",
							{
								class: "flex_shrink",
								style: {
									display:
										!this.props.dataMore || !this.props.dataMore.closeH ? "flex" : "none"
								}
							},
							!this.props.closeH &&
								(this.props.titleBox ||
									this.props.back ||
									this.props.more ||
									showHeader()) &&
								apivm.h(
									"view",
									{
										style:
											"height:auto;padding-top:" +
											headerTop() +
											"px;background:" +
											(this.props._this.data.headTheme || G.headTheme)
									},
									apivm.h(
										"view",
										{class: "header_warp flex_row"},
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.cTitle();
												},
												class: "header_main xy_center",
												style: this.props.titleStyle || null
											},
											this.props.titleBox && this.props.children.length >= 3
												? [this.props.children[2]]
												: [
														apivm.h(
															"text",
															{
																style:
																	loadConfiguration(4) + "font-weight: 600;color:" + G.headColor
															},
															this.data.title
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "header_left_box"},
											this.props.back && this.props.children.length >= 1
												? [this.props.children[0]]
												: [
														apivm.h(
															"view",
															{
																onClick: function() {
																	return this$1.close();
																},
																class: "header_btn",
																style: {
																	display:
																		showHeader() && this.props._this.data.pageType == "page"
																			? "flex"
																			: "none"
																}
															},
															apivm.h("a-iconfont", {
																name: "fanhui1",
																color: G.headColor,
																size: G.appFontSize + 1
															})
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "header_right_box"},
											this.props.more &&
												this.props.children.length >= 2 &&
												this.props.children[1]
										)
									)
								)
						),
						this.props.children.length >= 4 && this.props.children[3]
					),
					this.props.children.length >= 5
						? this.props.children.filter(function(item, index) {
								return index >= 4;
						  })
						: null,
					!this.props.dataMore && [
						apivm.h("previewer-img", {dataMore: G.imgPreviewerPop}),
						apivm.h("areas", {dataMore: G.areasPop}),
						apivm.h("select-item", {dataMore: G.selectPop}),
						apivm.h("z-actionSheet", {dataMore: G.actionSheetPop}),
						apivm.h("z-alert", {dataMore: G.alertPop})
					]
				]
			);
		};

		return YBasePage;
	})(Component);
	YBasePage.css = {
		".avm-toast,.avm-confirm-mask": {zIndex: "999"},
		element: {width: "auto", height: "auto"},
		".flex_shrink,div": {flexShrink: "0", WebkitOverflowScrolling: "touch"},
		".flex_w": {flex: "1", width: "1px"},
		".flex_h": {flex: "1", height: "1px"},
		".flex_row": {flexDirection: "row", alignItems: "center"},
		".xy_center": {alignItems: "center", justifyContent: "center"},
		".xy_100": {width: "100%", height: "100%"},
		".page_box": {
			position: "absolute",
			zIndex: "999",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		},
		".page_bg": {background: "#FFF"},
		".header_warp": {height: "44px"},
		".header_main": {
			height: "100%",
			flex: "1",
			flexDirection: "row",
			padding: "0 44px"
		},
		".header_left_box": {
			position: "absolute",
			zIndex: "999",
			left: "0",
			width: "auto",
			height: "44px"
		},
		".header_right_box": {
			position: "absolute",
			zIndex: "999",
			right: "0",
			width: "auto",
			height: "44px"
		},
		".header_btn": {
			width: "auto",
			height: "100%",
			minWidth: "36px",
			padding: "0 12px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".header_btn:active": {background: "rgba(0,0,0,0.05)"},
		".filterNone": {filter: "none"},
		".filterGray": {filter: "grayscale(1)"},
		".watermark_box": {
			position: "absolute",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		}
	};
	apivm.define("y-base-page", YBasePage);

	var Root = /*@__PURE__*/ (function(Component) {
		function Root(props) {
			Component.call(this, props);
			this.data = {
				pageParam: {},
				pageType: "",
				dTitle: "聊天设置",
				MG: !this.props.dataMore ? G : null,

				user: {
					hasAdd: true,
					hasRemove: false,
					data: [
						// {id:"1",name:"张三",url:""}
					]
				},

				listData: []
			};
		}

		if (Component) Root.__proto__ = Component;
		Root.prototype = Object.create(Component && Component.prototype);
		Root.prototype.constructor = Root;
		Root.prototype.onShow = function() {
			G.onShowNum++;
		};
		Root.prototype.baseInit = function(ref) {
			var detail = ref.detail;

			this.conversationType = this.data.pageParam.conversationType || "GROUP";
			this.targetId =
				this.data.pageParam.targetId || "platform5zxbz1775070463433666561";
			if (this.conversationType == "GROUP") {
				this.data.listData = [
					{
						show: false,
						type: "book",
						key: "bookGroup",
						style: "border-top:10px solid #F4F4F4;",
						isOwer: false,
						data: []
					},
					{
						show: false,
						type: "text",
						key: "bookIntro",
						hint: "导读",
						more: true,
						style: ""
					},
					{
						show: true,
						type: "text",
						key: "editGroupName",
						hint: "群组名称",
						moreText: "暂未命名",
						moreTextSize: 8,
						more: true,
						style: "border-top:10px solid #F4F4F4;"
					},
					{
						show: true,
						type: "text",
						key: "qrGroup",
						hint: "群组二维码",
						moreIcon: "erweima",
						moreSize: 4,
						more: true
					},
					{
						show: true,
						type: "text",
						key: "editGroupNotice",
						hint: "群公告",
						moreText: "",
						moreTextSize: 9,
						more: true
					},
					{
						show: false,
						type: "text",
						key: "transferGroup",
						hint: "转让群主",
						more: true
					},
					{
						show: platform() == "app",
						type: "text",
						key: "searchGroup",
						hint: "查找聊天内容",
						value: false,
						more: true,
						style: "border-top:10px solid #F4F4F4;"
					},
					{
						show: true,
						type: "switch",
						key: "dotDisturb",
						hint: "消息免打扰",
						value: false,
						style: "border-top:10px solid #F4F4F4;"
					},
					{show: true, type: "switch", key: "toTop", hint: "置顶聊天", value: false},
					{
						show: true,
						type: "text",
						key: "clearMsg",
						hint: "清除聊天记录",
						value: false,
						more: true
					},
					{
						show: true,
						type: "button",
						key: "quitGroup",
						hint: "退出群组",
						hintStyle: "text-align: center;color:" + G.appTheme + ";",
						value: false,
						style: "border-top:10px solid #F4F4F4;"
					},
					{
						show: false,
						type: "button",
						key: "dissolveGroup",
						hint: "解散群组",
						hintStyle: "text-align: center;color:" + G.appTheme + ";",
						value: false,
						style: "border-top:10px solid #F4F4F4;"
					}
				];
			} else {
				this.data.listData = [
					{
						show: platform() == "app",
						type: "text",
						key: "searchGroup",
						hint: "查找聊天内容",
						value: false,
						more: true,
						style: "border-top:10px solid #F4F4F4;"
					},
					{
						show: true,
						type: "switch",
						key: "dotDisturb",
						hint: "消息免打扰",
						value: false,
						style: "border-top:10px solid #F4F4F4;"
					},
					{show: true, type: "switch", key: "toTop", hint: "置顶聊天", value: false},
					{
						show: true,
						type: "button",
						key: "clearMsg",
						hint: "清除聊天记录",
						hintStyle: "text-align: center;color:" + G.appTheme + ";",
						value: false,
						style: "border-top:10px solid #F4F4F4;"
					}
				];
			}
			this.pageRefresh();
		};
		Root.prototype.pageRefresh = function() {
			this.getData(0);
		};
		Root.prototype.close = function() {
			if (this.props.dataMore) {
				this.props.dataMore.show = false;
			} else {
				closeWin();
			}
		};
		Root.prototype.getData = function(_type) {
			var this$1 = this;

			getBaseChatInfos(
				{
					targetId: this.targetId,
					conversationType: this.conversationType,
					force: 1
				},
				function(_item, _ret) {
					if (!_item) {
						toast("获取信息失败，请稍候重试");
					} else {
						this$1.oldInfo = _ret.data;
						if (this$1.conversationType == "GROUP") {
							this$1.showData = _item;
							getItemForKey("editGroupName", this$1.data.listData).moreText =
								this$1.oldInfo.groupName;
							getItemForKey("editGroupNotice", this$1.data.listData).moreText =
								this$1.oldInfo.callBoard;
							this$1.data.user.hasRemove = this$1.oldInfo.ownerUserId == G.userId;
							getItemForKey("transferGroup", this$1.data.listData).show =
								this$1.oldInfo.ownerUserId == G.userId;
							getItemForKey("dissolveGroup", this$1.data.listData).show =
								this$1.oldInfo.ownerUserId == G.userId;
							if (_item.businessCode == "book") {
								//读书群 关联的书籍
								ajaxProcess(
									{
										url: appUrl() + "syBook/findRelationBook?groupId=" + _item.id,
										param: setNewJSON({}),
										tag: "syBook/findRelationBook",
										name: "查询关联书籍"
									},
									function(ret, err) {
										var _data = ret ? ret.data || [] : [];
										var keyItem = getItemForKey("bookGroup", this$1.data.listData);
										keyItem.show = true;
										keyItem.data = _data;
										keyItem.groupId = _item.id;
										getItemForKey("bookIntro", this$1.data.listData).show = true;
									}
								);
							}
						} else {
							this$1.data.user.data = [_item];
						}
					}
				}
			);
			if (this.conversationType == "GROUP") {
				getGroupListUser(
					{
						param: {
							query: {
								chatGroupId: this.targetId.split(chatHeader())[1]
							}
						}
					},

					function(ret) {
						var data = ret ? ret.dealWith || [] : [];
						if (isArray(data) && data.length) {
							data.sort(function(a, b) {
								return b.isOwner - a.isOwner;
							});
							G.headTitle =
								(this$1.data.pageParam.title || this$1.data.dTitle) +
								"(" +
								data.length +
								")";
						}
						this$1.data.user.data = data;
						getItemForKey(
							"bookGroup",
							this$1.data.listData
						).isOwer = this$1.data.user.data.find(function(v) {
							return v.id == G.userId;
						}).isOwner
							? true
							: false; //群主支持修改
					}
				);
			}
			this.getDotDisturb();
			this.getTopConversation();
		};
		Root.prototype.openInfo = function(_item) {
			openWin_npcinfo({
				id: _item.id,
				reqType: "account",
				dotChat: this.conversationType == "GROUP"
			});
		};
		Root.prototype.openAdd = function() {
			if (this.conversationType == "GROUP") {
				openWin_chat_user({
					title: "群成员",
					id: this.oldInfo.id,
					optionType: "list",
					options: "add"
				});
			} else {
				var nowAddItem = JSON.parse(JSON.stringify(this.data.user.data[0]));
				nowAddItem.targetId = nowAddItem.id;
				nowAddItem.readonly = true;
				openWin_add({
					paramType: "createGroup",
					title: "创建群组",
					data: [nowAddItem]
				});
			}
		};
		Root.prototype.openRemove = function() {
			openWin_chat_user({
				title: "移出群成员",
				id: this.oldInfo.id,
				optionType: "removeUser"
			});
		};
		Root.prototype.openUserMore = function() {
			openWin_chat_user({
				title: "群成员",
				id: this.oldInfo.id,
				optionType: "list"
			});
		};
		Root.prototype.switchChange = function(ref) {
			var detail = ref.detail;

			var _item = detail;
			switch (_item.key) {
				case "dotDisturb":
					var notificationStatus =
						platform() == "app"
							? _item.value
								? "DO_NOT_DISTURB"
								: "NOTIFY"
							: _item.value
							? "1"
							: "-1";
					sendEvent({
						name: "rongCloud",
						extra: {
							type: "setConversationNotificationStatus",
							param: {
								conversationType: this.conversationType,
								targetId: this.targetId,
								notificationStatus: notificationStatus
							}
						}
					});
					break;
				case "toTop":
					sendEvent({
						name: "rongCloud",
						extra: {
							type: "setConversationToTop",
							param: {
								conversationType: this.conversationType,
								targetId: this.targetId,
								isTop: !_item.value
							}
						}
					});
					break;
			}
		};
		Root.prototype.getDotDisturb = function() {
			var this$1 = this;

			addEventListener(
				"rongCloud_getConversationNotificationStatus_" +
					this.conversationType +
					"_" +
					this.targetId,
				function(ret, err) {
					ret = ret.value.success;
					if (ret.status == "success") {
						getItemForKey("dotDisturb", this$1.data.listData).value =
							ret.result.code == 0;
					}
				}
			);
			sendEvent({
				name: "rongCloud",
				extra: {
					type: "getConversationNotificationStatus",
					param: {conversationType: this.conversationType, targetId: this.targetId}
				}
			});
		};
		Root.prototype.getTopConversation = function() {
			var this$1 = this;

			var monitor = "rongCloud_getTopConversationList_more";
			addEventListener(monitor, function(ret, err) {
				ret = ret.value.success;
				var data = ret ? ret.result || [] : [];
				var isTop = false;
				if (isArray(data) && data.length != 0) {
					data.forEach(function(_eItem) {
						//item index 原数组对象
						if (
							_eItem.conversationType == this$1.conversationType &&
							_eItem.targetId == this$1.targetId
						) {
							isTop = true;
						}
					});
				}
				getItemForKey("toTop", this$1.data.listData).value = isTop;
				removeEventListener(monitor);
			});
			sendEvent({
				name: "rongCloud",
				extra: {type: "getTopConversationList", monitor: monitor, param: {}}
			});
		};
		Root.prototype.openDetails = function(_item) {
			var this$1 = this;

			switch (_item.key) {
				case "editGroupName":
					if (!this.data.user.hasRemove) {
						alert({title: _item.hint, msg: this.oldInfo.groupName});
						return;
					}
					alert(
						{
							title: _item.hint,
							msg: this.oldInfo.groupName,
							placeholder: _item.hint,
							type: "input",
							buttons: ["确定", "取消"]
						},
						function(ret) {
							if (ret.buttonIndex == 1 && ret.content != this$1.oldInfo.groupName) {
								this$1.editAjax(2, ret.content);
							}
						}
					);
					break;
				case "editGroupNotice":
					if (!this.data.user.hasRemove) {
						alert({title: _item.hint, msg: this.oldInfo.callBoard, richText: true});
						return;
					}
					alert(
						{
							title: _item.hint,
							msg: this.oldInfo.callBoard,
							placeholder: _item.hint,
							type: "textarea",
							buttons: ["确定", "取消"]
						},
						function(ret) {
							if (ret.buttonIndex == 1 && ret.content != this$1.oldInfo.callBoard) {
								this$1.editAjax(3, ret.content);
							}
						}
					);
					break;
				case "clearMsg":
					alert(
						{title: "提示", msg: "确定清除聊天记录?", buttons: ["确定", "取消"]},
						function(ret) {
							if (ret.buttonIndex == "1") {
								sendEvent({
									name: "rongCloud",
									extra: {
										type: "clearMessages",
										param: {
											conversationType: this$1.conversationType,
											targetId: this$1.targetId
										}
									}
								});
								toast("清除成功");
							}
						}
					);
					break;
				case "quitGroup":
					if (this.data.user.hasRemove) {
						toast("退出群组前请先转让群主");
						return;
					}
					alert(
						{title: "提示", msg: "确定退出群组吗?", buttons: ["确定", "取消"]},
						function(ret) {
							if (ret.buttonIndex == "1") {
								showProgress("操作中");
								ajax(
									{u: appUrl() + "chatGroupMember/quit"},
									"chatGroupMember/quit",
									function(ret, err) {
										hideProgress();
										toast(ret ? ret.message || ret.data : NET_ERR);
										if (ret && ret.code == 200) {
											var cName = G.userName + " 已退出群聊";
											var cData = G.userName + "|OUI|" + G.userId + "|| 已退出群聊";
											sendEvent({
												name: "rongCloud",
												extra: {
													type: "sendCommandNotificationMessage",
													param: {
														conversationType: this$1.conversationType,
														targetId: this$1.targetId,
														name: cName,
														data: cData
													}
												}
											});
											setTimeout(function() {
												this$1.close();
											}, 500);
										}
									},
									"退出群",
									"post",
									{
										body: JSON.stringify({
											chatGroupId: this$1.oldInfo.id
										})
									}
								);
							}
						}
					);
					break;
				case "dissolveGroup":
					alert(
						{
							title: "提示",
							msg: "解散后群组将不可恢复, 是否继续?",
							buttons: ["确定", "取消"]
						},
						function(ret) {
							if (ret.buttonIndex == "1") {
								showProgress("操作中");
								ajax(
									{u: appUrl() + "chatGroup/dels"},
									"chatGroup/dels",
									function(ret, err) {
										hideProgress();
										toast(ret ? ret.message || ret.data : NET_ERR);
										if (ret && ret.code == 200) {
											setTimeout(function() {
												this$1.close();
											}, 500);
										}
									},
									"解散群",
									"post",
									{
										body: JSON.stringify({
											ids: [this$1.oldInfo.id]
										})
									}
								);
							}
						}
					);
					break;
				case "searchGroup":
					openWin_search({
						code: "searchMessages",
						conversationType: this.conversationType,
						targetId: this.targetId
					});
					break;
				case "qrGroup":
					this.showData.shareUrl =
						chatHeader() +
						"|groupQr|" +
						this.targetId.split(chatHeader())[1] +
						"|" +
						areaId();
					G.sharePosterPop = {
						show: true,
						shareImg: "",
						data: this.showData
					};

					break;
				case "transferGroup":
					openWin_chat_user({
						title: "转让群主",
						id: this.oldInfo.id,
						optionType: "transferGroup"
					});

				case "bookGroup":
					if (_item.isOwer) {
						var pageParam$1 = {
							groupId: _item.groupId
						};

						openWin("mo_book_all", "../mo_book_all/mo_book_all.stml", pageParam$1);
					}
					break;
				case "bookIntro":
					var pageParam = {
						bookIds: getItemForKey("bookGroup", this.data.listData)
							.data.map(function(v) {
								return v.id;
							})
							.join(","),
						pageKey: "4"
					};

					openWin(
						"mo_book_recommended_handle",
						"../mo_book_recommended_handle/mo_book_recommended_handle.stml",
						pageParam
					);
					break;
			}
		};
		Root.prototype.editAjax = function(_type, _more) {
			var this$1 = this;

			var cName = "",
				cData = "";
			if (_type == 2) {
				cName = G.userName + " 修改群名字为“" + _more + "”";
				cData = G.userName + "|OUI|" + G.userId + "|| 修改群名字为“" + _more + "”";
			}
			showProgress("操作中");
			ajax(
				{u: appUrl() + "chatGroup/edit"},
				"chatGroup/edit",
				function(ret, err) {
					hideProgress();
					toast(ret ? ret.message || ret.data : NET_ERR);
					if (ret && ret.code == 200) {
						if (cName) {
							sendEvent({
								name: "rongCloud",
								extra: {
									type: "sendCommandNotificationMessage",
									param: {
										conversationType: this$1.conversationType,
										targetId: this$1.targetId,
										name: cName,
										data: cData
									}
								}
							});
						}
						this$1.getData();
						if (_type == 3) {
							sendEvent({
								name: "rongCloud",
								extra: {
									type: "sendTextMessage",
									param: {
										conversationType: this$1.conversationType,
										targetId: this$1.targetId,
										text: "群公告：\n" + _more
									}
								}
							});
						}
					}
				},
				"修改群信息",
				"post",
				{
					body: JSON.stringify({
						form: {
							groupName: _type == 2 ? _more : this.oldInfo.groupName,
							id: this.oldInfo.id,
							callBoard: _type == 3 ? _more : this.oldInfo.callBoard
						},

						memberUserIds: this.oldInfo.memberUserIds,
						ownerUserId: this.oldInfo.ownerUserId
					})
				}
			);
		};
		Root.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"y-base-page",
				{_this: this, dataMore: this.props.dataMore},
				apivm.h("view", null),
				apivm.h("view", null),
				apivm.h("view", null),
				apivm.h(
					"y-scroll-view",
					{_this: this, refresh: true},
					apivm.h(
						"view",
						{class: "chatMore_userBox"},
						this.data.user.data.map(function(item, index) {
							return [
								index < (this$1.data.user.hasRemove ? 13 : 14) &&
									apivm.h(
										"view",
										{
											style: "display:flex;",
											class: "chatMore_userItem",
											onClick: function() {
												return this$1.openInfo(item);
											}
										},
										apivm.h(
											"view",
											{style: "" + loadConfigurationSize(29)},
											apivm.h("z-avatar", {src: showImg(item)}),
											item.isOwner == 1 &&
												apivm.h(
													"view",
													{
														style:
															"position: absolute; top: -5px; right: -13px;padding:0 4px;background:#F6631C;border-radius:2px;"
													},
													apivm.h(
														"text",
														{style: loadConfiguration(-6) + "color: #FFF;"},
														"群主"
													)
												)
										),
										apivm.h(
											"text",
											{class: "chatMore_userItemText", style: loadConfiguration(-2)},
											item.name
										)
									)
							];
						}),
						apivm.h(
							"view",
							{
								style: "display:" + (this.data.user.hasAdd ? "flex" : "none") + ";",
								class: "chatMore_userItem",
								onClick: function() {
									return this$1.openAdd();
								}
							},
							apivm.h(
								"view",
								{style: "" + loadConfigurationSize(29)},
								apivm.h("image", {
									class: "xy_100",
									src: shareAddress(1) + "image/icon_add.png"
								})
							)
						),
						apivm.h(
							"view",
							{
								style: "display:" + (this.data.user.hasRemove ? "flex" : "none") + ";",
								class: "chatMore_userItem",
								onClick: function() {
									return this$1.openRemove();
								}
							},
							apivm.h(
								"view",
								{style: "" + loadConfigurationSize(29)},
								apivm.h("image", {
									class: "xy_100",
									src: shareAddress(1) + "image/icon_remove.png"
								})
							)
						)
					),
					apivm.h(
						"view",
						{style: "padding:0 16px;"},
						this.data.user.data.length > (this.data.user.hasRemove ? 13 : 14) &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.openUserMore();
									},
									class: "flex_row",
									style:
										"border-top: 1px solid #F1F1F1;justify-content: center;padding:10px;"
								},
								apivm.h(
									"text",
									{style: loadConfiguration(1) + "color: #333;margin-right:4px;"},
									"查看更多群成员"
								),
								apivm.h("a-iconfont", {
									style: "transform: rotate(-90deg);",
									name: "xiangxia1",
									color: "#333",
									size: G.appFontSize
								})
							)
					),
					apivm.h(
						"view",
						null,
						this.data.listData.map(function(nItem, nIndex) {
							return apivm.h(
								"view",
								{
									class: "chatMore_btns_item",
									style:
										(nItem.style || "") +
										";display:" +
										(nItem.show ? "flex" : "none") +
										";",
									onClick: function() {
										return this$1.openDetails(nItem);
									}
								},
								apivm.h(
									"view",
									{
										class: "chatMore_btns_item_warp",
										style: !nItem.style ? "border-top: 1px solid #F1F1F1;" : ""
									},
									apivm.h(
										"view",
										null,
										nItem.icon &&
											apivm.h("a-iconfont", {
												name: nItem.icon,
												color: "#333",
												size: G.appFontSize + 6
											})
									),
									apivm.h(
										"view",
										{style: "max-height:142px;"},
										nItem.type == "book" &&
											apivm.h("y-choose-book", {
												showAddBook: nItem.isOwer,
												noClean: true,
												listSelect: nItem.data
											})
									),
									apivm.h(
										"text",
										{
											style:
												"" +
												loadConfiguration(1) +
												(nItem.hintStyle || "color: #333333;") +
												"flex:1;"
										},
										nItem.hint
									),
									apivm.h(
										"view",
										null,
										nItem.moreText
											? apivm.h(
													"text",
													{style: loadConfiguration(1) + "color: #999;margin:0 6px;"},
													showTextSize(nItem.moreText, nItem.moreTextSize || 7)
											  )
											: nItem.moreIcon
											? apivm.h("a-iconfont", {
													name: nItem.moreIcon,
													style: "margin:0 6px;",
													color: "#999",
													size: G.appFontSize + nItem.moreSize
											  })
											: []
									),
									nItem.type == "switch"
										? apivm.h("z-switch", {
												dataMore: nItem,
												onChange: this$1.switchChange
										  })
										: nItem.more
										? apivm.h("a-iconfont", {
												class: "chatMore_arrow_right",
												name: "fanhui1",
												color: "#333",
												size: G.appFontSize - 4
										  })
										: []
								)
							);
						})
					),
					apivm.h("view", {
						style:
							"padding-bottom:" +
							(!this.data.pageParam.footerH ? safeArea().bottom : 0) +
							"px;"
					})
				),

				apivm.h("share-poster-group", {dataMore: G.sharePosterPop})
			);
		};

		return Root;
	})(Component);
	Root.css = {
		".chatMore_userBox": {
			padding: "10px 0",
			flexDirection: "row",
			flexWrap: "wrap"
		},
		".chatMore_userItem": {
			width: "20%",
			padding: "10px 3px",
			alignItems: "center"
		},
		".chatMore_userItemText": {
			color: "#666666",
			marginTop: "5px",
			textAlign: "center"
		},
		".chatMore_btns_item": {padding: "0 16px", backgroundColor: "#FFF"},
		".chatMore_btns_item_warp": {
			minHeight: "60px",
			flexDirection: "row",
			alignItems: "center"
		},
		".chatMore_arrow_right": {transform: "rotate(180deg)"}
	};
	apivm.define("root", Root);
	apivm.render(apivm.h("root", null), "body");
})();
