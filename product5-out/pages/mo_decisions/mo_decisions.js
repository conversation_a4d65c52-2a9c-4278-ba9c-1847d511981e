(function() {
	var LOAD_ING = "加载中，请稍候...";
	var LOAD_MORE = "点击加载更多";
	var NET_ERR = "用户您好，系统正在更新，请稍后再试。";

	//参数是否为空
	function isParameters(_obj) {
		return _obj != null && _obj != undefined;
	}
	//是否数组
	function isArray(_obj) {
		return isParameters(_obj) && toString.apply(_obj) === "[object Array]";
	}
	//是否数字
	function isNumber(_obj) {
		return isParameters(_obj) && typeof _obj === "number";
	}
	//是否对象
	function isObject(_obj) {
		return isParameters(_obj) && typeof _obj === "object";
	}
	//是否方法
	function isFunction(_obj) {
		return isParameters(_obj) && typeof _obj === "function";
	}

	//获取随机数
	function getNum() {
		return Math.floor(Math.random() * 100000000);
	}

	//合并json
	function setNewJSON(_obj, _newobj, _dotReplace) {
		_obj = _obj || {};
		_newobj = _newobj || {};
		var returnObj = {};
		for (var key in _obj) {
			returnObj[key] = _obj[key];
		}
		for (var key in _newobj) {
			if (
				(_dotReplace && isParameters(returnObj[key])) ||
				isFunction(isParameters(returnObj[key]))
			)
				continue;
			returnObj[key] =
				isArray(returnObj[key]) || !isObject(returnObj[key])
					? _newobj[key]
					: setNewJSON(returnObj[key], _newobj[key]);
		}
		return returnObj;
	}

	//获取平台类型
	function platform() {
		return api.platform;
	}

	//app设置状态栏
	function setStatusBarStyle(_param) {
		try {
			api.setStatusBarStyle(_param);
		} catch (e) {}
	}

	//区域参数
	function safeArea() {
		try {
			return api.safeArea;
		} catch (e) {
			return {top: 0, left: 0, bottom: 0, right: 0};
		}
	}

	//页面参数对象
	function pageParam(_this) {
		try {
			var pageParam =
				(_this && _this.props
					? _this.props.pageParam || (_this.props.dataMore || {}).pageParam
					: null) ||
				api.pageParam ||
				{};
			if (pageParam.paramSaveKey) {
				pageParam = JSON.parse(getPrefs(pageParam.paramSaveKey) || "{}");
			}
			if (platform() == "web" && JSON.stringify(pageParam) == "{}") {
				pageParam = getOtherParam(window.location.href);
			}
			return pageParam;
		} catch (e) {
			return {};
		}
	}

	//获取缓存
	function getPrefs(key) {
		try {
			return api.getPrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				return window.parent[key];
			} else {
				return window[key];
			}
		}
	}

	//设置缓存
	function setPrefs(key, value) {
		if (!isParameters(value)) {
			removePrefs(key);
			return;
		}
		try {
			api.setPrefs({sync: true, key: key, value: value});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				window.parent[key] = value;
			} else {
				window[key] = value;
			}
		}
	}

	//删除缓存
	function removePrefs(key) {
		try {
			api.removePrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				delete window.parent[key];
			} else {
				delete window[key];
			}
		}
	}

	//添加监听
	function addEventListener(name, callback) {
		var keyback = function keyback(ret, err) {
			isFunction(callback) && callback(ret, err);
		};
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				if (!window.baseEventList) window.baseEventList = [];
				if (getItemForKey(name, window.baseEventList)) {
					delItemForKey(name, window.baseEventList);
				}
				window.baseEventList.push({key: name, value: keyback});
			} else {
				api.addEventListener({name: name}, keyback);
			}
		} catch (e) {}
	}
	//发送监听
	function sendEvent(name, extra) {
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				var pageframes = window.parent.document.getElementsByTagName("iframe");
				for (var i = 0; i < pageframes.length; i++) {
					if (isArray(pageframes[i].contentWindow.baseEventList)) {
						var sendItem = getItemForKey(
							isObject(name) ? name.name : name,
							pageframes[i].contentWindow.baseEventList
						);
						if (sendItem)
							sendItem.value({value: isObject(name) ? name.extra : extra});
					}
				}
			} else {
				api.sendEvent(isObject(name) ? name : {name: name, extra: extra});
			}
		} catch (e) {}
	}
	//加载框
	function showProgress(_param, modal) {
		var o = {
			style: "default",
			animationType: "fade",
			title: "加载中",
			text: "请稍候...",
			modal: true //是否模态，模态时整个页面将不可交互
		};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.title = isParameters(_param) ? _param : "";
			o.modal = !modal; //是否可以交互 反过来了
		}
		o.title = o.title.toString();
		api.showProgress(o);
	}

	//隐藏加载框
	function hideProgress() {
		api.hideProgress();
	}

	function closeWin(_param) {
		var o = {};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.name = _param;
		}
		try {
			if (api.pageParam.paramSaveKey) {
				removePrefs(api.pageParam.paramSaveKey);
			}
			videoPlayRemoves();
			api.closeWin(o);
		} catch (e) {}
	}

	//弹出alert
	function alert(_param, _callback) {
		var o = {title: "", msg: "", buttons: ["确定"]};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.msg = (isParameters(_param) ? _param : "").toString();
		}
		var alertBox = {
			title: o.title,
			content: (o.msg || o.content || "").toString(),
			placeholder: o.placeholder,
			closeType: o.closeType || "1",
			type: o.type || "text",
			timeout: o.timeout || 0,
			autoClose: o.autoClose,
			cancel: {
				show: o.buttons.length > 1 || o.closeType == "2" || o.closeType == "3",
				text: o.buttons[1],
				color: "#333333"
			},

			sure: {
				show: o.buttons.length > 0,
				text: o.buttons[0],
				color: "appTheme"
			},

			show: true,
			callback: function callback(ret) {
				_callback && _callback(ret);
			}
		};

		alertBox = setNewJSON(alertBox, _param.otherParam);
		G.alertPop = alertBox;
	}

	//网络请求
	function ajax(url, tag, callback, logText, method, data, header) {
		var getUrl = url,
			dataType = "json",
			cacheType = "",
			paramData = {},
			aId = areaId(),
			isWeb = "";
		if (isObject(url)) {
			getUrl = url.u;
			dataType = url.dt || "json";
			cacheType = url.t || "";
			paramData = url.paramData || {};
			aId = url.areaId || areaId();
			isWeb = url.web || "";
		}
		var o = {
			url: getUrl,
			tag: tag,
			method: method || "get",
			cache: false,
			timeout: 120,
			dataType: dataType,
			data: isObject(data) ? data : {},
			headers: setNewJSON(
				{
					"u-login-areaId": aId,
					Authorization: getPrefs("sys_token") || "",
					"content-type": "application/json",
					"u-terminal": G.terminal || "APP"
				},
				header || {}
			)
		};

		o = setNewJSON(o, paramData);
		if (o.url.indexOf("push/rongCloud") != -1) {
			if (o.method == "get") {
				o.url +=
					(o.url.indexOf("?") != -1 ? "&" : "?") +
					"environment=" +
					chatEnvironment();
			} else if (o.data.values) {
				o.data.values.environment = chatEnvironment();
			}
		}
		if (isWeb) {
			(o.headers.Authorization =
				(header || {}).Authorization || getPrefs("public_token") || ""),
				(o.headers["u-terminal"] = "PUBLIC");
		}
		var oldContentType = o.headers["content-type"];
		if (oldContentType == "file") {
			delete o.headers["content-type"];
		}
		if (platform() == "app" && logText) {
			console.log(logText + o.method + "：" + JSON.stringify(o));
		}
		var cbFun = function cbFun(ret, err) {
			if (isFunction(callback)) {
				// if(isObject(err)){
				// 	try{
				// 		ret = JSON.parse(err.msg);
				// 	}catch(e){
				// 		ret = JSON.parse(JSON.stringify(err));
				// 	}
				// 	err = null;
				// }
				if (platform() == "app" && logText) {
					console.log(
						"得到" + logText + "返回结果：" + JSON.stringify(ret ? ret : err)
					);
				}
				if (isObject(ret)) {
					ret.message = ret.message || ret.msg || "";
					var errcode = ret.code || "";
					if ((errcode == 302 || errcode == 2) && cacheType != "login") {
						cleanAllMsg();
						sendEvent({
							name: "index",
							extra: {type: "verificationToken", errmsg: ret.message}
						});
					}
					// if(!isObject(ret.data)){
					// 	ret.data = "";
					// }
				}
				callback(ret, err);
			}
		};
		if (platform() == "web") {
			var xhr = new XMLHttpRequest();
			xhr.open(o.method, o.url);
			for (var header in o.headers) {
				xhr.setRequestHeader(header, o.headers[header]);
			}
			var sendValue = "";
			if (oldContentType.indexOf("x-www-form-urlencoded") != -1) {
				var dValue = o.data.values || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue +=
						(!sendValue ? "" : "&") + vItem + "=" + encodeURIComponent(dValue[vItem]);
				}
			} else if (oldContentType.indexOf("file") != -1) {
				sendValue = new FormData();
				var dValue = o.data.values || {};
				var fileValue = o.data.files || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue.append(vItem, encodeURIComponent(dValue[vItem]));
				}
				for (var vItem in fileValue) {
					sendValue.append(vItem, fileValue[vItem]);
				}
			} else {
				sendValue = o.data.body || JSON.stringify(o.data.values);
			}
			xhr.onreadystatechange = function() {
				if (xhr.readyState === XMLHttpRequest.DONE) {
					var ret, err;
					if (!xhr.responseText) {
						err = {};
					} else {
						var response = this.responseText;
						if (o.dataType == "json") {
							try {
								ret = JSON.parse(response);
							} catch (e) {
								err = {msg: response};
							}
						} else {
							ret = response;
						}
					}
					cbFun(ret, err);
				}
			};
			xhr.send(sendValue);
		} else {
			api.cancelAjax({tag: tag});
			api.ajax(o, cbFun);
		}
	}

	var SECONDS_A_MINUTE = 60;
	var SECONDS_A_HOUR = SECONDS_A_MINUTE * 60;
	var SECONDS_A_DAY = SECONDS_A_HOUR * 24;
	var SECONDS_A_WEEK = SECONDS_A_DAY * 7;
	var MILLISECONDS_A_SECOND = 1e3;
	var MILLISECONDS_A_MINUTE = SECONDS_A_MINUTE * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_HOUR = SECONDS_A_HOUR * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_DAY = SECONDS_A_DAY * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_WEEK = SECONDS_A_WEEK * MILLISECONDS_A_SECOND; // English locales

	var MS = "millisecond";
	var S = "second";
	var MIN = "minute";
	var H = "hour";
	var D = "day";
	var W = "week";
	var M = "month";
	var Q = "quarter";
	var Y = "year";
	var DATE = "date";
	var FORMAT_DEFAULT = "YYYY-MM-DDTHH:mm:ssZ";
	var INVALID_DATE_STRING = "Invalid Date"; // regex

	var REGEX_PARSE = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/;
	var REGEX_FORMAT = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;

	var en = {
		name: "en",
		weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split(
			"_"
		),
		months: "January_February_March_April_May_June_July_August_September_October_November_December".split(
			"_"
		)
	};

	var padStart = function padStart(string, length, pad) {
		var s = String(string);
		if (!s || s.length >= length) return string;
		return "" + Array(length + 1 - s.length).join(pad) + string;
	};
	var padZoneStr = function padZoneStr(instance) {
		var negMinutes = -instance.utcOffset();
		var minutes = Math.abs(negMinutes);
		var hourOffset = Math.floor(minutes / 60);
		var minuteOffset = minutes % 60;
		return (
			"" +
			(negMinutes <= 0 ? "+" : "-") +
			padStart(hourOffset, 2, "0") +
			":" +
			padStart(minuteOffset, 2, "0")
		);
	};
	var monthDiff = function monthDiff(a, b) {
		if (a.date() < b.date()) return -monthDiff(b, a);
		var wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month());
		var anchor = a.clone().add(wholeMonthDiff, M);
		var c = b - anchor < 0;
		var anchor2 = a.clone().add(wholeMonthDiff + (c ? -1 : 1), M);
		return +(
			-(
				wholeMonthDiff +
				(b - anchor) / (c ? anchor - anchor2 : anchor2 - anchor)
			) || 0
		);
	};
	var absFloor = function absFloor(n) {
		return n < 0 ? Math.ceil(n) || 0 : Math.floor(n);
	};
	var prettyUnit = function prettyUnit(u) {
		var special = {
			M: M,
			y: Y,
			w: W,
			d: D,
			D: DATE,
			h: H,
			m: MIN,
			s: S,
			ms: MS,
			Q: Q
		};

		return (
			special[u] ||
			String(u || "")
				.toLowerCase()
				.replace(/s$/, "")
		);
	};
	var isUndefined = function isUndefined(s) {
		return s === undefined;
	};
	var U = {
		s: padStart,
		z: padZoneStr,
		m: monthDiff,
		a: absFloor,
		p: prettyUnit,
		u: isUndefined
	};

	var L = "en";
	var Ls = {};
	Ls[L] = en;
	var isDayjs = function isDayjs(d) {
		return d instanceof Dayjs;
	};
	var parseLocale = function parseLocale(preset, object, isLocal) {
		var l;
		if (!preset) return L;
		if (typeof preset === "string") {
			var presetLower = preset.toLowerCase();
			if (Ls[presetLower]) {
				l = presetLower;
			}
			if (object) {
				Ls[presetLower] = object;
				l = presetLower;
			}
			var presetSplit = preset.split("-");
			if (!l && presetSplit.length > 1) {
				return parseLocale(presetSplit[0]);
			}
		} else {
			var name = preset.name;
			Ls[name] = preset;
			l = name;
		}
		if (!isLocal && l) L = l;
		return l || (!isLocal && L);
	};
	var dayjs = function dayjs(date, c) {
		if (isDayjs(date)) {
			return date.clone();
		}
		var cfg = typeof c === "object" ? c : {};
		cfg.date = date;
		cfg.args = arguments;
		return new Dayjs(cfg);
	};
	var wrapper = function wrapper(date, instance) {
		return dayjs(date, {
			locale: instance.$L,
			utc: instance.$u,
			x: instance.$x,
			$offset: instance.$offset
		});
	};
	var Utils = U;
	Utils.l = parseLocale;
	Utils.i = isDayjs;
	Utils.w = wrapper;
	var parseDate = function parseDate(cfg) {
		var date = cfg.date,
			utc = cfg.utc;
		if (date === null) return new Date(NaN);
		if (Utils.u(date)) return new Date();
		if (date instanceof Date) return new Date(date);
		if (typeof date === "string" && !/Z$/i.test(date)) {
			var d = date.match(REGEX_PARSE);
			if (d) {
				var m = d[2] - 1 || 0;
				var ms = (d[7] || "0").substring(0, 3);
				if (utc) {
					return new Date(
						Date.UTC(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms)
					);
				}
				return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);
			}
		}
		return new Date(date);
	};
	var Dayjs = (function() {
		function Dayjs(cfg) {
			this.$L = parseLocale(cfg.locale, null, true);
			this.parse(cfg);
		}
		var _proto = Dayjs.prototype;
		_proto.parse = function parse(cfg) {
			this.$d = parseDate(cfg);
			this.$x = cfg.x || {};
			this.init();
		};
		_proto.init = function init() {
			var $d = this.$d;
			this.$y = $d.getFullYear();
			this.$M = $d.getMonth();
			this.$D = $d.getDate();
			this.$W = $d.getDay();
			this.$H = $d.getHours();
			this.$m = $d.getMinutes();
			this.$s = $d.getSeconds();
			this.$ms = $d.getMilliseconds();
		};
		_proto.$utils = function $utils() {
			return Utils;
		};
		_proto.isValid = function isValid() {
			return !(this.$d.toString() === INVALID_DATE_STRING);
		};
		_proto.isSame = function isSame(that, units) {
			var other = dayjs(that);
			return this.startOf(units) <= other && other <= this.endOf(units);
		};
		_proto.isAfter = function isAfter(that, units) {
			return dayjs(that) < this.startOf(units);
		};
		_proto.isBefore = function isBefore(that, units) {
			return this.endOf(units) < dayjs(that);
		};
		_proto.$g = function $g(input, get, set) {
			if (Utils.u(input)) return this[get];
			return this.set(set, input);
		};
		_proto.unix = function unix() {
			return Math.floor(this.valueOf() / 1000);
		};
		_proto.valueOf = function valueOf() {
			return this.$d.getTime();
		};
		_proto.startOf = function startOf(units, _startOf) {
			var _this = this;
			var isStartOf = !Utils.u(_startOf) ? _startOf : true;
			var unit = Utils.p(units);
			var instanceFactory = function instanceFactory(d, m) {
				var ins = Utils.w(
					_this.$u ? Date.UTC(_this.$y, m, d) : new Date(_this.$y, m, d),
					_this
				);
				return isStartOf ? ins : ins.endOf(D);
			};
			var instanceFactorySet = function instanceFactorySet(method, slice) {
				var argumentStart = [0, 0, 0, 0];
				var argumentEnd = [23, 59, 59, 999];
				return Utils.w(
					_this
						.toDate()
						[method].apply(
							_this.toDate("s"),
							(isStartOf ? argumentStart : argumentEnd).slice(slice)
						),
					_this
				);
			};
			var $W = this.$W,
				$M = this.$M,
				$D = this.$D;
			var utcPad = "set" + (this.$u ? "UTC" : "");
			switch (unit) {
				case Y:
					return isStartOf ? instanceFactory(1, 0) : instanceFactory(31, 11);
				case M:
					return isStartOf ? instanceFactory(1, $M) : instanceFactory(0, $M + 1);
				case W: {
					var weekStart = this.$locale().weekStart || 0;
					var gap = ($W < weekStart ? $W + 7 : $W) - weekStart;
					return instanceFactory(isStartOf ? $D - gap : $D + (6 - gap), $M);
				}
				case D:
				case DATE:
					return instanceFactorySet(utcPad + "Hours", 0);
				case H:
					return instanceFactorySet(utcPad + "Minutes", 1);
				case MIN:
					return instanceFactorySet(utcPad + "Seconds", 2);
				case S:
					return instanceFactorySet(utcPad + "Milliseconds", 3);
				default:
					return this.clone();
			}
		};
		_proto.endOf = function endOf(arg) {
			return this.startOf(arg, false);
		};
		_proto.$set = function $set(units, _int) {
			var _C$D$C$DATE$C$M$C$Y$C;
			var unit = Utils.p(units);
			var utcPad = "set" + (this.$u ? "UTC" : "");
			var name = ((_C$D$C$DATE$C$M$C$Y$C = {}),
			(_C$D$C$DATE$C$M$C$Y$C[D] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[DATE] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[M] = utcPad + "Month"),
			(_C$D$C$DATE$C$M$C$Y$C[Y] = utcPad + "FullYear"),
			(_C$D$C$DATE$C$M$C$Y$C[H] = utcPad + "Hours"),
			(_C$D$C$DATE$C$M$C$Y$C[MIN] = utcPad + "Minutes"),
			(_C$D$C$DATE$C$M$C$Y$C[S] = utcPad + "Seconds"),
			(_C$D$C$DATE$C$M$C$Y$C[MS] = utcPad + "Milliseconds"),
			_C$D$C$DATE$C$M$C$Y$C)[unit];
			var arg = unit === D ? this.$D + (_int - this.$W) : _int;
			if (unit === M || unit === Y) {
				var date = this.clone().set(DATE, 1);
				date.$d[name](arg);
				date.init();
				this.$d = date.set(DATE, Math.min(this.$D, date.daysInMonth())).$d;
			} else if (name) this.$d[name](arg);
			this.init();
			return this;
		};
		_proto.set = function set(string, _int2) {
			return this.clone().$set(string, _int2);
		};
		_proto.get = function get(unit) {
			return this[Utils.p(unit)]();
		};
		_proto.add = function add(number, units) {
			var _this2 = this,
				_C$MIN$C$H$C$S$unit;
			number = Number(number);
			var unit = Utils.p(units);
			var instanceFactorySet = function instanceFactorySet(n) {
				var d = dayjs(_this2);
				return Utils.w(d.date(d.date() + Math.round(n * number)), _this2);
			};
			if (unit === M) {
				return this.set(M, this.$M + number);
			}
			if (unit === Y) {
				return this.set(Y, this.$y + number);
			}
			if (unit === D) {
				return instanceFactorySet(1);
			}
			if (unit === W) {
				return instanceFactorySet(7);
			}
			var step =
				((_C$MIN$C$H$C$S$unit = {}),
				(_C$MIN$C$H$C$S$unit[MIN] = MILLISECONDS_A_MINUTE),
				(_C$MIN$C$H$C$S$unit[H] = MILLISECONDS_A_HOUR),
				(_C$MIN$C$H$C$S$unit[S] = MILLISECONDS_A_SECOND),
				_C$MIN$C$H$C$S$unit)[unit] || 1;
			var nextTimeStamp = this.$d.getTime() + number * step;
			return Utils.w(nextTimeStamp, this);
		};
		_proto.subtract = function subtract(number, string) {
			return this.add(number * -1, string);
		};
		_proto.format = function format(formatStr) {
			var _this3 = this;
			var locale = this.$locale();
			if (!this.isValid()) return locale.invalidDate || INVALID_DATE_STRING;
			var str = formatStr || FORMAT_DEFAULT;
			var zoneStr = Utils.z(this);
			var $H = this.$H,
				$m = this.$m,
				$M = this.$M;
			var weekdays = locale.weekdays,
				months = locale.months,
				meridiem = locale.meridiem;
			var getShort = function getShort(arr, index, full, length) {
				return (
					(arr && (arr[index] || arr(_this3, str))) || full[index].slice(0, length)
				);
			};
			var get$H = function get$H(num) {
				return Utils.s($H % 12 || 12, num, "0");
			};
			var meridiemFunc =
				meridiem ||
				function(hour, minute, isLowercase) {
					var m = hour < 12 ? "AM" : "PM";
					return isLowercase ? m.toLowerCase() : m;
				};
			var matches = {
				YY: String(this.$y).slice(-2),
				YYYY: this.$y,
				M: $M + 1,
				MM: Utils.s($M + 1, 2, "0"),
				MMM: getShort(locale.monthsShort, $M, months, 3),
				MMMM: getShort(months, $M),
				D: this.$D,
				DD: Utils.s(this.$D, 2, "0"),
				d: String(this.$W),
				dd: getShort(locale.weekdaysMin, this.$W, weekdays, 2),
				ddd: getShort(locale.weekdaysShort, this.$W, weekdays, 3),
				dddd: weekdays[this.$W],
				H: String($H),
				HH: Utils.s($H, 2, "0"),
				h: get$H(1),
				hh: get$H(2),
				a: meridiemFunc($H, $m, true),
				A: meridiemFunc($H, $m, false),
				m: String($m),
				mm: Utils.s($m, 2, "0"),
				s: String(this.$s),
				ss: Utils.s(this.$s, 2, "0"),
				SSS: Utils.s(this.$ms, 3, "0"),
				Z: zoneStr
			};

			return str.replace(REGEX_FORMAT, function(match, $1) {
				return $1 || matches[match] || zoneStr.replace(":", "");
			});
		};
		_proto.utcOffset = function utcOffset() {
			return -Math.round(this.$d.getTimezoneOffset() / 15) * 15;
		};
		_proto.diff = function diff(input, units, _float) {
			var _C$Y$C$M$C$Q$C$W$C$D$;
			var unit = Utils.p(units);
			var that = dayjs(input);
			var zoneDelta =
				(that.utcOffset() - this.utcOffset()) * MILLISECONDS_A_MINUTE;
			var diff = this - that;
			var result = Utils.m(this, that);
			result =
				((_C$Y$C$M$C$Q$C$W$C$D$ = {}),
				(_C$Y$C$M$C$Q$C$W$C$D$[Y] = result / 12),
				(_C$Y$C$M$C$Q$C$W$C$D$[M] = result),
				(_C$Y$C$M$C$Q$C$W$C$D$[Q] = result / 3),
				(_C$Y$C$M$C$Q$C$W$C$D$[W] = (diff - zoneDelta) / MILLISECONDS_A_WEEK),
				(_C$Y$C$M$C$Q$C$W$C$D$[D] = (diff - zoneDelta) / MILLISECONDS_A_DAY),
				(_C$Y$C$M$C$Q$C$W$C$D$[H] = diff / MILLISECONDS_A_HOUR),
				(_C$Y$C$M$C$Q$C$W$C$D$[MIN] = diff / MILLISECONDS_A_MINUTE),
				(_C$Y$C$M$C$Q$C$W$C$D$[S] = diff / MILLISECONDS_A_SECOND),
				_C$Y$C$M$C$Q$C$W$C$D$)[unit] || diff;
			return _float ? result : Utils.a(result);
		};
		_proto.daysInMonth = function daysInMonth() {
			return this.endOf(M).$D;
		};
		_proto.$locale = function $locale() {
			return Ls[this.$L];
		};
		_proto.locale = function locale(preset, object) {
			if (!preset) return this.$L;
			var that = this.clone();
			var nextLocaleName = parseLocale(preset, object, true);
			if (nextLocaleName) that.$L = nextLocaleName;
			return that;
		};
		_proto.clone = function clone() {
			return Utils.w(this.$d, this);
		};
		_proto.toDate = function toDate() {
			return new Date(this.valueOf());
		};
		_proto.toJSON = function toJSON() {
			return this.isValid() ? this.toISOString() : null;
		};
		_proto.toISOString = function toISOString() {
			return this.$d.toISOString();
		};
		_proto.toString = function toString() {
			return this.$d.toUTCString();
		};
		return Dayjs;
	})();
	var proto = Dayjs.prototype;
	dayjs.prototype = proto;
	[
		["$ms", MS],
		["$s", S],
		["$m", MIN],
		["$H", H],
		["$W", D],
		["$M", M],
		["$y", Y],
		["$D", DATE]
	].forEach(function(g) {
		proto[g[1]] = function(input) {
			return this.$g(input, g[0], g[1]);
		};
	});
	dayjs.extend = function(plugin, option) {
		if (!plugin.$i) {
			plugin(option, Dayjs, dayjs);
			plugin.$i = true;
		}
		return dayjs;
	};
	dayjs.locale = parseLocale;
	dayjs.isDayjs = isDayjs;
	dayjs.unix = function(timestamp) {
		return dayjs(timestamp * 1e3);
	};
	dayjs.en = Ls[L];
	dayjs.Ls = Ls;
	dayjs.p = {};

	var module6 = {
		name: sysSign() == "rd" ? "意见征集" : "网络议政",
		code: "6",
		businessCode: "opinioncollect"
	};
	var module9 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息",
		code: "9",
		businessCode: "cppcc_member"
	};
	var module9_1 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息变更申请",
		code: "9_1",
		businessCode: "cppccMemberCheckPrepare"
	};
	var module12 = {
		name: sysSign() == "rd" ? "圈子" : "委员说",
		code: "12",
		businessCode: "styleCircle"
	};

	//获取登录信息
	function getLoginInfo(_param, _callback) {
		ajax(
			{u: appUrl() + "login/user"},
			"login/user",
			function(ret, err) {
				if (ret && ret.code == 200 && ret.data.id != "anonymous") {
					saveLogin(ret.data);
				}
				_callback && _callback(ret, err);
			},
			"用户信息",
			"post",
			{
				values: _param.param
			},
			_param.header
		);
	}

	function getAllChatInfo() {
		var chatInfos = JSON.parse(getPrefs("chatInfos") || "[]"),
			addInfo = [];
		chatInfos.forEach(function(_eItem) {
			if (_eItem.id) {
				if (/^(?!h|(\.)|(\/)).*/.test(_eItem.url)) {
					_eItem.url = showImg(_eItem.url);
				}
				addInfo.push(_eItem);
			}
		});
		G.chatInfos = addInfo;
		setPrefs("chatInfos", JSON.stringify(G.chatInfos));
		return G.chatInfos;
	}

	function setAllChatInfo(_item) {
		getAllChatInfo();
		var nowInfo = JSON.parse(JSON.stringify(G.chatInfos));
		if (isArray(_item)) {
			_item.forEach(function(_eItem) {
				if (_eItem.id) {
					delItemForKey(_eItem.id, nowInfo, "id");
					nowInfo.push(_eItem);
				}
			});
		} else {
			if (!_item.id) {
				return;
			}
			delItemForKey(_item.id, nowInfo, "id");
			nowInfo.push(_item);
		}
		setPrefs("chatInfos", JSON.stringify(nowInfo));
		setRongChatInfo();
	}

	function setRongChatInfo() {
		if (platform() == "app" && api.require("zyRongCloudRTC")) {
			getAllChatInfo();
			var setUser = {
				apply: 1,
				users: G.chatInfos.map(function(obj) {
					return {id: chatHeader() + obj.id, name: obj.name, url: obj.url};
				}),
				groupUser: JSON.parse(getPrefs("rongGroupUser") || "[]"), //当前通话的群组成员列表id集合
				totalNumber: 10 //语音或视频最大人数
			};
			// console.log("融云设置信息："+JSON.stringify(setUser));
			api.require("zyRongCloudRTC").setUsers(setUser, function(ret) {
				removePrefs("rongGroupUser");
				setRongChatInfo();
				setTimeout(
					function() {
						sendEvent({name: "chat_refresh"});
					},
					api.systemType == "ios" ? 600 : 1500
				);
			});
		}
	}

	//去除某个视频播放
	function videoPlayRemove(_id) {
		delItemForKey(_id, G.playVideos);
	}

	//去除所有播放
	function videoPlayRemoves() {
		(G.playVideos || []).forEach(function(_id) {
			document.getElementById(_id) && document.getElementById(_id).pause();
			videoPlayRemove(_id);
		});
	}

	//全局页面引用变量
	var G = {
		sysSign: sysSign(), //人大政协标识
		pageWidth: "", //页面总宽度
		onShowNum: -1, //当前页面展示次数 1为首次
		appName: "", //app名字
		appFont: "", //app全局字体
		appFontSize: 0, //app全局字体大小
		appTheme: "", //app全局主题色
		headTheme: "", //head全局主题色
		headColor: "", //标题前景色
		headTitle: "", //标题栏文字
		loginInfo: "",

		careMode: false, //是否启用了关怀模式
		systemtTypeIsPlatform: false, //系统类型是否是平台版
		isAppReview: false, //app是否上架期间 隐藏和显示部分功能
		v: "", //缓存版本号

		uId: "", //普通用户id
		userId: "", //当前用户id 账号id
		userName: "", //当前用户名字
		userImg: "", //当前用户头像
		areaId: "", //当前地区id
		specialRoleKeys: [], //当前用户角色集合
		isAdmin: false, //是否管理员 拥有所有权限
		grayscale: "", //全局置灰
		watermark: "", //全局水印

		// chatInfos:[],
		// chatInfoTask:[],

		alertPop: {
			// alert提示框
			show: false
		},

		actionSheetPop: {
			//actionSheet弹出框
			show: false
		},

		imgPreviewerPop: {
			//图片预览
			show: false
		},

		areasPop: {
			// 全局地区弹窗
			show: false
		},

		numInputPop: {
			// 全局数字弹窗
			show: false
		},

		favoritePop: {
			//收藏弹窗
			show: false
		},

		sharePosterPop: {
			//分享海报
			show: false
		},

		sharePop: {
			//分享
			show: false
		},

		identifyAudioPop: {
			//语音输入
			show: false
		},

		selectPop: {
			//单多选
			show: false
		},

		qrcodePop: {
			//h5扫码
			show: false
		},

		addressBookPop: {
			//通讯录
			show: false
		},

		fileListPop: {
			//选择文件
			show: false
		}
	};

	//默认情况下是否展示标题栏
	function showHeader() {
		return platform() == "app";
	}

	//计算头部离顶上距离
	function headerTop(_type) {
		if (platform() == "mp" && _type) {
			var wxArea = wx.getMenuButtonBoundingClientRect();
			return wxArea.top + (_type == 2 ? wxArea.height : 0);
		}
		return showHeader() ? safeArea().top : 0;
	}

	//动态加载字体和大小
	function loadConfiguration(size) {
		return (
			"font-size:" +
			((G.appFontSize || 0) + (size || 0)) +
			"px;" +
			(G.appFont != "heitiSimplified" ? "font-family:" + G.appFont + ";" : "")
		);
	}

	//动态宽度配置
	function loadConfigurationSize(size, _who) {
		var changeSize = size || 0,
			cssWidth,
			cssHeight;
		if (isArray(size)) {
			cssWidth = "width:" + (G.appFontSize + (size[0] || 0)) + "px;";
			cssHeight = "height:" + (G.appFontSize + (size[1] || 0)) + "px;";
		} else {
			cssWidth = "width:" + (G.appFontSize + changeSize) + "px;";
			cssHeight = "height:" + (G.appFontSize + changeSize) + "px;";
		}
		if (!_who) {
			return cssWidth + cssHeight;
		} else {
			return _who == "w" ? cssWidth : cssHeight;
		}
	}

	//动态计算
	function dataForNum(_num) {
		return Array.from({length: _num || 0}).fill("");
	}

	//获取item参数
	function getItemForKey(_value, _list, _key, _child) {
		if (!isParameters(_list)) return;
		var hasChild = false,
			listLength = _list.length;
		for (var i = 0; i < listLength; i++) {
			var listItem = _list[i];
			if (isArray(listItem)) {
				hasChild = true;
				var result = getItemForKey(_value, listItem, _key, true);
				if (result) return result;
			} else {
				if (!isObject(listItem)) {
					if (listItem === _value) return listItem;
				} else {
					var listItemKey = listItem[_key || "key"];
					if (isArray(listItemKey)) {
						hasChild = true;
						var result = getItemForKey(_value, listItemKey, _key, true);
						if (result) {
							return listItem;
						}
					} else if (!isObject(listItemKey) && listItemKey === _value) {
						listItem["_i"] = i;
						return listItem;
					}
				}
				if (isObject(listItem) && isArray(listItem.children)) {
					hasChild = true;
					var result = getItemForKey(_value, listItem.children, _key, true);
					if (result) return result;
				}
			}
		}
		if (!_child && !hasChild) return false;
	}

	//删除item中的元素
	function delItemForKey(_obj, _list, _key) {
		var contrastObj = !isObject(_obj) ? _obj : _obj[_key || "key"];
		for (var i = 0; i < _list.length; i++) {
			if (
				(!isObject(_list[i]) ? _list[i] : _list[i][_key || "key"]) === contrastObj
			) {
				_list.splice(i, 1);
				delItemForKey(_obj, _list, _key);
				break;
			}
		}
	}

	//web和小程序阻止底部事件
	function stopBubble(e) {
		if (!e) return;
		if (platform() == "web") {
			e.preventDefault();
			e.stopPropagation();
		} else if (platform() == "mp") {
			e.$_canBubble = false;
		}
	}

	//适配图片
	function showImg(_item, _add) {
		if (_add === void 0) {
			_add = "";
		}
		var baseUrl = isObject(_item) ? _item.url || "" : _item || "";
		if (!baseUrl) return;
		if (/^(?!h|(\.)|(\/)).*/.test(baseUrl)) {
			baseUrl =
				appUrl() +
				"image/" +
				_add +
				(baseUrl.indexOf("-compress-") > -1
					? baseUrl.split("-compress-")[1]
					: baseUrl);
		}
		return baseUrl + (baseUrl.indexOf("?") != -1 ? "&" : "?") + "v=" + G.v;
	}

	//颜色转成rgba
	function colorRgba(_color, _alpha) {
		if (!_color) return;
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		var color = _color.toLowerCase();
		if (reg.test(color)) {
			if (color.length === 4) {
				var colorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
				}
				color = colorNew;
			}
			var colorChange = [];
			for (var i = 1; i < 7; i += 2) {
				colorChange.push(parseInt("0x" + color.slice(i, i + 2)));
			}
			return (
				"rgba(" +
				colorChange.join(",") +
				"," +
				(isParameters(_alpha) ? _alpha : "1") +
				")"
			);
		} else {
			return color;
		}
	}

	//删除所有缓存
	function cleanAllMsg() {
		var exitPrefs = [
			"isAutoLogin",
			"loginPassword",
			"sys_token",
			"sys_Id",
			"sys_UserID",
			"sys_UserName",
			"sys_AppPhoto",
			"sys_Mobile",
			"sys_Position",
			"sys_aresId",
			"sys_OfficeId",
			"sys_SpecialRoleKeys",
			"sdt_signin_phone",
			"public_token",
			"last14Msg",
			"sys_unread"
		];
		exitPrefs.forEach(function(_eItem) {
			removePrefs(_eItem);
		});
	}

	//保存登录信息
	function saveLogin(_info) {
		var infolist = _info || {};
		setPrefs("sys_Id", infolist.id);
		setPrefs("sys_UserID", infolist.accountId);
		setPrefs("sys_UserName", infolist.userName);
		setPrefs("sys_AppPhoto", infolist.headImg || infolist.photo); //headImg用户头像 photo代表头像
		setPrefs("sys_Mobile", infolist.mobile);
		setPrefs("sys_Position", infolist.position);
		if (!getPrefs("sys_aresId")) {
			setPrefs("sys_aresId", infolist.areaId);
		}
		setPrefs("sys_OfficeId", infolist.officeId);
		setPrefs(
			"sys_SpecialRoleKeys",
			JSON.stringify(infolist.specialRoleKeys || [])
		);
		if (infolist.id) {
			// //修改头像后保存下缓存数据
			setAllChatInfo({
				id: infolist.accountId,
				name: infolist.userName,
				url: infolist.headImg || infolist.photo
			});
		}
		sendEvent({name: "sys_refresh"});
	}

	//获取链接中参数
	function getOtherParam(_url) {
		if (_url.indexOf("?") != -1) {
			_url = _url.substring(_url.indexOf("?") + 1);
		}
		var params = _url.split("&"),
			rp = {};
		for (var j = 0; j < params.length; j++) {
			if (params[j]) {
				var data_key = params[j].substring(0, params[j].indexOf("="));
				if (!data_key) {
					continue;
				}
				var data_value = decodeURIComponent(
					params[j].substring(params[j].indexOf("=") + 1)
				);
				if (data_value.indexOf("{") == 0 || data_value.indexOf("[") == 0)
					data_value = JSON.parse(data_value);
				rp[data_key] = data_value;
			}
		}
		return rp;
	}

	//人大政协标识  rd人大 zx政协
	function sysSign() {
		return getPrefs("sys_sign") || "rd";
	}

	//配置地址
	function appUrl() {
		var prot = sysSign() == "rd" ? "20169" : "20170";
		return (
			getPrefs("sys_appUrl") || "https://productpc.cszysoft.com:" + prot + "/lzt/"
		);
	}
	//tomcat配置地址
	function tomcatAddress() {
		return (
			getPrefs("sys_tomcatAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http://cszysoft.com:9090/"
				: "https://cszysoft.com:9091/")
		);
	}
	//分享地址
	function shareAddress(_type) {
		if (_type == 1 && platform() != "mp") return "../../";
		return (
			getPrefs("sys_shareAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http:"
				: "https:") +
				"//cszysoft.com/appShare/" +
				(sysSign() == "rd" ? "platform5rd/" : "platform5zx/")
		);
	}
	//融云唯一前缀
	function chatHeader() {
		return getPrefs("sys_chatHeader") || "platform5" + sysSign();
	}
	//融云正测
	function chatEnvironment() {
		return getPrefs("sys_chatEnvironment") || "1";
	}
	//当前页面地区id
	function areaId() {
		return (
			(G._this && G._this.data.area ? G._this.data.area.key : "") ||
			pageParam().areaId ||
			getPrefs("sys_aresId") ||
			getPrefs("sys_platform") ||
			""
		);
	}

	var ZButton = /*@__PURE__*/ (function(Component) {
		function ZButton(props) {
			Component.call(this, props);
		}

		if (Component) ZButton.__proto__ = Component;
		ZButton.prototype = Object.create(Component && Component.prototype);
		ZButton.prototype.constructor = ZButton;
		ZButton.prototype.componentsClick = function(e) {
			stopBubble(e);
			if (!this.props.disabled && !this.props.readonly) {
				this.fire("click", {});
			}
		};
		ZButton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					id: "" + (this.props.id || ""),
					class: "z_button " + (this.props.class || ""),
					style:
						"border-radius:" +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;border-color:" +
						(this.props.color || G.appTheme) +
						";background:" +
						(this.props.plain ? "#FFF" : this.props.color || G.appTheme) +
						";opacity:" +
						(this.props.disabled ? "0.5" : "1") +
						";" +
						(this.props.style || ""),
					onClick: function(e) {
						return this$1.componentsClick(e);
					}
				},
				this.props.icon &&
					apivm.h("a-iconfont", {
						name: this.props.icon,
						style: "margin-right:5px;",
						color: this.props.plain ? this.props.color || G.appTheme : "#FFF",
						size: G.appFontSize - 1 + (this.props.size || 0)
					}),
				apivm.h(
					"text",
					{
						style:
							"color:" +
							(this.props.plain ? this.props.color || G.appTheme : "#FFF") +
							";" +
							loadConfiguration(this.props.size || 0)
					},
					this.props.text || this.props.children
				)
			);
		};

		return ZButton;
	})(Component);
	ZButton.css = {
		".z_button": {
			padding: "7px 12px",
			borderWidth: "1px",
			borderStyle: "solid",
			boxSizing: "border-box",
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("z-button", ZButton);

	var ZAlert = /*@__PURE__*/ (function(Component) {
		function ZAlert(props) {
			Component.call(this, props);
			this.data = {
				show: false,

				inputBox: {
					dotIcon: true,
					value: "",
					placeholder: "",
					autoFocus: true,
					cleanFocus: true,
					height: 150,
					confirmType: ""
				},

				inputBoxPass: {
					dotIcon: true,
					value: "",
					placeholder: "",
					inputType: "password",
					autoFocus: false,
					cleanFocus: true,
					height: 150,
					confirmType: ""
				},

				marginBottom: 0,
				timeout: 0
			};
			this.compute = {
				monitor: function() {
					var dm = this.props.dataMore;
					if (dm.show != this.data.show) {
						this.data.inputBox.autoFocus = true;
						this.data.show = dm.show;
						this.data.marginBottom = 0;
						if (this.data.show) {
							this.data.timeout = dm.timeout || 0;
							if (this.data.timeout > 0) {
								this.startTime();
							}
							if (
								this.props.dataMore.type == "input" ||
								this.props.dataMore.type == "textarea"
							) {
								this.data.inputBox.value = dm.content;
								this.data.inputBox.placeholder = dm.placeholder;
								this.data.inputBox.confirmType = dm.confirmType || "done";

								this.data.inputBoxPass.value = dm.content2;
								this.data.inputBoxPass.placeholder = dm.placeholder2;
								this.data.inputBoxPass.confirmType = dm.confirmType2 || "done";
							}
						}
					}
				}
			};
		}

		if (Component) ZAlert.__proto__ = Component;
		ZAlert.prototype = Object.create(Component && Component.prototype);
		ZAlert.prototype.constructor = ZAlert;
		ZAlert.prototype.closePage = function(_type) {
			this.props.dataMore.show = false;
			if (!_type) {
				G.alertPop.callback({buttonIndex: 2});
			}
		};
		ZAlert.prototype.closeStop = function(e) {
			stopBubble(e);
		};
		ZAlert.prototype.inputFocus = function(e) {
			if (platform() == "app" && api.systemType == "ios") {
				this.data.marginBottom = e.detail.height;
			}
		};
		ZAlert.prototype.inputBlur = function(e) {
			this.data.marginBottom = 0;
		};
		ZAlert.prototype.itemClick = function() {
			if (this.data.timeout > 0) {
				return;
			}
			if (
				this.props.dataMore.type == "input" ||
				this.props.dataMore.type == "textarea"
			) {
				this.props.dataMore.content = this.data.inputBox.value;
				this.props.dataMore.content2 = this.data.inputBoxPass.value;
			}
			this.props.dataMore.buttonIndex = 1;
			G.alertPop.callback(this.props.dataMore);
			this.closePage(1);
		};
		ZAlert.prototype.startTime = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.data.timeout--;
				if (this$1.data.timeout >= 0) {
					this$1.startTime();
				} else if (this$1.props.dataMore.autoClose) {
					this$1.itemClick();
				}
			}, 1000);
		};
		ZAlert.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box xy_center",
					onClick: this.closeStop,
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);z-index:1001;"
				},
				this.data.show && [
					apivm.h(
						"view",
						{
							class: "alert_warp",
							style: "margin-bottom:" + this.data.marginBottom + "px;",
							onClick: this.closeStop
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							null,
							(this.props.dataMore.title || this.props.dataMore.closeType == "2") &&
								apivm.h(
									"view",
									{style: "padding: 20px 20px 0;"},
									this.props.dataMore.title &&
										apivm.h(
											"text",
											{class: "alert_title", style: "" + loadConfiguration(4)},
											this.props.dataMore.title
										),
									apivm.h(
										"view",
										{
											style:
												"display:" +
												(this.props.dataMore.closeType == "2" ? "flex" : "none") +
												";position:absolute;right:0;top:0;"
										},
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.closePage();
												},
												style: "padding:20px 20px 10px 10px;"
											},
											apivm.h("a-iconfont", {
												name: "cuohao",
												color: "#666",
												size: G.appFontSize + 4
											})
										)
									)
								)
						),
						apivm.h(
							"scroll-view",
							{class: "alert_content_box", "scroll-y": true},
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "input" &&
									apivm.h("z-input", {
										id: "input",
										dataMore: this.data.inputBox,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.inputPassword &&
									apivm.h("z-input", {
										id: "password",
										style: "margin-top:15px;",
										dataMore: this.data.inputBoxPass,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "textarea" &&
									apivm.h("z-textarea", {
										class: "alert_textarea",
										dataMore: this.data.inputBox,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "richText" &&
									apivm.h("z-rich-text", {
										detail: true,
										nodes: this.props.dataMore.content
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "text" &&
									apivm.h(
										"text",
										{class: "alert_content", style: "" + loadConfiguration(1)},
										this.props.dataMore.content
									)
							)
						),
						apivm.h(
							"view",
							null,
							this.props.dataMore.closeType == "1" && [
								apivm.h(
									"view",
									{style: "width:100%;height:1px;padding:0 15px;flex-shrink: 0;"},
									apivm.h("view", {style: "height:1px;background: #F6F6F6;"})
								),
								apivm.h(
									"view",
									{class: "alert_btn_box"},
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.closePage();
											},
											class: "alert_btn_item",
											style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}
										},
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color:" +
													(this.props.dataMore.cancel.color == "appTheme"
														? G.appTheme
														: this.props.dataMore.cancel.color) +
													";"
											},
											this.props.dataMore.cancel.text
										)
									),
									apivm.h(
										"view",
										{style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}},
										apivm.h("view", {style: "width:1px;height:30px;background:#F6F6F6;"})
									),
									apivm.h(
										"view",
										{
											onClick: this.itemClick,
											class: "alert_btn_item",
											style: {display: this.props.dataMore.sure.show ? "flex" : "none"}
										},
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color:" +
													(this.props.dataMore.sure.color == "appTheme"
														? G.appTheme
														: this.props.dataMore.sure.color) +
													";opacity:" +
													(this.data.timeout > 0 ? "0.5" : "1") +
													";"
											},
											this.props.dataMore.sure.text,
											this.data.timeout > 0 ? "(" + this.data.timeout + ")" : ""
										)
									)
								)
							]
						),
						apivm.h(
							"view",
							null,
							this.props.dataMore.closeType != "1" &&
								this.props.dataMore.sure.show &&
								apivm.h(
									"view",
									{style: "padding-bottom:15px;", class: "alert_btn_box"},
									apivm.h(
										"z-button",
										{
											style: "width:210px;",
											disabled: this.data.timeout > 0,
											color:
												this.props.dataMore.sure.color == "appTheme"
													? G.appTheme
													: this.props.dataMore.sure.color,
											round: true,
											onClick: this.itemClick
										},
										this.props.dataMore.sure.text,
										this.data.timeout > 0 ? "(" + this.data.timeout + ")" : ""
									)
								)
						)
					),
					apivm.h(
						"view",
						null,
						this.props.dataMore.closeType == "3" &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.closePage();
									},
									style: "margin-top:15px;"
								},
								apivm.h("a-iconfont", {
									style: "transform: rotate(45deg);",
									name: "gengduo1",
									color: "#FFF",
									size: G.appFontSize + 26
								})
							)
					)
				]
			);
		};

		return ZAlert;
	})(Component);
	ZAlert.css = {
		".alert_warp": {background: "#FFF", borderRadius: "10px", width: "320px"},
		".alert_content_box": {
			margin: "20px 15px",
			maxHeight: "385px",
			width: "auto"
		},
		".alert_title": {color: "#333333", fontWeight: "bold", textAlign: "center"},
		".alert_content": {
			width: "100%",
			textAlign: "center",
			color: "#333333",
			wordWrap: "break-word"
		},
		".alert_btn_box": {
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_btn_item": {
			flex: "1",
			padding: "10px",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_textarea": {
			borderColor: "#ccc !important",
			borderRadius: "10px",
			padding: "8px",
			width: "100%"
		}
	};
	apivm.define("z-alert", ZAlert);

	var ZEcharts = /*@__PURE__*/ (function(Component) {
		function ZEcharts(props) {
			Component.call(this, props);
			this.data = {
				nowParam: "",
				isAddListener: false,
				isLoading: false
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (!this.data.isAddListener && this.props.dataMore.show) {
						this.data.isAddListener = true;
						setTimeout(function() {
							if (platform() == "web") {
								document
									.getElementById(this$1.props.dataMore.name)
									.addEventListener("load", function() {
										this$1.data.isLoading = true;
										this$1.sendMessage();
									});
							} else if (platform() == "app") {
								api.setFrameClient({frameName: this$1.props.dataMore.name}, function(
									ret,
									err
								) {
									if (ret.state == 2) {
										this$1.data.isLoading = true;
										setTimeout(function() {
											this$1.sendMessage();
										}, 400);
									}
								});
							} else {
								this$1.data.isLoading = true;
								this$1.sendMessage();
							}
						}, 0);
					}
					if (this.data.nowParam != JSON.stringify(this.props.dataMore)) {
						this.data.nowParam = JSON.stringify(this.props.dataMore);
						this.sendMessage();
					}
				},
				frameName: function() {}
			};
		}

		if (Component) ZEcharts.__proto__ = Component;
		ZEcharts.prototype = Object.create(Component && Component.prototype);
		ZEcharts.prototype.constructor = ZEcharts;
		ZEcharts.prototype.sendMessage = function() {
			if (this.data.isLoading) {
				if (platform() == "web") {
					var targetWindow = document.getElementById(this.props.dataMore.name)
						.contentWindow;
					targetWindow.postMessage(this.data.nowParam, "*");
				} else if (platform() == "app") {
					api.sendEvent({
						name: this.props.dataMore.name + "_changeEcharts",
						extra: this.props.dataMore
					});
				}
			}
		};
		ZEcharts.prototype.render = function() {
			return apivm.h(
				"view",
				{
					s: this.monitor,
					style: "" + (this.props.style || null),
					class: "xy_100 " + (this.props.class || null)
				},
				this.props.dataMore.show && [
					platform() != "mp"
						? apivm.h("frame", {
								id: this.props.dataMore.name,
								class: "xy_100",
								name: this.props.dataMore.name,
								url: shareAddress() + "html/echarts.html",
								pageParam: this.props.dataMore,
								useWKWebView: true,
								scaleEnabled: true,
								allowEdit: true
						  })
						: apivm.h("canvas", {id: this.props.dataMore.name, class: "xy_100"})
				]
			);
		};

		return ZEcharts;
	})(Component);
	apivm.define("z-echarts", ZEcharts);

	var ZEmpty = /*@__PURE__*/ (function(Component) {
		function ZEmpty(props) {
			Component.call(this, props);
		}

		if (Component) ZEmpty.__proto__ = Component;
		ZEmpty.prototype = Object.create(Component && Component.prototype);
		ZEmpty.prototype.constructor = ZEmpty;
		ZEmpty.prototype.getShowImg = function() {
			if (this.props.dataMore.type == 1 || this.props.dataMore.type == 2) {
				return showImg(
					shareAddress(1) + "image/icon_empty_" + this.props.dataMore.type + ".png"
				);
			}
			return showImg(this.props.dataMore.type);
		};
		ZEmpty.prototype.getShowText = function() {
			return (
				this.props.dataMore.text ||
				(this.props.dataMore.type == 1
					? "暂无数据"
					: this.props.dataMore.type == 2
					? "网络异常，请点击重试"
					: "")
			);
		};
		ZEmpty.prototype.refresh = function() {
			showProgress("刷新中");
			this.fire("refresh");
			setTimeout(function() {
				hideProgress();
			}, 2500);
		};
		ZEmpty.prototype.up = function() {
			if (this.props._this && isFunction(this.props._this.loadMore)) {
				this.props._this.loadMore({detail: {}});
			} else {
				this.fire("up", {});
			}
		};
		ZEmpty.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"view",
					null,
					this.props.dataMore.type &&
						this.props.dataMore.type != "load" &&
						apivm.h(
							"view",
							{
								id: "" + (this.props.id || ""),
								style: "margin:100px 0;" + (this.props.style || ""),
								class: "xy_center " + (this.props.class || "")
							},
							apivm.h("image", {
								style: "width:200px;height:200px;",
								src: this.getShowImg(),
								mode: "aspectFill"
							}),
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(this.props.size || 0) +
										"color:#666;padding:10px 20px;text-align: center;"
								},
								this.getShowText()
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == 2 &&
									apivm.h(
										"z-button",
										{
											style: "width:132px;margin-top:20px;",
											color: G.appTheme,
											round: true,
											onClick: this.refresh
										},
										"刷新"
									)
							),
							this.props.children
						)
				),
				apivm.h(
					"view",
					null,
					!this.props.dataMore.type &&
						this.props.dataMore.text &&
						apivm.h(
							"view",
							{class: "xy_center", onClick: this.up},
							apivm.h(
								"text",
								{style: loadConfiguration(-4) + "color:#bbb;padding:15px;"},
								this.props.dataMore.text
							)
						)
				)
			);
		};

		return ZEmpty;
	})(Component);
	apivm.define("z-empty", ZEmpty);

	var ZDivider = /*@__PURE__*/ (function(Component) {
		function ZDivider(props) {
			Component.call(this, props);
		}

		if (Component) ZDivider.__proto__ = Component;
		ZDivider.prototype = Object.create(Component && Component.prototype);
		ZDivider.prototype.constructor = ZDivider;
		ZDivider.prototype.render = function() {
			return apivm.h(
				"view",
				{style: "width:100%;height:1px;padding:0 16px;" + this.props.style || null},
				apivm.h("view", {
					style:
						"border-bottom:1px solid " +
						(this.props.color || "rgba(0,0,0,0.03)") +
						";"
				})
			);
		};

		return ZDivider;
	})(Component);
	apivm.define("z-divider", ZDivider);

	var ZSkeleton = /*@__PURE__*/ (function(Component) {
		function ZSkeleton(props) {
			Component.call(this, props);
		}

		if (Component) ZSkeleton.__proto__ = Component;
		ZSkeleton.prototype = Object.create(Component && Component.prototype);
		ZSkeleton.prototype.constructor = ZSkeleton;
		ZSkeleton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{style: "width:100%;"},
				(Array.isArray(dataForNum(this.props.cell || 3))
					? dataForNum(this.props.cell || 3)
					: Object.values(dataForNum(this.props.cell || 3))
				).map(function(item$1, index$1, list) {
					return apivm.h(
						"view",
						{class: "z_skeleton"},
						apivm.h(
							"view",
							null,
							(Array.isArray(dataForNum(this$1.props.row || 4))
								? dataForNum(this$1.props.row || 4)
								: Object.values(dataForNum(this$1.props.row || 4))
							).map(function(item$1, index$1, list) {
								return apivm.h("view", {
									class: "z_skeleton_row",
									style:
										"width:" +
										(!index$1 ? 40 : index$1 == list.length - 1 ? 60 : 100) +
										"%;"
								});
							})
						)
					);
				})
			);
		};

		return ZSkeleton;
	})(Component);
	ZSkeleton.css = {
		".z_skeleton": {width: "100%", padding: "10px 16px"},
		".z_skeleton_row": {
			marginTop: "12px",
			height: "16px",
			backgroundColor: "#f2f3f5"
		}
	};
	apivm.define("z-skeleton", ZSkeleton);

	var YScrollView = /*@__PURE__*/ (function(Component) {
		function YScrollView(props) {
			Component.call(this, props);
			this.data = {
				mId: "",
				scroll_view: "", //滚动到id
				scroll_animation: true, //滚动动画
				refreshEd: false
			};
			this.compute = {
				monitor: function() {
					if (!this.data.mId) {
						var nowId = this.props.id || "scroll_view" + getNum();
						this.data.mId = !document.getElementById(nowId)
							? nowId
							: "scroll_view" + getNum();
						(this.props.dataMore || {}).id = this.data.mId;
					}
					var dataMore = this.props.dataMore || {};
					if (this.data.scroll_view != dataMore.scroll_view) {
						this.data.scroll_animation = isParameters(dataMore.scroll_animation)
							? dataMore.scroll_animation
							: true;
						this.data.scroll_view = dataMore.scroll_view || "";
						if (this.data.scroll_view) {
							this.scrollTo(this.data.scroll_view);
						}
					}
				}
			};
		}

		if (Component) YScrollView.__proto__ = Component;
		YScrollView.prototype = Object.create(Component && Component.prototype);
		YScrollView.prototype.constructor = YScrollView;
		YScrollView.prototype.onrefresherrefresh = function(e) {
			var this$1 = this;

			if (this.props._this && isFunction(this.props._this.pageRefresh)) {
				this.props._this.pageRefresh({detail: {}});
			} else {
				this.fire("lower", {});
			}
			this.data.refreshEd = true;
			setTimeout(function() {
				this$1.data.refreshEd = false;
			}, 800);
		};
		YScrollView.prototype.onscrolltolower = function(e) {
			if (this.props._this && isFunction(this.props._this.loadMore)) {
				this.props._this.loadMore({detail: {}});
			} else {
				this.fire("up", {});
			}
		};
		YScrollView.prototype.onscroll = function(ref) {
			var detail = ref.detail;

			if (this.props._this && isFunction(this.props._this.pageScroll)) {
				this.props._this.pageScroll({detail: detail});
			} else {
				this.fire("scroll", detail);
			}
		};
		YScrollView.prototype.scrollTo = function(nowView) {
			var this$1 = this;

			var _animated = this.data.scroll_animation;
			if (document.getElementById(this.data.mId) && platform() != "mp") {
				var scrollTo =
					platform() == "app"
						? {view: nowView, animated: _animated}
						: this.props.dataMore.direction == "horizontal"
						? {
								left: this.getOffestValue(document.getElementById(nowView)).left,
								behavior: _animated ? "smooth" : "instant"
						  }
						: {
								top: this.getOffestValue(document.getElementById(nowView)).top,
								behavior: _animated ? "smooth" : "instant"
						  };
				document.getElementById(this.data.mId).scrollTo(scrollTo);
			}
			setTimeout(function() {
				this$1.props.dataMore.scroll_view = "";
			}, 500);
		};
		YScrollView.prototype.getOffestValue = function(elem) {
			var Far = null;
			var topValue = elem && elem.offsetTop;
			var leftValue = elem && elem.offsetLeft;
			var offsetFar = elem && elem.offsetParent;
			while (offsetFar) {
				if (offsetFar.id == this.data.mId) {
					break;
				}
				topValue += offsetFar.offsetTop;
				leftValue += offsetFar.offsetLeft;
				Far = offsetFar;
				offsetFar = offsetFar.offsetParent;
				if (offsetFar.id == this.data.mId) {
					break;
				}
			}
			return {top: topValue, left: leftValue, Far: Far};
		};
		YScrollView.prototype.render = function() {
			return apivm.h(
				"scroll-view",
				{
					s: this.monitor,
					id: "" + this.data.mId,
					style: "flex:1;" + (this.props.style || ""),
					class: "" + (this.props.class || ""),
					"refresher-background": "rgba(0,0,0,0)",
					"scroll-x": isParameters(this.props["scroll-x"])
						? this.props["scroll-x"]
						: false,
					"scroll-y": isParameters(this.props["scroll-y"])
						? this.props["scroll-y"]
						: true,
					bounces: isParameters(this.props["bounces"])
						? this.props["bounces"]
						: false,
					"scroll-into-view": platform() == "mp" ? this.data.scroll_view : null,
					"scroll-with-animation":
						platform() == "mp" ? this.data.scroll_animation : null,
					"refresher-enabled": isParameters(this.props["refresh"])
						? this.props["refresh"] &&
						  (platform() != "app" ? !this.props._this.props.dataMore : true)
						: false,
					"refresher-triggered": this.data.refreshEd,
					onScrolltolower: this.onscrolltolower,
					onRefresherrefresh: this.onrefresherrefresh,
					onScroll: this.onscroll
				},
				this.props.children
			);
		};

		return YScrollView;
	})(Component);
	apivm.define("y-scroll-view", YScrollView);

	var baoguo_hezi_o = "";
	var fuzhilianjiexian = "";
	var fuzhilianjiemian = "";
	var wenjian = "";
	var tianjia = "";
	var tianjiawenjian = "";
	var sousuowenjian = "";
	var wenjianshangchuan = "";
	var dalou = "";
	var wucan = "";
	var zengjia1 = "";
	var xinsui = "";
	var aixin = "";
	var jianshao2 = "";
	var yishoucang = "";
	var yuyinshibie = "";
	var shuruxiangce = "";
	var tupianshibie = "";
	var shuruwenjian = "";
	var shurupaizhao = "";
	var tongji4 = "";
	var pinglun1 = "";
	var gerentongxunlu = "";
	var fenxiang2 = "";
	var dianzan = "";
	var shoucang1 = "";
	var sousuo2 = "";
	var jiancexinbanben = "";
	var shezhi1 = "";
	var guanhuaimoshi = "";
	var xiugaimima = "";
	var anquantuichu = "";
	var dianhua = "";
	var shipintianchong = "";
	var xiangxiagengduo = "";
	var mubiao = "";
	var jianshao1 = "";
	var biaoqian = "";
	var duigou = "";
	var jinengbiaoqian = "";
	var jiantou_xiangyouliangci = "";
	var toupiao = "";
	var riqi = "";
	var gonggao = "";
	var tongji3 = "";
	var paihang = "";
	var mn_paiming_fill = "";
	var shaixuan = "";
	var envelope = "";
	var nvshangjia = "";
	var nan = "";
	var tongji1 = "";
	var tuceng = "";
	var tongji2 = "";
	var biaoqianlan_shouye = "";
	var shengyinjingyin = "";
	var shengyin = "";
	var shengyin1 = "";
	var tianxie = "";
	var erweima = "";
	var huanyuan = "";
	var lianjie = "";
	var changyonglogo28 = "";
	var pengyouquan = "";
	var zhixiangxia = "";
	var QQ = "";
	var quanxian = "";
	var baocun = "";
	var suoyouwenjian = "";
	var xiangxia1 = "";
	var xinzeng = "";
	var ziyuanxhdpi = "";
	var xiazai = "";
	var zhongmingming = "";
	var yunpan = "";
	var tongji = "";
	var kefu = "";
	var weibiaoti1 = "";
	var tongxunlu = "";
	var xinyongqia = "";
	var tuichu = "";
	var aixinxiantiao = "";
	var shezhi = "";
	var jianchaxinbanben = "";
	var yuyin = "";
	var shanchu = "";
	var remen = "";
	var tupian = "";
	var xiangji = "";
	var zan = "";
	var zan1 = "";
	var like = "";
	var unlike = "";
	var pinglun = "";
	var jushoucang = "";
	var jushoucanggift = "";
	var share = "";
	var shoucangfill = "";
	var fenxiang1 = "";
	var shoucang = "";
	var bofang = "";
	var daojishi = "";
	var cuohao = "";
	var duihao = "";
	var chakan = "";
	var shoujihaoma = "";
	var mima1 = "";
	var zhanghao1 = "";
	var yanzhengma1 = "";
	var kejian = "";
	var bukejian = "";
	var gengduo1 = "";
	var gengduogongneng = "";
	var gengduo2 = "";
	var gengduo3 = "";
	var fanhui1 = "";
	var fanhui2 = "";
	var sousuo = "";
	var sousuo1 = "";
	var saoyisao1 = "";
	var xiangxia = "";
	var xiangzuo = "";
	var fangxingweixuanzhong = "";
	var fangxingxuanzhongfill = "";
	var fangxingxuanzhong = "";
	var yuanxingweixuanzhong = "";
	var yuanxingxuanzhong = "";
	var yuanxingxuanzhongfill = "";
	var danxuan_xuanzhong = "";
	var danxuan_weixuanzhong = "";
	var gengduo = "";
	var fenxiang = "";
	var zhuanfa00 = "";
	var dingwei = "";
	var dingwei1 = "";
	var jianshao = "";
	var zengjia = "";
	var qingkong = "";
	var mima = "";
	var zhanghao = "";
	var yanzhengma = "";
	var icons = {
		baoguo_hezi_o: baoguo_hezi_o,
		fuzhilianjiexian: fuzhilianjiexian,
		fuzhilianjiemian: fuzhilianjiemian,
		wenjian: wenjian,
		tianjia: tianjia,
		tianjiawenjian: tianjiawenjian,
		sousuowenjian: sousuowenjian,
		wenjianshangchuan: wenjianshangchuan,
		"zuanshi-L": "",
		dalou: dalou,
		wucan: wucan,
		zengjia1: zengjia1,
		xinsui: xinsui,
		aixin: aixin,
		jianshao2: jianshao2,
		yishoucang: yishoucang,
		yuyinshibie: yuyinshibie,
		shuruxiangce: shuruxiangce,
		tupianshibie: tupianshibie,
		shuruwenjian: shuruwenjian,
		shurupaizhao: shurupaizhao,
		tongji4: tongji4,
		pinglun1: pinglun1,
		gerentongxunlu: gerentongxunlu,
		fenxiang2: fenxiang2,
		dianzan: dianzan,
		shoucang1: shoucang1,
		sousuo2: sousuo2,
		jiancexinbanben: jiancexinbanben,
		shezhi1: shezhi1,
		guanhuaimoshi: guanhuaimoshi,
		xiugaimima: xiugaimima,
		anquantuichu: anquantuichu,
		dianhua: dianhua,
		shipintianchong: shipintianchong,
		"31dianhua": "",
		xiangxiagengduo: xiangxiagengduo,
		mubiao: mubiao,
		jianshao1: jianshao1,
		biaoqian: biaoqian,
		"gantanhao-xianxingyuankuang": "",
		duigou: duigou,
		jinengbiaoqian: jinengbiaoqian,
		jiantou_xiangyouliangci: jiantou_xiangyouliangci,
		toupiao: toupiao,
		riqi: riqi,
		gonggao: gonggao,
		tongji3: tongji3,
		paihang: paihang,
		mn_paiming_fill: mn_paiming_fill,
		"line-084": "",
		"line-085": "",
		shaixuan: shaixuan,
		envelope: envelope,
		"envelope-open": "",
		"a-4_huaban1": "",
		nvshangjia: nvshangjia,
		nan: nan,
		tongji1: tongji1,
		"weibiaoti--": "",
		tuceng: tuceng,
		tongji2: tongji2,
		biaoqianlan_shouye: biaoqianlan_shouye,
		shengyinjingyin: shengyinjingyin,
		shengyin: shengyin,
		shengyin1: shengyin1,
		tianxie: tianxie,
		"file-download-fill": "",
		"file-damage-fill": "",
		"file-excel-fill": "",
		"file-copy-fill": "",
		"file-edit-fill": "",
		"file-music-fill": "",
		"file-gif-fill": "",
		"file-history-fill": "",
		"file-lock-fill": "",
		"file-info-fill": "",
		"file-pdf-fill": "",
		"file-text-fill": "",
		"file-ppt-fill": "",
		"file-upload-fill": "",
		"file-unknow-fill": "",
		"file-word-fill": "",
		"file-search-fill": "",
		"file-transfer-fill": "",
		"file-zip-fill": "",
		"folder-2-fill": "",
		"file-warning-fill": "",
		"folder-add-fill": "",
		erweima: erweima,
		"file-reduce-fill": "",
		huanyuan: huanyuan,
		lianjie: lianjie,
		changyonglogo28: changyonglogo28,
		pengyouquan: pengyouquan,
		zhixiangxia: zhixiangxia,
		QQ: QQ,
		quanxian: quanxian,
		baocun: baocun,
		suoyouwenjian: suoyouwenjian,
		xiangxia1: xiangxia1,
		"wj-gxwj": "",
		xinzeng: xinzeng,
		ziyuanxhdpi: ziyuanxhdpi,
		xiazai: xiazai,
		zhongmingming: zhongmingming,
		yunpan: yunpan,
		"file-code-fill": "",
		"file-add-fill": "",
		tongji: tongji,
		kefu: kefu,
		weibiaoti1: weibiaoti1,
		tongxunlu: tongxunlu,
		xinyongqia: xinyongqia,
		tuichu: tuichu,
		aixinxiantiao: aixinxiantiao,
		shezhi: shezhi,
		jianchaxinbanben: jianchaxinbanben,
		yuyin: yuyin,
		shanchu: shanchu,
		remen: remen,
		tupian: tupian,
		xiangji: xiangji,
		zan: zan,
		zan1: zan1,
		like: like,
		unlike: unlike,
		"like-fill": "",
		"unlike-fill": "",
		pinglun: pinglun,
		jushoucang: jushoucang,
		jushoucanggift: jushoucanggift,
		share: share,
		shoucangfill: shoucangfill,
		fenxiang1: fenxiang1,
		shoucang: shoucang,
		bofang: bofang,
		"bell-off": "",
		daojishi: daojishi,
		cuohao: cuohao,
		duihao: duihao,
		chakan: chakan,
		shoujihaoma: shoujihaoma,
		mima1: mima1,
		zhanghao1: zhanghao1,
		yanzhengma1: yanzhengma1,
		kejian: kejian,
		bukejian: bukejian,
		gengduo1: gengduo1,
		gengduogongneng: gengduogongneng,
		gengduo2: gengduo2,
		gengduo3: gengduo3,
		"a-14Bshanchu": "",
		fanhui1: fanhui1,
		fanhui2: fanhui2,
		sousuo: sousuo,
		sousuo1: sousuo1,
		saoyisao1: saoyisao1,
		xiangxia: xiangxia,
		xiangzuo: xiangzuo,
		fangxingweixuanzhong: fangxingweixuanzhong,
		fangxingxuanzhongfill: fangxingxuanzhongfill,
		fangxingxuanzhong: fangxingxuanzhong,
		yuanxingweixuanzhong: yuanxingweixuanzhong,
		yuanxingxuanzhong: yuanxingxuanzhong,
		yuanxingxuanzhongfill: yuanxingxuanzhongfill,
		danxuan_xuanzhong: danxuan_xuanzhong,
		danxuan_weixuanzhong: danxuan_weixuanzhong,
		gengduo: gengduo,
		fenxiang: fenxiang,
		zhuanfa00: zhuanfa00,
		dingwei: dingwei,
		dingwei1: dingwei1,
		jianshao: jianshao,
		zengjia: zengjia,
		qingkong: qingkong,
		mima: mima,
		zhanghao: zhanghao,
		yanzhengma: yanzhengma
	};

	var AMpcss = /*@__PURE__*/ (function(Component) {
		function AMpcss(props) {
			Component.call(this, props);
		}

		if (Component) AMpcss.__proto__ = Component;
		AMpcss.prototype = Object.create(Component && Component.prototype);
		AMpcss.prototype.constructor = AMpcss;
		AMpcss.prototype.render = function() {
			return;
		};

		return AMpcss;
	})(Component);
	AMpcss.css = {
		"@font-face": {
			fontFamily: '"iconfont_mp"',
			src:
				"url('https://at.alicdn.com/t/c/font_3560231_mff0m4knr.ttf') format('truetype')"
		}
	};

	apivm.define("a-mpcss", AMpcss);

	var AIconfont = /*@__PURE__*/ (function(Component) {
		function AIconfont(props) {
			Component.call(this, props);
		}

		if (Component) AIconfont.__proto__ = Component;
		AIconfont.prototype = Object.create(Component && Component.prototype);
		AIconfont.prototype.constructor = AIconfont;
		AIconfont.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"text",
					{
						style:
							"font-size:" +
							(this.props.size || 16) +
							"px;color:" +
							(this.props.color || "#999") +
							";\n\t\tfont-family: " +
							(api.platform == "mp" ? "iconfont_mp" : "iconfont;") +
							";\n\t\t" +
							(this.props.style || "") +
							";font-weight:400;",
						class: "" + (this.props.class || "")
					},
					icons[this.props.name + ""]
				),
				api.platform == "mp" && apivm.h("a-mpcss", null)
			);
		};

		return AIconfont;
	})(Component);
	AIconfont.css = {
		"@font-face": {
			fontFamily: '"iconfont"',
			src:
				"url('../../components/act/a-iconfont/fonts/iconfont.ttf') format('truetype')"
		}
	};
	apivm.define("a-iconfont", AIconfont);

	var YBasePage = /*@__PURE__*/ (function(Component) {
		function YBasePage(props) {
			Component.call(this, props);
			this.data = {
				show: false, //为组件时显示隐藏
				initFrist: true, //首次初始化
				title: "" //标题栏
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					if (!this.props.dataMore) {
						if (this.headTitle != G.headTitle) {
							this.headTitle = G.headTitle;
							this.setHeader();
						}
						if (this.headTheme != (G.showHeadTheme || G.headTheme)) {
							this.headTheme = G.showHeadTheme || G.headTheme;
							this.setHeadTheme();
						}
					} else {
						if (this.props.dataMore.show != this.data.show) {
							this.data.show = this.props.dataMore.show;
							videoPlayRemoves();
							if (this.data.show) {
								if (this.data.initFrist) {
									setTimeout(function() {
										this$1.baseInit();
									}, 110);
								} else {
									this.pageRefresh();
								}
							} else {
								if (this.props._this && isFunction(this.props._this.baseClose)) {
									this.props._this.baseClose({detail: {}});
								} else {
									this.fire("baseClose");
								}
							}
						}
					}
					if (
						(!this.props.dataMore || this.data.show) &&
						G.onShowNum != this.onShowNum
					) {
						this.onShowNum = G.onShowNum;
						if (this.isShow && this.onShowNum > 0) {
							this.pageRefresh();
							if (!this.props.dataMore) {
								console.log(
									(api.winName || "") +
										"第" +
										G.onShowNum +
										"次返回：" +
										JSON.stringify(this.props._this.data.pageParam)
								);
							}
						}
						this.isShow = true;
					}
				},
				detail: function() {}
			};
		}

		if (Component) YBasePage.__proto__ = Component;
		YBasePage.prototype = Object.create(Component && Component.prototype);
		YBasePage.prototype.constructor = YBasePage;
		YBasePage.prototype.installed = function() {
			var this$1 = this;
			var that = this.props._this;
			that.data.pageParam = pageParam(that);
			that.data.pageType = that.data.pageParam.pageType || "page";
			clearTimeout(that.data.oneTask);
			that.data.oneTask = setTimeout(function() {
				console.log("当前页面参数：" + JSON.stringify(that.data.pageParam));
				G.chatInfos = [];
				G.chatInfoTask = [];
				setTimeout(function() {
					if (!this$1.props.dataMore) {
						if (!G.headTitle) {
							G.headTitle = that.data.pageParam.title || that.data.dTitle || "";
						}
					} else {
						this$1.data.title = that.data.pageParam.title || that.data.dTitle || "";
					}
				}, 400);
				if (!this$1.props.dataMore) {
					G._this = that;
					if (that.data.pageParam.token) {
						setPrefs("sys_token", decodeURIComponent(that.data.pageParam.token));
						if (!getPrefs("sys_Mobile")) {
							getLoginInfo({header: {"u-login-areaId": ""}}, function(ret, err) {});
						}
					}
					if (that.data.pageParam.areaId) {
						setPrefs("sys_aresId", that.data.pageParam.areaId);
					}
					addEventListener("sys_refresh", function(ret) {
						this$1.initConfiguration();
					});
					var traverseList = function(_obj, _option) {
						for (var key in _obj) {
							var item = _obj[key];
							if (
								isObject(item) &&
								!isArray(item) &&
								key.endsWith("Pop") &&
								isParameters(item.show) &&
								item.show
							) {
								if (_option) {
									item.show = false;
								}
								return false;
							}
						}
						return true;
					};
					addEventListener("keyback", function(ret, err) {
						if (G.alertPop.show) {
							if (G.alertPop.cancel.show) {
								G.alertPop.show = false;
							}
							return;
						}
						if (!traverseList(G, true) || !traverseList(that.data, true)) {
							return;
						}
						this$1.close();
					});
					addEventListener("swiperight", function(ret) {
						if (G.nTouchmove) {
							return;
						}
						if (!traverseList(G, false) || !traverseList(that.data, false)) {
							return;
						}
						this$1.close(1);
					});
					this$1.initConfiguration();
					this$1.baseInit();
				}
			}, 50);
		};
		YBasePage.prototype.baseInit = function() {
			var this$1 = this;

			setTimeout(
				function() {
					var detail = {first: this$1.data.initFrist};
					if (this$1.props._this && isFunction(this$1.props._this.baseInit)) {
						this$1.props._this.baseInit({detail: detail});
					} else {
						this$1.fire("init", detail);
					}
					this$1.data.initFrist = false;
				},
				!this.props.dataMore ? 300 : 0
			);
		};
		YBasePage.prototype.close = function(_type) {
			if (this.props._this && isFunction(this.props._this.close)) {
				this.props._this.close({detail: {type: _type}});
			} else {
				this.fire("close", {type: _type});
			}
		};
		YBasePage.prototype.cTitle = function() {
			this.fire("cTitle");
		};
		YBasePage.prototype.pageRefresh = function(_frist) {
			if (!this.props.dataMore) {
				this.setHeadTheme();
			}
			if (!_frist) {
				if (this.props._this && isFunction(this.props._this.pageRefresh)) {
					this.props._this.pageRefresh({detail: {back: true}});
				} else {
					this.fire("pageRefresh", {back: true});
				}
			}
		};
		YBasePage.prototype.initConfiguration = function() {
			G.pageWidth =
				platform() == "web"
					? api.winWidth > api.winHeight
						? 600
						: api.winWidth
					: api.winWidth;
			G.showCaptcha = getPrefs("enableShortAuth") == "true";
			G.appName = getPrefs("sys_systemName") || "";
			G.terminal = getPrefs("terminal") || "";
			G.appFont = getPrefs("appFont") || "heitiSimplified";
			G.appFontSize = Number(getPrefs("appFontSize") || "16");
			G.sysSign = sysSign();
			G.appTheme =
				this.props._this.data.pageParam.appTheme ||
				getPrefs("appTheme" + G.sysSign) ||
				(G.sysSign == "rd" ? "#C61414" : "#3088FE");
			var headTheme =
				this.props._this.data.headTheme ||
				this.props._this.data.pageParam.headTheme ||
				getPrefs("headTheme") ||
				"transparent";
			G.headTheme = headTheme == "appTheme" ? G.appTheme : headTheme;
			G.careMode = parseInt(G.appFontSize) > 16;
			G.systemtTypeIsPlatform = getPrefs("sys_systemType") == "platform"; //系统类型是否是平台版
			G.loginInfo = getPrefs("sys_systemLoginContact") || "";
			if (platform() == "app") {
				G.isAppReview =
					JSON.parse(getPrefs("sys_appReviewVersion") || "{}")[api.systemType] ==
					api.appVersion;
			}
			G.v = getPrefs("sys_appVersion") || "";

			G.uId = getPrefs("sys_Id") || "";
			G.userId = getPrefs("sys_UserID") || "";
			G.userName = getPrefs("sys_UserName") || "";
			G.userImg = getPrefs("sys_AppPhoto") || "";
			G.areaId = getPrefs("sys_aresId") || "";
			G.specialRoleKeys = JSON.parse(getPrefs("sys_SpecialRoleKeys") || "[]");
			G.isAdmin =
				G.userId == "1" ||
				getItemForKey("dc_admin", G.specialRoleKeys) ||
				getItemForKey("admin", G.specialRoleKeys);
			G.grayscale = getPrefs("sys_grayscale") || "";
			var watermark = getPrefs("sys_watermark") || "";
			if (platform() == "app") {
				api.screenLayerFilter({
					region: "window",
					sat: G.grayscale == "true" ? 0 : 1
				});
			}
			if (watermark && watermark != "false") {
				var t = watermark
					.replace("user", G.userName)
					.replace("phone", getPrefs("sys_Mobile") || "")
					.replace("true", G.appName);
				if (t) {
					G.watermark =
						tomcatAddress() +
						"utils2/watermark?s=" +
						G.appFontSize +
						"&t=" +
						t +
						"&w=" +
						G.pageWidth +
						"&h=" +
						api.winHeight;
				}
			} else {
				G.watermark = false;
			}

			this.pageRefresh(true);
			if (platform() == "web") {
				var fontStyleId = "fontStyle";
				if (document.getElementById(fontStyleId)) {
					//存在的时候先删除
					document
						.getElementById(fontStyleId)
						.parentNode.removeChild(document.getElementById(fontStyleId));
				}
				var fontStyle = document.createElement("style");
				fontStyle.id = fontStyleId;
				switch (G.appFont) {
					case "shusongSimplified":
						fontStyle.innerText =
							"@font-face{font-family: shusongSimplified;src: url('../../res/fz_shusong_simplified.ttf')}";
						break;
					case "kaitiSimplified":
						fontStyle.innerText =
							"@font-face{font-family: kaitiSimplified;src: url('../../res/fz_kaiti_simplified.ttf')}";
						break;
					case "heitiTraditional":
						fontStyle.innerText =
							"@font-face{font-family: heitiTraditional;src: url('../../res/fz_heiti_traditional.ttf')}";
						break;
				}

				document.getElementsByTagName("head")[0].appendChild(fontStyle);
				this.fitWidth();
			}
		};
		YBasePage.prototype.fitWidth = function() {
			if (platform() == "web") {
				$("body").style.width = "100%";
				$("body").style.maxWidth = G.pageWidth + "px";
				$("body").style.minWidth = "300px";
				$("body").style.margin = "auto";
				$("body").style.position = "relative";
			}
		};
		YBasePage.prototype.isColorDarkOrLight = function(hexcolor) {
			try {
				var colors = colorRgba(hexcolor).match(/\d+\.?\d*/g);
				var red = colors[1],
					green = colors[2],
					blue = colors[3],
					brightness;
				brightness = (red * 299 + green * 587 + blue * 114) / 255000;
				return brightness >= 0.5 ? "light" : "dark";
			} catch (e) {
				return "";
			}
		};
		YBasePage.prototype.setHeadTheme = function() {
			var colorDarkOrLight = this.isColorDarkOrLight(
				this.headTheme == "transparent" ? "#FFF" : this.headTheme
			);
			G.headColor = colorDarkOrLight == "dark" ? "#FFF" : "#333";
			setStatusBarStyle({
				style: colorDarkOrLight == "light" ? "dark" : "light",
				color: "rgba(0,0,0,0)"
			});
		};
		YBasePage.prototype.setHeader = function() {
			if (this.props.dataMore) {
				return;
			}
			this.data.title = G.headTitle;
			if (platform() == "web") {
				if (window.parent) {
					window.parent.document.title = this.data.title;
				} else {
					document.title = this.data.title;
				}
			} else if (platform() == "mp") {
				wx.setNavigationBarTitle({title: this.data.title});
			}
		};
		YBasePage.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "page_box",
					class:
						"page_box page_bg " +
						(G.grayscale == "true" ? "filterGray" : "filterNone"),
					style: {display: !this.props.dataMore || this.data.show ? "flex" : "none"}
				},
				apivm.h(
					"view",
					{class: "watermark_box"},
					G.watermark &&
						apivm.h("image", {
							class: "xy_100",
							src: G.watermark,
							mode: "aspectFill",
							thumbnail: "false"
						})
				),
				G.appTheme && [
					apivm.h(
						"view",
						{class: "xy_100"},

						apivm.h(
							"view",
							{
								class: "flex_shrink",
								style: {
									display:
										!this.props.dataMore || !this.props.dataMore.closeH ? "flex" : "none"
								}
							},
							!this.props.closeH &&
								(this.props.titleBox ||
									this.props.back ||
									this.props.more ||
									showHeader()) &&
								apivm.h(
									"view",
									{
										style:
											"height:auto;padding-top:" +
											headerTop() +
											"px;background:" +
											(this.props._this.data.headTheme || G.headTheme)
									},
									apivm.h(
										"view",
										{class: "header_warp flex_row"},
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.cTitle();
												},
												class: "header_main xy_center",
												style: this.props.titleStyle || null
											},
											this.props.titleBox && this.props.children.length >= 3
												? [this.props.children[2]]
												: [
														apivm.h(
															"text",
															{
																style:
																	loadConfiguration(4) + "font-weight: 600;color:" + G.headColor
															},
															this.data.title
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "header_left_box"},
											this.props.back && this.props.children.length >= 1
												? [this.props.children[0]]
												: [
														apivm.h(
															"view",
															{
																onClick: function() {
																	return this$1.close();
																},
																class: "header_btn",
																style: {
																	display:
																		showHeader() && this.props._this.data.pageType == "page"
																			? "flex"
																			: "none"
																}
															},
															apivm.h("a-iconfont", {
																name: "fanhui1",
																color: G.headColor,
																size: G.appFontSize + 1
															})
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "header_right_box"},
											this.props.more &&
												this.props.children.length >= 2 &&
												this.props.children[1]
										)
									)
								)
						),
						this.props.children.length >= 4 && this.props.children[3]
					),
					this.props.children.length >= 5
						? this.props.children.filter(function(item, index) {
								return index >= 4;
						  })
						: null,
					!this.props.dataMore && [
						apivm.h("previewer-img", {dataMore: G.imgPreviewerPop}),
						apivm.h("areas", {dataMore: G.areasPop}),
						apivm.h("select-item", {dataMore: G.selectPop}),
						apivm.h("z-actionSheet", {dataMore: G.actionSheetPop}),
						apivm.h("z-alert", {dataMore: G.alertPop})
					]
				]
			);
		};

		return YBasePage;
	})(Component);
	YBasePage.css = {
		".avm-toast,.avm-confirm-mask": {zIndex: "999"},
		element: {width: "auto", height: "auto"},
		".flex_shrink,div": {flexShrink: "0", WebkitOverflowScrolling: "touch"},
		".flex_w": {flex: "1", width: "1px"},
		".flex_h": {flex: "1", height: "1px"},
		".flex_row": {flexDirection: "row", alignItems: "center"},
		".xy_center": {alignItems: "center", justifyContent: "center"},
		".xy_100": {width: "100%", height: "100%"},
		".page_box": {
			position: "absolute",
			zIndex: "999",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		},
		".page_bg": {background: "#FFF"},
		".header_warp": {height: "44px"},
		".header_main": {
			height: "100%",
			flex: "1",
			flexDirection: "row",
			padding: "0 44px"
		},
		".header_left_box": {
			position: "absolute",
			zIndex: "999",
			left: "0",
			width: "auto",
			height: "44px"
		},
		".header_right_box": {
			position: "absolute",
			zIndex: "999",
			right: "0",
			width: "auto",
			height: "44px"
		},
		".header_btn": {
			width: "auto",
			height: "100%",
			minWidth: "36px",
			padding: "0 12px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".header_btn:active": {background: "rgba(0,0,0,0.05)"},
		".filterNone": {filter: "none"},
		".filterGray": {filter: "grayscale(1)"},
		".watermark_box": {
			position: "absolute",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		}
	};
	apivm.define("y-base-page", YBasePage);

	var Root = /*@__PURE__*/ (function(Component) {
		function Root(props) {
			Component.call(this, props);
			this.data = {
				pageParam: {},
				pageType: "",
				dTitle: "领导决策",
				MG: !this.props.dataMore ? G : null,
				emptyBox: {
					type: "load",
					text: ""
				},

				pageNo: 1,
				pageSize: 15,
				refreshPageSize: 0,
				titleBox: {
					key: "",
					data: [
						{
							key: G.sysSign == "rd" ? "suggest" : "proposal",
							value: (G.sysSign == "rd" ? "建议" : "提案") + "实况"
						},
						{
							key: G.sysSign == "rd" ? "npcMemberScreenView" : "cppccMember",
							value: (G.sysSign == "rd" ? "代表" : "委员") + "信息"
						},
						{key: "system", value: "系统运行实况"}
					]
				},

				title: "",
				listData: []
			};
		}

		if (Component) Root.__proto__ = Component;
		Root.prototype = Object.create(Component && Component.prototype);
		Root.prototype.constructor = Root;
		Root.prototype.onShow = function() {
			G.onShowNum++;
		};
		Root.prototype.baseInit = function(ref) {
			var detail = ref.detail;

			this.id = this.data.pageParam.id || "";
			this.switchTab(this.data.titleBox.data[0]);
		};
		Root.prototype.pageRefresh = function() {
			this.getData(0);
		};
		Root.prototype.close = function() {
			if (this.props.dataMore) {
				this.props.dataMore.show = false;
			} else {
				closeWin();
			}
		};
		Root.prototype.switchTab = function(_item) {
			if (_item) {
				if (this.data.titleBox.key == _item.key) {
					return;
				}
				this.data.titleBox.key = _item.key;
				this.data.listData.forEach(function(obj) {
					if (platform() == "app" && obj.url) {
						api.cancelAjax({tag: obj.url});
					}
				});
			}
			var barCharts = {
				show: true,
				name: "",
				chart: {
					legend: {show: false},
					grid: {
						left: "1%",
						right: "1%",
						bottom: "1%",
						top: "10%",
						containLabel: true
					},
					tooltip: {trigger: "axis"},
					xAxis: {
						type: "category",
						axisLine: {show: false},
						axisTick: {show: false},
						data: ["30岁以下", "30~40岁", "40~50岁", "50~60岁", "60岁以上"],
						axisLabel: {
							show: true,
							interval: 0,
							rotate: 45,
							fontSize: 12,
							color: "#333",
							interval: "auto"
						}
					},

					yAxis: {
						type: "value",
						axisLine: {show: false},
						axisTick: {show: false},
						splitLine: {lineStyle: {type: "solid", color: "#F8F9FA"}},
						axisLabel: {show: true}
					},

					series: [
						{
							type: "bar",
							name: "数量",
							label: {normal: {show: true, position: "top"}},
							barWidth: 12, //柱图宽度
							itemStyle: {
								normal: {
									color: {
										x: 0,
										y: 0,
										x2: 0,
										y2: 1,
										colorStops: [
											{offset: 0, color: "#F6631C"},
											{offset: 1, color: "#FFD7C4"}
										]
									}
								}
							},
							data: [180, 222, 150, 367, 111]
						}
					]
				}
			};

			var pieCharts = {
				show: true,
				name: "",
				chart: {
					tooltip: {
						trigger: "item",
						formatter: "{b} : {c} ({d}%)"
					},

					legend: {
						bottom: "10",
						left: "center",
						data: ["文化", "社会", "生态文明", "政治", "文化1", "社会1"]
					},

					color: [
						"#50C614",
						"#559FFF",
						"#3088FE",
						"#734CFF",
						"#F6931C",
						"#F6631C",
						"#ECE522",
						"#61EC22"
					],
					series: [
						{
							type: "pie",
							radius: [35, 60],
							center: ["50%", "100"],
							avoidLabelOverlap: false,
							label: {
								normal: {show: true, formatter: "{b}\n{c} ({d}%)"},
								emphasis: {show: true, textStyle: {fontSize: "13", color: "#6670AB"}} //文字至于中间时，这里需为true
							},
							data: [
								{value: 1, name: "文化"},
								{value: 7, name: "社会"},
								{value: 4, name: "生态文明"},
								{value: 3, name: "政治"},
								{value: 1, name: "文化1"},
								{value: 7, name: "社会1"},
								{value: 4, name: "生态文明1"},
								{value: 3, name: "政治1"}
							]
						}
					]
				}
			};

			var lineCharts = {
				show: true,
				name: "",
				chart: {
					color: ["#F55130"], //线条颜色
					grid: {
						left: "5%",
						right: "5%",
						bottom: "5%",
						top: "10%",
						containLabel: true
					},
					tooltip: {trigger: "axis"},
					xAxis: {
						type: "category",
						data: ["一月", "一月", "一月", "一月", "一月", "一月"],
						axisLabel: {show: true, textStyle: {color: "#384D76", fontSize: 12}},
						axisTick: {show: false},
						axisLine: {show: false}
					},

					yAxis: {
						type: "value",
						axisLine: {show: false},
						axisTick: {show: false},
						splitLine: {lineStyle: {type: "solid", color: "#fafafa"}},
						axisLabel: {show: true, textStyle: {color: "#384D76", fontSize: 10}},
						axisTick: {show: false}
					},

					series: [
						{
							type: "line",
							name: "次数",
							smooth: true,
							showSymbol: false,
							itemStyle: {
								normal: {
									areaStyle: {type: "default"},
									color: {
										x: 0,
										y: 0,
										x2: 0,
										y2: 1,
										colorStops: [
											{offset: 0, color: "#000"},
											{offset: 0.5, color: "rgba(0, 0, 0, 0.7)"},
											{offset: 1, color: "rgba(0, 0, 0, 0)"}
										]
									}
								}
							},
							data: [11, 44, 23, 55, 6, 77]
						},
						{
							type: "bar",
							name: "人数",
							label: {normal: {show: true, position: "top"}},
							barWidth: 12, //柱图宽度
							itemStyle: {
								normal: {
									color: {
										x: 0,
										y: 0,
										x2: 0,
										y2: 1,
										colorStops: [
											{offset: 0, color: "#000"},
											{offset: 1, color: "#000"}
										]
									}
								}
							},
							data: [180, 222, 150, 367, 111]
						}
					]
				}
			};

			switch (this.data.titleBox.key) {
				case "suggest":
					this.data.listData = [
						{
							show: false,
							type: "label",
							key: "label",
							name: "",
							url: "suggestion_status_count",
							data: []
						},
						{
							show: false,
							type: "newest",
							key: "newest",
							name: "最新提交",
							nameType: 1,
							url: "suggestion_newest",
							data: []
						},
						{
							show: false,
							bottom: true,
							type: "hotword",
							key: "hotword",
							name: "热词分析",
							nameType: 1,
							url: "suggestion_hot_word",
							data: []
						},
						{
							show: false,
							bottom: true,
							type: "chart_list",
							showAll: false,
							key: "count",
							name: "建议状态统计",
							nameType: 2,
							url: "",
							head: [
								{name: "类别", key: "amount", max: 0, color: "#F6631C", tc: "#F6631C"}
							],
							data: []
						},
						{
							show: false,
							bottom: true,
							type: "charts",
							key: "charts1",
							name: "建议类别",
							nameType: 2,
							url: "suggestion_theme",
							charts: JSON.parse(JSON.stringify(barCharts)),
							height: 255
						},
						{
							show: false,
							bottom: true,
							type: "charts",
							key: "charts2",
							name: "提交人结构分布",
							nameType: 2,
							url: "suggestion_element",
							charts: JSON.parse(JSON.stringify(pieCharts)),
							height: 255
						},
						{
							show: false,
							bottom: true,
							type: "list",
							showAll: false,
							key: "over",
							name: "已接收建议",
							nameType: 2,
							url: "suggestion_receive",
							data: []
						},
						{
							show: false,
							bottom: true,
							type: "rankUnit",
							showAll: false,
							key: "office_top",
							name: "承办单位排名",
							nameType: 2,
							url: "suggestion_office_top",
							data: []
						},
						{
							show: false,
							bottom: true,
							type: "chart_list",
							showAll: false,
							key: "team",
							name: "各代表团建议数统计",
							nameType: 2,
							url: "suggestion_team",
							data: []
						}
					];

					break;
				case "proposal":
					this.data.listData = [
						{
							show: false,
							type: "label",
							key: "label",
							name: "",
							url: "proposal_status_count",
							data: []
						},
						{
							show: false,
							type: "newest",
							key: "newest",
							name: "最新提交",
							nameType: 1,
							url: "proposal_newest",
							data: []
						},
						{
							show: false,
							bottom: true,
							type: "hotword",
							key: "hotword",
							name: "热词分析",
							nameType: 1,
							url: "proposal_hot_word",
							data: []
						},
						{
							show: false,
							bottom: true,
							type: "chart_list",
							showAll: false,
							key: "count",
							name: "提案状态统计",
							nameType: 2,
							url: "",
							head: [
								{name: "类别", key: "amount", max: 0, color: "#F6631C", tc: "#F6631C"}
							],
							data: []
						},
						{
							show: false,
							bottom: true,
							type: "charts",
							key: "charts1",
							name: "提案类别",
							nameType: 2,
							url: "proposal_theme",
							charts: JSON.parse(JSON.stringify(barCharts)),
							height: 255
						},
						{
							show: false,
							bottom: true,
							type: "charts",
							key: "charts2",
							name: "提交人专委会分布",
							nameType: 2,
							url: "proposal_committee",
							charts: JSON.parse(JSON.stringify(pieCharts)),
							height: 255
						},
						{
							show: false,
							bottom: true,
							type: "list",
							showAll: false,
							key: "over",
							name: "已接收提案",
							nameType: 2,
							url: "proposal_receive",
							data: []
						},
						{
							show: false,
							bottom: true,
							type: "rankUnit",
							showAll: false,
							key: "office_top",
							name: "承办单位排名",
							nameType: 2,
							url: "proposal_office_top",
							data: []
						},
						{
							show: false,
							bottom: true,
							type: "chart_list",
							showAll: false,
							key: "team",
							name: "各界别提案数统计",
							nameType: 2,
							url: "proposal_team",
							data: []
						}
					];

					break;
				case "npcMemberScreenView":
					this.data.listData = [
						{
							show: false,
							bottom: true,
							type: "label",
							key: "label",
							name: "",
							url: "npc_full_view",
							data: []
						},
						{
							show: false,
							bottom: true,
							type: "charts",
							key: "charts1",
							name: "代表党派分布",
							nameType: 2,
							url: "npc_party_view",
							charts: JSON.parse(JSON.stringify(barCharts)),
							height: 255
						},
						{
							show: false,
							bottom: true,
							type: "rankCom",
							showAll: false,
							key: "",
							name: "代表所属专委会分布",
							nameType: 2,
							url: "npc_committee_view",
							data: []
						},
						{
							show: false,
							bottom: true,
							type: "rankPer",
							showAll: false,
							key: "",
							name: "代表履职排行榜",
							nameType: 2,
							url: "performduties_rank",
							data: []
						},
						{
							show: false,
							bottom: true,
							type: "chart_list",
							showAll: false,
							key: "",
							name: "代表代表团分布",
							nameType: 2,
							url: "npc_delegation_view",
							head: [{name: "amount", key: "代表团", max: 0, color: "#50C614"}],
							data: []
						},
						{
							show: false,
							type: "charts",
							key: "charts2",
							name: "性别占比",
							nameType: 1,
							url: "npc_sex_view",
							charts: JSON.parse(JSON.stringify(pieCharts)),
							height: 255
						},
						{
							show: false,
							bottom: true,
							type: "charts",
							key: "charts3",
							name: "学历占比",
							nameType: 1,
							url: "npc_education_view",
							charts: JSON.parse(JSON.stringify(pieCharts)),
							height: 255
						},
						{
							show: false,
							bottom: true,
							type: "chart_list",
							showAll: false,
							key: "",
							name: "年龄占比",
							nameType: 2,
							url: "npc_age_view",
							head: [{name: "amount", key: "年龄", max: 0, color: "#F6631C"}],
							data: []
						}
					];

					break;
				case "cppccMember":
					this.data.listData = [
						{
							show: false,
							bottom: true,
							type: "label",
							key: "label",
							name: "",
							url: "cppcc_full_view",
							data: []
						},
						{
							show: false,
							bottom: true,
							type: "charts",
							key: "charts1",
							name: "委员党派分布",
							nameType: 2,
							url: "cppcc_party_view",
							charts: JSON.parse(JSON.stringify(barCharts)),
							height: 255
						},
						{
							show: false,
							bottom: true,
							type: "rankCom",
							showAll: false,
							key: "",
							name: "委员所属委员会分布",
							nameType: 2,
							url: "cppcc_committee_view",
							data: []
						},
						{
							show: false,
							bottom: true,
							type: "rankPer",
							showAll: false,
							key: "",
							name: "委员履职排行榜",
							nameType: 2,
							url: "performduties_rank",
							data: []
						},
						{
							show: false,
							bottom: true,
							type: "chart_list",
							showAll: false,
							key: "",
							name: "委员界别分布",
							nameType: 2,
							url: "cppcc_sector_view",
							head: [{name: "amount", key: "代表团", max: 0, color: "#50C614"}],
							data: []
						},
						{
							show: false,
							type: "charts",
							key: "charts2",
							name: "性别占比",
							nameType: 1,
							url: "cppcc_sex_view",
							charts: JSON.parse(JSON.stringify(pieCharts)),
							height: 255
						},
						{
							show: false,
							bottom: true,
							type: "charts",
							key: "charts3",
							name: "学历占比",
							nameType: 1,
							url: "cppcc_education_view",
							charts: JSON.parse(JSON.stringify(pieCharts)),
							height: 255
						},
						{
							show: false,
							bottom: true,
							type: "chart_list",
							showAll: false,
							key: "",
							name: "年龄占比",
							nameType: 2,
							url: "cppcc_age_view",
							head: [{name: "amount", key: "年龄", max: 0, color: "#F6631C"}],
							data: []
						}
					];

					break;
				case "system":
					if (G.sysSign == "rd") {
						this.data.listData = [
							{
								show: false,
								type: "label",
								key: "",
								label: "用户数据概览",
								url: "user_count_about",
								data: []
							},
							{
								show: false,
								bottom: true,
								type: "label",
								key: "",
								label: "登录数据概览",
								url: "user_login_about",
								data: []
							},
							{
								show: false,
								bottom: true,
								type: "charts",
								key: "charts1",
								name: "人大代表活跃情况",
								nameType: 2,
								url: "npc_member_month_login",
								color: "#F6931C",
								head: [
									{name: "登录人数", color: "#F6931C"},
									{name: "登录次数", color: "#F6931C", type: "line"}
								],
								charts: JSON.parse(JSON.stringify(lineCharts)),
								height: 255
							},
							{
								show: false,
								bottom: true,
								type: "charts",
								key: "charts2",
								name: "系统用户活跃情况",
								nameType: 2,
								url: "user_month_login_about",
								color: "#559FFF",
								head: [
									{name: "登录人数", color: "#559FFF"},
									{name: "登录次数", color: "#559FFF", type: "line"}
								],
								charts: JSON.parse(JSON.stringify(lineCharts)),
								height: 255
							},
							{
								show: false,
								bottom: true,
								type: "rankMem",
								showAll: false,
								key: "",
								name: "人大代表活跃排名",
								nameType: 2,
								url: "npc_member_online",
								data: []
							},
							{
								show: false,
								bottom: true,
								type: "rankComUse",
								showAll: false,
								key: "",
								name: "各机关单位使用系统情况",
								nameType: 2,
								url: "committee_user_use",
								data: []
							}
						];
					} else {
						this.data.listData = [
							{
								show: false,
								type: "label",
								key: "",
								label: "用户数据概览",
								url: "user_count_about",
								data: []
							},
							{
								show: false,
								bottom: true,
								type: "label",
								key: "",
								label: "登录数据概览",
								url: "user_login_about",
								data: []
							},
							{
								show: false,
								bottom: true,
								type: "charts",
								key: "charts1",
								name: "政协委员活跃情况",
								nameType: 2,
								url: "cppcc_member_month_login",
								color: "#F6931C",
								head: [
									{name: "登录人数", color: "#F6931C"},
									{name: "登录次数", color: "#F6931C", type: "line"}
								],
								charts: JSON.parse(JSON.stringify(lineCharts)),
								height: 255
							},
							{
								show: false,
								bottom: true,
								type: "charts",
								key: "charts2",
								name: "系统用户活跃情况",
								nameType: 2,
								url: "user_month_login_about",
								color: "#559FFF",
								head: [
									{name: "登录人数", color: "#559FFF"},
									{name: "登录次数", color: "#559FFF", type: "line"}
								],
								charts: JSON.parse(JSON.stringify(lineCharts)),
								height: 255
							},
							{
								show: false,
								bottom: true,
								type: "rankMem",
								showAll: false,
								key: "",
								name: "政协委员活跃排名",
								nameType: 2,
								url: "cppcc_member_online",
								data: []
							},
							{
								show: false,
								bottom: true,
								type: "rankComUse",
								showAll: false,
								key: "",
								name: "各机关单位使用系统情况",
								nameType: 2,
								url: "committee_user_use",
								data: []
							}
						];
					}
					break;
				default:
					this.data.listData = [];
					break;
			}

			this.data.emptyBox.type = "load";
			this.pageRefresh();
		};
		Root.prototype.getData = function(_type) {
			var this$1 = this;

			//获取标题信息
			ajax(
				{u: appUrl() + "screenView/loadConfig/" + this.data.titleBox.key},
				"loadTitle",
				function(ret) {
					hideProgress();
					var code = ret ? ret.code || 0 : 0;
					var screen =
						ret && ret.data && ret.data.screen ? ret.data.screen || {} : {};
					if (code != 200 || !screen.screenCode) {
						this$1.data.listData = [];
						this$1.data.emptyBox.type = ret ? "1" : "2";
						this$1.data.emptyBox.text =
							ret && ret.code != 200 ? ret.message || ret.data : "";
						return;
					}
					this$1.data.emptyBox.type = "";
					this$1.data.emptyBox.text = "";
					this$1.data.title = screen.screenName || "";
				},
				"标题"
			);
			this.data.listData.forEach(function(_item) {
				if (_item.url) {
					this$1.getUrlData(_item, _item.url);
				}
			});
		};
		Root.prototype.getUrlData = function(_item, _url) {
			var this$1 = this;

			ajax(
				{u: appUrl() + "screenView/loadData/" + _url},
				_url,
				function(ret) {
					var code = ret ? ret.code || 0 : 0;
					var data = ret ? ret.data || "" : "";
					if (code != 200 || !data) {
						return;
					}
					function getMaxNum(_list, _key) {
						var maxNum = 0;
						_list.forEach(function(obj) {
							if (isNumber(obj[_key]) && obj[_key] > maxNum) {
								maxNum = obj[_key];
							}
						});
						return maxNum;
					}
					switch (_url) {
						case "suggestion_status_count": //建议状态数量
						case "proposal_status_count": //提案状态数量
							var elements = data.elements || [];
							_item.data = elements.filter(function(obj) {
								return obj.key == "amount" || obj.key == "todayTotal";
							});
							_item.show = _item.data.length ? true : false;
							var count = getItemForKey("count", this$1.data.listData);
							if (!count) {
								return;
							}
							var countData = elements.filter(function(obj) {
									return obj.key != "amount" && obj.key != "todayTotal";
								}),
								newList = [];
							count.head.forEach(function(obj) {
								obj.max = getMaxNum(countData, obj.key);
							});
							countData.forEach(function(obj) {
								var newItem = {
									title: obj.name || "",
									value: []
								};

								count.head.forEach(function(obj2) {
									newItem.value.push({
										value: obj[obj2.key],
										schedule: (obj[obj2.key] / obj2.max) * 100
									});
								});
								newList.push(newItem);
							});
							count.data = newList;
							count.show = count.data.length ? true : false;
							break;
						case "suggestion_newest": //建议最新提交
						case "proposal_newest": //提案最新提交
						case "suggestion_receive": //已接收建议
						case "proposal_receive": //已接收提案
							var elements = data.elements || [],
								newList = [];
							elements.forEach(function(_nItem) {
								newList.push({
									title: _nItem.title || "",
									name: _nItem.suggestionUserName || "",
									time: _nItem.submitDate || _nItem.suggestionDate || ""
								});
							});
							_item.data = newList;
							_item.show = _item.data.length ? true : false;
							break;
						case "suggestion_hot_word": //热词
						case "proposal_hot_word": //热词
							var elements = data.elements || [];
							elements.forEach(function(_nItem, _nIndex) {
								_nItem.color = [
									G.appTheme,
									"#F6931C",
									G.appTheme,
									"#50C614",
									"#559FFF"
								][_nIndex % 5];
							});
							_item.data = elements;
							_item.show = _item.data.length ? true : false;
							break;
						case "suggestion_theme": //建议类别 竖柱状图
						case "proposal_theme": //提案类别 竖柱状图
						case "npc_party_view": //建议类别 竖柱状图
						case "cppcc_party_view": //委员党派 竖柱状图
							var elements = data.elements || [];
							var nowCharts = _item.charts.chart;
							_item.charts.name = _url;
							nowCharts.series[0].itemStyle.normal.color.colorStops[0].color =
								"#3088FE";
							nowCharts.series[0].itemStyle.normal.color.colorStops[1].color =
								"#C6DEFB";
							nowCharts.xAxis.data = [];
							nowCharts.series[0].data = [];
							elements.forEach(function(_nItem) {
								nowCharts.xAxis.data.push(_nItem.name);
								nowCharts.series[0].data.push(_nItem.amount);
							});
							_item.show = elements.length ? true : false;
							break;
						case "suggestion_element": //提交人结构分布 饼图
						case "proposal_committee": //提交人专委会分布 饼图
						case "npc_sex_view": //性别占比 饼图
						case "cppcc_sex_view": //性别占比 饼图
						case "npc_education_view": //学历占比 饼图
						case "cppcc_education_view": //学历占比 饼图
							var elements = data.elements || [];
							var nowCharts = _item.charts.chart;
							_item.charts.name = _url;
							nowCharts.legend.data = [];
							nowCharts.series[0].data = [];
							elements.forEach(function(_nItem) {
								nowCharts.legend.data.push(_nItem.name);
								nowCharts.series[0].data.push({
									value: _nItem.amount,
									name: _nItem.name
								});
							});
							_item.height = 230 + (nowCharts.legend.data.length / 2.5) * 20;
							_item.show = elements.length ? true : false;
							break;
						case "user_month_login_about": //系统用户活跃情况 线图
						case "npc_member_month_login": //人大代表活跃情况 线图
						case "cppcc_member_month_login": //政协委员活跃情况 线图
							var elements = data.elements || [];
							var nowCharts = _item.charts.chart;
							nowCharts.series[0].itemStyle.normal.color.colorStops[0].color =
								_item.color;
							nowCharts.series[0].itemStyle.normal.color.colorStops[1].color = colorRgba(
								_item.color,
								0.7
							);
							nowCharts.series[0].itemStyle.normal.color.colorStops[2].color = colorRgba(
								_item.color,
								0.0
							);
							nowCharts.series[1].itemStyle.normal.color.colorStops[0].color =
								_item.color;
							nowCharts.series[1].itemStyle.normal.color.colorStops[1].color = colorRgba(
								_item.color,
								0.3
							);
							_item.charts.name = _url;
							(nowCharts.xAxis.data = []),
								(nowCharts.series[0].data = []),
								(nowCharts.series[1].data = []);
							elements.forEach(function(_nItem) {
								nowCharts.xAxis.data.push(_nItem.name);
								nowCharts.series[0].data.push(_nItem.amount);
								nowCharts.series[1].data.push(_nItem.userAmount);
							});
							_item.show = elements.length ? true : false;
							break;
						case "suggestion_office_top": //承办单位排名
						case "proposal_office_top": //承办单位排名
						case "performduties_rank": //代表履职排行榜
						case "npc_committee_view": //代表所属专委会分布
						case "cppcc_committee_view": //委员所属委员会分布
						case "npc_member_online": //人大代表活跃排名
						case "cppcc_member_online": //政协委员活跃情况
						case "committee_user_use": //各机关单位使用系统情况
							var elements = data.elements || [],
								newList = [];
							elements.forEach(function(_nItem) {
								newList.push({
									name: _nItem.name,
									amount: _nItem.amount
								});
							});
							_item.data = newList;
							_item.show = _item.data.length ? true : false;
							break;
						case "suggestion_team": //各代表团建议数统计
						case "proposal_team": //各界别提案数统计
						case "npc_delegation_view": //代表代表团分布
						case "cppcc_sector_view": //委员界别分布
						case "npc_age_view": //年龄占比
						case "cppcc_age_view": //年龄占比
							var elements = data.elements || [],
								newList = [];
							var head = data.head || [],
								newHead = [];
							if (!head.length) {
								head = _item.head || [];
							}
							var allMax = 0;
							head.forEach(function(_nItem) {
								var nowMax = getMaxNum(elements, _nItem.name);
								if (isNumber(nowMax) && nowMax > allMax) {
									allMax = nowMax;
								}
								newHead.push({
									name: _nItem.key,
									key: _nItem.name,
									max: 0,
									color:
										_nItem.color ||
										(_nItem.name == "personAmount" ? "#F6931C" : "#50C614"),
									tc: _nItem.tc
								});
							});
							newHead.forEach(function(obj) {
								obj.max = allMax;
							});
							_item.head = newHead;
							elements.forEach(function(obj) {
								var newItem = {
									title: obj.name || "",
									value: []
								};

								_item.head.forEach(function(obj2) {
									newItem.value.push({
										value: obj[obj2.key],
										schedule: (obj[obj2.key] / obj2.max) * 100
									});
								});
								newList.push(newItem);
							});
							_item.data = newList;
							_item.show = _item.data.length ? true : false;
							break;
						case "npc_full_view": //代表统计
						case "cppcc_full_view": //委员统计
						case "user_count_about": //用户数据
						case "user_login_about": //登录数据
							var elements = data.elements || [];
							_item.data = elements;
							_item.show = _item.data.length ? true : false;
							break;
					}
				},
				_item.name || _url
			);
		};
		Root.prototype.loadMore = function() {
			if (
				(this.data.emptyBox.text == LOAD_MORE ||
					this.data.emptyBox.text == NET_ERR) &&
				this.data.pageNo != 1
			) {
				this.data.emptyBox.text = LOAD_ING;
				this.getData(this.data.listData.length ? 1 : 0);
			}
		};
		Root.prototype.openShowAll = function(_item) {
			_item.showAll = !_item.showAll;
		};
		Root.prototype.openAll = function(_item) {
			alert({
				title: _item.name,
				msg: _item.amount + "",
				buttons: ["确定"],
				closeType: "4"
			});
		};
		Root.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"y-base-page",
				{_this: this, dataMore: this.props.dataMore},
				apivm.h("view", null),
				apivm.h("view", null),
				apivm.h("view", null),
				apivm.h(
					"y-scroll-view",
					{_this: this, refresh: true},
					apivm.h(
						"view",
						{class: "decisions_title_box"},
						this.data.titleBox.data.map(function(item, index) {
							return [
								apivm.h(
									"view",
									{style: "padding:5px;width:50%;"},
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.switchTab(item);
											},
											class: "rounded_shadow",
											style:
												"width:100%;padding:12px 10px 12px 20px;background:" +
												(this$1.data.titleBox.key == item.key
													? colorRgba(G.appTheme, 0.8)
													: "#FFF") +
												";"
										},
										apivm.h("image", {
											class: "decisions_title_img",
											src:
												shareAddress(1) +
												"image/icon_tj_" +
												(this$1.data.titleBox.key == item.key ? "sel" : "del") +
												"_" +
												G.sysSign +
												".png"
										}),
										apivm.h(
											"text",
											{
												style:
													loadConfiguration() +
													"color:" +
													(this$1.data.titleBox.key == item.key ? "#FFFFFF" : "#333333") +
													";font-weight: 600;position: relative;"
											},
											item.value
										)
									)
								)
							];
						})
					),
					apivm.h(
						"view",
						null,
						!this.data.emptyBox.type &&
							apivm.h(
								"view",
								{class: "flex_row xy_center", style: "padding:20px 14px 0;"},
								apivm.h("image", {
									style: "width:50px;height:9px;flex-shrink:0;",
									src: shareAddress(1) + "image/icon_tj_tleft_" + G.sysSign + ".png"
								}),
								apivm.h(
									"view",
									{style: "flex:1;margin:0 10px;"},
									apivm.h(
										"text",
										{
											style:
												loadConfiguration(4) +
												"color:#333;font-weight: 600;text-align: center;"
										},
										this.data.title
									),
									apivm.h(
										"view",
										{
											class: "xy_center",
											style: "position:absolute;bottom: -2px;left:0;right:0;"
										},
										apivm.h("image", {
											style: "width:70%;height:1px;",
											src: shareAddress(1) + "image/icon_tj_tbottom_" + G.sysSign + ".png"
										})
									)
								),
								apivm.h("image", {
									style: "width:50px;height:9px;flex-shrink:0;",
									src: shareAddress(1) + "image/icon_tj_tright_" + G.sysSign + ".png"
								})
							)
					),
					apivm.h(
						"view",
						null,
						!this.data.emptyBox.type &&
							this.data.listData.map(function(item, index) {
								return (
									item.show && [
										apivm.h(
											"view",
											null,
											item.name && [
												apivm.h(
													"view",
													null,
													item.nameType == 1 &&
														apivm.h(
															"view",
															{class: "flex_row", style: "padding:20px 16px 0;"},
															apivm.h("view", {
																style:
																	loadConfigurationSize(-9) +
																	"background: #FFFFFF;border: 1px solid " +
																	G.appTheme +
																	";border-radius:50%;"
															}),
															apivm.h(
																"text",
																{
																	style:
																		loadConfiguration(-2) +
																		"flex:1;font-weight: bold;color:#333;margin-left:4px;"
																},
																item.name
															)
														)
												),
												apivm.h(
													"view",
													null,
													item.nameType == 2 &&
														apivm.h(
															"view",
															{class: "flex_row", style: "padding:20px 16px 0;"},
															apivm.h(
																"view",
																null,
																apivm.h("image", {
																	class: "decisions_title_bg",
																	src:
																		shareAddress(1) + "image/icon_tj_tbg_" + G.sysSign + ".png"
																}),
																apivm.h(
																	"text",
																	{
																		style:
																			loadConfiguration(1) +
																			"font-weight: bold;color:#333;position: relative;"
																	},
																	item.name
																)
															)
														)
												)
											]
										),
										apivm.h(
											"view",
											null,
											item.head &&
												item.head.length > 1 &&
												apivm.h(
													"view",
													{
														style:
															"padding:5px 4px 0 16px;flex-direction:row;flex-wrap: wrap;"
													},
													item.head.map(function(nItem) {
														return [
															apivm.h(
																"view",
																{class: "flex_row", style: "margin:15px 16px 0 0;"},
																apivm.h("view", {
																	style:
																		loadConfigurationSize(
																			nItem.type == "line" ? [-6, -14] : -10
																		) +
																		";background:" +
																		nItem.color +
																		";"
																}),
																apivm.h(
																	"text",
																	{
																		style:
																			loadConfiguration(-4) + "flex:1;color:#333;margin-left:6px;"
																	},
																	nItem.name
																)
															)
														];
													})
												)
										),
										apivm.h(
											"view",
											null,
											item.type == "label" && [
												apivm.h(
													"view",
													{style: "padding:15px 10px 0;flex-direction:row;flex-wrap: wrap;"},
													apivm.h(
														"view",
														{
															class: "flex_row",
															style:
																"display:" + (item.label ? "flex" : "none") + ";width:33.33%;"
														},
														apivm.h(
															"view",
															{style: "flex-direction: row;align-items: flex-start;"},
															apivm.h("view", {
																style:
																	loadConfigurationSize(-11) +
																	"border: 1px solid #F04B39;border-radius:50%;margin-top:8px;"
															}),
															apivm.h(
																"text",
																{
																	style:
																		loadConfiguration(-2) +
																		"font-weight: bold;color:#333;margin-left:4px;"
																},
																item.label
															)
														)
													),
													item.data.map(function(nItem) {
														return [
															apivm.h(
																"view",
																{
																	onClick: function() {
																		return this$1.openAll(nItem);
																	},
																	style: "padding:5px;width:33.33%;"
																},
																apivm.h(
																	"view",
																	{
																		class: "rounded_shadow xy_center",
																		style: "background: #FFFFFF;width:100%;padding:8px;"
																	},
																	apivm.h(
																		"text",
																		{
																			class: "text_one",
																			style:
																				loadConfiguration(-2) +
																				"color:#333;width: 100%;text-align: center;"
																		},
																		nItem.name
																	),
																	apivm.h(
																		"text",
																		{
																			style:
																				loadConfiguration(8) +
																				"font-weight: bold;color:" +
																				G.appTheme +
																				";margin-top:9px;"
																		},
																		nItem.amount
																	)
																)
															)
														];
													})
												)
											]
										),
										apivm.h(
											"view",
											null,
											item.type == "newest" && [
												apivm.h(
													"view",
													null,
													item.data.map(function(nItem) {
														return [
															apivm.h(
																"view",
																{style: "padding:10px 16px 0;"},
																apivm.h(
																	"view",
																	{class: "newest_item"},
																	apivm.h(
																		"text",
																		{
																			style:
																				loadConfiguration() +
																				"font-weight: bold;color:#333;margin-bottom:10px;"
																		},
																		nItem.title
																	),
																	apivm.h(
																		"view",
																		{class: "flex_row"},
																		apivm.h(
																			"text",
																			{style: loadConfiguration(-2) + "color:#333;"},
																			nItem.name
																		),
																		apivm.h(
																			"text",
																			{
																				style:
																					loadConfiguration(-2) + "color:#333;margin-left:40px;"
																			},
																			nItem.time
																		)
																	)
																)
															)
														];
													})
												)
											]
										),
										apivm.h(
											"view",
											null,
											item.type == "list" && [
												apivm.h(
													"view",
													{style: "padding:5px 16px 0;"},
													item.data.map(function(nItem, nIndex) {
														return (
															(item.showAll || nIndex < 5) && [
																apivm.h(
																	"view",
																	{
																		style:
																			"border: 1px solid #EEEEEE;border-radius: 4px;flex-direction:row;align-items: flex-start;padding:10px;margin-top:15px;"
																	},
																	apivm.h("view", {
																		style:
																			loadConfigurationSize(-11) +
																			";background:#F6931C;margin-top:" +
																			G.appFontSize / 2 +
																			"px;"
																	}),
																	apivm.h(
																		"view",
																		{class: "flex_w", style: "margin-left:10px;"},
																		apivm.h(
																			"text",
																			{
																				class: "text_one",
																				style: loadConfiguration() + "color:#333;margin-bottom:6px;"
																			},
																			nItem.title
																		),
																		apivm.h(
																			"view",
																			{class: "flex_row"},
																			apivm.h(
																				"text",
																				{style: loadConfiguration(-2) + "color:#333;"},
																				nItem.name
																			),
																			apivm.h(
																				"text",
																				{
																					style:
																						loadConfiguration(-2) + "color:#333;margin-left:40px;"
																				},
																				nItem.time
																			)
																		)
																	)
																)
															]
														);
													})
												)
											]
										),
										apivm.h(
											"view",
											null,
											item.type == "hotword" && [
												apivm.h(
													"view",
													{
														style: "padding:0 0 15px 26px;flex-direction:row;flex-wrap: wrap;"
													},
													item.data.map(function(nItem) {
														return [
															apivm.h(
																"text",
																{
																	style:
																		loadConfiguration() +
																		"color:" +
																		nItem.color +
																		";margin:14px 30px 7px 0;"
																},
																"# ",
																nItem.name
															)
														];
													})
												)
											]
										),
										apivm.h(
											"view",
											null,
											item.type == "chart_list" && [
												apivm.h(
													"view",
													null,
													item.data.map(function(nItem, nIndex) {
														return (
															(item.showAll || nIndex < 5) && [
																apivm.h(
																	"view",
																	{style: "padding:20px 0 0 16px;"},
																	apivm.h(
																		"text",
																		{
																			style:
																				loadConfiguration() +
																				"font-weight: bold;color:#333;margin-bottom:3px;"
																		},
																		nItem.title
																	),
																	nItem.value.map(function(uItem, uIndex) {
																		return [
																			apivm.h(
																				"view",
																				{class: "flex_row"},
																				apivm.h(
																					"view",
																					{class: "flex_w"},
																					apivm.h(
																						"view",
																						{
																							style:
																								"width:100%;height:8px;background: #F8F9FA;border-radius: 10px;"
																						},
																						apivm.h("view", {
																							style:
																								"height:8px;width:" +
																								uItem.schedule +
																								"%;border-radius: 10px;background:linear-gradient(to right, " +
																								colorRgba(item.head[uIndex].color, 0.3) +
																								", " +
																								colorRgba(item.head[uIndex].color, 1) +
																								");"
																						})
																					)
																				),
																				apivm.h(
																					"view",
																					{
																						class: "xy_center",
																						style: "min-width:" + G.appFontSize * 4 + "px;"
																					},
																					apivm.h(
																						"text",
																						{
																							style:
																								loadConfiguration(-2) +
																								"color:" +
																								(item.head[uIndex].tc || "#999") +
																								";"
																						},
																						uItem.value
																					)
																				)
																			)
																		];
																	})
																)
															]
														);
													})
												)
											]
										),
										apivm.h(
											"view",
											null,
											item.key == "charts1" && [
												apivm.h(
													"view",
													{style: "padding:20px 16px 0;height:" + item.height + "px;"},
													apivm.h("z-echarts", {id: item.url, dataMore: item.charts})
												)
											]
										),
										apivm.h(
											"view",
											null,
											item.key == "charts2" && [
												apivm.h(
													"view",
													{style: "padding:20px 16px 0;height:" + item.height + "px;"},
													apivm.h("z-echarts", {id: item.url, dataMore: item.charts})
												)
											]
										),
										apivm.h(
											"view",
											null,
											item.key == "charts3" && [
												apivm.h(
													"view",
													{style: "padding:20px 16px 0;height:" + item.height + "px;"},
													apivm.h("z-echarts", {id: item.url, dataMore: item.charts})
												)
											]
										),
										apivm.h(
											"view",
											null,
											item.type == "rankUnit" && [
												apivm.h(
													"view",
													{style: "padding:2px 16px 0;"},
													item.data.map(function(nItem, nIndex) {
														return (
															(item.showAll || nIndex < 5) && [
																apivm.h(
																	"view",
																	{class: "flex_row", style: "padding:18px 20px 18px 0;"},
																	apivm.h(
																		"view",
																		{
																			style:
																				"width:" +
																				(G.appFontSize + 1) * 3 +
																				"px;align-items: center; justify-content: center;margin-left:-10px;"
																		},
																		nIndex < 3
																			? apivm.h("image", {
																					style: "" + loadConfigurationSize(4),
																					mode: "aspectFill",
																					thumbnail: "false",
																					src:
																						shareAddress(1) +
																						"image/icon_decisions_unit" +
																						nIndex +
																						".png"
																			  })
																			: apivm.h(
																					"text",
																					{
																						style:
																							loadConfiguration() + "font-weight: 600;color: #C5C7C9;"
																					},
																					(nIndex < 9 ? "0" : "") + (nIndex + 1)
																			  )
																	),
																	apivm.h(
																		"text",
																		{
																			style:
																				loadConfiguration() + "flex:1;color:#333;margin-right:10px;"
																		},
																		nItem.name
																	),
																	apivm.h(
																		"text",
																		{style: loadConfiguration(-4) + "color:#333;"},
																		"承办建议："
																	),
																	apivm.h(
																		"text",
																		{
																			style:
																				loadConfiguration(-2) +
																				"color:" +
																				(nIndex < 3 ? "#F6631C" : "#C5C7C9") +
																				";font-weight: 600;"
																		},
																		nItem.amount
																	)
																),
																apivm.h("z-divider", null)
															]
														);
													})
												)
											]
										),
										apivm.h(
											"view",
											null,
											item.type == "rankPer" && [
												apivm.h(
													"view",
													{style: "padding:10px 16px 0;"},
													item.data.map(function(nItem, nIndex) {
														return (
															(item.showAll || nIndex < 5) && [
																apivm.h(
																	"view",
																	{
																		class: "rounded_shadow flex_row",
																		style:
																			"margin-top:10px;background: #FFFFFF;padding:12px 20px 12px 0;"
																	},
																	apivm.h(
																		"view",
																		{
																			style:
																				"width:" +
																				(G.appFontSize + 1) * 3 +
																				"px;align-items: center; justify-content: center;margin-left:-10px;"
																		},
																		nIndex < 3
																			? apivm.h("image", {
																					style: "" + loadConfigurationSize(4),
																					mode: "aspectFill",
																					thumbnail: "false",
																					src:
																						shareAddress(1) +
																						"image/icon_performance_rank_" +
																						(nIndex + 1) +
																						".png"
																			  })
																			: apivm.h(
																					"text",
																					{
																						style:
																							loadConfiguration() + "font-weight: 600;color: #C5C7C9;"
																					},
																					(nIndex < 9 ? "0" : "") + (nIndex + 1)
																			  )
																	),
																	apivm.h(
																		"text",
																		{
																			style:
																				loadConfiguration() + "flex:1;color:#333;margin-right:10px;"
																		},
																		nItem.name
																	),
																	apivm.h("a-iconfont", {
																		style: "margin:0 4px;",
																		name: "mn_paiming_fill",
																		color: nIndex < 3 ? "#F6631C" : "#C5C7C9",
																		size: G.appFontSize - 4
																	}),
																	apivm.h(
																		"text",
																		{
																			style:
																				loadConfiguration(-2) +
																				"color:" +
																				(nIndex < 3 ? "#F6631C" : "#C5C7C9") +
																				";font-weight: 600;"
																		},
																		nItem.amount
																	)
																)
															]
														);
													})
												)
											]
										),
										apivm.h(
											"view",
											null,
											item.type == "rankCom" && [
												apivm.h(
													"view",
													{style: "padding:10px 16px 0;"},
													apivm.h(
														"view",
														{class: "flex_row", style: "padding:18px 20px 18px 0;"},
														apivm.h("view", {
															style:
																"width:" +
																(G.appFontSize + 1) * 3 +
																"px;align-items: center; justify-content: center;margin-left:-10px;"
														}),
														apivm.h(
															"text",
															{
																style:
																	loadConfiguration(-2) +
																	"flex:1;font-weight: 600;color:#333;margin-right:10px;"
															},
															"专委会"
														),
														apivm.h(
															"text",
															{
																style:
																	loadConfiguration(-2) +
																	"font-weight: 600;color:#333;min-width:70px;"
															},
															"代表数"
														)
													),
													", ",
													apivm.h("z-divider", null),
													item.data.map(function(nItem, nIndex) {
														return (
															(item.showAll || nIndex < 5) && [
																apivm.h(
																	"view",
																	{class: "flex_row", style: "padding:18px 20px 18px 0;"},
																	apivm.h(
																		"view",
																		{
																			style:
																				"width:" +
																				(G.appFontSize + 1) * 3 +
																				"px;align-items: center; justify-content: center;margin-left:-10px;"
																		},
																		apivm.h(
																			"text",
																			{style: loadConfiguration() + "color: #F6631C;"},
																			(nIndex < 9 ? "0" : "") + (nIndex + 1),
																			"."
																		)
																	),
																	apivm.h(
																		"text",
																		{
																			style:
																				loadConfiguration() + "flex:1;color:#333;margin-right:10px;"
																		},
																		nItem.name
																	),
																	apivm.h(
																		"text",
																		{style: loadConfiguration() + "color:#333;min-width:70px;"},
																		nItem.amount
																	)
																),
																apivm.h("z-divider", null)
															]
														);
													})
												)
											]
										),
										apivm.h(
											"view",
											null,
											item.type == "rankMem" && [
												apivm.h(
													"view",
													{style: "padding:10px 0 0;"},
													item.data.map(function(nItem, nIndex) {
														return (
															(item.showAll || nIndex < 5) && [
																apivm.h(
																	"view",
																	{class: "flex_row", style: "padding:18px 36px 18px 16px;"},
																	apivm.h(
																		"view",
																		{
																			style:
																				"width:" +
																				(G.appFontSize + 1) * 3 +
																				"px;align-items: center; justify-content: center;margin-left:-10px;"
																		},
																		nIndex < 3
																			? apivm.h("image", {
																					style: "" + loadConfigurationSize(4),
																					mode: "aspectFill",
																					thumbnail: "false",
																					src:
																						shareAddress(1) +
																						"image/icon_rank_" +
																						(nIndex + 1) +
																						".png"
																			  })
																			: apivm.h(
																					"text",
																					{
																						style:
																							loadConfiguration() + "font-weight: 600;color: #C5C7C9;"
																					},
																					(nIndex < 9 ? "0" : "") + (nIndex + 1)
																			  )
																	),
																	apivm.h(
																		"text",
																		{
																			style:
																				loadConfiguration() + "flex:1;color:#333;margin-right:10px;"
																		},
																		nItem.name
																	),
																	apivm.h(
																		"text",
																		{style: loadConfiguration(-4) + "color:#333;"},
																		"登录次数："
																	),
																	apivm.h(
																		"text",
																		{
																			style:
																				loadConfiguration(-2) +
																				"color:" +
																				(nIndex < 3 ? "#F6631C" : "#C5C7C9") +
																				";font-weight: 600;"
																		},
																		nItem.amount
																	)
																),
																apivm.h("z-divider", null)
															]
														);
													})
												)
											]
										),
										apivm.h(
											"view",
											null,
											item.type == "rankComUse" && [
												apivm.h(
													"view",
													{style: "padding:10px 0 0;"},
													apivm.h(
														"view",
														{class: "flex_row", style: "padding:18px 16px;"},
														apivm.h("view", {
															style:
																"width:" +
																G.appFontSize * 2 +
																"px;align-items: center; justify-content: center;margin-left:-10px;"
														}),
														apivm.h(
															"text",
															{
																style:
																	loadConfiguration(-2) +
																	"flex:1;font-weight: 600;color:#333;margin-right:10px;"
															},
															"机关单位名称"
														),
														apivm.h(
															"text",
															{
																style:
																	loadConfiguration(-2) +
																	"font-weight: 600;color:#333;min-width:" +
																	G.appFontSize * 5 +
																	"px;"
															},
															"累计使用/次"
														)
													),
													", ",
													apivm.h("z-divider", null),
													item.data.map(function(nItem, nIndex) {
														return (
															(item.showAll || nIndex < 5) && [
																apivm.h(
																	"view",
																	{class: "flex_row", style: "padding:18px 16px;"},
																	apivm.h(
																		"view",
																		{
																			style:
																				"width:" +
																				G.appFontSize * 2 +
																				"px;align-items: center; justify-content: center;margin-left:-10px;"
																		},
																		apivm.h("view", {
																			style: loadConfigurationSize(-11) + ";background:#F6631C;"
																		})
																	),
																	apivm.h(
																		"text",
																		{
																			style:
																				loadConfiguration() + "flex:1;color:#333;margin-right:10px;"
																		},
																		nItem.name
																	),
																	apivm.h(
																		"text",
																		{
																			style:
																				loadConfiguration() +
																				"color:#333;min-width:" +
																				G.appFontSize * 5 +
																				"px;"
																		},
																		nItem.amount
																	)
																),
																apivm.h("z-divider", null)
															]
														);
													})
												)
											]
										),
										apivm.h(
											"view",
											null,
											isParameters(item.showAll) &&
												item.data.length > 5 &&
												apivm.h(
													"view",
													{
														onClick: function() {
															return this$1.openShowAll(item);
														},
														class: "flex_row xy_center",
														style: "padding:15px 0 10px;"
													},
													apivm.h(
														"text",
														{style: loadConfiguration(-4) + "color: #666;margin-right:6px;"},
														item.showAll ? "收起" : "展开",
														"更多"
													),
													apivm.h("a-iconfont", {
														name: "xiangxiagengduo",
														style:
															"transform: rotate(" + (item.showAll ? "180" : "0") + "deg);",
														color: "#666",
														size: G.appFontSize - 4
													})
												)
										),
										apivm.h(
											"view",
											null,
											item.bottom &&
												apivm.h("view", {
													style: "height:20px;border-bottom: 10px solid rgba(0,0,0,0.03);"
												})
										)
									]
								);
							})
					),
					apivm.h(
						"view",
						null,
						this.data.emptyBox.type == "load" && apivm.h("z-skeleton", null)
					),
					apivm.h("z-empty", {
						_this: this,
						dataMore: this.data.emptyBox,
						onRefresh: function() {
							return this$1.switchTab();
						}
					}),
					apivm.h("view", {style: "padding-bottom:" + safeArea().bottom + "px;"})
				)
			);
		};

		return Root;
	})(Component);
	Root.css = {
		".rounded_shadow": {
			borderTopLeftRadius: "4px",
			borderTopRightRadius: "4px",
			borderBottomRightRadius: "4px",
			borderBottomLeftRadius: "4px",
			boxShadow: "0px 2px 10px 1px rgba(24,64,118,0.08)"
		},
		".decisions_title_box": {
			padding: "15px 10px",
			flexDirection: "row",
			flexWrap: "wrap",
			borderBottom: "10px solid rgba(0,0,0,0.03)"
		},
		".decisions_title_img": {
			position: "absolute",
			bottom: "0",
			right: "4px",
			width: "96px",
			height: "30px"
		},
		".decisions_title_bg": {
			position: "absolute",
			bottom: "0",
			right: "-91px",
			width: "154px",
			height: "16px"
		},
		".newest_item": {
			background: "linear-gradient(180deg, #FFFFFF 0%, #F8F9FA 100%)",
			borderRadius: "4px 4px 4px 4px",
			padding: "15px"
		},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden",
			display: "inline"
		}
	};
	apivm.define("root", Root);
	apivm.render(apivm.h("root", null), "body");
})();
