(function() {
	var LOAD_ING = "加载中，请稍候...";
	var LOAD_MORE = "点击加载更多";
	var LOAD_ALL = "已加载完";
	var NET_ERR$1 = "用户您好，系统正在更新，请稍后再试。";

	//参数是否为空
	function isParameters(_obj) {
		return _obj != null && _obj != undefined;
	}
	//是否数组
	function isArray(_obj) {
		return isParameters(_obj) && toString.apply(_obj) === "[object Array]";
	}
	//是否数字
	function isNumber(_obj) {
		return isParameters(_obj) && typeof _obj === "number";
	}
	//是否对象
	function isObject(_obj) {
		return isParameters(_obj) && typeof _obj === "object";
	}
	//是否方法
	function isFunction(_obj) {
		return isParameters(_obj) && typeof _obj === "function";
	}

	//获取随机数
	function getNum() {
		return Math.floor(Math.random() * 100000000);
	}

	//去除所有空格
	function trimAll(str) {
		return str.replace(/\s*/g, "");
	}

	//合并json
	function setNewJSON(_obj, _newobj, _dotReplace) {
		_obj = _obj || {};
		_newobj = _newobj || {};
		var returnObj = {};
		for (var key in _obj) {
			returnObj[key] = _obj[key];
		}
		for (var key in _newobj) {
			if (
				(_dotReplace && isParameters(returnObj[key])) ||
				isFunction(isParameters(returnObj[key]))
			)
				continue;
			returnObj[key] =
				isArray(returnObj[key]) || !isObject(returnObj[key])
					? _newobj[key]
					: setNewJSON(returnObj[key], _newobj[key]);
		}
		return returnObj;
	}

	//移除字符串所有标签
	function removeTag(str) {
		if (!str) return str;
		return decodeCharacter(
			str
				.replace(/<!--[\w\W\r\n]*?-->/gim, "")
				.replace(/(<[^\s\/>]+)\b[^>]*>/gi, "$1>")
				.replace(/<[^>]+>/g, "")
				.replace(/\s*/g, "")
		);
	}

	//移除字符串所有标签
	function removeTagAll(str) {
		if (!str) return str;
		return decodeCharacter(
			str
				.replace(/<video[^>]*>(.*?)<\/video>/g, "【视频】")
				.replace(/<img[^>]*>/g, "【图片】")
				.replace(/<!--[\w\W\r\n]*?-->/gim, "")
				.replace(/(<[^\s\/>]+)\b[^>]*>/gi, "$1>")
				.replace(/<[^>]+>/g, "")
				.replace(/\s*/g, "")
		);
	}

	//转义字符串
	function decodeCharacter(str) {
		if (!str) return str;
		return str
			.replace(/&amp;/g, "&")
			.replace(/(&nbsp;|&ensp;|&emsp;)/g, " ")
			.replace(/&mdash;/g, "—")
			.replace(/&ldquo;/g, "“")
			.replace(/&rsquo;/g, "’")
			.replace(/&lsquo;/g, "‘")
			.replace(/&rdquo;/g, "”")
			.replace(/&middot;/g, "·")
			.replace(/&hellip;/g, "…")
			.replace(/&quot;/g, '"')
			.replace(/&lt;/g, "<")
			.replace(/&gt;/g, ">");
	}

	//获取平台类型
	function platform() {
		return api.platform;
	}

	//app设置状态栏
	function setStatusBarStyle(_param) {
		try {
			api.setStatusBarStyle(_param);
		} catch (e) {}
	}

	//区域参数
	function safeArea() {
		try {
			return api.safeArea;
		} catch (e) {
			return {top: 0, left: 0, bottom: 0, right: 0};
		}
	}

	//页面参数对象
	function pageParam(_this) {
		try {
			var pageParam =
				(_this && _this.props
					? _this.props.pageParam || (_this.props.dataMore || {}).pageParam
					: null) ||
				api.pageParam ||
				{};
			if (pageParam.paramSaveKey) {
				pageParam = JSON.parse(getPrefs(pageParam.paramSaveKey) || "{}");
			}
			if (platform() == "web" && JSON.stringify(pageParam) == "{}") {
				pageParam = getOtherParam(window.location.href);
			}
			return pageParam;
		} catch (e) {
			return {};
		}
	}

	//获取缓存
	function getPrefs(key) {
		try {
			return api.getPrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				return window.parent[key];
			} else {
				return window[key];
			}
		}
	}

	//设置缓存
	function setPrefs(key, value) {
		if (!isParameters(value)) {
			removePrefs(key);
			return;
		}
		try {
			api.setPrefs({sync: true, key: key, value: value});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				window.parent[key] = value;
			} else {
				window[key] = value;
			}
		}
	}

	//删除缓存
	function removePrefs(key) {
		try {
			api.removePrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				delete window.parent[key];
			} else {
				delete window[key];
			}
		}
	}

	//添加监听
	function addEventListener(name, callback) {
		var keyback = function keyback(ret, err) {
			isFunction(callback) && callback(ret, err);
		};
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				if (!window.baseEventList) window.baseEventList = [];
				if (getItemForKey(name, window.baseEventList)) {
					delItemForKey(name, window.baseEventList);
				}
				window.baseEventList.push({key: name, value: keyback});
			} else {
				api.addEventListener({name: name}, keyback);
			}
		} catch (e) {}
	}
	//移除监听
	function removeEventListener(name) {
		if (
			platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			delItemForKey(name, window.baseEventList);
		} else {
			try {
				api.removeEventListener({name: name});
			} catch (e) {}
		}
	}
	//发送监听
	function sendEvent(name, extra) {
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				var pageframes = window.parent.document.getElementsByTagName("iframe");
				for (var i = 0; i < pageframes.length; i++) {
					if (isArray(pageframes[i].contentWindow.baseEventList)) {
						var sendItem = getItemForKey(
							isObject(name) ? name.name : name,
							pageframes[i].contentWindow.baseEventList
						);
						if (sendItem)
							sendItem.value({value: isObject(name) ? name.extra : extra});
					}
				}
			} else {
				api.sendEvent(isObject(name) ? name : {name: name, extra: extra});
			}
		} catch (e) {}
	}
	//加载框
	function showProgress(_param, modal) {
		var o = {
			style: "default",
			animationType: "fade",
			title: "加载中",
			text: "请稍候...",
			modal: true //是否模态，模态时整个页面将不可交互
		};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.title = isParameters(_param) ? _param : "";
			o.modal = !modal; //是否可以交互 反过来了
		}
		o.title = o.title.toString();
		api.showProgress(o);
	}

	//隐藏加载框
	function hideProgress() {
		api.hideProgress();
	}

	//处理app链接 可以不带app会拼接上 或者需要带上特定参数
	function handleSYSLink(_link) {
		if (!_link) return;
		_link = _link.replace("{{tomcatAddress}}", tomcatAddress());
		_link = _link.replace("{{shareAddress}}", shareAddress());
		_link = _link.replace("{{token}}", encodeURIComponent(getPrefs("sys_token")));
		_link = _link.replace("{{sysUrl}}", appUrl());
		_link = _link.replace("{{areaId}}", areaId()); //当前跳转页面的地区id，例如：430000
		_link = _link.replace("{{userId}}", G.userId); //当前用户id，例如：1
		_link = _link.replace("{{iszx}}", G.sysSign == "zx"); //当前系统类型，例如：true (true为政协，flase为人大)
		_link = _link.replace("{{appTheme}}", G.appTheme); //当前app主题颜色，例如：#3657C0
		_link = _link.replace("{{careMode}}", G.careMode); //当前是否为关怀模式：例如：true (关怀模式下字体大4px)
		if (_link.indexOf("?ndata=") != -1) {
			if (_link.indexOf("sysUrl-zy-") == -1) _link += "-zyz-sysUrl-zy-" + appUrl();
			if (_link.indexOf("sysAreaId-zy-") == -1)
				_link += "-zyz-sysAreaId-zy-" + areaId();
			if (_link.indexOf("iszx-zy-") == -1)
				_link += "-zyz-iszx-zy-" + (G.sysSign == "zx");
			if (_link.indexOf("appTheme-zy-") == -1)
				_link += "-zyz-appTheme-zy-" + G.appTheme;
			if (_link.indexOf("careMode-zy-") == -1)
				_link += "-zyz-careMode-zy-" + G.careMode;
		}
		return _link;
	}

	//打开新页面
	function openWin(name, url, pageParam, _more) {
		url = handleSYSLink(url); //先处理跳转链接
		if (url.indexOf("http") != 0) {
			url =
				platform() == "web"
					? url.substring(url.lastIndexOf("/") + 1)
					: url.indexOf("..") != 0
					? "../" + url.split(".")[0] + "/" + url
					: url;
		}
		var o = {
			name: name,
			url: url,
			pageParam: pageParam || {},
			bounces: false,
			bgColor: "#FFF",
			slidBackEnabled: false, //ios滑动返回
			vScrollBarEnabled: true,
			hScrollBarEnabled: true,
			scaleEnabled: true,
			animation: {
				type: "push",
				subType: "from_right",
				duration: 300
			},

			reload: true, // 去除设置
			allowEdit: true, //去除设置 默认都可以复制粘贴
			delay: 0,
			overScrollMode: "scrolls",
			defaultRefreshHeader: "swipe"
		};

		if (isObject(_more)) {
			o = setNewJSON(o, _more);
		}
		if (G.headTheme != getPrefs("headTheme") && G.headTheme != "transparent") {
			o.pageParam.headTheme = G.headTheme;
		}
		if (
			G.appTheme != getPrefs("appTheme" + G.sysSign) &&
			G.appTheme != (G.sysSign == "rd" ? "#C61414" : "#3088FE")
		) {
			o.pageParam.appTheme = G.appTheme;
		}
		if (
			o.pageParam.areaId != areaId() ||
			(areaId() != getPrefs("sys_aresId") && areaId() != getPrefs("sys_platform"))
		) {
			o.pageParam.areaId = o.pageParam.areaId || areaId();
		}
		if (o.pageParam.paramSaveKey) {
			setPrefs(o.pageParam.paramSaveKey, JSON.stringify(o.pageParam));
			o.pageParam = {paramSaveKey: o.pageParam.paramSaveKey};
		}
		videoPlayRemoves();
		api.openWin(o);
	}

	function closeWin(_param) {
		var o = {};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.name = _param;
		}
		try {
			if (api.pageParam.paramSaveKey) {
				removePrefs(api.pageParam.paramSaveKey);
			}
			videoPlayRemoves();
			api.closeWin(o);
		} catch (e) {}
	}

	function toast(_param, location, global) {
		var o = {
			msg: "",
			duration: 2000,
			location: location || "middle",
			global: global ? true : false
		};

		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.msg = isParameters(_param) ? _param : "";
		}
		o.msg = o.msg.toString();
		api.toast(o);
	}

	//弹出actionSheet
	function actionSheet(_param, _callback) {
		var o = {title: "", cancelTitle: "取消", destructiveTitle: ""};
		o = setNewJSON(o, _param);
		var oldButton = o.buttons || [],
			newButton = [];
		oldButton.forEach(function(item) {
			newButton.push(isObject(item) ? item : {name: item});
		});
		var actionSheetBox = {
			title: o.title,
			cancel: o.cancelTitle,
			data: newButton,
			active: o.active,
			show: true,
			callback: function callback(ret) {
				_callback && _callback(ret);
			},
			pageClose: function pageClose() {
				_param.pageClose && _param.pageClose();
			}
		};

		G.actionSheetPop = actionSheetBox;
	}

	//弹出alert
	function alert(_param, _callback) {
		var o = {title: "", msg: "", buttons: ["确定"]};
		if (isObject(_param)) {
			o = setNewJSON(o, _param);
		} else {
			o.msg = (isParameters(_param) ? _param : "").toString();
		}
		var alertBox = {
			title: o.title,
			content: (o.msg || o.content || "").toString(),
			placeholder: o.placeholder,
			closeType: o.closeType || "1",
			type: o.type || "text",
			timeout: o.timeout || 0,
			autoClose: o.autoClose,
			cancel: {
				show: o.buttons.length > 1 || o.closeType == "2" || o.closeType == "3",
				text: o.buttons[1],
				color: "#333333"
			},

			sure: {
				show: o.buttons.length > 0,
				text: o.buttons[0],
				color: "appTheme"
			},

			show: true,
			callback: function callback(ret) {
				_callback && _callback(ret);
			}
		};

		alertBox = setNewJSON(alertBox, _param.otherParam);
		G.alertPop = alertBox;
	}

	//网络请求
	function ajax(url, tag, callback, logText, method, data, header) {
		var getUrl = url,
			dataType = "json",
			cacheType = "",
			paramData = {},
			aId = areaId(),
			isWeb = "";
		if (isObject(url)) {
			getUrl = url.u;
			dataType = url.dt || "json";
			cacheType = url.t || "";
			paramData = url.paramData || {};
			aId = url.areaId || areaId();
			isWeb = url.web || "";
		}
		var o = {
			url: getUrl,
			tag: tag,
			method: method || "get",
			cache: false,
			timeout: 120,
			dataType: dataType,
			data: isObject(data) ? data : {},
			headers: setNewJSON(
				{
					"u-login-areaId": aId,
					Authorization: getPrefs("sys_token") || "",
					"content-type": "application/json",
					"u-terminal": G.terminal || "APP"
				},
				header || {}
			)
		};

		o = setNewJSON(o, paramData);
		if (o.url.indexOf("push/rongCloud") != -1) {
			if (o.method == "get") {
				o.url +=
					(o.url.indexOf("?") != -1 ? "&" : "?") +
					"environment=" +
					chatEnvironment();
			} else if (o.data.values) {
				o.data.values.environment = chatEnvironment();
			}
		}
		if (isWeb) {
			(o.headers.Authorization =
				(header || {}).Authorization || getPrefs("public_token") || ""),
				(o.headers["u-terminal"] = "PUBLIC");
		}
		var oldContentType = o.headers["content-type"];
		if (oldContentType == "file") {
			delete o.headers["content-type"];
		}
		if (platform() == "app" && logText) {
			console.log(logText + o.method + "：" + JSON.stringify(o));
		}
		var cbFun = function cbFun(ret, err) {
			if (isFunction(callback)) {
				// if(isObject(err)){
				// 	try{
				// 		ret = JSON.parse(err.msg);
				// 	}catch(e){
				// 		ret = JSON.parse(JSON.stringify(err));
				// 	}
				// 	err = null;
				// }
				if (platform() == "app" && logText) {
					console.log(
						"得到" + logText + "返回结果：" + JSON.stringify(ret ? ret : err)
					);
				}
				if (isObject(ret)) {
					ret.message = ret.message || ret.msg || "";
					var errcode = ret.code || "";
					if ((errcode == 302 || errcode == 2) && cacheType != "login") {
						cleanAllMsg();
						sendEvent({
							name: "index",
							extra: {type: "verificationToken", errmsg: ret.message}
						});
					}
					// if(!isObject(ret.data)){
					// 	ret.data = "";
					// }
				}
				callback(ret, err);
			}
		};
		if (platform() == "web") {
			var xhr = new XMLHttpRequest();
			xhr.open(o.method, o.url);
			for (var header in o.headers) {
				xhr.setRequestHeader(header, o.headers[header]);
			}
			var sendValue = "";
			if (oldContentType.indexOf("x-www-form-urlencoded") != -1) {
				var dValue = o.data.values || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue +=
						(!sendValue ? "" : "&") + vItem + "=" + encodeURIComponent(dValue[vItem]);
				}
			} else if (oldContentType.indexOf("file") != -1) {
				sendValue = new FormData();
				var dValue = o.data.values || {};
				var fileValue = o.data.files || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue.append(vItem, encodeURIComponent(dValue[vItem]));
				}
				for (var vItem in fileValue) {
					sendValue.append(vItem, fileValue[vItem]);
				}
			} else {
				sendValue = o.data.body || JSON.stringify(o.data.values);
			}
			xhr.onreadystatechange = function() {
				if (xhr.readyState === XMLHttpRequest.DONE) {
					var ret, err;
					if (!xhr.responseText) {
						err = {};
					} else {
						var response = this.responseText;
						if (o.dataType == "json") {
							try {
								ret = JSON.parse(response);
							} catch (e) {
								err = {msg: response};
							}
						} else {
							ret = response;
						}
					}
					cbFun(ret, err);
				}
			};
			xhr.send(sendValue);
		} else {
			api.cancelAjax({tag: tag});
			api.ajax(o, cbFun);
		}
	}

	function getPicture(_param, callback) {
		var o = {
			sourceType: "camera",
			encodingType: "jpg",
			mediaValue: "pic",
			targetWidth: 720,
			count: 1,
			max: 0
		};

		o = setNewJSON(o, _param);
		try {
			if (platform() == "web") {
				var h5Input = document.createElement("input");
				h5Input.style = "display: none;";
				h5Input.type = "file";
				h5Input.accept = "image/*";
				var ua = navigator.userAgent.toLowerCase();
				var version = "";
				if (ua.indexOf("android") > 0) {
					var reg = /android [\d._]+/gi;
					var v_info = ua.match(reg);
					version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, ".");
					version = parseInt(version.split(".")[0]);
				}
				if (!version || Number(version) <= 13) {
					h5Input.multiple = "multiple";
				}
				h5Input.onchange = function() {
					var listLength =
						o.max != 0 && h5Input.files.length > o.max ? o.max : h5Input.files.length;
					for (var i = 0; i < listLength; i++) {
						callback({data: h5Input.files[i]}, null);
					}
				};
				document.body.appendChild(h5Input);
				h5Input.click();
			} else if (platform() == "mp") {
				wx.chooseImage({
					count: o.max != 0 ? o.max : 9,
					sourceType: [o.sourceType == "camera" ? "camera" : "album"],
					success: function success(res) {
						for (var i = 0; i < res.tempFiles.length; i++) {
							callback({data: res.tempFiles[i]}, null);
						}
					}
				});
			} else if (platform() == "app") {
				var preName = o.sourceType == "camera" ? "camera" : "photos";
				if (
					!confirmPer(
						preName,
						"getPicture",
						o.reason || "用于上传并使用图片的功能，若取消将无法使用图片功能"
					)
				) {
					addEventListener(preName + "Per_" + "getPicture", function(ret, err) {
						if (ret.value.granted) {
							getPicture(_param, callback);
						}
						removeEventListener(preName + "Per_" + "getPicture");
					});
					return;
				}
				if (o.sourceType == "camera") {
					api.getPicture(o, function(ret, err) {
						isFunction(callback) && callback(ret, err);
					});
				} else {
					api.require("WXPhotoPicker").open(
						{
							max: o.max != 0 ? o.max : 9,
							styles: {
								mark: {checked: G.appTheme},
								bottomTabBar: {sendText: "确定", sendBgColor: G.appTheme}
							},
							type: "image"
						},
						function(ret) {
							var eventType = ret ? ret.eventType : "cancel";
							if (eventType == "cancel") return;
							if (eventType == "confirm" && ret.list.length != 0) {
								for (var i = 0; i < ret.list.length; i++) {
									callback({data: ret.list[i].path}, null);
								}
							}
						}
					);
				}
			}
		} catch (e) {}
	}

	function hasPermission(one_per) {
		if (platform() == "app") {
			if (!one_per) return;
			var rets = api.hasPermission({list: one_per.split(",")});
			if (one_per.indexOf(",") != -1) {
				alert("判断结果：" + JSON.stringify(rets));
				return;
			} else {
				return rets;
			}
		}
	}

	function requestPermission(one_per, callback, _fName) {
		if (platform() == "app") {
			if (!one_per) return;
			api.requestPermission({list: one_per.split(",")}, function(ret) {
				console.log(JSON.stringify(ret));
				ret.list.forEach(function(_eItem, _eIndex, _eArr) {
					sendEvent(_eItem.name + "Per_" + _fName, {granted: _eItem.granted});
				});
				isFunction(callback) && callback(ret, err);
			});
		}
	}

	function confirmPer(perm, _fName, _reason) {
		if (platform() == "app") {
			var has = hasPermission(perm);
			if (!has || !has[0] || !has[0].granted) {
				var hintWord = {
					camera: "相机",
					storage: "存储",
					photos: "照片",
					microphone: "麦克风",
					location: "位置",
					phone: "电话",
					"phone-r": "通话状态",
					"phone-r-log": "通话记录"
				};

				var iosHint =
					"请在" +
					(api.uiMode == "phone" ? "iPhone" : "iPad") +
					"的“设置-隐私-" +
					hintWord[perm] +
					"”中请允许访问" +
					hintWord[perm] +
					(_reason ? "，" + _reason : "。");
				var androidHint =
					"使用该功能需要" +
					hintWord[perm] +
					"权限，" +
					(_reason || "请前往系统设置开启权限。");
				alert(
					{
						title: "无法使用" + hintWord[perm],
						msg: api.systemType == "ios" ? iosHint : androidHint,
						buttons: ["下一步", "取消"]
					},
					function(ret) {
						if (1 == ret.buttonIndex) {
							requestPermission(perm, null, _fName);
						} else {
							sendEvent(perm + "Per_" + _fName, {granted: false});
						}
					}
				);
				return false;
			}
			return true;
		}
		return true;
	}

	var SECONDS_A_MINUTE = 60;
	var SECONDS_A_HOUR = SECONDS_A_MINUTE * 60;
	var SECONDS_A_DAY = SECONDS_A_HOUR * 24;
	var SECONDS_A_WEEK = SECONDS_A_DAY * 7;
	var MILLISECONDS_A_SECOND = 1e3;
	var MILLISECONDS_A_MINUTE = SECONDS_A_MINUTE * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_HOUR = SECONDS_A_HOUR * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_DAY = SECONDS_A_DAY * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_WEEK = SECONDS_A_WEEK * MILLISECONDS_A_SECOND; // English locales

	var MS = "millisecond";
	var S = "second";
	var MIN = "minute";
	var H = "hour";
	var D = "day";
	var W = "week";
	var M = "month";
	var Q = "quarter";
	var Y = "year";
	var DATE = "date";
	var FORMAT_DEFAULT = "YYYY-MM-DDTHH:mm:ssZ";
	var INVALID_DATE_STRING = "Invalid Date"; // regex

	var REGEX_PARSE = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/;
	var REGEX_FORMAT = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;

	var en = {
		name: "en",
		weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split(
			"_"
		),
		months: "January_February_March_April_May_June_July_August_September_October_November_December".split(
			"_"
		)
	};

	var padStart = function padStart(string, length, pad) {
		var s = String(string);
		if (!s || s.length >= length) return string;
		return "" + Array(length + 1 - s.length).join(pad) + string;
	};
	var padZoneStr = function padZoneStr(instance) {
		var negMinutes = -instance.utcOffset();
		var minutes = Math.abs(negMinutes);
		var hourOffset = Math.floor(minutes / 60);
		var minuteOffset = minutes % 60;
		return (
			"" +
			(negMinutes <= 0 ? "+" : "-") +
			padStart(hourOffset, 2, "0") +
			":" +
			padStart(minuteOffset, 2, "0")
		);
	};
	var monthDiff = function monthDiff(a, b) {
		if (a.date() < b.date()) return -monthDiff(b, a);
		var wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month());
		var anchor = a.clone().add(wholeMonthDiff, M);
		var c = b - anchor < 0;
		var anchor2 = a.clone().add(wholeMonthDiff + (c ? -1 : 1), M);
		return +(
			-(
				wholeMonthDiff +
				(b - anchor) / (c ? anchor - anchor2 : anchor2 - anchor)
			) || 0
		);
	};
	var absFloor = function absFloor(n) {
		return n < 0 ? Math.ceil(n) || 0 : Math.floor(n);
	};
	var prettyUnit = function prettyUnit(u) {
		var special = {
			M: M,
			y: Y,
			w: W,
			d: D,
			D: DATE,
			h: H,
			m: MIN,
			s: S,
			ms: MS,
			Q: Q
		};

		return (
			special[u] ||
			String(u || "")
				.toLowerCase()
				.replace(/s$/, "")
		);
	};
	var isUndefined = function isUndefined(s) {
		return s === undefined;
	};
	var U = {
		s: padStart,
		z: padZoneStr,
		m: monthDiff,
		a: absFloor,
		p: prettyUnit,
		u: isUndefined
	};

	var L = "en";
	var Ls = {};
	Ls[L] = en;
	var isDayjs = function isDayjs(d) {
		return d instanceof Dayjs;
	};
	var parseLocale = function parseLocale(preset, object, isLocal) {
		var l;
		if (!preset) return L;
		if (typeof preset === "string") {
			var presetLower = preset.toLowerCase();
			if (Ls[presetLower]) {
				l = presetLower;
			}
			if (object) {
				Ls[presetLower] = object;
				l = presetLower;
			}
			var presetSplit = preset.split("-");
			if (!l && presetSplit.length > 1) {
				return parseLocale(presetSplit[0]);
			}
		} else {
			var name = preset.name;
			Ls[name] = preset;
			l = name;
		}
		if (!isLocal && l) L = l;
		return l || (!isLocal && L);
	};
	var dayjs = function dayjs(date, c) {
		if (isDayjs(date)) {
			return date.clone();
		}
		var cfg = typeof c === "object" ? c : {};
		cfg.date = date;
		cfg.args = arguments;
		return new Dayjs(cfg);
	};
	var wrapper = function wrapper(date, instance) {
		return dayjs(date, {
			locale: instance.$L,
			utc: instance.$u,
			x: instance.$x,
			$offset: instance.$offset
		});
	};
	var Utils = U;
	Utils.l = parseLocale;
	Utils.i = isDayjs;
	Utils.w = wrapper;
	var parseDate = function parseDate(cfg) {
		var date = cfg.date,
			utc = cfg.utc;
		if (date === null) return new Date(NaN);
		if (Utils.u(date)) return new Date();
		if (date instanceof Date) return new Date(date);
		if (typeof date === "string" && !/Z$/i.test(date)) {
			var d = date.match(REGEX_PARSE);
			if (d) {
				var m = d[2] - 1 || 0;
				var ms = (d[7] || "0").substring(0, 3);
				if (utc) {
					return new Date(
						Date.UTC(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms)
					);
				}
				return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);
			}
		}
		return new Date(date);
	};
	var Dayjs = (function() {
		function Dayjs(cfg) {
			this.$L = parseLocale(cfg.locale, null, true);
			this.parse(cfg);
		}
		var _proto = Dayjs.prototype;
		_proto.parse = function parse(cfg) {
			this.$d = parseDate(cfg);
			this.$x = cfg.x || {};
			this.init();
		};
		_proto.init = function init() {
			var $d = this.$d;
			this.$y = $d.getFullYear();
			this.$M = $d.getMonth();
			this.$D = $d.getDate();
			this.$W = $d.getDay();
			this.$H = $d.getHours();
			this.$m = $d.getMinutes();
			this.$s = $d.getSeconds();
			this.$ms = $d.getMilliseconds();
		};
		_proto.$utils = function $utils() {
			return Utils;
		};
		_proto.isValid = function isValid() {
			return !(this.$d.toString() === INVALID_DATE_STRING);
		};
		_proto.isSame = function isSame(that, units) {
			var other = dayjs(that);
			return this.startOf(units) <= other && other <= this.endOf(units);
		};
		_proto.isAfter = function isAfter(that, units) {
			return dayjs(that) < this.startOf(units);
		};
		_proto.isBefore = function isBefore(that, units) {
			return this.endOf(units) < dayjs(that);
		};
		_proto.$g = function $g(input, get, set) {
			if (Utils.u(input)) return this[get];
			return this.set(set, input);
		};
		_proto.unix = function unix() {
			return Math.floor(this.valueOf() / 1000);
		};
		_proto.valueOf = function valueOf() {
			return this.$d.getTime();
		};
		_proto.startOf = function startOf(units, _startOf) {
			var _this = this;
			var isStartOf = !Utils.u(_startOf) ? _startOf : true;
			var unit = Utils.p(units);
			var instanceFactory = function instanceFactory(d, m) {
				var ins = Utils.w(
					_this.$u ? Date.UTC(_this.$y, m, d) : new Date(_this.$y, m, d),
					_this
				);
				return isStartOf ? ins : ins.endOf(D);
			};
			var instanceFactorySet = function instanceFactorySet(method, slice) {
				var argumentStart = [0, 0, 0, 0];
				var argumentEnd = [23, 59, 59, 999];
				return Utils.w(
					_this
						.toDate()
						[method].apply(
							_this.toDate("s"),
							(isStartOf ? argumentStart : argumentEnd).slice(slice)
						),
					_this
				);
			};
			var $W = this.$W,
				$M = this.$M,
				$D = this.$D;
			var utcPad = "set" + (this.$u ? "UTC" : "");
			switch (unit) {
				case Y:
					return isStartOf ? instanceFactory(1, 0) : instanceFactory(31, 11);
				case M:
					return isStartOf ? instanceFactory(1, $M) : instanceFactory(0, $M + 1);
				case W: {
					var weekStart = this.$locale().weekStart || 0;
					var gap = ($W < weekStart ? $W + 7 : $W) - weekStart;
					return instanceFactory(isStartOf ? $D - gap : $D + (6 - gap), $M);
				}
				case D:
				case DATE:
					return instanceFactorySet(utcPad + "Hours", 0);
				case H:
					return instanceFactorySet(utcPad + "Minutes", 1);
				case MIN:
					return instanceFactorySet(utcPad + "Seconds", 2);
				case S:
					return instanceFactorySet(utcPad + "Milliseconds", 3);
				default:
					return this.clone();
			}
		};
		_proto.endOf = function endOf(arg) {
			return this.startOf(arg, false);
		};
		_proto.$set = function $set(units, _int) {
			var _C$D$C$DATE$C$M$C$Y$C;
			var unit = Utils.p(units);
			var utcPad = "set" + (this.$u ? "UTC" : "");
			var name = ((_C$D$C$DATE$C$M$C$Y$C = {}),
			(_C$D$C$DATE$C$M$C$Y$C[D] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[DATE] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[M] = utcPad + "Month"),
			(_C$D$C$DATE$C$M$C$Y$C[Y] = utcPad + "FullYear"),
			(_C$D$C$DATE$C$M$C$Y$C[H] = utcPad + "Hours"),
			(_C$D$C$DATE$C$M$C$Y$C[MIN] = utcPad + "Minutes"),
			(_C$D$C$DATE$C$M$C$Y$C[S] = utcPad + "Seconds"),
			(_C$D$C$DATE$C$M$C$Y$C[MS] = utcPad + "Milliseconds"),
			_C$D$C$DATE$C$M$C$Y$C)[unit];
			var arg = unit === D ? this.$D + (_int - this.$W) : _int;
			if (unit === M || unit === Y) {
				var date = this.clone().set(DATE, 1);
				date.$d[name](arg);
				date.init();
				this.$d = date.set(DATE, Math.min(this.$D, date.daysInMonth())).$d;
			} else if (name) this.$d[name](arg);
			this.init();
			return this;
		};
		_proto.set = function set(string, _int2) {
			return this.clone().$set(string, _int2);
		};
		_proto.get = function get(unit) {
			return this[Utils.p(unit)]();
		};
		_proto.add = function add(number, units) {
			var _this2 = this,
				_C$MIN$C$H$C$S$unit;
			number = Number(number);
			var unit = Utils.p(units);
			var instanceFactorySet = function instanceFactorySet(n) {
				var d = dayjs(_this2);
				return Utils.w(d.date(d.date() + Math.round(n * number)), _this2);
			};
			if (unit === M) {
				return this.set(M, this.$M + number);
			}
			if (unit === Y) {
				return this.set(Y, this.$y + number);
			}
			if (unit === D) {
				return instanceFactorySet(1);
			}
			if (unit === W) {
				return instanceFactorySet(7);
			}
			var step =
				((_C$MIN$C$H$C$S$unit = {}),
				(_C$MIN$C$H$C$S$unit[MIN] = MILLISECONDS_A_MINUTE),
				(_C$MIN$C$H$C$S$unit[H] = MILLISECONDS_A_HOUR),
				(_C$MIN$C$H$C$S$unit[S] = MILLISECONDS_A_SECOND),
				_C$MIN$C$H$C$S$unit)[unit] || 1;
			var nextTimeStamp = this.$d.getTime() + number * step;
			return Utils.w(nextTimeStamp, this);
		};
		_proto.subtract = function subtract(number, string) {
			return this.add(number * -1, string);
		};
		_proto.format = function format(formatStr) {
			var _this3 = this;
			var locale = this.$locale();
			if (!this.isValid()) return locale.invalidDate || INVALID_DATE_STRING;
			var str = formatStr || FORMAT_DEFAULT;
			var zoneStr = Utils.z(this);
			var $H = this.$H,
				$m = this.$m,
				$M = this.$M;
			var weekdays = locale.weekdays,
				months = locale.months,
				meridiem = locale.meridiem;
			var getShort = function getShort(arr, index, full, length) {
				return (
					(arr && (arr[index] || arr(_this3, str))) || full[index].slice(0, length)
				);
			};
			var get$H = function get$H(num) {
				return Utils.s($H % 12 || 12, num, "0");
			};
			var meridiemFunc =
				meridiem ||
				function(hour, minute, isLowercase) {
					var m = hour < 12 ? "AM" : "PM";
					return isLowercase ? m.toLowerCase() : m;
				};
			var matches = {
				YY: String(this.$y).slice(-2),
				YYYY: this.$y,
				M: $M + 1,
				MM: Utils.s($M + 1, 2, "0"),
				MMM: getShort(locale.monthsShort, $M, months, 3),
				MMMM: getShort(months, $M),
				D: this.$D,
				DD: Utils.s(this.$D, 2, "0"),
				d: String(this.$W),
				dd: getShort(locale.weekdaysMin, this.$W, weekdays, 2),
				ddd: getShort(locale.weekdaysShort, this.$W, weekdays, 3),
				dddd: weekdays[this.$W],
				H: String($H),
				HH: Utils.s($H, 2, "0"),
				h: get$H(1),
				hh: get$H(2),
				a: meridiemFunc($H, $m, true),
				A: meridiemFunc($H, $m, false),
				m: String($m),
				mm: Utils.s($m, 2, "0"),
				s: String(this.$s),
				ss: Utils.s(this.$s, 2, "0"),
				SSS: Utils.s(this.$ms, 3, "0"),
				Z: zoneStr
			};

			return str.replace(REGEX_FORMAT, function(match, $1) {
				return $1 || matches[match] || zoneStr.replace(":", "");
			});
		};
		_proto.utcOffset = function utcOffset() {
			return -Math.round(this.$d.getTimezoneOffset() / 15) * 15;
		};
		_proto.diff = function diff(input, units, _float) {
			var _C$Y$C$M$C$Q$C$W$C$D$;
			var unit = Utils.p(units);
			var that = dayjs(input);
			var zoneDelta =
				(that.utcOffset() - this.utcOffset()) * MILLISECONDS_A_MINUTE;
			var diff = this - that;
			var result = Utils.m(this, that);
			result =
				((_C$Y$C$M$C$Q$C$W$C$D$ = {}),
				(_C$Y$C$M$C$Q$C$W$C$D$[Y] = result / 12),
				(_C$Y$C$M$C$Q$C$W$C$D$[M] = result),
				(_C$Y$C$M$C$Q$C$W$C$D$[Q] = result / 3),
				(_C$Y$C$M$C$Q$C$W$C$D$[W] = (diff - zoneDelta) / MILLISECONDS_A_WEEK),
				(_C$Y$C$M$C$Q$C$W$C$D$[D] = (diff - zoneDelta) / MILLISECONDS_A_DAY),
				(_C$Y$C$M$C$Q$C$W$C$D$[H] = diff / MILLISECONDS_A_HOUR),
				(_C$Y$C$M$C$Q$C$W$C$D$[MIN] = diff / MILLISECONDS_A_MINUTE),
				(_C$Y$C$M$C$Q$C$W$C$D$[S] = diff / MILLISECONDS_A_SECOND),
				_C$Y$C$M$C$Q$C$W$C$D$)[unit] || diff;
			return _float ? result : Utils.a(result);
		};
		_proto.daysInMonth = function daysInMonth() {
			return this.endOf(M).$D;
		};
		_proto.$locale = function $locale() {
			return Ls[this.$L];
		};
		_proto.locale = function locale(preset, object) {
			if (!preset) return this.$L;
			var that = this.clone();
			var nextLocaleName = parseLocale(preset, object, true);
			if (nextLocaleName) that.$L = nextLocaleName;
			return that;
		};
		_proto.clone = function clone() {
			return Utils.w(this.$d, this);
		};
		_proto.toDate = function toDate() {
			return new Date(this.valueOf());
		};
		_proto.toJSON = function toJSON() {
			return this.isValid() ? this.toISOString() : null;
		};
		_proto.toISOString = function toISOString() {
			return this.$d.toISOString();
		};
		_proto.toString = function toString() {
			return this.$d.toUTCString();
		};
		return Dayjs;
	})();
	var proto = Dayjs.prototype;
	dayjs.prototype = proto;
	[
		["$ms", MS],
		["$s", S],
		["$m", MIN],
		["$H", H],
		["$W", D],
		["$M", M],
		["$y", Y],
		["$D", DATE]
	].forEach(function(g) {
		proto[g[1]] = function(input) {
			return this.$g(input, g[0], g[1]);
		};
	});
	dayjs.extend = function(plugin, option) {
		if (!plugin.$i) {
			plugin(option, Dayjs, dayjs);
			plugin.$i = true;
		}
		return dayjs;
	};
	dayjs.locale = parseLocale;
	dayjs.isDayjs = isDayjs;
	dayjs.unix = function(timestamp) {
		return dayjs(timestamp * 1e3);
	};
	dayjs.en = Ls[L];
	dayjs.Ls = Ls;
	dayjs.p = {};

	//获取列表评论
	function getListComment(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "comment/twoLevelTree",
				param: setNewJSON({}, _param.param),
				tag: "twoLevelTree",
				name: "评论"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var setData = function setData(_eItem) {
						var item = {};
						item.id = _eItem.id || "";
						item.createBy = _eItem.publishAccountId || "";
						item.name = _eItem.commentUserName || "";
						item.url = _eItem.headImg || _eItem.photo;
						item.content = _eItem.commentContent || "";
						item.time = _eItem.createDate || "";
						item.likeNum = _eItem.praisesCount;
						item.likeIs = _eItem.hasClickPraises;
						item.replyName = _eItem.toCommenter || "";
						item.attachments = _eItem.fileInfos || [];
						item.userRoles = _eItem.userRoles || [];
						item.userArea = _eItem.userArea || "";
						item.checkedStatus = _eItem.checkedStatus || 0;
						item.replyList = [];
						return item;
					};
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = setData(_eItem);
						(_eItem.children || []).forEach(function(_nItem) {
							var nItem = setData(_nItem);
							item.replyList.push(nItem);
						});
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取当前工作人员管理工作站栏目
	function getWorkerStation(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "stationNotice/minestation",
				param: setNewJSON({query: {isUsing: 1}}, _param.param),
				tag: "minestation",
				name: "工作站"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.id = _eItem.id || "";
						item.name = _eItem.name || "";
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	function getMemberIdentity(role) {
		var rName = G.sysSign == "rd" ? "代表" : "委员";
		switch (role) {
			case "nation":
				return "国" + rName;
			case "province":
				return "省" + rName;
			case "city":
				return "市" + rName;
			case "county":
				return "区/县" + rName;
			case "town":
				return "乡镇(街道)" + rName;
		}
	}

	//获取工作站留言回复列表
	function getList50_2_ans(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "stationLetterAnswer/list",
				param: setNewJSON({pageNo: 1, pageSize: 999}, _param.param),
				areaId: _param.areaId,
				tag: "list50_2_ans",
				name: "留言回复"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.content = _eItem.content || "";
						item.attachments = _eItem.attachments || [];
						item.addText =
							_eItem.answerUser +
							" " +
							dayjs(_eItem.answerTime).format("YYYY-MM-DD HH:mm");
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取工作站留言评价列表
	function getList50_2_eva(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "stationLetterEvaluate/list",
				param: setNewJSON({pageNo: 1, pageSize: 999}, _param.param),
				areaId: _param.areaId,
				tag: "list50_2_eva",
				name: "留言评价"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.id = _eItem.id;
						item.title = _eItem.evaluateResultName || "";
						item.content = _eItem.content || "";
						item.attachments = _eItem.attachments || [];
						item.addText = dayjs(_eItem.evaluateTime).format("YYYY-MM-DD HH:mm");
						item.isWorker = _param.isWorker;
						item.checkStatus = _eItem.checkStatus || 0;
						var tag = item.checkStatus;
						if (tag == 0 || tag == 2) {
							item.tag =
								tag == 0
									? {text: "待审核", color: "#e6a23c"}
									: {text: "审核不通过", color: "#BF2222"};
						}
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取工作站任务回执列表
	function getList50_4_callback(_param, _callback) {
		ajaxProcess(
			{
				url: _param.url || appUrl() + "stationNoticeCallback/list",
				param: setNewJSON({pageNo: 1, pageSize: 20}, _param.param),
				areaId: _param.areaId,
				tag: "list50_4_ca",
				name: "工作站任务回执"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.id = _eItem.id || "";
						item.title = _eItem.name || "";
						item.content = removeTag(_eItem.content || "");
						item.time = _eItem.publishTime || _eItem.createDate || "";
						item.source = _eItem.publishOfficeId || "";
						item.channelId = _eItem.noticeTaskName || ""; //类型
						item.isTop = _eItem.isTop || ""; //置顶
						item.attachmentIds = _eItem.attachmentIds || ""; //附件
						item.isReceipt = _eItem.isReceipt || ""; //回执1
						item.isRedDot = _eItem.isRedDot || ""; //是否未读
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取工作站值班列表
	function getList50_5(_param, _callback, dget) {
		if ((!G.userRoles || G.userRoles.length == 0) && !dget) {
			getDictionary(
				{param: {dictCodes: ["station_member_area_type"]}, tag: "t2"},
				function(ret, err) {
					var data = ret ? ret.dealWith || [] : [];
					G.userRoles = data;
					getList50_5(_param, _callback, 1);
				}
			);
			return;
		}
		if (_param.param.startTime) {
			_param.param.startTime = dayjs(_param.param.startTime)
				.subtract(1, "second")
				.valueOf();
		}
		ajaxProcess(
			{
				url: _param.url || appUrl() + "stationOnDuty/listapp",
				param: setNewJSON({}, _param.param),
				areaId: _param.areaId,
				tag: _param.tag || "list50_duty",
				name: "工作站值班"
			},
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {
							users: []
						};

						item.id = _eItem.id || "";
						item.stationId = _eItem.stationId;
						item.title = _eItem.theme || "";
						item.startTime = _eItem.startTime;
						item.endTime = _eItem.endTime;
						var timeFormat = dayjs(item.startTime).format("YYYY-MM-DD");
						item.time =
							dayjs(item.startTime).format("YYYY-MM-DD HH:mm") +
							"至" +
							dayjs(item.endTime).format("YYYY-MM-DD HH:mm");
						if (timeFormat == dayjs().format("YYYY-MM-DD")) {
							item.tag = {text: "今日", color: G.appTheme};
						}
						if (!getItemForKey(timeFormat, nowList, "timeId")) {
							item.timeId = timeFormat;
						}
						var memberList = _eItem.memberList || [];
						memberList.forEach(function(_uItem) {
							var uItem = {
								position: _uItem.position || ""
							};

							uItem.id = _uItem.id || "";
							uItem.targetId = _uItem.accountId || "";
							uItem.name = _uItem.userName || "";
							uItem.url = _uItem.photo || _uItem.headImg || "";
							uItem.topUserRole = _uItem.topUserRole || "";
							uItem.label =
								(getItemForKey(uItem.topUserRole, G.userRoles) || {}).value ||
								getMemberIdentity(uItem.topUserRole) ||
								"";
							item.users.push(uItem);
						});
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			}
		);
	}

	//获取全局搜索详情展示文本
	function getSearchAllModel(_param, _callback) {
		ajax(
			{u: appUrl() + "hadoop_api/datax/api/Directory/model"},
			_param.tag || "detailsmodel",
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					ret.dealWith = data.filter(function(item) {
						return item.detailsDisplay == 1;
					});
				}
				_callback(ret, err);
			},
			"model",
			"post",
			{
				body: JSON.stringify(_param.param)
			}
		);
	}

	//获取全局搜索详情
	function getDetailsSearchAll(_param, _callback) {
		ajax(
			{u: appUrl() + "hadoop_api/datax/pubApi/dataInfo"},
			_param.tag || "details" + _param.param.detailId,
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || {} : {};
				if (isObject(data) && isArray(data.detail) && data.detail.length > 0) {
					data = data.detail[0];
					if (isObject(data)) {
						var contentBefore = [];
						_param.showAllModel.forEach(function(_eItem) {
							var field = _eItem.mapperField || _eItem.field;
							var text = data[field] || "-";
							var regex = /<\/?[a-z][a-z0-9]*\b[^>]*>|<br\s*\/?>|\n/gi;
							if (regex.test(text)) {
								contentBefore.push({
									hint: _eItem.fieldName
								});

								_eItem.fieldName = "";
								_eItem.type = "richText";
							}
							contentBefore.push({
								hint: _eItem.fieldName,
								text: text,
								type: _eItem.type
							});
						});
						ret.dealWith = {
							title: _param.pageParam.dTitle || "",
							contentBefore: contentBefore
						};
					}
				}
				_callback(ret, err);
			},
			"详情",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	//获取app文本详情
	function getDetailsAppText(_param, _callback) {
		ajax(
			{u: appUrl() + ("apptext/type/" + _param.pt)},
			_param.tag || "details" + _param.pt,
			function(ret, err) {
				var data = ret ? ret.data || [] : [];
				if (isArray(data) && data.length) {
					var id = _param.param.detailId;
					var nowItem = data.length > 1 && id ? getItemForKey(id, data, "id") : null;
					data = nowItem || data[0];
					ret.dealWith = {
						title: "",
						content: data.content || "",
						data: data
					};

					if (!_param.dt) {
						G.headTitle = data.title || "";
					}
				}
				_callback(ret, err);
			},
			"详情",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			{Authorization: ""}
		);
	}

	//获取资讯详情
	function getDetails5(_param, _callback) {
		ajax(
			{u: appUrl() + "newsContent/info"},
			_param.tag || "details" + _param.param.detailId,
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || {} : {};
				if (data.id) {
					var unshiftContent = "",
						pushContent = "";
					var fileLink = data.linkDetailVos || [];
					var infoPic = data.infoPic || "";
					var infoContent = data.infoContent || "";
					infoPic = infoPic
						? infoPic.indexOf(",") != -1
							? infoPic.split(",")[0]
							: infoPic
						: "";
					var infoVideo = data.infoVideo || "";
					if (isParameters(fileLink) && fileLink.length) {
						var useType1 = [],
							useType2 = [];
						fileLink.forEach(function(_mItem) {
							if (
								_mItem.fileId ||
								(isArray(_mItem.attachments) && _mItem.attachments.length)
							) {
								if (_mItem.useType == "1" && useType1.length < 3) {
									useType1.push({url: _mItem.attachments[0].newFileName});
								} else if (_mItem.useType == "2") {
									useType2.push(
										_mItem.fileId ||
											(_mItem.attachments.length ? _mItem.attachments[0].id : "")
									);
								}
							}
						});
						if (!infoPic && useType1.length) {
							infoPic = useType1[0].url;
						}
						if (useType2.length && useType2[0]) {
							infoVideo = useType2[0];
						}
					}
					var match = infoContent.match(/<img[^>]+src="([^">]+)"/);
					var cImg = match ? match[1] : "";
					if (!infoPic && cImg) {
						infoPic = cImg;
					}
					if (infoVideo) {
						unshiftContent += '<video src="' + infoVideo + '"></video></br>';
					}
					ret.dealWith = {
						code: module5.code,
						name: {"1": "资讯", "2": "资料", "7": "履职技巧"}[data.moduleId || "1"],
						id: data.id,
						title: data.infoTitle || "",
						subTitle: data.infoSubtitle || "",
						content: unshiftContent + infoContent + pushContent,
						url: infoPic,
						attachments: data.attachments || [],
						time: dayjs(data.pubTime).format("YYYY-MM-DD"),
						source: data.infoSource,
						leftAdd: [
							{text: dayjs(data.pubTime).format("YYYY-MM-DD")},
							{text: data.infoSource}
						],
						rightAdd: [],
						share: data.isShare == "1",
						comment: data.isCompraise || "1",
						commentList: true,
						screen: data.isScreen || "1",
						module: data.moduleId || "1"
					};
				}
				_callback(ret, err);
			},
			"详情",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	//获取联系点资讯详情
	function getDetails39_3(_param, _callback) {
		ajax(
			{u: appUrl() + "white_api/lawConsultRegulation/info"},
			_param.tag || "details" + _param.param.detailId,
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || {} : {};
				if (data.id) {
					var unshiftContent = "",
						pushContent = "";
					var infoPic = data.images || "";
					var infoContent = data.content || "";
					infoPic = infoPic
						? infoPic.indexOf(",") != -1
							? infoPic.split(",")[0]
							: infoPic
						: "";
					var infoVideo = data.infoVideo || "";
					var match = infoContent.match(/<img[^>]+src="([^">]+)"/);
					var cImg = match ? match[1] : "";
					if (!infoPic && cImg) {
						infoPic = cImg;
					}
					if (infoVideo) {
						unshiftContent += '<video src="' + infoVideo + '"></video></br>';
					}
					ret.dealWith = {
						code: _param.code,
						// name:{"pin":"一站一品","news":"信息发布","state":"国家机关进站"}[data.businessCode],
						id: data.id,
						title: data.title || "",
						subTitle: data.infoSubtitle || "",
						content: unshiftContent + infoContent + pushContent,
						url: infoPic,
						attachments: data.attachments || [],
						time: dayjs(data.publishTime).format("YYYY-MM-DD"),
						source: data.bannerName,
						leftAdd: [
							{text: data.bannerName},
							{text: dayjs(data.createDate).format("YYYY-MM-DD")}
						],
						rightAdd: [],
						share: data.isShare == "1",
						comment: false,
						commentList: false,
						screen: data.isScreen || "1"
					};

					if (_param.code == module39_4.code) {
						ret.dealWith.tag = {text: "未开始", color: "#e6a23c"};
						if (data.status == "进行中") {
							ret.dealWith.tag = {text: "进行中", color: "#3894FF"};
						} else if (data.status == "已结束") {
							ret.dealWith.tag = {text: "已结束", color: "#777676"};
						}
						ret.dealWith.contentBefore = [
							{text: "\u6240\u5C5E\u8054\u7CFB\u70B9\uFF1A" + (data.pointNames || "")},
							{
								text:
									"活动时间：" +
									dayjs(data.startDate).format("YYYY-MM-DD HH:mm") +
									"至" +
									dayjs(data.endDate).format("YYYY-MM-DD HH:mm")
							},
							{text: "活动地点：" + (data.publishLocation || "")}
						];
					}
				}
				_callback(ret, err);
			},
			"详情",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	//获取工作站资讯详情
	function getDetails50_1(_param, _callback) {
		ajax(
			{u: appUrl() + "stationNews/info"},
			_param.tag || "details" + _param.param.detailId,
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || {} : {};
				if (data.id) {
					var unshiftContent = "",
						pushContent = "";
					var infoPic = data.infoPic || "";
					var infoContent = data.infoContent || "";
					infoPic = infoPic
						? infoPic.indexOf(",") != -1
							? infoPic.split(",")[0]
							: infoPic
						: "";
					var infoVideo = data.infoVideo || "";
					var match = infoContent.match(/<img[^>]+src="([^">]+)"/);
					var cImg = match ? match[1] : "";
					if (!infoPic && cImg) {
						infoPic = cImg;
					}
					if (infoVideo) {
						unshiftContent += '<video src="' + infoVideo + '"></video></br>';
					}
					ret.dealWith = {
						code: module50_1.code,
						name: {pin: "一站一品", news: "信息发布", state: "国家机关进站"}[
							data.businessCode
						],
						id: data.id,
						title: data.infoTitle || "",
						subTitle: data.infoSubtitle || "",
						content: unshiftContent + infoContent + pushContent,
						url: infoPic,
						attachments: data.attachments || [],
						time: dayjs(data.pubTime).format("YYYY-MM-DD"),
						source: data.infoSource,
						leftAdd: [{text: dayjs(data.pubTime).format("YYYY-MM-DD")}],
						rightAdd: [],
						share: data.isShare == "1",
						comment: false,
						commentList: false,
						screen: data.isScreen || "1"
					};
				}
				_callback(ret, err);
			},
			"详情",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	//获取工作站留言详情
	function getDetails50_2(_param, _callback) {
		ajax(
			{u: appUrl() + "stationLetter/info"},
			_param.tag || "details" + _param.param.detailId,
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || {} : {};
				if (data.id) {
					var attachments = data.attachments || [];
					var pictures = data.pictures || "";
					if (!attachments.length && pictures) {
						pictures.split(",").forEach(function(_nItem) {
							attachments.push({
								extName: "png",
								newFileName: _nItem
							});
						});
					}
					var tag = data.checkStatus;
					if (data.hasEvaluate && data.hasEvaluate != "0") {
						tag = 4;
					} else if (data.hasAnswer) {
						tag = 3;
					}

					ret.dealWith = {
						code: module50_2.code,
						id: data.id,
						title: data.title || "",
						content: data.content || "",
						senderId: data.senderId || "",
						contentBefore: [
							{text: "群众：" + replaceExceptFirst(data.senderUserName || "")},
							{text: "群众电话：" + replaceExceptFirst(data.senderMobile || "")},
							{
								text: "反映时间：" + dayjs(data.receiveTime).format("YYYY-MM-DD HH:mm")
							},
							{
								text:
									(G.sysSign == "rd" ? "站点" : "工作室") +
									"\uFF1A" +
									(data.stationName || "")
							},
							{
								text:
									"\u9A7B\u7AD9" +
									(G.sysSign == "rd" ? "代表" : "委员") +
									"\uFF1A" +
									(data.receiverName || "")
							}
						],

						otherAdd: [],
						optionBtn: [],
						attachments: attachments,
						time: dayjs(data.createDate).format("YYYY-MM-DD HH:mm"),
						leftAdd: [],
						rightAdd: [],
						share: false,
						comment: false,
						commentList: false,
						screen: false,
						userId: data.senderId || "",
						checkStatus: data.checkStatus,
						showCheckStatus: data.showCheckStatus,
						myEvaluateStatus: data.myEvaluateStatus,
						hasAnswer: data.hasAnswer,
						hasEvaluate: data.hasEvaluate,
						tag: [
							{text: "待审核", color: "#e6a23c"},
							{text: "未回复", color: "#F6931C"},
							{text: "审核不通过", color: "#BF2222"},
							{text: "已回复", color: "#3894FF"},
							{text: "已评价", color: "#2BBD4B"}
						][tag]
					};

					if (data.stationLetterType.name) {
						ret.dealWith.rightAdd.push({
							type: "tag",
							tType: 1,
							text: data.stationLetterType.name,
							color: G.appTheme
						});
					}
					ret.dealWith.rightAdd.push({
						type: "tag",
						tType: 1,
						text: ret.dealWith.tag.text,
						color: ret.dealWith.tag.color
					});
				}
				_callback(ret, err);
			},
			"详情",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	//获取工作站活动详情
	function getDetails50_3(_param, _callback) {
		ajax(
			{u: appUrl() + "stationActivity/info"},
			_param.tag || "details" + _param.param.detailId,
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || {} : {};
				if (data.id) {
					var attachments = data.attachments || [];
					ret.dealWith = {
						code: module50_3.code,
						id: data.id,
						title: data.title || "",
						content: data.content || "",
						contentBefore: [
							{
								text:
									"\u6D3B\u52A8" +
									(G.sysSign == "rd" ? "站点" : "工作室") +
									"\uFF1A" +
									(data.stationName || "")
							},
							{
								text:
									"活动时间：" +
									dayjs(data.beginTime).format("YYYY-MM-DD HH:mm") +
									"至" +
									dayjs(data.endTime).format("YYYY-MM-DD HH:mm")
							},
							{text: "活动地点：" + (data.address || "")}
						],

						userStatus: data.userStatus,
						otherAdd: [],
						optionBtn: [],
						attachments: attachments,
						time: dayjs(data.createDate).format("YYYY-MM-DD HH:mm"),
						leftAdd: [],
						rightAdd: [],
						share: false,
						comment: false,
						commentList: false,
						screen: false,
						users: (data.users || []).map(function(obj) {
							return {
								id: obj.accountId,
								name: obj.userName,
								url: obj.photo || obj.headImg || "",
								position: obj.position
							};
						})
					};

					ret.dealWith.otherAdd.push({
						title: "参与人员(" + ret.dealWith.users.length + ")",
						data: [],
						key: "participants",
						right: true
					});
					ret.dealWith.tag = {text: "未开始", color: "#e6a23c"};
					if (data.activityStatus == "进行中") {
						ret.dealWith.tag = {text: "进行中", color: "#3894FF"};
					} else if (data.activityStatus == "已结束") {
						ret.dealWith.tag = {text: "已结束", color: "#777676"};
					}
					ret.dealWith.leftAdd.push({
						type: "tag",
						tType: 1,
						text: ret.dealWith.tag.text,
						color: ret.dealWith.tag.color
					});
					if (data.stationActivityType.name) {
						ret.dealWith.leftAdd.push({
							type: "tag",
							tType: 1,
							text: data.stationActivityType.name,
							color: G.appTheme
						});
					}
				}
				_callback(ret, err);
			},
			"详情",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	//获取工作站任务详情
	function getDetails50_4(_param, _callback) {
		ajax(
			{u: appUrl() + "stationNotice/info"},
			_param.tag || "details" + _param.param.detailId,
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || {} : {};
				if (data.id) {
					var attachments = data.attachments || [];
					ret.dealWith = {
						code: module50_4.code,
						id: data.id,
						title: data.name || "",
						content: data.content || "",
						otherAdd: [],
						contentAdd: [{text: "下达单位：" + (data.publishStationName || "")}],
						optionBtn: [],
						attachments: attachments,
						time: dayjs(data.publishTime).format("YYYY-MM-DD HH:mm"),
						leftAdd: [{text: dayjs(data.publishTime).format("YYYY-MM-DD HH:mm")}],
						rightAdd: [],
						share: false,
						comment: false,
						commentList: false,
						screen: false,
						publishStationId: data.publishStationId,
						noticeCallbackType: (data.noticeCallbackType || {}).value,
						options: data.options || [],
						receiveStations: data.receiveStations || [],
						receiveMembers: data.receiveMembers || [],
						readStations: data.readStations || [],
						hasCallback: data.hasCallback,
						callbackStartTime: data.callbackStartTime,
						callbackEndTime: data.callbackEndTime
					};

					if (data.noticeTaskName) {
						ret.dealWith.rightAdd.push({
							type: "tag",
							tType: 1,
							text: data.noticeTaskName,
							color: G.appTheme
						});
					}
				}
				_callback(ret, err);
			},
			"详情",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	//获取工作站值班日志详情
	function getDetails50_5_feedback(_param, _callback) {
		ajax(
			{u: appUrl() + "feedback/info"},
			_param.tag || "details" + _param.param.detailId,
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || {} : {};
				if (data.id) {
					var attachments = data.attachments || [];
					ret.dealWith = {
						content: data.content || "",
						attachments: attachments
					};
				}
				_callback(ret, err);
			},
			"详情",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	//获取工作站履职补录详情
	function getDetails50_7(_param, _callback) {
		ajax(
			{u: appUrl() + "stationdutiesroom/info"},
			_param.tag || "details" + _param.param.detailId,
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || {} : {};
				if (data.id) {
					var attachments = data.attachments || [];
					ret.dealWith = {
						code: module50_7.code,
						id: data.id,
						title: data.title || "",
						content: data.content || "",
						otherAdd: [],
						contentBefore: [
							{text: "履职类型：" + ((data.stationActivityType || {}).name || "")},
							{text: "活动日期：" + dayjs(data.activityDate).format("YYYY-MM-DD")}
						],
						optionBtn: [],
						attachments: attachments,
						time: dayjs(data.createDate).format("YYYY-MM-DD HH:mm"),
						leftAdd: [
							{text: data.userName},
							{text: dayjs(data.createDate).format("YYYY-MM-DD HH:mm")}
						],
						rightAdd: [],
						share: false,
						comment: true,
						commentList: true
					};
				}
				_callback(ret, err);
			},
			"详情",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	//获取工作站履职补录详情
	function getDetails50_7_re(_param, _callback) {
		ajax(
			{u: appUrl() + "stationdutiesroom/info"},
			_param.tag || "details" + _param.param.detailId,
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || {} : {};
				if (data.id) {
					var attachments = data.attachments || [];
					ret.dealWith = {
						code: module50_7_re.code,
						id: data.id,
						title: data.title || "",
						content: data.content || "",
						contentBefore: [
							{text: "履职类型：" + ((data.stationActivityType || {}).name || "")},
							{text: "活动日期：" + dayjs(data.activityDate).format("YYYY-MM-DD")}
						],
						attachments: attachments,
						time: dayjs(data.createDate).format("YYYY-MM-DD HH:mm"),
						leftAdd: [
							{text: data.userName},
							{text: dayjs(data.createDate).format("YYYY-MM-DD HH:mm")}
						],
						rightAdd: [
							[
								{text: "待审核", color: "#F6931C"},
								{text: "已通过", color: "#50C614"},
								{text: "审核未通过", color: "#999999"}
							][data.passStatus || 0]
						],
						optionBtn: [],
						otherAdd: [],
						share: false,
						comment: false,
						commentList: false,
						passStatus: data.passStatus || 0,
						passReason: data.passReason || ""
					};

					if (data.passStatus) {
						ret.dealWith.otherAdd.push({
							title: "审核信息",
							data: [
								{
									content:
										"<div>\u5BA1\u6838\u4EBA\uFF1A" +
										data.passByName +
										"</div><div>\u5BA1\u6838\u7ED3\u679C\uFF1A" +
										ret.dealWith.rightAdd[0].text +
										"</div><div>\u5BA1\u6838\u610F\u89C1\uFF1A" +
										(data.passReason || "") +
										"</div>",
									addText: dayjs(data.passTime).format("YYYY-MM-DD HH:mm")
								}
							]
						});
					}
				}
				console.log(JSON.stringify(ret));
				_callback(ret, err);
			},
			"详情",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	var module5 = {
		name: "资讯",
		code: "5",
		businessCode: "informationContent",
		behaviorCode: "information_content"
	};
	var module6 = {
		name: sysSign() == "rd" ? "意见征集" : "网络议政",
		code: "6",
		businessCode: "opinioncollect"
	};
	var module9 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息",
		code: "9",
		businessCode: "cppcc_member"
	};
	var module9_1 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息变更申请",
		code: "9_1",
		businessCode: "cppccMemberCheckPrepare"
	};
	var module12 = {
		name: sysSign() == "rd" ? "圈子" : "委员说",
		code: "12",
		businessCode: "styleCircle"
	};
	var module17 = {name: "个人收藏", code: "17", listFlat: true};
	var module39_4 = {name: "联系点活动", code: "39_4"};
	var module50_1 = {name: "工作站资讯", code: "50_1"};
	var module50_2 = {name: "工作站留言", code: "50_2"};
	var module50_3 = {name: "工作站活动", code: "50_3"};
	var module50_4 = {name: "工作站任务接收", code: "50_4"};
	var module50_5 = {name: "工作站值班", code: "50_5"};
	var module50_7 = {
		name: "工作站履职补录",
		code: "50_7",
		businessCode: "stationDutiesRoom"
	};
	var module50_7_re = {name: "补录审核", code: "50_7_re"};

	//打开链接
	function openWin_url(_param) {
		_param.url = handleSYSLink(_param.url);
		if (
			(_param.wopen || _param.url.indexOf("wopen") != -1) &&
			platform() == "web"
		) {
			window.open(_param.url);
		} else {
			openWin("mo_details_url", "../mo_details_url/mo_details_url.stml", _param);
		}
	}

	//打开图片预览
	function openWin_imgPreviewer(_param) {
		G.imgPreviewerPop = {
			show: true,
			index: _param.index,
			imgs: _param.imgs
		};

		console.log(JSON.stringify(G.imgPreviewerPop));
	}

	//打开附件预览
	function openWin_filePreviewer(_param) {
		openWin("mo_details_url", "../mo_details_url/mo_details_url.stml", _param);
	}

	//打开聊天转发
	function openWin_chat_share(_param, _callback) {
		_param.title = _param.title || "发送给";
		_param.callback = _param.callback || "chat_share_callback";
		addEventListener(_param.callback, function(ret) {
			_callback && _callback(ret);
		});
		openWin("mo_chat_share", "../mo_chat_share/mo_chat_share.stml", _param);
	}

	//打开收藏
	function openWin_collect(_item) {
		var openPage = "mo_favorite";
		var param = {};
		param.code = module17.code;
		openWin(
			openPage + (_item.id || module17.code),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
	}

	//打开代表工作站留言
	function openWin_workstation_lam(_item) {
		var openPage = "mo_workstation_lam";
		var param = {};
		param = setNewJSON(param, _item);
		openWin(
			openPage + (_item.id || ""),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
	}

	//打开代表工作站更多值班
	function openWin_workstation_duty(_item) {
		var openPage = _item.id ? "mo_details_n" : "mo_workstation_duty";
		var param = {};
		param.code = _item.code || module50_5.code;
		param = setNewJSON(param, _item);
		openWin(
			openPage + (_item.id || ""),
			"../" + openPage + "/" + openPage + ".stml",
			param
		);
	}

	//获取配置信息 登录后
	function getAppConfig(_param, _callback) {
		var postParam = {
			codes: isArray(_param) ? _param : _param.codes
		};

		ajax(
			{u: appUrl() + "config/read"},
			"config/read",
			function(ret, err) {
				_callback && _callback(ret, err);
			},
			"取app配置",
			"post",
			{
				body: JSON.stringify(postParam)
			}
		);
	}

	//获取字典数据
	function getDictionary(_param, _callback) {
		ajax(
			{u: appUrl() + "dictionary/selector"},
			"dictionary" + _param.tag,
			function(ret, err) {
				var data = ret ? ret.data || "" : "";
				data = data ? data[_param.param.dictCodes[0]] : [];
				if (isArray(data) && data.length) {
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						item.key = _eItem.id;
						item.value = _eItem.name || "";
						nowList.push(item);
					});
					ret.dealWith = nowList;
				}
				_callback && _callback(ret, err);
			},
			"字典",
			"post",
			{
				body: JSON.stringify(setNewJSON({}, _param.param))
			}
		);
	}

	//获取登录信息
	function getLoginInfo(_param, _callback) {
		ajax(
			{u: appUrl() + "login/user"},
			"login/user",
			function(ret, err) {
				if (ret && ret.code == 200 && ret.data.id != "anonymous") {
					saveLogin(ret.data);
				}
				_callback && _callback(ret, err);
			},
			"用户信息",
			"post",
			{
				values: _param.param
			},
			_param.header
		);
	}

	//弹窗提示
	function ajaxAlert(_param, _callback) {
		var param = {
			title: "提示",
			msg: _param.msg || "",
			buttons: ["确定", "取消"]
		};

		if (_param.alertParam) {
			param = setNewJSON(param, _param.alertParam);
		}
		console.log(_param.url + "\n" + JSON.stringify(_param.param));
		alert(param, function(ret) {
			if (ret.buttonIndex == "1") {
				ajaxProcess(_param, _callback);
			}
		});
	}

	//请求
	function ajaxProcess(_param, _callback) {
		if (_param.toast) showProgress(_param.toast);
		ajax(
			{u: _param.url, areaId: _param.areaId, web: _param.web},
			_param.tag || "ajaxProcess",
			function(ret, err) {
				hideProgress();
				if (_param.toast) {
					toast(ret ? ret.message || ret.data : NET_ERR$1);
				}
				_callback && _callback(ret, err);
			},
			_param.name || "\u64CD\u4F5C",
			"post",
			{
				body: JSON.stringify(_param.param)
			},
			_param.header
		);
	}

	function getAllChatInfo() {
		var chatInfos = JSON.parse(getPrefs("chatInfos") || "[]"),
			addInfo = [];
		chatInfos.forEach(function(_eItem) {
			if (_eItem.id) {
				if (/^(?!h|(\.)|(\/)).*/.test(_eItem.url)) {
					_eItem.url = showImg(_eItem.url);
				}
				addInfo.push(_eItem);
			}
		});
		G.chatInfos = addInfo;
		setPrefs("chatInfos", JSON.stringify(G.chatInfos));
		return G.chatInfos;
	}

	function setAllChatInfo(_item) {
		getAllChatInfo();
		var nowInfo = JSON.parse(JSON.stringify(G.chatInfos));
		if (isArray(_item)) {
			_item.forEach(function(_eItem) {
				if (_eItem.id) {
					delItemForKey(_eItem.id, nowInfo, "id");
					nowInfo.push(_eItem);
				}
			});
		} else {
			if (!_item.id) {
				return;
			}
			delItemForKey(_item.id, nowInfo, "id");
			nowInfo.push(_item);
		}
		setPrefs("chatInfos", JSON.stringify(nowInfo));
		setRongChatInfo();
	}

	function setRongChatInfo() {
		if (platform() == "app" && api.require("zyRongCloudRTC")) {
			getAllChatInfo();
			var setUser = {
				apply: 1,
				users: G.chatInfos.map(function(obj) {
					return {id: chatHeader() + obj.id, name: obj.name, url: obj.url};
				}),
				groupUser: JSON.parse(getPrefs("rongGroupUser") || "[]"), //当前通话的群组成员列表id集合
				totalNumber: 10 //语音或视频最大人数
			};
			// console.log("融云设置信息："+JSON.stringify(setUser));
			api.require("zyRongCloudRTC").setUsers(setUser, function(ret) {
				removePrefs("rongGroupUser");
				setRongChatInfo();
				setTimeout(
					function() {
						sendEvent({name: "chat_refresh"});
					},
					api.systemType == "ios" ? 600 : 1500
				);
			});
		}
	}

	// 点赞/取消点赞
	function optionPraises(_param, _other) {
		var param = {
			businessCode: _param.code,
			businessId: _param.id
		};

		param = setNewJSON(param, _other);
		if (_param.likeIs) {
			param = {form: param};
		}
		ajax(
			{u: appUrl() + ("praises/" + (_param.likeIs ? "add" : "dels"))},
			"praises/add",
			function(ret, err) {},
			(_param.likeIs ? "" : "取消") + "\u70B9\u8D5E",
			"post",
			{
				body: JSON.stringify(param)
			}
		);
	}

	//获取评论点赞总数
	function getCommentCount(_param) {
		ajax(
			{u: appUrl() + "praises/count"},
			"praises/count",
			function(ret, err) {
				var data = ret ? ret.data || {} : {};
				_param.commentNum = data.commentCount || 0;
				_param.likeNum = data.praisesCount || 0;
				_param.likeIs = data.hasClickPraises || false;
			},
			"评论点赞总数",
			"post",
			{
				body: JSON.stringify({
					businessCode: _param.code,
					businessId: _param.id,
					isExcludeSubComment: _param.isExcludeSubComment
				})
			}
		);
	}

	// 是否收藏
	function getIsCollect(_param, _callback) {
		ajax(
			{u: appUrl() + "favorite/findBusinessIds"},
			"findBusinessIds",
			function(ret) {
				var data = ret && ret.code == 200 ? ret.data || [] : [];
				_callback && _callback(getItemForKey(_param.id, data));
			},
			"\u662F\u5426\u6536\u85CF",
			"post",
			{
				body: JSON.stringify({
					businessCode: _param.code
				})
			}
		);
	}

	// 收藏/取消收藏
	function optionCollect(_param, _callback) {
		var param = {
			businessCode: _param.code,
			businessId: _param.id,
			theme: _param.theme,
			ids: _param.ids,
			folderId: _param.folderId || "0"
		};

		if (_param.collect) {
			param = {form: param};
		}
		ajax(
			{u: appUrl() + ("favorite/" + (_param.collect ? "add" : "dels"))},
			"favorite/add",
			function(ret, err) {
				_callback && _callback(ret, err);
			},
			(_param.collect ? "" : "取消") + "\u6536\u85CF",
			"post",
			{
				body: JSON.stringify(param)
			}
		);
	}

	function dealVideoId(_id) {
		return _id.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, "");
	}

	//当前页面播放视频集合
	function videoPlayPush(_id) {
		if (!G.playVideos) {
			G.playVideos = [];
		}
		if (getItemForKey(_id, G.playVideos)) {
			return;
		}
		videoPlayRemoves();
		G.playVideos.push(_id);
		console.log("当前播放：" + JSON.stringify(G.playVideos));
	}

	//去除某个视频播放
	function videoPlayRemove(_id) {
		delItemForKey(_id, G.playVideos);
	}

	//去除所有播放
	function videoPlayRemoves() {
		(G.playVideos || []).forEach(function(_id) {
			document.getElementById(_id) && document.getElementById(_id).pause();
			videoPlayRemove(_id);
		});
	}

	//全局页面引用变量
	var G = {
		sysSign: sysSign(), //人大政协标识
		pageWidth: "", //页面总宽度
		onShowNum: -1, //当前页面展示次数 1为首次
		appName: "", //app名字
		appFont: "", //app全局字体
		appFontSize: 0, //app全局字体大小
		appTheme: "", //app全局主题色
		headTheme: "", //head全局主题色
		headColor: "", //标题前景色
		headTitle: "", //标题栏文字
		loginInfo: "",

		careMode: false, //是否启用了关怀模式
		systemtTypeIsPlatform: false, //系统类型是否是平台版
		isAppReview: false, //app是否上架期间 隐藏和显示部分功能
		v: "", //缓存版本号

		uId: "", //普通用户id
		userId: "", //当前用户id 账号id
		userName: "", //当前用户名字
		userImg: "", //当前用户头像
		areaId: "", //当前地区id
		specialRoleKeys: [], //当前用户角色集合
		isAdmin: false, //是否管理员 拥有所有权限
		grayscale: "", //全局置灰
		watermark: "", //全局水印

		// chatInfos:[],
		// chatInfoTask:[],

		alertPop: {
			// alert提示框
			show: false
		},

		actionSheetPop: {
			//actionSheet弹出框
			show: false
		},

		imgPreviewerPop: {
			//图片预览
			show: false
		},

		areasPop: {
			// 全局地区弹窗
			show: false
		},

		numInputPop: {
			// 全局数字弹窗
			show: false
		},

		favoritePop: {
			//收藏弹窗
			show: false
		},

		sharePosterPop: {
			//分享海报
			show: false
		},

		sharePop: {
			//分享
			show: false
		},

		identifyAudioPop: {
			//语音输入
			show: false
		},

		selectPop: {
			//单多选
			show: false
		},

		qrcodePop: {
			//h5扫码
			show: false
		},

		addressBookPop: {
			//通讯录
			show: false
		},

		fileListPop: {
			//选择文件
			show: false
		}
	};

	//默认情况下是否展示标题栏
	function showHeader() {
		return platform() == "app";
	}

	//计算头部离顶上距离
	function headerTop(_type) {
		if (platform() == "mp" && _type) {
			var wxArea = wx.getMenuButtonBoundingClientRect();
			return wxArea.top + (_type == 2 ? wxArea.height : 0);
		}
		return showHeader() ? safeArea().top : 0;
	}

	//底部可视区域
	function footerBottom() {
		return safeArea().bottom;
	}

	//动态加载字体和大小
	function loadConfiguration(size) {
		return (
			"font-size:" +
			((G.appFontSize || 0) + (size || 0)) +
			"px;" +
			(G.appFont != "heitiSimplified" ? "font-family:" + G.appFont + ";" : "")
		);
	}

	//动态宽度配置
	function loadConfigurationSize(size, _who) {
		var changeSize = size || 0,
			cssWidth,
			cssHeight;
		if (isArray(size)) {
			cssWidth = "width:" + (G.appFontSize + (size[0] || 0)) + "px;";
			cssHeight = "height:" + (G.appFontSize + (size[1] || 0)) + "px;";
		} else {
			cssWidth = "width:" + (G.appFontSize + changeSize) + "px;";
			cssHeight = "height:" + (G.appFontSize + changeSize) + "px;";
		}
		if (!_who) {
			return cssWidth + cssHeight;
		} else {
			return _who == "w" ? cssWidth : cssHeight;
		}
	}

	//动态计算
	function dataForNum(_num) {
		return Array.from({length: _num || 0}).fill("");
	}

	//获取item参数
	function getItemForKey(_value, _list, _key, _child) {
		if (!isParameters(_list)) return;
		var hasChild = false,
			listLength = _list.length;
		for (var i = 0; i < listLength; i++) {
			var listItem = _list[i];
			if (isArray(listItem)) {
				hasChild = true;
				var result = getItemForKey(_value, listItem, _key, true);
				if (result) return result;
			} else {
				if (!isObject(listItem)) {
					if (listItem === _value) return listItem;
				} else {
					var listItemKey = listItem[_key || "key"];
					if (isArray(listItemKey)) {
						hasChild = true;
						var result = getItemForKey(_value, listItemKey, _key, true);
						if (result) {
							return listItem;
						}
					} else if (!isObject(listItemKey) && listItemKey === _value) {
						listItem["_i"] = i;
						return listItem;
					}
				}
				if (isObject(listItem) && isArray(listItem.children)) {
					hasChild = true;
					var result = getItemForKey(_value, listItem.children, _key, true);
					if (result) return result;
				}
			}
		}
		if (!_child && !hasChild) return false;
	}

	//删除item中的元素
	function delItemForKey(_obj, _list, _key) {
		var contrastObj = !isObject(_obj) ? _obj : _obj[_key || "key"];
		for (var i = 0; i < _list.length; i++) {
			if (
				(!isObject(_list[i]) ? _list[i] : _list[i][_key || "key"]) === contrastObj
			) {
				_list.splice(i, 1);
				delItemForKey(_obj, _list, _key);
				break;
			}
		}
	}

	//web和小程序阻止底部事件
	function stopBubble(e) {
		if (!e) return;
		if (platform() == "web") {
			e.preventDefault();
			e.stopPropagation();
		} else if (platform() == "mp") {
			e.$_canBubble = false;
		}
	}

	//移动事件 不左滑关闭屏幕
	function touchmove() {
		G.nTouchmove = true;
		clearTimeout(G.touchmoveTask);
		G.touchmoveTask = setTimeout(function() {
			G.nTouchmove = false;
		}, 1000);
	}

	//适配图片
	function showImg(_item, _add) {
		if (_add === void 0) {
			_add = "";
		}
		var baseUrl = isObject(_item) ? _item.url || "" : _item || "";
		if (!baseUrl) return;
		if (/^(?!h|(\.)|(\/)).*/.test(baseUrl)) {
			baseUrl =
				appUrl() +
				"image/" +
				_add +
				(baseUrl.indexOf("-compress-") > -1
					? baseUrl.split("-compress-")[1]
					: baseUrl);
		}
		return baseUrl + (baseUrl.indexOf("?") != -1 ? "&" : "?") + "v=" + G.v;
	}

	//获取元素位置宽高
	function getBoundingClientRect(_id, _callback) {
		if (!document.getElementById(_id)) return;
		if (platform() == "mp") {
			document
				.getElementById(_id)
				.$$getBoundingClientRect()
				.then(function(res) {
					return _callback(res);
				});
		} else {
			return _callback(document.getElementById(_id).getBoundingClientRect());
		}
	}

	//颜色转成rgba
	function colorRgba(_color, _alpha) {
		if (!_color) return;
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		var color = _color.toLowerCase();
		if (reg.test(color)) {
			if (color.length === 4) {
				var colorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
				}
				color = colorNew;
			}
			var colorChange = [];
			for (var i = 1; i < 7; i += 2) {
				colorChange.push(parseInt("0x" + color.slice(i, i + 2)));
			}
			return (
				"rgba(" +
				colorChange.join(",") +
				"," +
				(isParameters(_alpha) ? _alpha : "1") +
				")"
			);
		} else {
			return color;
		}
	}

	//删除所有缓存
	function cleanAllMsg() {
		var exitPrefs = [
			"isAutoLogin",
			"loginPassword",
			"sys_token",
			"sys_Id",
			"sys_UserID",
			"sys_UserName",
			"sys_AppPhoto",
			"sys_Mobile",
			"sys_Position",
			"sys_aresId",
			"sys_OfficeId",
			"sys_SpecialRoleKeys",
			"sdt_signin_phone",
			"public_token",
			"last14Msg",
			"sys_unread"
		];
		exitPrefs.forEach(function(_eItem) {
			removePrefs(_eItem);
		});
	}

	//列表无数据处理
	function dealData(_type, _this, ret) {
		if (!_type) {
			_this.data.listData = [];
			_this.data.emptyBox.type = ret ? "1" : "2";
			_this.data.emptyBox.text =
				ret && ret.code != 200 ? ret.message || ret.data : "";
		} else {
			_this.data.emptyBox.text = ret
				? ret.code == 200
					? LOAD_ALL
					: ret.message || ret.data
				: NET_ERR$1;
		}
	}

	//保存登录信息
	function saveLogin(_info) {
		var infolist = _info || {};
		setPrefs("sys_Id", infolist.id);
		setPrefs("sys_UserID", infolist.accountId);
		setPrefs("sys_UserName", infolist.userName);
		setPrefs("sys_AppPhoto", infolist.headImg || infolist.photo); //headImg用户头像 photo代表头像
		setPrefs("sys_Mobile", infolist.mobile);
		setPrefs("sys_Position", infolist.position);
		if (!getPrefs("sys_aresId")) {
			setPrefs("sys_aresId", infolist.areaId);
		}
		setPrefs("sys_OfficeId", infolist.officeId);
		setPrefs(
			"sys_SpecialRoleKeys",
			JSON.stringify(infolist.specialRoleKeys || [])
		);
		if (infolist.id) {
			// //修改头像后保存下缓存数据
			setAllChatInfo({
				id: infolist.accountId,
				name: infolist.userName,
				url: infolist.headImg || infolist.photo
			});
		}
		sendEvent({name: "sys_refresh"});
	}

	//获取链接中参数
	function getOtherParam(_url) {
		if (_url.indexOf("?") != -1) {
			_url = _url.substring(_url.indexOf("?") + 1);
		}
		var params = _url.split("&"),
			rp = {};
		for (var j = 0; j < params.length; j++) {
			if (params[j]) {
				var data_key = params[j].substring(0, params[j].indexOf("="));
				if (!data_key) {
					continue;
				}
				var data_value = decodeURIComponent(
					params[j].substring(params[j].indexOf("=") + 1)
				);
				if (data_value.indexOf("{") == 0 || data_value.indexOf("[") == 0)
					data_value = JSON.parse(data_value);
				rp[data_key] = data_value;
			}
		}
		return rp;
	}

	//通用上传附件 item格式  url本地地址路径 uploadId上传后的id	state状态【1上传中2完成3失败】
	function uploadFile(_item, callback) {
		if (_item._fileAjax || _item.module == "-noUpload") return;
		_item._fileAjax = true;
		_item.state = 1;
		if (_item.showToast) {
			showProgress("上传中");
		}
		var nCallack = function nCallack(ret, err) {
			hideProgress();
			var code = ret ? ret.code : "";
			if (code == 200) {
				var data = ret.data || {};
				_item.state = 2;
				_item.uploadId = data.id;
				_item.otherInfo = data;
			} else {
				_item.state = 3;
				_item.error = ret ? ret.message || ret.data : err.data || err.body || "";
			}
			callback && callback(_item);
		};
		if (platform() == "mp") {
			wx.uploadFile({
				url: appUrl() + "file/upload",
				filePath: _item.url.path,
				name: "file",
				header: {
					"Content-Type": "multipart/form-data",
					"u-login-areaId": areaId(),
					Authorization: getPrefs("sys_token") || ""
				},

				success: function success(res) {
					nCallack(JSON.parse(res.data), null);
				},
				fail: function fail(err) {
					nCallack(null, JSON.parse(err.data));
				}
			});
		} else {
			ajax(
				{u: appUrl() + "file/upload", web: _item.web},
				"file/upload" + _item.url,
				nCallack,
				"上传附件",
				"post",
				{
					files: {file: _item.url}
				},
				{"content-type": "file"}
			);
		}
	}

	//获取文件类型 并返回数据
	function getFileInfo(_name) {
		var name = (_name || "").toLocaleLowerCase(),
			fileInfo = {name: "file-unknow-fill", color: "#bccbd7", type: "unknown"};
		try {
			if (name.indexOf(".") != -1)
				name = name.split(".")[name.split(".").length - 1];
			switch (name) {
				case "xlsx":
				case "xlsm":
				case "xlsb":
				case "xltx":
				case "xltm":
				case "xls":
				case "xlt":
				case "et":
				case "csv":
				case "uos": //excel格式
					fileInfo.name = "file-excel-fill";
					fileInfo.color = "#00bd76";
					fileInfo.type = "excel";
					fileInfo.convertType = "0";
					break;
				case "doc":
				case "docx":
				case "docm":
				case "dotx":
				case "dotm":
				case "dot":
				case "xps":
				case "rtf":
				case "wps":
				case "wpt":
				case "uot": //word格式
					fileInfo.name = "file-word-fill";
					fileInfo.color = "#387efa";
					fileInfo.type = "word";
					fileInfo.convertType = "0";
					break;
				case "pdf": //pdf格式
					fileInfo.name = "file-pdf-fill";
					fileInfo.color = "#e9494a";
					fileInfo.type = "pdf";
					fileInfo.convertType = "20";
					break;
				case "ppt":
				case "pptx":
				case "pps":
				case "pot":
				case "pptm":
				case "potx":
				case "potm":
				case "ppsx":
				case "ppsm":
				case "ppa":
				case "ppam":
				case "dps":
				case "dpt":
				case "uop": //ppt
					fileInfo.name = "file-ppt-fill";
					fileInfo.color = "#ff7440";
					fileInfo.type = "ppt";
					fileInfo.convertType = "0";
					break;
				case "bmp":
				case "gif":
				case "jpg":
				case "pic":
				case "png":
				case "tif":
				case "jpeg":
				case "jpe":
				case "icon":
				case "jfif":
				case "dib": //图片格式 case 'webp':
					fileInfo.name = "file-text-fill";
					fileInfo.color = "#ff7440";
					fileInfo.type = "image";
					fileInfo.convertType = "440";
					break;
				case "txt": //文本
					fileInfo.name = "file-text-fill";
					fileInfo.color = "#2696ff";
					fileInfo.type = "txt";
					fileInfo.convertType = "0";
					break;
				case "rar":
				case "zip":
				case "7z":
				case "tar":
				case "gz":
				case "jar":
				case "ios": //压缩格式
					fileInfo.name = "file-zip-fill";
					fileInfo.color = "#a5b0c0";
					fileInfo.type = "compression";
					fileInfo.convertType = "19";
					break;
				case "mp4":
				case "avi":
				case "flv":
				case "f4v":
				case "webm":
				case "m4v":
				case "mov":
				case "3gp":
				case "rm":
				case "rmvb":
				case "mkv":
				case "mpeg":
				case "wmv": //视频格式
					fileInfo.name = "file-music-fill";
					fileInfo.color = "#e14a4a";
					fileInfo.type = "video";
					fileInfo.convertType = "450";
					break;
				case "mp3":
				case "m4a":
				case "amr":
				case "pcm":
				case "wav":
				case "aiff":
				case "aac":
				case "ogg":
				case "wma":
				case "flac":
				case "alac":
				case "wma":
				case "cda": //音频格式
					fileInfo.name = "file-music-fill";
					fileInfo.color = "#8043ff";
					fileInfo.type = "voice";
					fileInfo.convertType = "660";
					break;
				case "folder": //文件夹
					fileInfo.name = "folder-2-fill";
					fileInfo.color = "#ffd977";
					fileInfo.type = "folder";
					break;
			}
		} catch (e) {
			console.log(e.message);
		}
		return fileInfo;
	}

	//展示省略文字
	function showTextSize(_text, _size, _middle) {
		if (_size && _text) {
			if (_text.length > _size) {
				if (_middle) {
					var mSize = _size / 2;
					var nLast = getSizeText(_text, mSize);
					var nNext = getSizeText(_text, mSize, 1);
					if (nLast.length + nNext.length < _text.length) {
						_text = nLast + "..." + nNext;
					}
				} else {
					var nText = getSizeText(_text, _size);
					_text = nText + (nText.length < _text.length ? "..." : "");
				}
			}
		}
		return _text;
	}

	function getSizeText(_text, _size, _next) {
		var texts = _text.split("");
		var nowSize = 0,
			nowLength = 0;
		if (_next) {
			for (var i = texts.length - 1; i >= 0; i--) {
				nowSize += /[\u4E00-\u9FA5]|[\u0800-\u4E00]/.test(texts[i]) ? 1 : 0.7;
				nowLength++;
				if (nowSize >= _size) {
					break;
				}
			}
			return _text.substring(texts.length - nowLength);
		} else {
			for (var i = 0; i < texts.length; i++) {
				nowSize += /[\u4E00-\u9FA5]|[\u0800-\u4E00]/.test(texts[i]) ? 1 : 0.7;
				nowLength++;
				if (nowSize >= _size) {
					break;
				}
			}
			return _text.substring(0, nowLength);
		}
	}

	//选择文件并上传
	function chooseFile(_item, callback) {
		var max = isNumber(_item.max) ? _item.max : 0;
		if (platform() == "app") {
			if (api.systemType == "ios") {
				if (!confirmPer("storage", "chooseFile")) {
					//存储权限
					addEventListener("storagePer_chooseFile", function(ret, err) {
						removeEventListener("storagePer_chooseFile");
						if (ret.value.granted) {
							chooseFile(_item, callback);
						}
					});
					return;
				}
			} else {
				if (!api.require("zyRongCloud").hasAllFilesPermission()) {
					alert(
						{
							title: "提示",
							msg: "选择本机文件需要您授权访问所有文件权限，是否继续?",
							buttons: ["确定", "取消"]
						},
						function(ret) {
							if (ret.buttonIndex == "1") {
								api.require("zyRongCloud").requestAllFilesPermission(function(ret) {
									if (ret.status) {
										chooseFile(_item, callback);
									}
								});
							}
						}
					);
					return;
				}
			}
			var fileBrowser = api.require("fileBrowser");
			fileBrowser.open({}, function(ret, err) {
				fileBrowser.close();
				setTimeout(function() {
					_item.url = ret.url;
					uploadFile(_item, function(ret) {
						callback && callback(ret);
					});
				}, 500);
			});
		} else if (platform() == "web") {
			var h5Input = document.createElement("input");
			h5Input.type = "file";
			h5Input.accept = "";
			var ua = navigator.userAgent.toLowerCase();
			var version = "";
			if (ua.indexOf("android") > 0) {
				var reg = /android [\d._]+/gi;
				var v_info = ua.match(reg);
				version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, ".");
				version = parseInt(version.split(".")[0]);
			}
			if (!version || Number(version) <= 13) {
				h5Input.multiple = "multiple";
			}
			h5Input.click();
			h5Input.onchange = function() {
				var listLength =
					max != 0 && h5Input.files.length > max ? max : h5Input.files.length;
				for (var i = 0; i < listLength; i++) {
					(function(j) {
						var nItem = JSON.parse(JSON.stringify(_item));
						nItem.url = h5Input.files[j];
						uploadFile(nItem, function(ret) {
							callback && callback(ret);
						});
					})(i);
				}
			};
		} else if (platform() == "mp") {
			wx.chooseMessageFile({
				count: max != 0 ? max : 9,
				type: "file",
				success: function success(res) {
					for (var i = 0; i < res.tempFiles.length; i++) {
						(function(j) {
							var nItem = JSON.parse(JSON.stringify(_item));
							nItem.url = res.tempFiles[j];
							uploadFile(nItem, function(ret) {
								callback && callback(ret);
							});
						})(i);
					}
				}
			});
		}
	}

	//转换成html格式
	function convertRichText(value) {
		value = (isParameters(value) ? value : "") + "";
		if (isObject(value) || isArray(value)) {
			value = JSON.stringify(value);
		}
		var textList = value.split("\n");
		var str = "";
		for (var i = 0; i < textList.length; i++) {
			var addText = textList[i].replace(/&amp;/g, "&").replace(/ /g, "&nbsp;");
			if (addText) {
				str += "<p>" + addText + "</p>";
			}
		}
		return str;
	}

	// 判断是否为微信环境
	function isWeChat() {
		return /micromessenger/i.test(window.navigator.userAgent);
	}

	// 判断是否为小程序环境
	function isMiniProgram() {
		// 通过wx对象存在与否进行判断
		if (typeof wx === "object" && typeof wx.miniprogram === "object") {
			return true;
		} else {
			return false;
		}
	}

	//替换非第一个字符为*
	function replaceExceptFirst(inputString) {
		return inputString.replace(/(\S)(\S*)/g, function(match, firstChar, rest) {
			return firstChar + "*".repeat(rest.length);
		});
	}

	//人大政协标识  rd人大 zx政协
	function sysSign() {
		return getPrefs("sys_sign") || "rd";
	}

	//配置地址
	function appUrl() {
		var prot = sysSign() == "rd" ? "20169" : "20170";
		return (
			getPrefs("sys_appUrl") || "https://productpc.cszysoft.com:" + prot + "/lzt/"
		);
	}
	//tomcat配置地址
	function tomcatAddress() {
		return (
			getPrefs("sys_tomcatAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http://cszysoft.com:9090/"
				: "https://cszysoft.com:9091/")
		);
	}
	//分享地址
	function shareAddress(_type) {
		if (_type == 1 && platform() != "mp") return "../../";
		return (
			getPrefs("sys_shareAddress") ||
			(platform() == "web" && window.location.protocol == "http:"
				? "http:"
				: "https:") +
				"//cszysoft.com/appShare/" +
				(sysSign() == "rd" ? "platform5rd/" : "platform5zx/")
		);
	}
	//融云唯一前缀
	function chatHeader() {
		return getPrefs("sys_chatHeader") || "platform5" + sysSign();
	}
	//融云正测
	function chatEnvironment() {
		return getPrefs("sys_chatEnvironment") || "1";
	}
	//当前页面地区id
	function areaId() {
		return (
			(G._this && G._this.data.area ? G._this.data.area.key : "") ||
			pageParam().areaId ||
			getPrefs("sys_aresId") ||
			getPrefs("sys_platform") ||
			""
		);
	}

	//添加list中参数
	function addPostParam(_list) {
		var param = {};
		for (var i = 0; i < _list.length; i++) {
			var _eItem = _list[i];
			if (!_eItem.key || _eItem.notUpdate || _eItem.hide) {
				continue;
			}
			if (
				!_eItem.hide &&
				!_eItem.noRequired &&
				!(isArray(_eItem.value)
					? _eItem.value.length > 0
					: isNumber(_eItem.value)
					? true
					: _eItem.value)
			) {
				var dhint;
				switch (_eItem.type) {
					case "input":
					case "textarea":
						dhint = "请输入";
						break;
					case "attach":
					case "picture":
					case "signature":
					case "user":
						dhint = "请添加";
						break;
					default:
						dhint = "请选择";
						break;
				}

				dhint += _eItem.title || "";
				toast(_eItem.hint || dhint);
				return;
			}
			if (_eItem.type == "switch") {
				param[_eItem.key] = _eItem.value ? 1 : 0;
			} else if (isArray(_eItem.value)) {
				//数组转换
				if (isNumber(_eItem.min) && _eItem.value.length < _eItem.min) {
					toast(
						"\u8BF7\u9009\u62E9\u81F3\u5C11" + _eItem.min + "\u4E2A" + _eItem.title
					);
					return;
				}
				if (_eItem.valueType == "array") {
					param[_eItem.key] = _eItem.value.map(function(obj) {
						return isObject(obj) ? obj[_eItem.valueKey || "id"] : obj;
					});
				} else {
					param[_eItem.key] = _eItem.value
						.map(function(obj) {
							return isObject(obj) ? obj[_eItem.valueKey || "id"] : obj;
						})
						.join(_eItem.valueType || ",");
				}
			} else if (_eItem.type == "text" && _eItem.textType == "time") {
				//时间转换
				param[_eItem.key] = dayjs(_eItem.value).valueOf();
			} else if (_eItem.html) {
				//内容需要转换成html
				param[_eItem.key] = convertRichText(_eItem.value);
			} else {
				param[_eItem.key] = _eItem.value;
			}
		}
		return param;
	}

	//将二级提交参数 变更为一级
	function setParamToFirst(_list, _param) {
		if (!_param.form) return;
		(_list || []).forEach(function(_eItem) {
			if (!_eItem.key || _eItem.notUpdate || _eItem.hide) return;
			if (_eItem.paramFirst) {
				//将参数提到form外 到一级
				_param[_eItem.key] = _param.form[_eItem.key];
				delete _param.form[_eItem.key];
			}
		});
	}

	//获取字典数据
	function getDictData(_list, _callback, _edit) {
		function getDictCodes(_list, _data) {
			_list.forEach(function(_eItem) {
				if (_eItem.dictKey) _data.push({key: _eItem.key, dict: _eItem.dictKey});
				if (_eItem.type == "addMore") {
					getDictCodes(_eItem.children, _data);
				}
			});
		}
		var dictCodes = [];
		getDictCodes(_list, dictCodes);
		if (!dictCodes.length) return;
		//获取字典数据并赋值
		ajax(
			{u: appUrl() + "dictionary/selector"},
			"dictionaryselector",
			function(ret, err) {
				if (ret && ret.code == "200") {
					var data = ret ? ret.data || {} : {};
					dictCodes.forEach(function(_eItem) {
						var datas = data[_eItem.dict] || [];
						var dataItem = getItemForKey(_eItem.key, _list);
						if (isArray(datas) && datas.length && dataItem) {
							var showData = [];
							datas.forEach(function(_eItem, _eIndex, _eArr) {
								var key = _eItem.key || _eItem.id;
								var value = _eItem.name;
								showData.push({key: key, value: value, readonly: false, hide: false});
							});
							dataItem.data = showData;

							if (
								!dataItem.value &&
								(isParameters(dataItem.dValue) || isParameters(dataItem.dIndex)) &&
								!_edit
							) {
								var nowItem = isParameters(dataItem.dValue)
									? getItemForKey(dataItem.dValue, dataItem.data)
									: dataItem.data[dataItem.dIndex];
								if (nowItem) {
									dataItem.value = nowItem["key"];
								}
							}
							_callback && _callback();
						}
					});
				}
			},
			"字典列表",
			"post",
			{
				body: JSON.stringify({
					dictCodes: dictCodes.map(function(obj) {
						return obj.dict;
					})
				})
			}
		);
	}

	var ZSwitch = /*@__PURE__*/ (function(Component) {
		function ZSwitch(props) {
			Component.call(this, props);
		}

		if (Component) ZSwitch.__proto__ = Component;
		ZSwitch.prototype = Object.create(Component && Component.prototype);
		ZSwitch.prototype.constructor = ZSwitch;
		ZSwitch.prototype.change = function(e) {
			this.props.dataMore.value = e.detail.value;
			this.fire("change", this.props.dataMore);
		};
		ZSwitch.prototype.render = function() {
			return apivm.h("switch", {
				id: true,
				checked: this.props.dataMore.value,
				disabled: this.props.disabled || false,
				color: this.props.color || G.appTheme,
				onChange: this.change
			});
		};

		return ZSwitch;
	})(Component);
	apivm.define("z-switch", ZSwitch);

	var ItemSwitch = /*@__PURE__*/ (function(Component) {
		function ItemSwitch(props) {
			Component.call(this, props);
		}

		if (Component) ItemSwitch.__proto__ = Component;
		ItemSwitch.prototype = Object.create(Component && Component.prototype);
		ItemSwitch.prototype.constructor = ItemSwitch;
		ItemSwitch.prototype.input = function(e) {
			this.fire("input", this.props.item);
		};
		ItemSwitch.prototype.render = function() {
			return apivm.h(
				"view",
				{
					style:
						"display:" +
						(isParameters(this.props.item.hide) && this.props.item.hide
							? "none"
							: "flex") +
						";border-top:" +
						(this.props.item.line || 0) +
						"px solid rgba(0,0,0,0.03);"
				},
				apivm.h(
					"view",
					{style: "padding:" + (this.props.item.exhibit ? 13 : 18) + "px 16px;"},
					apivm.h(
						"text",
						{
							class: "required",
							style:
								"display:" +
								(!this.props.item.noRequired ? "flex" : "none") +
								";top:" +
								(this.props.item.exhibit ? 13 : 18) +
								"px;"
						},
						"*"
					),
					apivm.h(
						"view",
						{class: "flex_row"},
						apivm.h(
							"text",
							{
								class: "" + (this.props.item.twa ? "text_default" : "text_one"),
								style:
									loadConfiguration() +
									"color:#333;width:" +
									(this.props.item.twa
										? "auto"
										: G.appFontSize * 4 + (api.systemType == "ios" ? 6 : 4) + "px") +
									";"
							},
							this.props.item.title
						),
						apivm.h(
							"view",
							{class: "flex_w"},
							!this.props.item.exhibit &&
								apivm.h(
									"view",
									{style: "margin-left:10px;flex-direction: row-reverse;"},
									apivm.h("z-switch", {
										onChange: this.input,
										disabled: this.props.dataMore.readonly || this.props.item.readonly,
										dataMore: this.props.item,
										color: G.appTheme,
										height: 31
									})
								)
						)
					),
					apivm.h(
						"view",
						null,
						this.props.item.exhibit == 1 &&
							apivm.h("view", {style: "margin-top:10px;"})
					)
				)
			);
		};

		return ItemSwitch;
	})(Component);
	ItemSwitch.css = {
		".required": {
			position: "absolute",
			zIndex: "1",
			color: "#FF0000",
			left: "8px",
			fontSize: "16px",
			lineHeight: "20px"
		},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden",
			display: "inline"
		},
		".text_default": {
			textOverflow: "clip",
			whiteSpace: "normal",
			overflow: "visible",
			display: "inline"
		}
	};
	apivm.define("item-switch", ItemSwitch);

	var ZRadio = /*@__PURE__*/ (function(Component) {
		function ZRadio(props) {
			Component.call(this, props);
		}

		if (Component) ZRadio.__proto__ = Component;
		ZRadio.prototype = Object.create(Component && Component.prototype);
		ZRadio.prototype.constructor = ZRadio;
		ZRadio.prototype.render = function() {
			return apivm.h("a-iconfont", {
				size: G.appFontSize + (this.props.size || 0),
				name: this.props.checked
					? this.props.type == 2
						? "danxuan_xuanzhong"
						: this.props.type == 3
						? "fangxingxuanzhongfill"
						: "yuanxingxuanzhongfill"
					: this.props.type == 3
					? "fangxingweixuanzhong"
					: "danxuan_weixuanzhong",
				color: this.props.checked ? this.props.color || G.appTheme : "#999"
			});
		};

		return ZRadio;
	})(Component);
	apivm.define("z-radio", ZRadio);

	var ItemRadio = /*@__PURE__*/ (function(Component) {
		function ItemRadio(props) {
			Component.call(this, props);
		}

		if (Component) ItemRadio.__proto__ = Component;
		ItemRadio.prototype = Object.create(Component && Component.prototype);
		ItemRadio.prototype.constructor = ItemRadio;
		ItemRadio.prototype.input = function(e) {
			this.fire("input", this.props.item);
		};
		ItemRadio.prototype.isSelectValue = function(_item) {
			if (isArray(this.props.item.value)) {
				return getItemForKey(_item.key, this.props.item.value);
			} else {
				return _item.key == this.props.item.value;
			}
		};
		ItemRadio.prototype.cSelectbox = function(_item, _index) {
			if (
				this.props.dataMore.readonly ||
				this.props.item.readonly ||
				_item.readonly
			) {
				return;
			}
			if (isArray(this.props.item.value)) {
				if (getItemForKey(_item.key, this.props.item.value)) {
					delItemForKey(_item.key, this.props.item.value);
				} else {
					this.props.item.value.push(_item.key);
				}
			} else {
				this.props.item.value = _item.key;
			}
			this.input();
		};
		ItemRadio.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					style:
						"display:" +
						(isParameters(this.props.item.hide) && this.props.item.hide
							? "none"
							: "flex") +
						";border-top:" +
						(this.props.item.line || 0) +
						"px solid rgba(0,0,0,0.03);"
				},
				apivm.h(
					"view",
					{style: "padding:" + (this.props.item.exhibit ? 13 : 18) + "px 16px;"},
					apivm.h(
						"text",
						{
							class: "required",
							style:
								"display:" +
								(!this.props.item.noRequired ? "flex" : "none") +
								";top:" +
								(this.props.item.exhibit ? 13 : 18) +
								"px;"
						},
						"*"
					),
					apivm.h(
						"view",
						{style: "flex-direction:row; align-items: flex-start;"},
						apivm.h(
							"text",
							{
								class: "" + (this.props.item.twa ? "text_default" : "text_one"),
								style:
									loadConfiguration() +
									"color:#333;width:" +
									(this.props.item.twa
										? "auto"
										: G.appFontSize * 4 + (api.systemType == "ios" ? 6 : 4) + "px") +
									";"
							},
							this.props.item.title
						),
						apivm.h(
							"view",
							{class: "flex_w"},
							!this.props.item.exhibit && [
								apivm.h(
									"view",
									null,
									!this.props.item.showType &&
										apivm.h(
											"view",
											{style: "flex-direction:row;flex-wrap: wrap;margin:-5px 0 0 10px;"},
											(Array.isArray(this.props.item.data)
												? this.props.item.data
												: Object.values(this.props.item.data)
											).map(function(nItem, nIndex, nList) {
												return apivm.h(
													"view",
													{
														onClick: function() {
															return this$1.cSelectbox(nItem, nIndex);
														},
														style:
															"display:" +
															(!nItem.hide ? "flex" : "none") +
															";flex-direction:row; align-items: center;padding:5px 0;margin-right:" +
															(nIndex != nList.length - 1
																? this$1.props.item.rRightW || 12
																: "0") +
															"px;"
													},
													apivm.h("z-radio", {
														checked: this$1.isSelectValue(nItem),
														type: isArray(this$1.props.item.value) ? 3 : 2,
														size: 4,
														color:
															this$1.props.dataMore.readonly ||
															this$1.props.item.readonly ||
															nItem.readonly
																? "#C5C7C9"
																: this$1.isSelectValue(nItem)
																? G.appTheme
																: "#999"
													}),
													apivm.h(
														"text",
														{
															style:
																loadConfiguration() +
																"color:" +
																(this$1.props.dataMore.readonly ||
																this$1.props.item.readonly ||
																nItem.readonly
																	? "#C5C7C9"
																	: "#333") +
																";margin-left:5px;"
														},
														nItem.value
													)
												);
											})
										)
								),
								apivm.h(
									"view",
									null,
									this.props.item.showType == 1 &&
										apivm.h(
											"view",
											{style: "flex-direction:row;flex-wrap: wrap;margin:-16px 0 0 10px;"},
											(Array.isArray(this.props.item.data)
												? this.props.item.data
												: Object.values(this.props.item.data)
											).map(function(nItem, nIndex, nList) {
												return apivm.h(
													"view",
													{
														onClick: function() {
															return this$1.cSelectbox(nItem, nIndex);
														},
														style:
															"display:" +
															(!nItem.hide ? "flex" : "none") +
															";background:" +
															(this$1.isSelectValue(nItem) ? G.appTheme : "#F4F5F7") +
															";border-radius:2px;padding:6px 8px;margin:10px " +
															(nIndex != nList.length - 1
																? this$1.props.item.rRightW || 12
																: "0") +
															"px 0 0;"
													},
													apivm.h(
														"text",
														{
															style:
																loadConfiguration(-2) +
																"color:" +
																(this$1.isSelectValue(nItem) ? "#FFF" : "#333") +
																";"
														},
														nItem.value
													)
												);
											})
										)
								)
							]
						)
					),
					apivm.h(
						"view",
						null,
						this.props.item.exhibit == 1 && [
							apivm.h(
								"view",
								null,
								!this.props.item.showType &&
									apivm.h(
										"view",
										{style: "flex-direction:row;flex-wrap: wrap;margin-top:5px;"},
										(Array.isArray(this.props.item.data)
											? this.props.item.data
											: Object.values(this.props.item.data)
										).map(function(nItem, nIndex, nList) {
											return apivm.h(
												"view",
												{
													onClick: function() {
														return this$1.cSelectbox(nItem, nIndex);
													},
													style:
														"display:" +
														(!nItem.hide ? "flex" : "none") +
														";flex-direction:row; align-items: center;padding:5px 0;margin-right:" +
														(nIndex != nList.length - 1
															? this$1.props.item.rRightW || 12
															: "0") +
														"px;"
												},
												apivm.h("z-radio", {
													checked: this$1.isSelectValue(nItem),
													type: isArray(this$1.props.item.value) ? 3 : 2,
													size: 4,
													color:
														this$1.props.dataMore.readonly ||
														this$1.props.item.readonly ||
														nItem.readonly
															? "#C5C7C9"
															: this$1.isSelectValue(nItem)
															? G.appTheme
															: "#999"
												}),
												apivm.h(
													"text",
													{
														style:
															loadConfiguration() +
															"color:" +
															(this$1.props.dataMore.readonly ||
															this$1.props.item.readonly ||
															nItem.readonly
																? "#C5C7C9"
																: "#333") +
															";margin-left:5px;"
													},
													nItem.value
												)
											);
										})
									)
							),
							apivm.h(
								"view",
								null,
								this.props.item.showType == 1 &&
									apivm.h(
										"view",
										{style: "flex-direction:row;flex-wrap: wrap;margin-top:2px;"},
										(Array.isArray(this.props.item.data)
											? this.props.item.data
											: Object.values(this.props.item.data)
										).map(function(nItem, nIndex, nList) {
											return apivm.h(
												"view",
												{
													onClick: function() {
														return this$1.cSelectbox(nItem, nIndex);
													},
													style:
														"display:" +
														(!nItem.hide ? "flex" : "none") +
														";background:" +
														(this$1.isSelectValue(nItem) ? G.appTheme : "#F4F5F7") +
														";border-radius:2px;padding:6px 8px;margin:10px " +
														(nIndex != nList.length - 1
															? this$1.props.item.rRightW || 12
															: "0") +
														"px 0 0;"
												},
												apivm.h(
													"text",
													{
														style:
															loadConfiguration(-2) +
															"color:" +
															(this$1.isSelectValue(nItem) ? "#FFF" : "#333") +
															";"
													},
													nItem.value
												)
											);
										})
									)
							)
						]
					)
				)
			);
		};

		return ItemRadio;
	})(Component);
	ItemRadio.css = {
		".required": {
			position: "absolute",
			zIndex: "1",
			color: "#FF0000",
			left: "8px",
			fontSize: "16px",
			lineHeight: "20px"
		},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden",
			display: "inline"
		},
		".text_default": {
			textOverflow: "clip",
			whiteSpace: "normal",
			overflow: "visible",
			display: "inline"
		}
	};
	apivm.define("item-radio", ItemRadio);

	var ZTextarea = /*@__PURE__*/ (function(Component) {
		function ZTextarea(props) {
			Component.call(this, props);
			this.data = {
				textareaId: this.props.id || "z_textarea" + getNum()
			};
		}

		if (Component) ZTextarea.__proto__ = Component;
		ZTextarea.prototype = Object.create(Component && Component.prototype);
		ZTextarea.prototype.constructor = ZTextarea;
		ZTextarea.prototype.installed = function() {
			var this$1 = this;

			this.props.dataMore.textareaId = this.data.textareaId;
			if (this.props.dataMore.autoFocus) {
				setTimeout(function() {
					document.getElementById(this$1.data.textareaId).focus();
				}, 500);
			}
		};
		ZTextarea.prototype.input = function(e) {
			var value = e.detail.value;
			if (this.props.dataMore.expression) {
				value = value.replace(new RegExp(this.props.dataMore.expression, "g"), "");
			}
			this.props.dataMore.value = value;
			this.fire("input", e.detail);
		};
		ZTextarea.prototype.blur = function(e) {
			this.fire("blur", e.detail);
		};
		ZTextarea.prototype.focus = function(e) {
			document.getElementById(this.data.textareaId).focus();
			this.fire("focus", e.detail);
		};
		ZTextarea.prototype.render = function() {
			return apivm.h("textarea", {
				id: this.data.textareaId,
				style:
					"" +
					loadConfiguration() +
					(api.systemType == "android" ? "min-" : "") +
					"height: " +
					(this.props.dataMore.height || "250") +
					"px;" +
					(this.props.style || ""),
				class: "z_textarea " + (this.props.class || ""),
				placeholder:
					this.props.dataMore.replyPlaceholder ||
					this.props.dataMore.placeholder ||
					this.props.dataMore.hint ||
					"请输入" + (this.props.dataMore.title || ""),
				"placeholder-style": "color:#ccc;",
				"auto-height": api.systemType == "android",
				value: this.props.dataMore.value,
				onInput: this.input,
				onBlur: this.blur,
				onFocus: this.focus,
				maxlength: this.props.dataMore.maxlength || this.props.dataMore.max
			});
		};

		return ZTextarea;
	})(Component);
	ZTextarea.css = {
		".z_textarea": {
			background: "transparent",
			borderColor: "transparent",
			color: "#333",
			padding: "0",
			width: "100%",
			fontFamily: "none"
		},
		".z_textarea::placeholder": {color: "#ccc"}
	};
	apivm.define("z-textarea", ZTextarea);

	var PreviewerImg = /*@__PURE__*/ (function(Component) {
		function PreviewerImg(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				frameName: "previewerImg"
			};
			this.compute = {
				monitor: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.props.dataMore.watermark = G.watermark;
							this.baseInit();
						} else {
							if (platform() == "app" && this.UIPhotoViewer) {
								this.UIPhotoViewer.close();
							}
						}
					}
					if (this.data.show) {
						if (this.nowParam != JSON.stringify(this.props.dataMore)) {
							this.nowParam = JSON.stringify(this.props.dataMore);
							this.sendMessage();
						}
					}
				}
			};
		}

		if (Component) PreviewerImg.__proto__ = Component;
		PreviewerImg.prototype = Object.create(Component && Component.prototype);
		PreviewerImg.prototype.constructor = PreviewerImg;
		PreviewerImg.prototype.baseInit = function() {
			var this$1 = this;

			var data = this.props.dataMore;
			switch (platform()) {
				case "app":
					if (G.watermark) {
						//有水印使用web
						addEventListener(this.data.frameName + "_msg", function() {
							this$1.closePage();
						});
						api.setFrameClient({frameName: this.data.frameName}, function(ret, err) {
							if (ret.state == 2) {
								this$1.isLoading = true;
								setTimeout(function() {
									this$1.sendMessage();
								}, 400);
							}
						});
						return;
					}
					this.UIPhotoViewer = api.require("UIPhotoViewer");
					this.UIPhotoViewer.open(
						{
							images: data.imgs,
							activeIndex: data.index,
							gestureClose: true,
							bgColor: "#000"
						},
						function(ret, err) {
							switch (ret.eventType) {
								case "click":
									this$1.closePage();
									break;
								case "gestureColse":
									this$1.UIPhotoViewer = null;
									this$1.closePage();
									break;
								case "longPress":
									var nowImg = data.imgs[ret.index];
									api.actionSheet(
										{
											buttons: ["保存到相册"],
											cancelTitle: "取消"
										},
										function(ret) {
											switch (ret.buttonIndex) {
												case 1:
													api.saveMediaToAlbum(
														{
															path: nowImg
														},
														function(ret) {
															toast(ret && ret.status ? "已保存到手机相册" : "保存失败");
														}
													);
													break;
											}
										}
									);
									break;
							}
						}
					);
					break;
				case "mp":
					wx.previewImage({
						urls: data.imgs,
						current: data.imgs[data.index],
						complete: function() {
							this$1.closePage();
						}
					});

					break;
				case "web":
					window.addEventListener("message", function(event) {
						this$1.closePage();
					});
					document
						.getElementById(this.data.frameName)
						.addEventListener("load", function() {
							this$1.isLoading = true;
							this$1.sendMessage();
						});
					break;
			}
		};
		PreviewerImg.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		PreviewerImg.prototype.sendMessage = function() {
			if (this.isLoading && this.props.dataMore.show) {
				if (platform() == "web") {
					if (!document.getElementById(this.data.frameName)) {
						return;
					}
					var targetWindow = document.getElementById(this.data.frameName)
						.contentWindow;
					targetWindow.postMessage(this.nowParam, "*");
				} else if (platform() == "app") {
					sendEvent(this.data.frameName + "_open", this.props.dataMore);
				}
			}
		};
		PreviewerImg.prototype.render = function() {
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0);"
				},
				this.props.dataMore.show &&
					(platform() == "web" || G.watermark) &&
					apivm.h("frame", {
						id: this.data.frameName,
						class: "xy_100",
						name: this.data.frameName,
						url: shareAddress() + "html/previewerImg.html",
						pageParam: {name: this.data.frameName},
						useWKWebView: true,
						scaleEnabled: true,
						allowEdit: true
					})
			);
		};

		return PreviewerImg;
	})(Component);
	apivm.define("previewer-img", PreviewerImg);

	var ZVideo = /*@__PURE__*/ (function(Component) {
		function ZVideo(props) {
			Component.call(this, props);
			this.data = {
				controls: true
			};
			this.compute = {
				getSrc: function() {
					var src = this.props.src;
					if (src.indexOf("http") != 0) {
						src = appUrl() + "file/preview/" + src;
					}
					return src;
				}
			};
		}

		if (Component) ZVideo.__proto__ = Component;
		ZVideo.prototype = Object.create(Component && Component.prototype);
		ZVideo.prototype.constructor = ZVideo;
		ZVideo.prototype.firstPause = function() {
			var this$1 = this;

			if (!this.isPause) {
				this.isPause = true;
				if (platform() == "app" && !this.props.poster) {
					this.data.controls = false;
					setTimeout(function() {
						if (!this$1.props.autoplay) {
							document.getElementById(dealVideoId(this$1.props.src)) &&
								document.getElementById(dealVideoId(this$1.props.src)).pause();
						}
						this$1.data.controls = true;
					}, 50);
				} else {
					this.data.controls = true;
				}
			}
		};
		ZVideo.prototype.loadedmetadata = function(e) {
			if (api.systemType != "ios") {
				this.isPause = false;
				this.firstPause();
			}
		};
		ZVideo.prototype.play = function() {
			videoPlayPush(dealVideoId(this.props.src));
		};
		ZVideo.prototype.pause = function() {
			videoPlayRemove(dealVideoId(this.props.src));
		};
		ZVideo.prototype.render = function() {
			return apivm.h("video", {
				id: dealVideoId(this.props.src),
				style: "width:100%;height:" + G.pageWidth * 0.56 + "px;",
				controls: this.data.controls,
				autoplay:
					(platform() == "app" && !this.props.poster) || this.props.autoplay,
				onLoadedmetadata: this.loadedmetadata,
				onWaiting: this.firstPause,
				onPlay: this.play,
				onPause: this.pause,
				onEnded: this.pause,
				src: this.getSrc,
				poster: showImg(this.props.poster)
			});
		};

		return ZVideo;
	})(Component);
	apivm.define("z-video", ZVideo);

	var ZRichText = /*@__PURE__*/ (function(Component) {
		function ZRichText(props) {
			Component.call(this, props);
			this.data = {
				showText: null,
				listData: [],
				hasExpand: false, //是否有展开
				isExpand: false, //是否展开了
				appDetailsStyle: ""
			};
			this.compute = {
				monitor: function() {
					if (this.props.nodes != this.data.showText) {
						this.data.showText = this.props.nodes;
						this.dealWithCon();
					}
				}
			};
		}

		if (Component) ZRichText.__proto__ = Component;
		ZRichText.prototype = Object.create(Component && Component.prototype);
		ZRichText.prototype.constructor = ZRichText;
		ZRichText.prototype.expandShow = function(e) {
			stopBubble(e);
			this.data.isExpand = !this.data.isExpand;
			this.dealWithCon();
		};
		ZRichText.prototype.dealWithCon = function() {
			var this$1 = this;

			var expText =
				(isParameters(this.data.showText) ? this.data.showText : "") + "";
			if (isObject(expText) || isArray(expText)) {
				expText = JSON.stringify(expText);
			}
			this.data.appDetailsStyle = getPrefs("appDetailsStyle");
			if (this.data.appDetailsStyle == "edit") {
				this.data.showText = expText;
				return;
			}

			var notTagText = removeTag(expText);
			this.data.hasExpand =
				isParameters(this.props.expand) && notTagText.length > this.props.expand;
			expText =
				this.data.hasExpand && !this.data.isExpand
					? notTagText.substring(0, this.props.expand) + "..."
					: expText;

			expText = expText.replace(
				/(<style(.*?)<\/style>|<link(.*?)<\/link>|<script(.*?)<\/script>|<!--[\w\W\r\n]*?-->|^\s*|\s*$)/gi,
				""
			);
			var reLabel = function(_bel) {
				var oldText = expText;
				expText = expText.replace(
					new RegExp("<" + _bel + "[^>]*>(.*?)</" + _bel + ">", "gi"),
					"$1"
				);
				if (expText != oldText && expText.indexOf(_bel) != -1) {
					reLabel(_bel);
				}
			};
			["span"].forEach(function(item) {
				reLabel(item);
			});
			expText = expText.replace(/<\s*\/?\s*br\s*\/?\s*>/gi, "</br>");
			expText = expText.replace(/\n/gi, "</br>");
			expText = decodeCharacter(expText);
			expText = expText.replace(/<ins(.*?)>(.*?)<\/ins>/gi, "$2");
			if (!expText.startsWith("<") || !expText.endsWith(">")) {
				expText = "<div>" + expText + "</div>";
			}
			// console.log("解析一："+expText);
			var newText = expText;
			//清空表格td中所有的标签
			var rdRegex = /<td(.*?)>(.*?)<\/td>/gi;
			var match;
			while ((match = rdRegex.exec(expText)) !== null) {
				var nText = removeTag(match[2]);
				newText = newText.replace(match[2], nText);
			}
			expText = newText;
			// console.log("解析二："+expText);
			var strRegex = /<\/?\w+[^>]*>/gi;
			var nowIndexs = [],
				viewIndexs = [];
			var startIndex = [];
			expText.replace(strRegex, function(item, index) {
				//循环对应的内容和角标
				var nlabel = item.match(/<\/*([^> ]+)[^>]*>/)[1].toLocaleLowerCase();
				var styleMatch = /style\s*=\s*['"]([^'"]*)['"]/i.exec(item);
				var nStyle = styleMatch ? styleMatch[1] : "";
				if (nlabel == "img") {
					nStyle = nStyle
						.replace("width: auto;", "width:100%;")
						.replace("width:auto;", "width:100%;");
				}
				var nowItem = {label: nlabel, index: index, text: item, style: nStyle};
				viewIndexs.push(nowItem);
				// console.log(index + "-" + nlabel + "-" + item);
				if (/^<(p|div|span).*/i.test(item)) {
					var nowItems = {
						index: nowIndexs.length,
						endIndex: 0,
						label: nlabel,
						start: nowItem,
						end: null,
						style: nStyle
					};
					startIndex.push(nowItems);
					nowIndexs.push(nowItems);
				} else if (/^<\/(p|div|span)>/i.test(item)) {
					if (startIndex.length) {
						nowIndexs[startIndex[startIndex.length - 1].index].end = nowItem;
						nowIndexs[startIndex[startIndex.length - 1].index].endIndex =
							nowIndexs.length - 1;
						startIndex.pop();
					}
				} else if (/^<.*?>$/.test(item)) {
					nowIndexs.push({
						index: nowIndexs.length,
						endIndex: nowIndexs.length,
						label: nlabel,
						start: nowItem,
						end: nowItem,
						style: nStyle
					});
				}
			});
			var showTexts = [];
			var tableData = null,
				isTable = false,
				tableItem = null;
			viewIndexs.forEach(function(item, index) {
				var minIndex = item.index + item.text.length;
				var maxIndex =
					index != viewIndexs.length - 1
						? viewIndexs[index + 1].index
						: expText.length;
				var viewText = expText.substring(minIndex, maxIndex);
				if (item.label == "table") {
					if (!isTable) {
						isTable = true;
						tableData = {
							label: "table",
							widths: [],
							data: [],
							index: minIndex,
							style: item.style
						};
					} else {
						isTable = false;
						showTexts.push(tableData);
					}
					return;
				}
				if (isTable) {
					if (item.text == "</tr>") {
						tableData.data.push(tableItem);
					} else if (item.label == "tr") {
						tableItem = {label: "tr", data: [], index: minIndex, style: item.style};
					} else if (item.text != "</td>" && item.label == "td") {
						var widthMatch = item.text.match(/width="([^"]*)"/);
						var tdWidth = widthMatch ? widthMatch[1] + "px" : null;
						if (!tdWidth) {
							tdWidth = this$1.getStyle(item.style, "width") || "150px";
						}
						if (!tableData.data.length) {
							if (tdWidth.indexOf("%") != -1) {
								//是百分比宽度
								var nW = Number(tdWidth.replace(/%/g, ""));
								tdWidth = ((nW < 30 ? 30 : nW) * G.pageWidth) / 100 + "px";
							}
							tableData.widths.push(tdWidth);
						}
						var showStyle = "";
						showStyle +=
							"border:" +
							(this$1.getStyle(item.style, "border") || "1px solid #ccc") +
							";";
						showStyle +=
							"border-left:" + this$1.getStyle(item.style, "border-left") + ";";
						showStyle +=
							"border-right:" + this$1.getStyle(item.style, "border-right") + ";";
						showStyle +=
							"border-top:" + this$1.getStyle(item.style, "border-top") + ";";
						showStyle +=
							"border-bottom:" + this$1.getStyle(item.style, "border-bottom") + ";";
						showStyle +=
							"background:" + this$1.getStyle(item.style, "background") + ";";
						showStyle += "color:" + this$1.getStyle(item.style, "color") + ";";
						tableItem.data.push({
							label: "td",
							index: minIndex,
							text: viewText,
							style: showStyle
						});
					}
					return;
				}
				if (/^(?!p|div|span).*$/i.test(item.label)) {
					if (
						item.label == "br" &&
						!item.text &&
						showTexts.length &&
						showTexts[showTexts.length - 1].label == "br"
					) {
						return;
					}
					var addItem = {label: item.label, index: item.index, style: item.style};
					minIndex = minIndex + item.text.length;
					var srcMatch = /src\s*=\s*['"]([^'"]*)['"]/i.exec(item.text);
					if (srcMatch) {
						addItem.src = srcMatch[1];
					}
					var hrefMatch = /href\s*=\s*['"]([^'"]*)['"]/i.exec(item.text);
					if (hrefMatch) {
						addItem.href = hrefMatch[1];
					}
					if (addItem.label == "img" && !addItem.src);
					else {
						showTexts.push(addItem);
					}
				}
				if (viewText.replace(/^\s*|\s*$/, "")) {
					var showText = {
						label: "text",
						index: minIndex,
						text: viewText,
						style: item.style
					};
					if (item.label == "a") {
						showText.label = "text_a";
						showText.href = showTexts.length
							? showTexts[showTexts.length - 1].href
							: "";
					}
					showTexts.push(showText);
				}
			});
			// console.log(JSON.stringify(nowIndexs));
			// console.log(JSON.stringify(viewIndexs));
			// console.log(JSON.stringify(showTexts));
			this.data.listData = showTexts;
		};
		ZRichText.prototype.openImages = function(e, _item, _index) {
			stopBubble(e);
			var imgs = this.data.listData.filter(function(item, index) {
				return item.label == "img";
			});
			openWin_imgPreviewer({
				index: getItemForKey(_item.index, imgs, "index")._i,
				imgs: imgs.map(function(obj) {
					return obj.src;
				})
			});
		};
		ZRichText.prototype.openHrefs = function(e, _item, _index) {
			stopBubble(e);
			if (!_item.href) {
				return;
			}
			if (platform() == "web") {
				window.open(_item.href);
				return;
			}
			openWin_url({url: _item.href});
		};
		ZRichText.prototype.getStyle = function(_text, _item) {
			var match = new RegExp('[;"]s*' + _item + "s*:s*([^;]+);").exec(_text);
			if (match) {
				var matchText = match[1].replace(/pt/g, "px");
				return matchText;
			}
			return "";
		};
		ZRichText.prototype.nTouchmove = function() {
			touchmove();
		};
		ZRichText.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					style: "" + (this.props.style || ""),
					class: "" + (this.props.class || "")
				},
				apivm.h(
					"view",
					null,
					this.data.appDetailsStyle != "edit" &&
						this.data.listData.map(function(item, index) {
							return [
								apivm.h(
									"view",
									null,
									item.label == "text" &&
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(this$1.props.size || 0) +
													"line-height:" +
													(G.appFontSize + (this$1.props.size || 0)) * 1.8 +
													"px;" +
													(this$1.props.detail &&
													((platform() == "mp" && item.text.indexOf("　") != 0) ||
														(platform() == "web" &&
															item.text.indexOf(" ") != 0 &&
															item.text.indexOf("　") != 0))
														? "text-indent:2em;"
														: ""),
												class: "richText"
											},
											this$1.props.detail &&
												item.text.indexOf(" ") != 0 &&
												item.text.indexOf("　") != 0
												? api.systemType == "android"
													? "					"
													: api.systemType == "ios"
													? "	 "
													: ""
												: "",
											item.text
										)
								),
								apivm.h(
									"view",
									null,
									item.label == "text_a" &&
										apivm.h(
											"view",
											{
												onClick: function(e) {
													return this$1.openHrefs(e, item, index);
												}
											},
											apivm.h(
												"text",
												{
													style: loadConfiguration(this$1.props.size || 0) + "color: blue;",
													class: "richText"
												},
												item.text
											)
										)
								),
								apivm.h(
									"view",
									null,
									item.label == "br" &&
										apivm.h("view", {
											style:
												"height:" + (G.appFontSize - 6 + (this$1.props.size || 0)) + "px;"
										})
								),
								apivm.h(
									"view",
									null,
									item.label == "img" &&
										apivm.h(
											"view",
											{
												class: "richImgBox",
												onClick: function(e) {
													return this$1.openImages(e, item, index);
												}
											},
											apivm.h("image", {
												class: "richImg",
												style: item.style || "",
												mode: "widthFix",
												thumbnail: "false",
												src: item.src
											})
										)
								),
								apivm.h(
									"view",
									null,
									(item.label == "video" || item.label == "source") &&
										apivm.h(
											"view",
											{class: "richImgBox"},
											item.src && apivm.h("z-video", {src: item.src})
										)
								),
								apivm.h(
									"view",
									null,
									item.label == "table" &&
										apivm.h(
											"scroll-view",
											{"scroll-x": true, "scroll-y": false},
											apivm.h(
												"view",
												{
													onTouchStart: this$1.nTouchmove,
													onTouchMove: this$1.nTouchmove,
													onTouchEnd: this$1.nTouchmove
												},
												(item.data || []).map(function(nItem, nIndex) {
													return apivm.h(
														"view",
														{
															class: "richTable_item",
															style: "margin-top:" + (nIndex ? -1 : 0) + "px;"
														},
														(nItem.data || []).map(function(uItem, uIndex) {
															return apivm.h(
																"view",
																{
																	class: "richTable_item_td",
																	style:
																		uItem.style +
																		"width:" +
																		item.widths[uIndex] +
																		";margin-left:" +
																		(uIndex ? -1 : 0) +
																		"px;"
																},
																apivm.h(
																	"text",
																	{
																		style:
																			loadConfiguration(this$1.props.size || 0) +
																			"text-align: center;",
																		class: "richText"
																	},
																	uItem.text
																)
															);
														})
													);
												})
											)
										)
								)
							];
						})
				),
				apivm.h(
					"view",
					null,
					this.data.appDetailsStyle == "edit" &&
						apivm.h("rich-text", {nodes: this.data.showText})
				),
				apivm.h(
					"view",
					null,
					this.data.hasExpand &&
						apivm.h(
							"view",
							{
								style:
									"padding: 7px 0;flex-direction:row; align-items: center;justify-content: flex-start;",
								onClick: this.expandShow
							},
							apivm.h(
								"text",
								{style: loadConfiguration((this.props.size || 0) - 2) + "color:#999"},
								this.data.isExpand ? "收起" : "展开"
							),
							apivm.h("a-iconfont", {
								name: "xiangxiagengduo",
								style:
									"margin-left:5px;transform: rotate(" +
									(this.data.isExpand ? "180" : "0") +
									"deg);",
								color: "#999",
								size: G.appFontSize - 3
							})
						)
				)
			);
		};

		return ZRichText;
	})(Component);
	ZRichText.css = {
		".richImgBox": {
			alignItems: "center",
			justifyContent: "center",
			margin: "10px 0"
		},
		".richImg": {maxWidth: "100% !important"},
		".richTable_item": {flexFlow: "row nowrap"},
		".richTable_item_td": {
			alignItems: "center",
			justifyContent: "center",
			flexShrink: "0",
			padding: "5px"
		},
		".richText": {wordWrap: "break-word", wordBreak: "break-all"}
	};
	apivm.define("z-rich-text", ZRichText);

	var ItemTextarea = /*@__PURE__*/ (function(Component) {
		function ItemTextarea(props) {
			Component.call(this, props);
		}

		if (Component) ItemTextarea.__proto__ = Component;
		ItemTextarea.prototype = Object.create(Component && Component.prototype);
		ItemTextarea.prototype.constructor = ItemTextarea;
		ItemTextarea.prototype.input = function(e) {
			this.fire("input", this.props.item);
		};
		ItemTextarea.prototype.render = function() {
			return apivm.h(
				"view",
				{
					style:
						"display:" +
						(isParameters(this.props.item.hide) && this.props.item.hide
							? "none"
							: "flex") +
						";border-top:" +
						(this.props.item.line || 0) +
						"px solid rgba(0,0,0,0.03);"
				},
				apivm.h(
					"view",
					{style: "padding: 13px 16px;"},
					apivm.h(
						"text",
						{
							class: "required",
							style:
								"display:" +
								(!this.props.item.noRequired ? "flex" : "none") +
								";top:13px;"
						},
						"*"
					),
					apivm.h(
						"view",
						{class: "flex_row"},
						apivm.h(
							"text",
							{style: loadConfiguration(1) + "color:#333;"},
							this.props.item.title
						),
						this.props.item.titleHint &&
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(-2) +
										"flex:1;margin:2px 0 0 10px;color:#999;text-align: right;"
								},
								this.props.item.titleHint
							)
					),
					apivm.h(
						"view",
						{style: "padding-top:7px;"},
						apivm.h(
							"view",
							null,
							this.props.dataMore.readonly
								? apivm.h("z-rich-text", {detail: true, nodes: this.props.item.value})
								: apivm.h("z-textarea", {
										dataMore: this.props.item,
										onInput: this.input
								  })
						),
						apivm.h(
							"view",
							{style: "flex-direction:row-reverse;margin:5px 0 2px;"},
							apivm.h(
								"text",
								{style: loadConfiguration(-4) + "color: #999999;"},
								"已有字数",
								this.props.item.value.length
							)
						)
					)
				)
			);
		};

		return ItemTextarea;
	})(Component);
	ItemTextarea.css = {
		".required": {
			position: "absolute",
			zIndex: "1",
			color: "#FF0000",
			left: "8px",
			fontSize: "16px",
			lineHeight: "20px"
		}
	};
	apivm.define("item-textarea", ItemTextarea);

	var ZTag = /*@__PURE__*/ (function(Component) {
		function ZTag(props) {
			Component.call(this, props);
		}

		if (Component) ZTag.__proto__ = Component;
		ZTag.prototype = Object.create(Component && Component.prototype);
		ZTag.prototype.constructor = ZTag;
		ZTag.prototype.getTagStyle = function() {
			var color = this.props.color || G.appTheme;
			var rc = "",
				rbg = "",
				rbor = "";
			switch (this.props.type) {
				case "2":
					rc = color;
					rbg = "#FFF";
					rbor = colorRgba(color);
					break;
				case "3":
					rc = "#FFF";
					rbg = color;
					rbor = color;
					break;
				default:
					rc = color;
					rbg = colorRgba(color, 0.1);
					rbor = "transparent";
					break;
			}

			return (
				"color:" +
				rc +
				";background:" +
				rbg +
				";border: 1px solid " +
				rbor +
				";border-radius: " +
				(this.props.roundSize || 2) +
				"px;"
			);
		};
		ZTag.prototype.render = function() {
			return apivm.h(
				"view",
				{style: "flex-direction:row;"},
				apivm.h(
					"text",
					{
						id: "" + (this.props.id || ""),
						class: "tag_box " + (this.props.class || ""),
						style:
							"" +
							loadConfiguration((this.props.size || 0) - 2) +
							this.getTagStyle() +
							(this.props.style || "")
					},
					this.props.text || this.props.children
				)
			);
		};

		return ZTag;
	})(Component);
	ZTag.css = {".tag_box": {padding: "1px 10px"}};
	apivm.define("z-tag", ZTag);

	var ZImage = /*@__PURE__*/ (function(Component) {
		function ZImage(props) {
			Component.call(this, props);
			this.data = {
				imgId: "img_" + getNum(),
				imgMode: this.props.mode || "aspectFill",
				imgThumbnail: isParameters(this.props.thumbnail)
					? this.props.thumbnail
					: true,
				showFilter: false,
				show: true
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					var scrollBox = this.props.scrollBox;
					if (scrollBox) {
						getBoundingClientRect("box_" + this.data.imgId, function(ret) {
							// console.log("scrollBox:----"+JSON.stringify(scrollBox) + "--" + api.winHeight);
							// console.log("img:-------"+JSON.stringify(ret));
							this$1.data.show = !(
								ret.top + ret.height < -150 || ret.top - 150 > api.winHeight
							);
						});
					}
				}
			};
		}

		if (Component) ZImage.__proto__ = Component;
		ZImage.prototype = Object.create(Component && Component.prototype);
		ZImage.prototype.constructor = ZImage;
		ZImage.prototype.load = function(e) {
			if (!this.props.src || !document.getElementById(this.data.imgId)) {
				return;
			}
			var nowProportion = 0;
			var imgWidth =
				platform() == "app"
					? e.detail.width
					: document.getElementById(this.data.imgId).naturalWidth;
			var imgHeight =
				platform() == "app"
					? e.detail.height
					: document.getElementById(this.data.imgId).naturalHeight;
			if (imgWidth && imgHeight) {
				nowProportion = Number((imgWidth / imgHeight).toFixed(2));
			}
			if (nowProportion && this.props.proportionMin && this.props.proportionMax) {
				if (
					nowProportion < Number(this.props.proportionMin) ||
					nowProportion > Number(this.props.proportionMax)
				) {
					this.data.showFilter = true;
					this.data.imgMode = "aspectFit";
				} else {
					this.data.showFilter = false;
					this.data.imgMode = this.props.mode || "aspectFill";
				}
			}
		};
		ZImage.prototype.error = function() {
			this.fire("error");
		};
		ZImage.prototype.render = function() {
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					class: "" + (this.props.class || ""),
					style:
						"overflow:hidden;border-radius: " +
						(this.props.round ? "50%" : "0px") +
						";width:100%;height:100%;" +
						(this.props.style || "")
				},
				this.data.showFilter &&
					this.data.show &&
					apivm.h("image", {
						class: "z_imageFilter",
						mode: "aspectFill",
						src: this.props.src,
						thumbnail: true
					}),
				apivm.h(
					"view",
					{class: "z_image", id: "box_" + this.data.imgId},
					this.data.show &&
						apivm.h("image", {
							id: this.data.imgId,
							class: "xy_100",
							mode: this.data.imgMode,
							src: this.props.src,
							thumbnail: this.data.imgThumbnail,
							onLoad: this.load,
							onError: this.error
						})
				)
			);
		};

		return ZImage;
	})(Component);
	ZImage.css = {
		".z_image": {
			width: "100%",
			height: "100%",
			filter: "none",
			opacity: "1",
			position: "absolute",
			left: "0",
			top: "0"
		},
		".z_imageFilter": {
			width: "100%",
			height: "100%",
			filter: "blur(4px)",
			opacity: "0.7",
			position: "relative",
			left: "0",
			top: "0"
		}
	};
	apivm.define("z-image", ZImage);

	var ZAvatarMember = /*@__PURE__*/ (function(Component) {
		function ZAvatarMember(props) {
			Component.call(this, props);
			this.data = {
				esrc: appUrl() + "img/default_user_head.jpg",
				asrc: null,
				bsrc: ""
			};
			this.compute = {
				monitor: function() {
					var url = isObject(this.props.src) ? this.props.src.url : this.props.src;
					if (this.data.asrc != url || !this.data.asrc) {
						this.data.asrc = url || this.data.esrc;
						this.data.bsrc = "";
					}
				}
			};
		}

		if (Component) ZAvatarMember.__proto__ = Component;
		ZAvatarMember.prototype = Object.create(Component && Component.prototype);
		ZAvatarMember.prototype.constructor = ZAvatarMember;
		ZAvatarMember.prototype.error = function() {
			this.data.bsrc = this.data.esrc;
		};
		ZAvatarMember.prototype.render = function() {
			return apivm.h(
				"view",
				{s: this.monitor, class: "xy_100"},
				apivm.h("z-image", {
					style: "border-radius: 2px;",
					scrollBox: this.props.scrollBox,
					mode: "scaleToFill",
					thumbnail: "false",
					src: showImg(this.data.bsrc || this.data.asrc, "117x157-compress-"),
					onError: this.error
				})
			);
		};

		return ZAvatarMember;
	})(Component);
	apivm.define("z-avatar-member", ZAvatarMember);

	var Item5051 = /*@__PURE__*/ (function(Component) {
		function Item5051(props) {
			Component.call(this, props);
		}

		if (Component) Item5051.__proto__ = Component;
		Item5051.prototype = Object.create(Component && Component.prototype);
		Item5051.prototype.constructor = Item5051;
		Item5051.prototype.showNow = function() {
			return (
				this.props.dataMore &&
				dayjs(this.props.dataMore.value).format("YYYY-MM-DD") ==
					dayjs(this.props.item.startTime).format("YYYY-MM-DD")
			);
		};
		Item5051.prototype.openDetail = function(e) {
			stopBubble(e);
			var item = this.props.item;
			if (!this.props.detail) {
				openWin_workstation_duty({id: item.id});
			}
		};
		Item5051.prototype.openUser = function(e, _item) {
			stopBubble(e);
			var item = this.props.item;
			openWin_workstation_lam({memberId: _item.id, stationId: item.stationId});
		};
		Item5051.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					id: this.props.item.timeId,
					style: "padding: " + (this.props.flat ? "0" : "8px 16px") + ";"
				},
				apivm.h(
					"view",
					null,
					this.showNow() &&
						apivm.h("view", {
							style: "height:15px;border-radius:4px;background:" + G.appTheme
						})
				),
				apivm.h(
					"view",
					{
						onClick: this.openDetail,
						style:
							"margin-top: " +
							(this.showNow() ? "-8" : "0") +
							"px;" +
							(this.props.flat
								? "border-top:" +
								  (this.props.index ? 1 : 0) +
								  "px solid #e8e8e8;padding: 15px 0px;"
								: "box-shadow:0px 2px 10px 1px rgba(24,64,118,0.08); border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-left-radius: 4px; border-bottom-right-radius: 4px;padding: 15px 10px;") +
							";background-color: rgba(255, 255, 255, " +
							(G.watermark ? 0.6 : 1) +
							");"
					},
					apivm.h(
						"view",
						{class: "flex_row"},
						apivm.h(
							"text",
							{
								style:
									loadConfiguration(-2) + "flex:1;color: #999999;margin-right:10px;"
							},
							this.props.item.time
						),
						this.props.item.tag &&
							apivm.h(
								"z-tag",
								{type: this.props.item.tag.type, color: this.props.item.tag.color},
								this.props.item.tag.text
							)
					),
					apivm.h(
						"view",
						{class: "flex_row", style: "flex-wrap: wrap;margin-top:12px;"},
						this.props.item.title.split("").map(function(nItem, nIndex) {
							return (
								nIndex < Math.floor((G.pageWidth - 60) / G.appFontSize) * 2 &&
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(1) +
											"margin:2px 0;font-weight: 500;color: " +
											(this$1.props.search &&
											this$1.props.search.result &&
											this$1.props.search.result.indexOf(nItem) > -1
												? G.appTheme
												: "#333") +
											";"
									},
									nIndex == Math.floor((G.pageWidth - 60) / G.appFontSize) * 2 - 1
										? "..."
										: nItem
								)
							);
						})
					),
					apivm.h(
						"view",
						null,
						this.props.item.users.map(function(nItem) {
							return [
								apivm.h(
									"view",
									{
										class: "flex_row",
										style:
											"margin-top:15px;background: #F9FAFD;border-radius: 5px;padding:12px 16px;",
										onClick: function(e) {
											return this$1.openUser(e, nItem);
										}
									},
									apivm.h(
										"view",
										{style: "width:54px;height:67px;margin-right:23px;"},
										apivm.h("z-avatar-member", {src: showImg(nItem)})
									),
									apivm.h(
										"view",
										{class: "flex_w"},
										apivm.h(
											"view",
											{class: "flex_row"},
											apivm.h(
												"text",
												{style: "color: #333;" + loadConfiguration()},
												nItem.name
											),
											apivm.h(
												"view",
												{class: "flex_shrink"},
												nItem.label &&
													apivm.h(
														"z-tag",
														{style: "margin-left:9px;", color: G.appTheme, roundSize: "3"},
														nItem.label
													)
											)
										),
										apivm.h(
											"text",
											{style: "margin-top:5px;color: #999;" + loadConfiguration(-4)},
											nItem.position
										)
									)
								)
							];
						})
					)
				)
			);
		};

		return Item5051;
	})(Component);
	apivm.define("item50-5-1", Item5051);

	var ZAvatar = /*@__PURE__*/ (function(Component) {
		function ZAvatar(props) {
			Component.call(this, props);
			this.data = {
				esrc: appUrl() + "img/default_user_head.jpg",
				asrc: null,
				bsrc: ""
			};
			this.compute = {
				monitor: function() {
					var url = isObject(this.props.src) ? this.props.src.url : this.props.src;
					if (this.data.asrc != url || !this.data.asrc) {
						this.data.asrc = url || this.data.esrc;
						this.data.bsrc = "";
					}
				}
			};
		}

		if (Component) ZAvatar.__proto__ = Component;
		ZAvatar.prototype = Object.create(Component && Component.prototype);
		ZAvatar.prototype.constructor = ZAvatar;
		ZAvatar.prototype.error = function() {
			this.data.bsrc = this.data.esrc;
		};
		ZAvatar.prototype.render = function() {
			return apivm.h(
				"view",
				{s: this.monitor, class: "xy_100"},
				apivm.h("z-image", {
					round: true,
					mode: "scaleToFill",
					thumbnail: "false",
					src: showImg(this.data.bsrc || this.data.asrc, "150x150-compress-"),
					onError: this.error
				})
			);
		};

		return ZAvatar;
	})(Component);
	apivm.define("z-avatar", ZAvatar);

	var ZInput = /*@__PURE__*/ (function(Component) {
		function ZInput(props) {
			Component.call(this, props);
			this.data = {
				inputId: this.props.id || "z_input" + getNum()
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.autoFocus) {
						this.props.dataMore.autoFocus = false;
						this.props.dataMore.inputId = this.data.inputId;
						setTimeout(function() {
							document.getElementById(this$1.data.inputId).focus();
						}, 500);
					}
					if (this.props.dataMore.inputId != this.data.inputId) {
						this.props.dataMore.inputId = this.data.inputId;
					}
				}
			};
		}

		if (Component) ZInput.__proto__ = Component;
		ZInput.prototype = Object.create(Component && Component.prototype);
		ZInput.prototype.constructor = ZInput;
		ZInput.prototype.inputConfirm = function(e) {
			document.getElementById(this.data.inputId).blur();
			this.fire("confirm", e.detail);
		};
		ZInput.prototype.inputIng = function(e) {
			var nValue = (e.detail || {}).value;
			if (!nValue) {
				//解决安卓上清空输入无效的问题
				if (!this.i) {
					this.props.dataMore.value = "";
					this.i = 1;
				} else {
					this.props.dataMore.value = " ".repeat(this.i++ % 2);
					this.props.dataMore.value = " ".repeat(this.i++ % 2);
				}
			} else {
				this.props.dataMore.value = nValue;
			}
			if (this.props.dataMore.number) {
				if (!this.props.dataMore.value) {
					return;
				}
				if (/[^-?\d+(\.\d+)?$]/.test(this.props.dataMore.value)) {
					this.props.dataMore.value = this.props.dataMore.value.replace(
						/[^-?\d+(\.\d+)?$]/g,
						""
					);
					toast("请输入数字！");
					return;
				}
			}
			if (this.props.dataMore.expression) {
				//有正则表达示
				this.props.dataMore.value = this.props.dataMore.value.replace(
					new RegExp(this.props.dataMore.expression, "g"),
					""
				);
			}
			this.fire("input", e.detail);
		};
		ZInput.prototype.inputBlur = function(e) {
			this.fire("blur", e.detail);
		};
		ZInput.prototype.inputFocus = function(e) {
			document.getElementById(this.data.inputId).focus();
			this.fire("focus", e.detail);
		};
		ZInput.prototype.clean = function() {
			var this$1 = this;

			this.inputIng({detail: {value: ""}});
			this.fire("clean");
			if (this.props.dataMore.cleanFocus) {
				setTimeout(function() {
					document.getElementById(this$1.data.inputId).focus();
				}, 150);
			}
		};
		ZInput.prototype.switchLook = function() {
			this.props.dataMore.isLook = !this.props.dataMore.isLook;
			this.props.dataMore.inputType = this.props.dataMore.isLook
				? "text"
				: "password";
		};
		ZInput.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "z_input_box " + (this.props.class || ""),
					style:
						"border-radius:" +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;background:" +
						(this.props.bg || "rgba(0,0,0,0.05)") +
						";justify-content: " +
						(this.props.justify || "flex-start") +
						";" +
						(this.props.style || "")
				},
				apivm.h(
					"view",
					null,
					this.props.children.length >= 1 && this.props.children[0]
				),
				apivm.h(
					"view",
					null,
					!this.props.dataMore.dotIcon &&
						!this.props.dotIcon &&
						apivm.h(
							"view",
							{onClick: this.inputConfirm, style: "padding: 5px;marign-right:5px;"},
							apivm.h("a-iconfont", {
								name: this.props.dataMore.icon || "sousuo",
								color: "#999",
								size: G.appFontSize + (this.props.dataMore.iconSize || 0)
							})
						)
				),
				this.props.type == 2
					? apivm.h(
							"text",
							{
								style:
									"line-height:" +
									(G.appFontSize + 14) +
									"px;color:#999;" +
									loadConfiguration()
							},
							this.props.dataMore.placeholder || this.props.placeholder
					  )
					: apivm.h("input", {
							id: this.data.inputId,
							style:
								loadConfiguration() +
								"height:" +
								(G.appFontSize + 14) +
								"px;" +
								(this.props.dataMore.inputStyle || ""),
							"placeholder-style": "color:#ccc;",
							class: "z_input_input flex_w",
							type: this.props.dataMore.inputType || "text",
							placeholder:
								this.props.dataMore.placeholder ||
								this.props.dataMore.hint ||
								this.props.placeholder ||
								"请输入" + (this.props.dataMore.title || ""),
							onInput: function(e) {
								if (typeof this$1 != "undefined") {
									this$1.props.dataMore.value = e.target.value;
								} else {
									this$1.data.this.props.dataMore.value = e.target.value;
								}
								this$1.inputIng(e);
							},
							maxlength: this.props.dataMore.maxlength || this.props.dataMore.max,
							disabled:
								(isParameters(this.props.disabled) ? this.props.disabled : false) ||
								isParameters(this.props.readonly)
									? this.props.readonly
									: false,
							"confirm-type":
								this.props.confirmType || this.props.dataMore.confirmType || "search",
							"keyboard-type": this.props.dataMore.keyboardType || "default",
							onConfirm: this.inputConfirm,
							onBlur: this.inputBlur,
							onFocus: this.inputFocus,
							value:
								typeof this == "undefined"
									? this.data.this.props.dataMore.value
									: this.props.dataMore.value
					  }),
				apivm.h(
					"view",
					null,
					this.props.dataMore.value &&
						!this.props.dataMore.dotCleanIcon &&
						apivm.h(
							"view",
							{onClick: this.clean, style: "padding: 5px;"},
							apivm.h("a-iconfont", {
								name: "qingkong",
								color: "#666",
								size: G.appFontSize
							})
						)
				),
				apivm.h(
					"view",
					null,
					isParameters(this.props.dataMore.isLook) &&
						this.props.dataMore.value &&
						apivm.h(
							"view",
							{
								onClick: function() {
									return this$1.switchLook();
								},
								style: "padding:5px 10px 5px 3px;"
							},
							apivm.h("a-iconfont", {
								name: this.props.dataMore.isLook ? "kejian" : "bukejian",
								color: "#919191",
								size: G.appFontSize + 4
							})
						)
				),
				apivm.h(
					"view",
					null,
					this.props.children.length >= 2 && this.props.children[1]
				)
			);
		};

		return ZInput;
	})(Component);
	ZInput.css = {
		".z_input_box": {
			width: "100%",
			flexDirection: "row",
			padding: "2px 5px 2px 8px",
			alignItems: "center"
		},
		".z_input_input": {
			background: "transparent",
			borderColor: "transparent",
			color: "#333",
			paddingRight: "5px"
		},
		".z_input_input::placeholder": {color: "#ccc"}
	};
	apivm.define("z-input", ZInput);

	var ZDivider = /*@__PURE__*/ (function(Component) {
		function ZDivider(props) {
			Component.call(this, props);
		}

		if (Component) ZDivider.__proto__ = Component;
		ZDivider.prototype = Object.create(Component && Component.prototype);
		ZDivider.prototype.constructor = ZDivider;
		ZDivider.prototype.render = function() {
			return apivm.h(
				"view",
				{style: "width:100%;height:1px;padding:0 16px;" + this.props.style || null},
				apivm.h("view", {
					style:
						"border-bottom:1px solid " +
						(this.props.color || "rgba(0,0,0,0.03)") +
						";"
				})
			);
		};

		return ZDivider;
	})(Component);
	apivm.define("z-divider", ZDivider);

	var Bu503Users = /*@__PURE__*/ (function(Component) {
		function Bu503Users(props) {
			Component.call(this, props);
			this.data = {
				inputBox: {
					show: false,
					value: "",
					result: "",
					placeholder: "请输入关键词"
				}
			};
		}

		if (Component) Bu503Users.__proto__ = Component;
		Bu503Users.prototype = Object.create(Component && Component.prototype);
		Bu503Users.prototype.constructor = Bu503Users;
		Bu503Users.prototype.penetrate = function() {};
		Bu503Users.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		Bu503Users.prototype.getData = function() {
			this.data.inputBox.result = this.data.inputBox.value;
		};
		Bu503Users.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style: "height:20%;flex-shrink: 0;"
				}),
				this.props.dataMore.show &&
					apivm.h(
						"view",
						{
							class: "flex_h pages_warp",
							onClick: function() {
								return this$1.penetrate();
							}
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							{class: "header_warp flex_row"},
							apivm.h(
								"view",
								{class: "header_main xy_center"},
								apivm.h(
									"text",
									{
										style: loadConfiguration(1) + "font-weight: 600;color:" + G.headColor
									},
									this.props.dataMore.title || "参与人员"
								)
							),
							apivm.h(
								"view",
								{class: "header_left_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{style: loadConfiguration(1) + "color:#666;margin:0 4px;"},
										"关闭"
									)
								)
							)
						),
						apivm.h("z-divider", null),
						apivm.h(
							"y-scroll-view",
							{_this: this},
							apivm.h(
								"view",
								{style: "padding: 4px 16px"},
								apivm.h("z-input", {
									dataMore: this.data.inputBox,
									onConfirm: function() {
										return this$1.getData(0);
									},
									onClean: function() {
										return this$1.getData(0);
									}
								})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.listData.map(function(item, index) {
									return (
										(this$1.data.inputBox.result
											? item.name.indexOf(this$1.data.inputBox.result) > -1
											: true) && [
											apivm.h(
												"view",
												{class: "address_item"},
												apivm.h(
													"view",
													{style: loadConfigurationSize(30) + "margin-right:15px;"},
													apivm.h("z-avatar", {src: showImg(item)})
												),
												apivm.h(
													"view",
													{class: "flex_w"},
													apivm.h(
														"view",
														{
															style:
																"flex-wrap: wrap;flex-direction: row;align-items: flex-start;"
														},
														item.name.split("").map(function(nItem, nIndex) {
															return apivm.h(
																"text",
																{
																	style:
																		loadConfiguration() +
																		"margin:2px 0;color: " +
																		(this$1.data.inputBox &&
																		this$1.data.inputBox.result &&
																		this$1.data.inputBox.result.indexOf(nItem) > -1
																			? G.appTheme
																			: "#333") +
																		";"
																},
																nItem
															);
														})
													),
													item.position &&
														apivm.h(
															"text",
															{style: loadConfiguration(-2) + "margin-top:7px;color:#666;"},
															item.position
														)
												)
											),
											apivm.h("z-divider", null)
										]
									);
								})
							)
						)
					)
			);
		};

		return Bu503Users;
	})(Component);
	Bu503Users.css = {
		".pages_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		},
		".address_item": {
			minHeight: "62px",
			padding: "5px 16px",
			flexDirection: "row",
			alignItems: "center"
		}
	};
	apivm.define("bu-50-3-users", Bu503Users);

	var YSuspendedBtns = /*@__PURE__*/ (function(Component) {
		function YSuspendedBtns(props) {
			Component.call(this, props);
		}

		if (Component) YSuspendedBtns.__proto__ = Component;
		YSuspendedBtns.prototype = Object.create(Component && Component.prototype);
		YSuspendedBtns.prototype.constructor = YSuspendedBtns;
		YSuspendedBtns.prototype.itemclick = function(_item) {
			this.fire("click", _item);
		};
		YSuspendedBtns.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					class: "suspendedBtn_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";bottom:" +
						(safeArea().bottom + 98) +
						"px;"
				},
				apivm.h(
					"view",
					{style: "flex-direction:column-reverse;"},
					this.props.dataMore.data.map(function(item, index) {
						return [
							(isParameters(item.show) ? item.show : true) &&
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.itemclick(item);
										},
										class: "suspendedBtn_item",
										style:
											"margin-bottom:" +
											(index ? 10 : 0) +
											"px;" +
											loadConfigurationSize(item.bgSize || 28) +
											"background: " +
											(item.bg == "appTheme" ? G.appTheme : item.bg || G.appTheme) +
											";" +
											item.iStyle
									},
									item.type == "img"
										? apivm.h("image", {
												src: item.src,
												class: "xy_100",
												mode: "aspectFill",
												alt: ""
										  })
										: item.type == "text"
										? [
												item.src &&
													apivm.h("a-iconfont", {
														style: "" + (item.style || ""),
														name: item.src,
														color:
															item.color == "appTheme" ? G.appTheme : item.color || "#fff",
														size:
															G.appFontSize + (isParameters(item.size) ? Number(item.size) : 4)
													}),
												apivm.h(
													"text",
													{
														style:
															loadConfiguration(-2) +
															"color:" +
															(item.color == "appTheme" ? G.appTheme : item.color || "#fff") +
															";"
													},
													item.value
												)
										  ]
										: apivm.h("a-iconfont", {
												style: "" + (item.style || ""),
												name: item.src,
												color: item.color == "appTheme" ? G.appTheme : item.color || "#fff",
												size:
													G.appFontSize + (isParameters(item.size) ? Number(item.size) : 4)
										  })
								)
						];
					})
				)
			);
		};

		return YSuspendedBtns;
	})(Component);
	YSuspendedBtns.css = {
		".suspendedBtn_box": {position: "absolute", zIndex: "999", right: "16px"},
		".suspendedBtn_item": {
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center",
			boxShadow: "0px 4px 12px 1px rgba(24,64,118,0.15)",
			borderTopLeftRadius: "54px",
			borderTopRightRadius: "54px",
			borderBottomRightRadius: "54px",
			borderBottomLeftRadius: "54px"
		}
	};
	apivm.define("y-suspended-btns", YSuspendedBtns);

	var ZButton = /*@__PURE__*/ (function(Component) {
		function ZButton(props) {
			Component.call(this, props);
		}

		if (Component) ZButton.__proto__ = Component;
		ZButton.prototype = Object.create(Component && Component.prototype);
		ZButton.prototype.constructor = ZButton;
		ZButton.prototype.componentsClick = function(e) {
			stopBubble(e);
			if (!this.props.disabled && !this.props.readonly) {
				this.fire("click", {});
			}
		};
		ZButton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					id: "" + (this.props.id || ""),
					class: "z_button " + (this.props.class || ""),
					style:
						"border-radius:" +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;border-color:" +
						(this.props.color || G.appTheme) +
						";background:" +
						(this.props.plain ? "#FFF" : this.props.color || G.appTheme) +
						";opacity:" +
						(this.props.disabled ? "0.5" : "1") +
						";" +
						(this.props.style || ""),
					onClick: function(e) {
						return this$1.componentsClick(e);
					}
				},
				this.props.icon &&
					apivm.h("a-iconfont", {
						name: this.props.icon,
						style: "margin-right:5px;",
						color: this.props.plain ? this.props.color || G.appTheme : "#FFF",
						size: G.appFontSize - 1 + (this.props.size || 0)
					}),
				apivm.h(
					"text",
					{
						style:
							"color:" +
							(this.props.plain ? this.props.color || G.appTheme : "#FFF") +
							";" +
							loadConfiguration(this.props.size || 0)
					},
					this.props.text || this.props.children
				)
			);
		};

		return ZButton;
	})(Component);
	ZButton.css = {
		".z_button": {
			padding: "7px 12px",
			borderWidth: "1px",
			borderStyle: "solid",
			boxSizing: "border-box",
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("z-button", ZButton);

	var ZActionsheet = /*@__PURE__*/ (function(Component) {
		function ZActionsheet(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				dotClose: true
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.data.dotClose = true;
							setTimeout(function() {
								this$1.data.dotClose = false;
							}, 300);
						} else {
							G.actionSheetPop.pageClose();
						}
					}
				}
			};
		}

		if (Component) ZActionsheet.__proto__ = Component;
		ZActionsheet.prototype = Object.create(Component && Component.prototype);
		ZActionsheet.prototype.constructor = ZActionsheet;
		ZActionsheet.prototype.closePage = function() {
			if (this.data.dotClose) {
				return;
			}
			this.props.dataMore.show = false;
		};
		ZActionsheet.prototype.itemClick = function(_item, _index) {
			if (this.data.dotClose || _item.disabled) {
				return;
			}
			_item.buttonIndex = _index + 1;
			G.actionSheetPop.callback(_item);
			this.closePage();
		};
		ZActionsheet.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				this.data.show && [
					apivm.h("view", {
						onClick: function() {
							return this$1.closePage();
						},
						class: "actionSheet_cancel"
					}),
					apivm.h(
						"view",
						{
							class: this.props.dataMore.title ? "actionSheet_warp" : "",
							style: "flex-shrink: 0;"
						},
						this.props.dataMore.title &&
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(-1) +
										";color:#aaa;text-align: center;padding: 15px;"
								},
								this.props.dataMore.title
							)
					),
					apivm.h(
						"scroll-view",
						{
							class: !this.props.dataMore.title ? "actionSheet_warp" : "",
							style: "height: auto;background: #FFF;flex-shrink: 1;",
							"scroll-y": true
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						(this.props.dataMore.data || []).map(function(item, index) {
							return (
								(isParameters(item.show) ? item.show : true) && [
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.itemClick(item, index);
											},
											class: "actionSheet_item",
											style:
												"justify-content:" +
												(item.justify || "center") +
												";opacity: " +
												(item.disabled ? "0.3" : "1") +
												";"
										},
										apivm.h(
											"view",
											null,
											item.icon &&
												apivm.h("a-iconfont", {
													style: "margin-right:10px;",
													name: item.icon,
													color: (isParameters(this$1.props.dataMore.active) &&
													isObject(this$1.props.dataMore.active)
													? item.id === this$1.props.dataMore.active.id
													: item.name === this$1.props.dataMore.active)
														? G.appTheme
														: item.color || "#333",
													size:
														G.appFontSize + (isParameters(item.size) ? Number(item.size) : 4)
												})
										),
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(-1) +
													";color:" +
													((isParameters(this$1.props.dataMore.active) &&
													isObject(this$1.props.dataMore.active)
													? item.id === this$1.props.dataMore.active.id
													: item.name === this$1.props.dataMore.active)
														? G.appTheme
														: item.color || "#333")
											},
											item.name
										)
									),
									index != this$1.props.dataMore.data.length - 1 &&
										!this$1.props.dataMore.cancel &&
										apivm.h(
											"view",
											{style: "width:100%;height:1px;padding:0 15px;flex-shrink: 0;"},
											apivm.h("view", {style: "height:1px;background: #F6F6F6;"})
										)
								]
							);
						})
					),
					apivm.h(
						"view",
						{style: "flex-shrink: 0;"},
						this.props.dataMore.cancel &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.closePage();
									},
									class: "actionSheet_item",
									style:
										"justify-content:center;border-top:10px solid #f6f6f6;background: #FFF; "
								},
								apivm.h(
									"text",
									{style: loadConfiguration(-2) + ";color:#333"},
									this.props.dataMore.cancel || "取消"
								)
							)
					),
					apivm.h("view", {
						style:
							"background:#fff;flex-shrink: 0;padding-bottom:" +
							safeArea().bottom +
							"px;"
					})
				]
			);
		};

		return ZActionsheet;
	})(Component);
	ZActionsheet.css = {
		".actionSheet_cancel": {flex: "1", minHeight: "20%", flexShrink: "0"},
		".actionSheet_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		},
		".actionSheet_item": {
			width: "100%",
			minHeight: "60px",
			alignItems: "center",
			flexDirection: "row",
			padding: "5px 16px"
		}
	};
	apivm.define("z-actionSheet", ZActionsheet);

	var ItemPicture = /*@__PURE__*/ (function(Component) {
		function ItemPicture(props) {
			Component.call(this, props);
		}

		if (Component) ItemPicture.__proto__ = Component;
		ItemPicture.prototype = Object.create(Component && Component.prototype);
		ItemPicture.prototype.constructor = ItemPicture;
		ItemPicture.prototype.addPic = function() {
			var this$1 = this;

			var keyItem = this.props.item;
			var max = -1;
			if (isNumber(keyItem.max) && keyItem.max > 0) {
				max = keyItem.max - keyItem.value.length;
			}
			if (max <= 0) {
				toast("最多选择" + keyItem.max + "张图片");
				return;
			}
			actionSheet(
				{
					title: "请选择图片来源",
					buttons: ["相机", "相册"]
				},
				function(ret, err) {
					var _index = ret.buttonIndex;
					getPicture(
						{
							sourceType: _index == 1 ? "camera" : "photos",
							destinationType: "url",
							max: max
						},
						function(ret, err) {
							var dataUrl = ret ? ret.data || ret.base64Data : "";
							if (dataUrl) {
								var _item = {showToast: true, url: dataUrl};
								uploadFile(_item, function(ret) {
									if (ret.otherInfo) {
										ret.otherInfo.url = appUrl() + "image/" + ret.otherInfo.newFileName;
										keyItem.value.push(ret.otherInfo);
										this$1.fire("input", this$1.props.item);
									} else {
										toast(ret.error);
									}
								});
							}
						}
					);
				}
			);
		};
		ItemPicture.prototype.openImages = function(e, _item, _index) {
			stopBubble(e);
			openWin_imgPreviewer({
				index: _index,
				imgs: this.props.item.value.map(function(obj) {
					return obj.url;
				})
			});
		};
		ItemPicture.prototype.delFile = function(e, _item, _index) {
			stopBubble(e);
			delItemForKey(_item, this.props.item.value, "id");
			this.fire("input", this.props.item);
			if (!_item.dotSystemDel) {
				ajax(
					{u: appUrl() + "file/clear"},
					"clear" + _item.id,
					function(ret, err) {},
					"删除附件",
					"post",
					{
						body: JSON.stringify({fileIds: [_item.id]})
					}
				);
			}
		};
		ItemPicture.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					style:
						"display:" +
						(isParameters(this.props.item.hide) && this.props.item.hide
							? "none"
							: "flex") +
						";border-top:" +
						(this.props.item.line || 0) +
						"px solid rgba(0,0,0,0.03);"
				},
				apivm.h(
					"view",
					{style: "padding: 13px 0 13px 16px;"},
					apivm.h(
						"text",
						{
							class: "required",
							style:
								"display:" +
								(!this.props.item.noRequired ? "flex" : "none") +
								";top:13px;"
						},
						"*"
					),
					apivm.h(
						"view",
						{class: "flex_row"},
						this.props.item.title &&
							apivm.h(
								"text",
								{style: loadConfiguration() + "color:#333;"},
								this.props.item.title,
								this.props.item.max
									? "(" + this.props.item.value.length + "/" + this.props.item.max + ")"
									: ""
							)
					),
					apivm.h(
						"view",
						{style: "flex-direction:row;flex-wrap: wrap;"},
						(Array.isArray(this.props.item.value)
							? this.props.item.value
							: Object.values(this.props.item.value)
						).map(function(nItem, nIndex) {
							return apivm.h(
								"view",
								{
									class: "aItem_pic",
									onClick: function(e) {
										return this$1.openImages(e, nItem, nIndex);
									}
								},
								apivm.h("image", {
									style:
										"width:100%;height:" +
										((G.pageWidth - 16 - (this$1.props.w || 0)) * 0.25 - 16) +
										"px;border:0px;",
									src: showImg(nItem),
									mode: "aspectFill"
								}),
								apivm.h(
									"view",
									{
										style:
											"position:absolute;top: 6px; right: 6px;z-index: 1;width:auto;height:auto;border:0px;",
										onClick: function(e) {
											return this$1.delFile(e, nItem, nIndex);
										}
									},
									apivm.h("a-iconfont", {
										name: "qingkong",
										color: "rgba(0,0,0,0.65)",
										size: G.appFontSize + 3
									})
								)
							);
						}),
						(!this.props.item.max ||
							this.props.item.max > this.props.item.value.length) &&
							apivm.h(
								"view",
								{class: "aItem_pic"},
								apivm.h(
									"view",
									{
										onClick: this.addPic,
										class: "xy_center",
										style:
											"position:relative;top:0;right:0;z-index:1;width:100%;height:" +
											((G.pageWidth - 16 - (this.props.w || 0)) * 0.25 - 16) +
											"px;border:1px solid #ddd;"
									},
									apivm.h("a-iconfont", {
										name: "xinzeng",
										color: "#ddd",
										size: G.appFontSize + 8
									})
								)
							)
					)
				)
			);
		};

		return ItemPicture;
	})(Component);
	ItemPicture.css = {
		".required": {
			position: "absolute",
			zIndex: "1",
			color: "#FF0000",
			left: "8px",
			fontSize: "16px",
			lineHeight: "20px"
		},
		".aItem_pic": {
			padding: "16px 16px 0 0",
			width: "25%",
			height: "auto",
			boxSizing: "border-box"
		}
	};
	apivm.define("item-picture", ItemPicture);

	var Bu50Option = /*@__PURE__*/ (function(Component) {
		function Bu50Option(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				submitText: "提交"
			};
			this.compute = {
				monitor: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						this.baseInit();
					}
				}
			};
		}

		if (Component) Bu50Option.__proto__ = Component;
		Bu50Option.prototype = Object.create(Component && Component.prototype);
		Bu50Option.prototype.constructor = Bu50Option;
		Bu50Option.prototype.baseInit = function() {
			var list = this.props.dataMore.listData;
			getDictData(list, null);
		};
		Bu50Option.prototype.penetrate = function() {};
		Bu50Option.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		Bu50Option.prototype.submitActive = function() {
			var param = this.props.dataMore.param();
			var list = this.props.dataMore.listData;
			var getPostParam = addPostParam(list);
			if (!getPostParam) {
				return;
			}
			param = setNewJSON(param, getPostParam);
			if (this.props.dataMore.form) {
				param = {form: param};
				setParamToFirst(list, param);
			}
			this.props.dataMore.callback(param);
		};
		Bu50Option.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style: "height:20%;flex-shrink: 0;"
				}),
				this.props.dataMore.show &&
					apivm.h(
						"view",
						{
							class: "flex_h pages_warp",
							onClick: function() {
								return this$1.penetrate();
							}
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							{class: "header_warp flex_row"},
							apivm.h(
								"view",
								{class: "header_main xy_center"},
								apivm.h(
									"text",
									{
										style: loadConfiguration(1) + "font-weight: 600;color:" + G.headColor
									},
									this.props.dataMore.title || ""
								)
							),
							apivm.h(
								"view",
								{class: "header_left_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{style: loadConfiguration(1) + "color:#666;margin:0 4px;"},
										"关闭"
									)
								)
							)
						),
						apivm.h("z-divider", null),
						apivm.h(
							"y-scroll-view",
							{_this: this},
							apivm.h(
								"view",
								null,
								this.props.dataMore.listData.map(function(item, index) {
									return [
										item.type == "radio"
											? [
													apivm.h("item-radio", {
														dataMore: this$1.props.dataMore,
														item: item,
														index: index
													})
											  ]
											: item.type == "textarea"
											? [
													apivm.h("item-textarea", {
														dataMore: this$1.props.dataMore,
														item: item,
														index: index
													})
											  ]
											: item.type == "picture"
											? [
													apivm.h("item-picture", {
														dataMore: this$1.props.dataMore,
														item: item,
														index: index
													})
											  ]
											: []
									];
								})
							),
							apivm.h(
								"view",
								{class: "flex_row", style: "padding:20px 40px;"},
								apivm.h("z-button", {
									onClick: function() {
										return this$1.submitActive();
									},
									disabled: this.props.dataMore.readonly,
									round: true,
									style: "flex:1;padding:7px 10px;",
									color: G.appTheme,
									text: this.data.submitText
								})
							)
						)
					)
			);
		};

		return Bu50Option;
	})(Component);
	Bu50Option.css = {
		".pages_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		}
	};
	apivm.define("bu-50-option", Bu50Option);

	var ZAlert = /*@__PURE__*/ (function(Component) {
		function ZAlert(props) {
			Component.call(this, props);
			this.data = {
				show: false,

				inputBox: {
					dotIcon: true,
					value: "",
					placeholder: "",
					autoFocus: true,
					cleanFocus: true,
					height: 150,
					confirmType: ""
				},

				inputBoxPass: {
					dotIcon: true,
					value: "",
					placeholder: "",
					inputType: "password",
					autoFocus: false,
					cleanFocus: true,
					height: 150,
					confirmType: ""
				},

				marginBottom: 0,
				timeout: 0
			};
			this.compute = {
				monitor: function() {
					var dm = this.props.dataMore;
					if (dm.show != this.data.show) {
						this.data.inputBox.autoFocus = true;
						this.data.show = dm.show;
						this.data.marginBottom = 0;
						if (this.data.show) {
							this.data.timeout = dm.timeout || 0;
							if (this.data.timeout > 0) {
								this.startTime();
							}
							if (
								this.props.dataMore.type == "input" ||
								this.props.dataMore.type == "textarea"
							) {
								this.data.inputBox.value = dm.content;
								this.data.inputBox.placeholder = dm.placeholder;
								this.data.inputBox.confirmType = dm.confirmType || "done";

								this.data.inputBoxPass.value = dm.content2;
								this.data.inputBoxPass.placeholder = dm.placeholder2;
								this.data.inputBoxPass.confirmType = dm.confirmType2 || "done";
							}
						}
					}
				}
			};
		}

		if (Component) ZAlert.__proto__ = Component;
		ZAlert.prototype = Object.create(Component && Component.prototype);
		ZAlert.prototype.constructor = ZAlert;
		ZAlert.prototype.closePage = function(_type) {
			this.props.dataMore.show = false;
			if (!_type) {
				G.alertPop.callback({buttonIndex: 2});
			}
		};
		ZAlert.prototype.closeStop = function(e) {
			stopBubble(e);
		};
		ZAlert.prototype.inputFocus = function(e) {
			if (platform() == "app" && api.systemType == "ios") {
				this.data.marginBottom = e.detail.height;
			}
		};
		ZAlert.prototype.inputBlur = function(e) {
			this.data.marginBottom = 0;
		};
		ZAlert.prototype.itemClick = function() {
			if (this.data.timeout > 0) {
				return;
			}
			if (
				this.props.dataMore.type == "input" ||
				this.props.dataMore.type == "textarea"
			) {
				this.props.dataMore.content = this.data.inputBox.value;
				this.props.dataMore.content2 = this.data.inputBoxPass.value;
			}
			this.props.dataMore.buttonIndex = 1;
			G.alertPop.callback(this.props.dataMore);
			this.closePage(1);
		};
		ZAlert.prototype.startTime = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.data.timeout--;
				if (this$1.data.timeout >= 0) {
					this$1.startTime();
				} else if (this$1.props.dataMore.autoClose) {
					this$1.itemClick();
				}
			}, 1000);
		};
		ZAlert.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box xy_center",
					onClick: this.closeStop,
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);z-index:1001;"
				},
				this.data.show && [
					apivm.h(
						"view",
						{
							class: "alert_warp",
							style: "margin-bottom:" + this.data.marginBottom + "px;",
							onClick: this.closeStop
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							null,
							(this.props.dataMore.title || this.props.dataMore.closeType == "2") &&
								apivm.h(
									"view",
									{style: "padding: 20px 20px 0;"},
									this.props.dataMore.title &&
										apivm.h(
											"text",
											{class: "alert_title", style: "" + loadConfiguration(4)},
											this.props.dataMore.title
										),
									apivm.h(
										"view",
										{
											style:
												"display:" +
												(this.props.dataMore.closeType == "2" ? "flex" : "none") +
												";position:absolute;right:0;top:0;"
										},
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.closePage();
												},
												style: "padding:20px 20px 10px 10px;"
											},
											apivm.h("a-iconfont", {
												name: "cuohao",
												color: "#666",
												size: G.appFontSize + 4
											})
										)
									)
								)
						),
						apivm.h(
							"scroll-view",
							{class: "alert_content_box", "scroll-y": true},
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "input" &&
									apivm.h("z-input", {
										id: "input",
										dataMore: this.data.inputBox,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.inputPassword &&
									apivm.h("z-input", {
										id: "password",
										style: "margin-top:15px;",
										dataMore: this.data.inputBoxPass,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "textarea" &&
									apivm.h("z-textarea", {
										class: "alert_textarea",
										dataMore: this.data.inputBox,
										onBlur: this.inputBlur,
										onFocus: this.inputFocus
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "richText" &&
									apivm.h("z-rich-text", {
										detail: true,
										nodes: this.props.dataMore.content
									})
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == "text" &&
									apivm.h(
										"text",
										{class: "alert_content", style: "" + loadConfiguration(1)},
										this.props.dataMore.content
									)
							)
						),
						apivm.h(
							"view",
							null,
							this.props.dataMore.closeType == "1" && [
								apivm.h(
									"view",
									{style: "width:100%;height:1px;padding:0 15px;flex-shrink: 0;"},
									apivm.h("view", {style: "height:1px;background: #F6F6F6;"})
								),
								apivm.h(
									"view",
									{class: "alert_btn_box"},
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.closePage();
											},
											class: "alert_btn_item",
											style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}
										},
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color:" +
													(this.props.dataMore.cancel.color == "appTheme"
														? G.appTheme
														: this.props.dataMore.cancel.color) +
													";"
											},
											this.props.dataMore.cancel.text
										)
									),
									apivm.h(
										"view",
										{style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}},
										apivm.h("view", {style: "width:1px;height:30px;background:#F6F6F6;"})
									),
									apivm.h(
										"view",
										{
											onClick: this.itemClick,
											class: "alert_btn_item",
											style: {display: this.props.dataMore.sure.show ? "flex" : "none"}
										},
										apivm.h(
											"text",
											{
												style:
													loadConfiguration(1) +
													"color:" +
													(this.props.dataMore.sure.color == "appTheme"
														? G.appTheme
														: this.props.dataMore.sure.color) +
													";opacity:" +
													(this.data.timeout > 0 ? "0.5" : "1") +
													";"
											},
											this.props.dataMore.sure.text,
											this.data.timeout > 0 ? "(" + this.data.timeout + ")" : ""
										)
									)
								)
							]
						),
						apivm.h(
							"view",
							null,
							this.props.dataMore.closeType != "1" &&
								this.props.dataMore.sure.show &&
								apivm.h(
									"view",
									{style: "padding-bottom:15px;", class: "alert_btn_box"},
									apivm.h(
										"z-button",
										{
											style: "width:210px;",
											disabled: this.data.timeout > 0,
											color:
												this.props.dataMore.sure.color == "appTheme"
													? G.appTheme
													: this.props.dataMore.sure.color,
											round: true,
											onClick: this.itemClick
										},
										this.props.dataMore.sure.text,
										this.data.timeout > 0 ? "(" + this.data.timeout + ")" : ""
									)
								)
						)
					),
					apivm.h(
						"view",
						null,
						this.props.dataMore.closeType == "3" &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.closePage();
									},
									style: "margin-top:15px;"
								},
								apivm.h("a-iconfont", {
									style: "transform: rotate(45deg);",
									name: "gengduo1",
									color: "#FFF",
									size: G.appFontSize + 26
								})
							)
					)
				]
			);
		};

		return ZAlert;
	})(Component);
	ZAlert.css = {
		".alert_warp": {background: "#FFF", borderRadius: "10px", width: "320px"},
		".alert_content_box": {
			margin: "20px 15px",
			maxHeight: "385px",
			width: "auto"
		},
		".alert_title": {color: "#333333", fontWeight: "bold", textAlign: "center"},
		".alert_content": {
			width: "100%",
			textAlign: "center",
			color: "#333333",
			wordWrap: "break-word"
		},
		".alert_btn_box": {
			flexDirection: "row",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_btn_item": {
			flex: "1",
			padding: "10px",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_textarea": {
			borderColor: "#ccc !important",
			borderRadius: "10px",
			padding: "8px",
			width: "100%"
		}
	};
	apivm.define("z-alert", ZAlert);

	var CollectOk = /*@__PURE__*/ (function(Component) {
		function CollectOk(props) {
			Component.call(this, props);
		}

		if (Component) CollectOk.__proto__ = Component;
		CollectOk.prototype = Object.create(Component && Component.prototype);
		CollectOk.prototype.constructor = CollectOk;
		CollectOk.prototype.openCollect = function() {
			openWin_collect({});
		};
		CollectOk.prototype.render = function() {
			return apivm.h(
				"view",
				{
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";position:absolute;z-index:999;left:0;right:0;bottom:0;"
				},
				this.props.dataMore.show &&
					apivm.h(
						"view",
						{style: "padding:10px 16px " + (safeArea().bottom + 70) + "px 16px;"},
						apivm.h(
							"view",
							{
								class: "flex_row",
								style:
									"background: #FFFFFF;box-shadow: 0px 2px 10px 1px rgba(0,0,0,0.08);border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom-right-radius: 4px; border-bottom-left-radius: 4px;padding:14px 10px;"
							},
							apivm.h(
								"text",
								{
									style:
										loadConfiguration() +
										"color:#333;font-weight: 600;padding: 0px 10px;flex:1;"
								},
								"已成功添加收藏"
							),
							apivm.h(
								"view",
								{onClick: this.openCollect, class: "flex_row"},
								apivm.h(
									"text",
									{
										style:
											loadConfiguration() + "color:" + G.appTheme + ";margin-right:6px;"
									},
									"前往查看"
								),
								apivm.h("a-iconfont", {
									style: "transform: rotate(180deg) scale(0.7,0.7);",
									name: "fanhui1",
									color: G.appTheme,
									size: G.appFontSize - 4
								})
							)
						)
					)
			);
		};

		return CollectOk;
	})(Component);
	apivm.define("collect-ok", CollectOk);

	//区域截图
	function screenshotIntoPic(_id, _callback, _dm) {
		switch (platform()) {
			case "app":
				api.screenCapture(
					{
						region: _id
					},
					function(ret) {
						_callback(ret ? ret.savePath || "" : "");
					}
				);
				break;
			case "web":
				var imgData = _dm.shareImg || document.querySelector(_id).toDataURL() || "";
				_dm.shareImg = imgData;
				_callback(imgData);
				break;
			case "mp":
				document
					.querySelector(_id)
					.$$getNodesRef()
					.then(function(nodesRef) {
						nodesRef
							.node(function(res) {
								var canvas = res.node;
								wx.canvasToTempFilePath({
									canvas: canvas,
									success: function success(res) {
										_callback(res.tempFilePath);
									},
									fail: function fail() {
										_callback("");
									}
								});
							})
							.exec();
					});
				break;
		}
	}

	//图片转成file
	function dataURLtoFile(_path) {
		var data = _path.split(",");
		var type = data[0].match(/:(.*?);/)[1];
		var suffix = type.split("/")[1];
		var bstr = window.atob(data[1]);
		var n = bstr.length;
		var u8Arr = new Uint8Array(n);
		while (n--) {
			u8Arr[n] = bstr.charCodeAt(n);
		}
		return new File([u8Arr], new Date().valueOf() + "." + suffix, {type: type});
	}

	//图片保存到相册
	function picInfoAlbum(_path, _callback) {
		switch (platform()) {
			case "app":
				api.saveMediaToAlbum(
					{
						path: _path
					},
					function(ret) {
						_callback(ret && ret.status ? "已保存到手机相册" : "");
					}
				);
				break;
			case "web":
				try {
					var base64 = _path.split(",")[1];
					var imgName = new Date().valueOf() + ".png";
					var trans = window.trans || window.parent.trans;
					if (trans) {
						trans.saveImage(
							{
								base64Str: base64,
								album: true,
								imgPath: "fs://file_temporary/",
								imgName: imgName
							},
							function(ret, err) {
								if (ret) {
									_callback(ret && ret.status ? "已保存到手机相册" : "");
								} else {
									_callback(false, err.msg);
								}
							}
						);
						return;
					}
					if (isWeChat() || isMiniProgram()) {
						toast("请长按图片保存或转发");
						return;
					}
					var bytes = atob(base64);
					var ab = new ArrayBuffer(bytes.length);
					var ia = new Uint8Array(ab);
					for (var i = 0; i < bytes.length; i++) {
						ia[i] = bytes.charCodeAt(i);
					}
					var blob = new Blob([ab], {type: "application/octet-stream"});
					var url = URL.createObjectURL(blob);
					var a = document.createElement("a");
					a.href = url;
					a.download = imgName;
					var e = document.createEvent("MouseEvents");
					e.initMouseEvent(
						"click",
						true,
						false,
						window,
						0,
						0,
						0,
						0,
						0,
						false,
						false,
						false,
						false,
						0,
						null
					);
					a.dispatchEvent(e);
					URL.revokeObjectURL(url);
					_callback("已保存");
				} catch (e) {
					_callback(false);
				}
				break;
			case "mp":
				wx.saveImageToPhotosAlbum({
					filePath: _path,
					success: function success() {
						_callback("已保存到手机相册");
					},
					fail: function fail() {
						_callback(false);
					}
				});

				break;
		}
	}

	//点
	function canvasPoint(x, y) {
		return {x: x, y: y};
	}

	//圆角：区域、角度、ctx对象
	function canvasRoundedRect(rect, r, ctx) {
		var lt, lb, rt, rb;
		if (isArray(r)) {
			lt = r[0];
			rt = r[1];
			rb = r[2];
			lb = r[3];
		} else {
			lt = r;
			rt = r;
			rb = r;
			lb = r;
		}
		var ptA = canvasPoint(rect.x + lt, rect.y);
		var ptB = canvasPoint(rect.x + rect.width, rect.y);
		var ptC = canvasPoint(rect.x + rect.width, rect.y + rect.height);
		var ptD = canvasPoint(rect.x, rect.y + rect.height);
		var ptE = canvasPoint(rect.x, rect.y);
		ctx.beginPath();
		ctx.moveTo(ptA.x, ptA.y);
		ctx.arcTo(ptB.x, ptB.y, ptC.x, ptC.y, rt);
		ctx.arcTo(ptC.x, ptC.y, ptD.x, ptD.y, rb);
		ctx.arcTo(ptD.x, ptD.y, ptE.x, ptE.y, lb);
		ctx.arcTo(ptE.x, ptE.y, ptA.x, ptA.y, lt);
		ctx.closePath();
	}

	//计算文字地区
	function canvasCalcText(_text, _width, _left, _top, _margin, ctx) {
		_text = _text + "";
		if (ctx.measureText(_text).width < _width) {
			ctx.fillText(_text, _left, _top);
			return;
		}
		var nowText = "",
			nowList = _text.split("");
		for (var i = 0; i < nowList.length; i++) {
			nowText += nowList[i];
			if (ctx.measureText(nowText).width > _width + 5) {
				nowText = nowText.substring(0, nowText.length - 1);
				break;
			}
		}
		ctx.fillText(nowText, _left, _top);
		if (nowText != _text) {
			canvasCalcText(
				_text.substring(nowText.length),
				_width,
				_left,
				_top + _margin,
				_margin,
				ctx
			);
		}
	}

	//展示文字
	function canvasShowText(_param, ctx) {
		getBoundingClientRect(_param.id, function(ret) {
			ctx.save();
			ctx.textBaseline = _param.tLine || "top";
			ctx.textAlign = _param.tAlign || "left";
			ctx.fillStyle = _param.color || "#333333";
			ctx.font =
				(_param.weight || "400") +
				" " +
				(G.appFontSize + (_param.size || 0)) +
				"px Arial";
			canvasCalcText(
				_param.text,
				ret.width,
				ret.left - _param.bRect.left,
				ret.top - _param.bRect.top + (_param.spacing || 5) / 2 + 2,
				G.appFontSize + (_param.size || 0) + (_param.spacing || 5),
				ctx
			);
			ctx.restore();
		});
	}

	//展示图片 type:none|cover|contain
	function canvasShowImg(_param, ctx, canvas) {
		getBoundingClientRect(_param.id, function(ret) {
			var image = platform() == "mp" ? canvas.createImage() : new Image();
			image.crossOrigin = "anonymous";
			image.onload = function() {
				ctx.save();
				var imgLeft = ret.left - _param.bRect.left,
					imgTop = ret.top - _param.bRect.top;
				canvasRoundedRect(
					{x: imgLeft, y: imgTop, width: ret.width, height: ret.height},
					_param.round || 0,
					ctx
				);
				if (_param.bg) {
					ctx.fillStyle = _param.bg;
					ctx.fill();
				}
				ctx.clip();
				if (_param.type == "cover" || _param.type == "contain") {
					var sx, sy, sw, sh, imgRatio, canvasRatio;
					canvasRatio = ret.width / ret.height;
					imgRatio = image.width / image.height;
					if (imgRatio <= canvasRatio) {
						sw = {cover: image.width, contain: imgRatio * ret.width}[_param.type];
						sh = {cover: image.width / canvasRatio, contain: ret.height}[_param.type];
						sx = {cover: 0, contain: (ret.width - imgRatio * ret.width) / 2}[
							_param.type
						];
						sy = {cover: (image.height - image.width / canvasRatio) / 2, contain: 0}[
							_param.type
						];
					} else {
						sw = {cover: image.height * canvasRatio, contain: ret.width}[_param.type];
						sh = {cover: image.height, contain: ret.width / imgRatio}[_param.type];
						sx = {cover: (image.width - image.height * canvasRatio) / 2, contain: 0}[
							_param.type
						];
						sy = {cover: 0, contain: (ret.height - ret.width / imgRatio) / 2}[
							_param.type
						];
					}
					if (_param.type == "cover") {
						ctx.drawImage(
							this,
							sx,
							sy,
							sw,
							sh,
							imgLeft,
							imgTop,
							ret.width,
							ret.height
						);
					} else {
						ctx.drawImage(this, imgLeft + sx, imgTop + sy, sw, sh);
					}
				} else {
					ctx.drawImage(this, imgLeft, imgTop, ret.width, ret.height);
				}
				ctx.restore();
				_param.callback && _param.callback(true);
			};
			image.onerror = function(e) {
				_param.callback && _param.callback(false);
			};
			image.src = _param.src || "";
		});
	}

	//画布中加载图片回调
	function canvasImgLoad(_dm) {
		if (_dm.canvasImg <= 0) {
			if (platform() == "web" && (isWeChat() || isMiniProgram())) {
				toast("请长按图片保存或转发");
			}
			setTimeout(function() {
				hideProgress();
				if (platform() == "web") {
					screenshotIntoPic("#sharePosterCanvas", function(_path) {}, _dm);
				}
			}, 50);
		}
	}

	//设置背景 阴影
	function canvasShowBG(_param, ctx) {
		getBoundingClientRect(_param.id, function(ret) {
			ctx.save();
			canvasRoundedRect(
				{
					x: ret.left - _param.bRect.left,
					y: ret.top - _param.bRect.top,
					width: ret.width,
					height: ret.height
				},
				_param.round || 0,
				ctx
			);
			if (_param.bg) {
				ctx.fillStyle = _param.bg;
			}
			if (_param.shadow) {
				ctx.shadowColor = _param.shadow;
				ctx.shadowBlur = 10;
			}
			ctx.fill();
			ctx.restore();
		});
	}

	var SharePoster = /*@__PURE__*/ (function(Component) {
		function SharePoster(props) {
			Component.call(this, props);
			this.data = {
				show: false,

				btns: [
					{
						show: true,
						key: "platform",
						value: "平台好友",
						type: "img",
						src: appUrl() + "pageImg/open/logo"
					},
					// {show:true,key:"session",value:"微信好友",type:"icon",bg:"#50C614",color:"#FFF",src:"changyonglogo28",size:16},
					// {show:true,key:"timeline",value:"朋友圈",type:"icon",bg:"#50C614",color:"#FFF",src:"pengyouquan",size:13},
					{
						show: true,
						key: "QFriend",
						value: "QQ",
						type: "icon",
						bg: "#3AA2F3",
						color: "#FFF",
						src: "QQ",
						size: 13
					},
					{
						show: true,
						key: "QZone",
						value: "QQ空间",
						type: "icon",
						bg: "#3AA2F3",
						color: "#FFF",
						src: "pengyouquan",
						size: 13
					},
					{
						show: true,
						key: "save",
						value: "保存图片",
						type: "icon",
						bg: "",
						color: "",
						src: "zhixiangxia",
						size: 13
					}
				]
			};
			this.compute = {
				monitor: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.baseInit();
						}
					}
				}
			};
		}

		if (Component) SharePoster.__proto__ = Component;
		SharePoster.prototype = Object.create(Component && Component.prototype);
		SharePoster.prototype.constructor = SharePoster;
		SharePoster.prototype.baseInit = function() {
			var showBtns = this.props.dataMore.btns || [];
			this.data.btns.forEach(function(_item) {
				_item.show = !showBtns.length || getItemForKey(_item.key, showBtns);
				if (_item.key != "save" && _item.key != "platform") {
					_item.show = _item.show && platform() == "app";
				}
			});
		};
		SharePoster.prototype.penetrate = function() {};
		SharePoster.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		SharePoster.prototype.shareBtn = function(_item) {
			var this$1 = this;

			switch (_item.key) {
				case "platform":
					var openCallback = function(_path) {
						openWin_chat_share(
							{
								fType: "sendImageMessage",
								fImagePath: _path
							},
							function() {
								this$1.closePage();
								G.sharePop.show = false;
							}
						);
					};
					var savecallback = function(_path) {
						if (_path) {
							if (platform() != "web") {
								openCallback(_path);
							} else {
								showProgress("分享中");
								uploadFile({url: dataURLtoFile(_path)}, function(ret) {
									hideProgress();
									if (ret.state == 2) {
										openCallback(appUrl() + "image/" + ret.otherInfo.newFileName);
									} else {
										toast(ret.error);
									}
								});
							}
						} else {
							toast("保存失败");
						}
					};
					screenshotIntoPic(
						platform() == "app" ? "#sharePoster" : "#sharePosterCanvas",
						savecallback,
						this.props.dataMore
					);
					break;
				case "save":
					var savecallback = function(_path) {
						if (_path) {
							picInfoAlbum(_path, function(ret, err) {
								toast(ret || err || "保存失败");
								if (ret) {
									setTimeout(function() {
										this$1.closePage();
										G.sharePop.show = false;
									}, 500);
								}
							});
						} else {
							toast("保存失败");
						}
					};
					screenshotIntoPic(
						platform() == "app" ? "#sharePoster" : "#sharePosterCanvas",
						savecallback,
						this.props.dataMore
					);
					break;
				case "QFriend":
				case "QZone":
					var qq = api.require("QQPlus");
					if (!qq) {
						toast("未绑定模块，请联系管理员");
						return;
					}
					qq.setIsPermissionGranted({granted: true});
					qq.installed(function(ret, err) {
						if (!ret.status) {
							toast("当前设备未安装QQ客户端");
							return;
						}
						api.screenCapture(
							{
								region: "#sharePoster"
							},
							function(ret, err) {
								var param = {
									imgPath: ret.savePath,
									type: _item.key
								};

								console.log(JSON.stringify(param));
								qq.shareImage(param, function(ret, err) {
									toast(ret.status ? "分享成功！" : JSON.stringify(err));
								});
							}
						);
					});
					break;
				case "session":
				case "timeline":
					var wx = api.require("wxPlus");
					if (!wx) {
						toast("未绑定模块，请联系管理员");
						return;
					}
					wx.isInstalled(function(ret, err) {
						if (!ret.installed) {
							toast("当前设备未安装wx客户端");
							return;
						}
						api.screenCapture(
							{
								region: "#sharePoster"
							},
							function(ret, err) {
								var param = {
									contentUrl: ret.savePath,
									scene: _item.key
								};

								console.log(JSON.stringify(param));
								wx.shareImage(param, function(ret, err) {
									toast(ret.status ? "分享成功！" : JSON.stringify(err));
								});
							}
						);
					});
					break;
				default:
					toast("分享到" + _item.value);
					break;
			}
		};
		SharePoster.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.data.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				this.data.show && [
					apivm.h(
						"view",
						{class: "flex_h xy_center"},
						apivm.h(
							"view",
							{class: "poster_base"},
							this.props.dataMore.shareImg
								? apivm.h("image", {
										class: "poster_box",
										style: this.props.canvasStyle,
										src: this.props.dataMore.shareImg,
										mode: "aspectFill",
										thumbnail: "false"
								  })
								: [
										apivm.h("canvas", {
											type: "2d",
											id: "sharePosterCanvas",
											class: "canvas",
											style:
												"display:" +
												(platform() != "app" ? "flex" : "none") +
												";" +
												this.props.canvasStyle
										}),
										this.props.children
								  ]
						)
					),
					apivm.h(
						"view",
						{
							class: "share_warp",
							onClick: function() {
								return this$1.penetrate();
							}
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							{class: "share_btn_box"},
							this.data.btns.map(function(item, index, list) {
								return (
									(isParameters(item.show) ? item.show : true) &&
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.shareBtn(item);
											},
											class: "share_btn_item"
										},
										item.type == "img"
											? apivm.h("image", {
													style: "" + loadConfigurationSize(34),
													class: "share_btn_icon",
													src: item.src
											  })
											: apivm.h(
													"view",
													{
														style:
															loadConfigurationSize(34) +
															"background:" +
															(item.bg || "#F4F5F7") +
															";",
														class: "share_btn_icon"
													},
													apivm.h("a-iconfont", {
														name: item.src,
														color: item.color || "#333",
														size:
															G.appFontSize + (isParameters(item.size) ? Number(item.size) : 8)
													})
											  ),
										apivm.h(
											"text",
											{style: loadConfiguration(-2) + "color:#333;margin-top:8px;"},
											item.value
										)
									)
								);
							})
						),
						apivm.h(
							"view",
							null,
							!this.props.dataMore.dotCancel &&
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										style:
											"padding:18px;justify-content:center;border-top:10px solid rgba(0,0,0,0.03);"
									},
									apivm.h(
										"text",
										{style: loadConfiguration() + ";color:#333;text-align: center;"},
										"取消"
									)
								)
						),
						apivm.h("view", {style: "padding-bottom:" + safeArea().bottom + "px;"})
					)
				]
			);
		};

		return SharePoster;
	})(Component);
	SharePoster.css = {
		".poster_base": {width: "320px"},
		".canvas": {position: "absolute", left: "0", right: "0", zIndex: "11"},
		".poster_box": {
			background: "#FFFFFF",
			borderRadius: "10px",
			overflow: "hidden"
		},
		".share_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px",
			flexShrink: "0",
			maxHeight: "80%"
		},
		".share_btn_box": {padding: "10px 0", flexDirection: "row", flexWrap: "wrap"},
		".share_btn_item": {
			padding: "10px 5px",
			width: "25%",
			alignItems: "center",
			justifyContent: "center"
		},
		".share_btn_icon": {
			borderRadius: "50%",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("share-poster", SharePoster);

	var SharePosterNews = /*@__PURE__*/ (function(Component) {
		function SharePosterNews(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				canvasStyle: ""
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show && platform() != "app") {
							setTimeout(function() {
								this$1.baseInit();
							}, 0);
						}
					}
				}
			};
		}

		if (Component) SharePosterNews.__proto__ = Component;
		SharePosterNews.prototype = Object.create(Component && Component.prototype);
		SharePosterNews.prototype.constructor = SharePosterNews;
		SharePosterNews.prototype.baseInit = function() {
			var this$1 = this;

			var dm = this.props.dataMore;
			var dmd = this.props.dataMore.data;
			var createPic = function(canvas) {
				showProgress();
				var ctx;
				var bRect = null;

				dm.canvasImg = 0;
				dm.canvasErr = "";

				//设置画布大小背景圆角
				getBoundingClientRect("sharePoster", function(ret) {
					bRect = ret;
					this$1.data.canvasStyle =
						"width:" + bRect.width + "px;height:" + bRect.height + "px;";
					var dpr =
						platform() == "mp"
							? wx.getSystemInfoSync().pixelRatio
							: window.devicePixelRatio;
					canvas.width = bRect.width * dpr;
					canvas.height = bRect.height * dpr;
					ctx = canvas.getContext("2d");
					ctx.scale(dpr, dpr);
					canvasRoundedRect(
						{x: 0, y: 0, width: bRect.width, height: bRect.height},
						10,
						ctx
					);
					ctx.fillStyle = "white";
					ctx.fill();
					ctx.restore();
					var loadOtherElement = function() {
						// 标题背景
						dm.canvasImg++;
						canvasShowImg(
							{
								id: "posterTitleBg",
								src: shareAddress(1) + "image/bg_poster_top.png",
								round: [10, 10, 0, 0],
								bRect: bRect,
								callback: function(ret) {
									dm.canvasImg--;
									if (!ret) {
										dm.canvasErr = true;
									}
									canvasImgLoad(dm);
									//标题
									canvasShowText(
										{
											id: "posterTitle",
											text: showTextSize(dmd.title, 25),
											color: "#FFF",
											size: 1,
											spacing: 10,
											bRect: bRect
										},
										ctx
									);
								}
							},
							ctx,
							canvas
						);

						//文字
						canvasShowText(
							{
								id: "posterText1",
								text: showTextSize(dmd.source, 8),
								color: "#333",
								size: -2,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);
						canvasShowText(
							{
								id: "posterText2",
								text: (dmd.source ? "| " : "") + dmd.time,
								color: "#999",
								size: -2,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);
						canvasShowText(
							{
								id: "posterText3",
								text: showTextSize(removeTagAll(dmd.content), 95),
								color: "#999",
								size: -2,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);
						canvasShowText(
							{
								id: "posterText4",
								text: showTextSize(G.appName, G.careMode ? 6 : 8),
								weight: "600",
								color: "#333",
								size: 0,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);
						canvasShowText(
							{
								id: "posterText5",
								text: "长按识别 查看详情",
								color: "#666",
								size: -4,
								spacing: 5,
								bRect: bRect
							},
							ctx
						);

						canvasShowBG(
							{id: "posterLine1", round: 0, bg: "#F1F1F1", bRect: bRect},
							ctx
						);

						//图标
						dm.canvasImg++;
						canvasShowImg(
							{
								id: "posterIcon",
								src: appUrl() + "pageImg/open/logo",
								type: "contain",
								round: 0,
								bRect: bRect,
								callback: function(ret) {
									dm.canvasImg--;
									if (!ret) {
										dm.canvasErr = true;
									}
									canvasImgLoad(dm);
								}
							},
							ctx,
							canvas
						);

						//二维码
						dm.canvasImg++;
						canvasShowImg(
							{
								id: "posterQr",
								src: showImg(tomcatAddress() + "utils/qr?text=" + dmd.shareUrl),
								round: 0,
								bRect: bRect,
								callback: function(ret) {
									dm.canvasImg--;
									if (!ret) {
										dm.canvasErr = true;
									}
									canvasImgLoad(dm);
								}
							},
							ctx,
							canvas
						);
						canvasImgLoad(dm);
					};
					//有水印
					if (G.watermark) {
						canvasShowImg(
							{
								id: "sharePoster",
								src: G.watermark,
								round: 10,
								bRect: bRect,
								callback: function(ret) {
									loadOtherElement();
								}
							},
							ctx,
							canvas
						);
					} else {
						loadOtherElement();
					}
				});
			};
			var _id = document.querySelector("#sharePosterCanvas");
			if (!_id) {
				return;
			}
			switch (platform()) {
				case "web":
					createPic(_id);
					break;
				case "mp":
					_id.$$getNodesRef().then(function(nodesRef) {
						nodesRef
							.node(function(res) {
								createPic(res.node);
							})
							.exec();
					});
					break;
			}
		};
		SharePosterNews.prototype.render = function() {
			return apivm.h(
				"share-poster",
				{
					s: this.monitor,
					dataMore: this.props.dataMore,
					canvasStyle: this.data.canvasStyle
				},
				this.props.dataMore.show && [
					apivm.h(
						"view",
						{id: "sharePoster", class: "poster_box"},
						apivm.h("view", {
							class: "watermark_box",
							style:
								"display:" +
								(platform() != "app" ? "flex" : "none") +
								";z-index:2;background:#FFF;"
						}),
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							{
								id: "posterTitleBg",
								class: "poster_top",
								style:
									"background-image:url('" +
									shareAddress(1) +
									"image/bg_poster_top.png');background-size: 100% 100%;"
							},
							apivm.h(
								"text",
								{
									id: "posterTitle",
									style: loadConfiguration(1) + "color: #FFF;line-height:27px;"
								},
								showTextSize(this.props.dataMore.data.title, 25)
							)
						),
						apivm.h(
							"view",
							{style: "padding:15px;"},
							apivm.h(
								"view",
								{class: "flex_row"},
								apivm.h(
									"view",
									null,
									this.props.dataMore.data.source &&
										apivm.h(
											"text",
											{
												id: "posterText1",
												style: loadConfiguration(-2) + "color: #333;margin-right:10px;"
											},
											showTextSize(this.props.dataMore.data.source, 8)
										)
								),
								apivm.h(
									"view",
									null,
									this.props.dataMore.data.time &&
										apivm.h(
											"text",
											{id: "posterText2", style: loadConfiguration(-2) + "color: #999;"},
											(this.props.dataMore.data.source ? "| " : "") +
												this.props.dataMore.data.time
										)
								)
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.data.content &&
									apivm.h(
										"view",
										{style: "margin-top:15px;"},
										apivm.h(
											"text",
											{id: "posterText3", style: loadConfiguration(-2) + "color: #999;"},
											showTextSize(removeTagAll(this.props.dataMore.data.content), 95)
										)
									)
							)
						),
						apivm.h(
							"view",
							{style: "margin-top:20px;padding:0 16px;"},
							apivm.h("view", {
								id: "posterLine1",
								style: "width:100%;height:1px;background:#F1F1F1"
							})
						),
						apivm.h(
							"view",
							{style: "flex-direction:row;align-items: center;padding:15px 10px;"},
							apivm.h("image", {
								id: "posterIcon",
								style: loadConfigurationSize(36) + "border-radius:15px;",
								src: appUrl() + "pageImg/open/logo",
								mode: "aspectFit"
							}),
							apivm.h(
								"view",
								{class: "flex_w", style: "margin:0 10px;"},
								apivm.h(
									"text",
									{
										id: "posterText4",
										style: loadConfiguration() + "color:#333;font-weight: 600;"
									},
									showTextSize(G.appName, G.careMode ? 6 : 8)
								),
								apivm.h(
									"text",
									{
										id: "posterText5",
										style: loadConfiguration(-4) + "color:#666;margin-top:6px;"
									},
									"长按识别 查看详情"
								)
							),
							apivm.h(
								"view",
								{style: "" + loadConfigurationSize(56)},
								apivm.h("image", {
									id: "posterQr",
									class: "xy_100",
									src:
										tomcatAddress() +
										"utils/qr?text=" +
										this.props.dataMore.data.shareUrl,
									mode: "aspectFill"
								})
							)
						)
					)
				]
			);
		};

		return SharePosterNews;
	})(Component);
	SharePosterNews.css = {
		".poster_top": {padding: "20px 20px", width: "100%", minHeight: "94px"}
	};
	apivm.define("share-poster-news", SharePosterNews);

	// 复制
	function copyText(_text, _callback) {
		console.log("copy:" + _text);
		if (platform() == "app") {
			api.require("clipBoard").set(
				{
					value: _text
				},
				function(ret, err) {
					_callback && _callback(ret, err);
				}
			);
		} else if (platform() == "web") {
			htmlCopyText(_text, function(ret) {
				_callback && _callback(ret, null);
			});
		} else if (platform() == "mp") {
			wx.setClipboardData({
				data: _text,
				success: function success(ret) {
					_callback && _callback(ret, null);
				},
				fail: function fail(err) {
					_callback && _callback(null, err);
				}
			});
		}
	}

	// web复制
	function htmlCopyText(_text, callback) {
		var success = true;
		var textarea = document.createElement("textarea");
		textarea.value = _text;
		document.body.appendChild(textarea);
		textarea.select();
		textarea.setSelectionRange(0, textarea.value.length); // 兼容 iOS 设备
		try {
			if (navigator.clipboard) {
				navigator.clipboard.writeText(_text).then(
					function() {
						success = true;
					},
					function(err) {
						success = false;
					}
				);
			} else {
				var input = document.createElement("input");
				input.setAttribute("value", _text);
				document.body.appendChild(input);
				input.select();
				input.setSelectionRange(0, input.value.length); // 兼容 iOS 设备
				success = document.execCommand("copy");
				document.body.removeChild(input);
			}
		} catch (err) {
			success = false;
		} finally {
			document.body.removeChild(textarea);
		}
		callback && callback(success);
	}

	var Share = /*@__PURE__*/ (function(Component) {
		function Share(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				btns: [
					{
						show: true,
						key: "platform",
						value: "平台好友",
						type: "img",
						src: appUrl() + "pageImg/open/logo"
					},
					// {show:true,key:"session",value:"微信好友",type:"icon",bg:"#50C614",color:"#FFF",src:"changyonglogo28",size:16},
					// {show:true,key:"timeline",value:"朋友圈",type:"icon",bg:"#50C614",color:"#FFF",src:"pengyouquan",size:13},
					{
						show: true,
						key: "QFriend",
						value: "QQ",
						type: "icon",
						bg: "#3AA2F3",
						color: "#FFF",
						src: "QQ",
						size: 13
					},
					{
						show: true,
						key: "QZone",
						value: "QQ空间",
						type: "icon",
						bg: "#3AA2F3",
						color: "#FFF",
						src: "pengyouquan",
						size: 13
					},
					{
						show: true,
						key: "qrcode",
						value: "分享海报",
						type: "icon",
						bg: "",
						color: "",
						src: "tupian",
						size: 13
					},
					{
						show: true,
						key: "link",
						value: "复制链接",
						type: "icon",
						bg: "",
						color: "",
						src: "lianjie",
						size: 13
					}
				]
			};
			this.compute = {
				monitor: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							this.baseInit();
						}
					}
				}
			};
		}

		if (Component) Share.__proto__ = Component;
		Share.prototype = Object.create(Component && Component.prototype);
		Share.prototype.constructor = Share;
		Share.prototype.baseInit = function() {
			var showBtns = this.props.dataMore.btns || [];
			this.data.btns.forEach(function(_item) {
				_item.show = !showBtns.length || getItemForKey(_item.key, showBtns);
				if (
					_item.key == "session" ||
					_item.key == "timeline" ||
					_item.key == "QFriend" ||
					_item.key == "QZone"
				) {
					_item.show = _item.show && platform() == "app";
				} else if (_item.key == "qrcode") {
					_item.show = _item.show && !G.sharePop.dotQr;
				}
			});
		};
		Share.prototype.penetrate = function() {};
		Share.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		Share.prototype.getShowName = function() {
			switch (G.sharePop.data.code) {
				case "5":
					return {"1": "[资讯]", "2": "[资料]", "7": "[履职技巧]"}[
						G.sharePop.data.module
					];
				case "50_member":
					return "[驻站" + (G.sysSign == "rd" ? "代表" : "委员") + "]";
			}
		};
		Share.prototype.shareBtn = function(_item) {
			var this$1 = this;

			if (_item.key == "platform") {
				openWin_chat_share(
					{
						fType: "sendRichContentMessage",
						fName: this.getShowName(),
						fId: G.sharePop.data.id
					},
					function() {
						this$1.closePage();
					}
				);
				return;
			}
			var param = this.props.dataMore.param;
			var shareUrl = G.sharePop.data.shareUrl;
			if (!shareUrl) {
				showProgress("操作中");
				shareUrl =
					shareAddress() +
					"pages/index/?" +
					JSON.stringify(param)
						.replace(/\{/g, "%7B")
						.replace(/\}/g, "%7D")
						.replace(/\"/g, "%22");
				ajax(
					{u: appUrl() + "longShortLink/exchange"},
					"longShortLink",
					function(ret, err) {
						hideProgress();
						var data = ret ? ret.data || "" : "";
						G.sharePop.data.shareUrl = data ? appUrl() + "viewing/" + data : shareUrl;
						this$1.shareBtn(_item);
					},
					"转短链接",
					"post",
					{
						values: {
							longUrl: shareUrl
						}
					},

					{
						"content-type": "application/x-www-form-urlencoded"
					}
				);

				return;
			}
			switch (_item.key) {
				case "qrcode":
					G.sharePosterPop = {
						show: true,
						shareImg: "",
						data: G.sharePop.data
					};

					break;
				case "link":
					copyText(shareUrl, function(ret, err) {
						toast("复制" + (ret ? "成功" : "失败"));
					});
					break;
				case "QFriend":
				case "QZone":
					var qq = api.require("QQPlus");
					if (!qq) {
						toast("未绑定模块，请联系管理员");
						return;
					}
					qq.setIsPermissionGranted({granted: true});
					qq.installed(function(ret, err) {
						if (!ret.status) {
							toast("当前设备未安装QQ客户端");
							return;
						}
						var param = {
							url: shareUrl,
							title: G.sharePop.data.title.substring(0, 50),
							description: removeTagAll(G.sharePop.data.content).substring(0, 100),
							imgUrl: showImg(G.sharePop.data.url),
							type: _item.key
						};

						qq.shareNews(param, function(ret, err) {
							toast(ret.status ? "分享成功！" : JSON.stringify(err));
						});
					});
					break;
				case "session":
				case "timeline":
					var wx = api.require("wxPlus");
					if (!wx) {
						toast("未绑定模块，请联系管理员");
						return;
					}
					wx.isInstalled(function(ret, err) {
						if (!ret.installed) {
							toast("当前设备未安装wx客户端");
							return;
						}
						var param = {
							contentUrl: shareUrl,
							title: G.sharePop.data.title.substring(0, 50),
							description: removeTagAll(G.sharePop.data.content).substring(0, 100),
							thumb: "widget://image/icon_" + G.sysSign + ".png",
							scene: _item.key
						};

						wx.shareWebpage(param, function(ret, err) {
							toast(ret.status ? "分享成功！" : JSON.stringify(err));
						});
					});
					break;
				default:
					toast("分享到" + _item.value);
					break;
			}
		};
		Share.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					class: "flex_h"
				}),
				this.props.dataMore.show &&
					apivm.h(
						"view",
						{
							class: "share_warp",
							onClick: function() {
								return this$1.penetrate();
							}
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"text",
							{
								style:
									loadConfiguration(1) + "color:#333;font-weight: 600;padding:20px 16px;"
							},
							this.props.dataMore.title || "分享到"
						),
						apivm.h(
							"view",
							{class: "share_btn_box"},
							this.data.btns.map(function(item, index, list) {
								return (
									(isParameters(item.show) ? item.show : true) &&
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.shareBtn(item);
											},
											class: "share_btn_item"
										},
										item.type == "img"
											? apivm.h("image", {
													style: "" + loadConfigurationSize(34),
													class: "share_btn_icon",
													src: item.src
											  })
											: apivm.h(
													"view",
													{
														style:
															loadConfigurationSize(34) +
															"background:" +
															(item.bg || "#F4F5F7") +
															";",
														class: "share_btn_icon"
													},
													apivm.h("a-iconfont", {
														name: item.src,
														color: item.color || "#333",
														size:
															G.appFontSize + (isParameters(item.size) ? Number(item.size) : 8)
													})
											  ),
										apivm.h(
											"text",
											{style: loadConfiguration(-2) + "color:#333;margin-top:8px;"},
											item.value
										)
									)
								);
							})
						),
						apivm.h(
							"view",
							null,
							!this.props.dataMore.dotCancel &&
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										style:
											"padding:18px;justify-content:center;border-top:10px solid rgba(0,0,0,0.03);"
									},
									apivm.h(
										"text",
										{style: loadConfiguration() + ";color:#333;text-align: center;"},
										"取消"
									)
								)
						),
						apivm.h("view", {style: "padding-bottom:" + safeArea().bottom + "px;"})
					)
			);
		};

		return Share;
	})(Component);
	Share.css = {
		".share_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px",
			flexShrink: "0",
			maxHeight: "80%"
		},
		".share_btn_box": {padding: "10px 0", flexDirection: "row", flexWrap: "wrap"},
		".share_btn_item": {
			padding: "10px 5px",
			width: "25%",
			alignItems: "center",
			justifyContent: "center"
		},
		".share_btn_icon": {
			borderRadius: "50%",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("share", Share);

	var ZEmpty = /*@__PURE__*/ (function(Component) {
		function ZEmpty(props) {
			Component.call(this, props);
		}

		if (Component) ZEmpty.__proto__ = Component;
		ZEmpty.prototype = Object.create(Component && Component.prototype);
		ZEmpty.prototype.constructor = ZEmpty;
		ZEmpty.prototype.getShowImg = function() {
			if (this.props.dataMore.type == 1 || this.props.dataMore.type == 2) {
				return showImg(
					shareAddress(1) + "image/icon_empty_" + this.props.dataMore.type + ".png"
				);
			}
			return showImg(this.props.dataMore.type);
		};
		ZEmpty.prototype.getShowText = function() {
			return (
				this.props.dataMore.text ||
				(this.props.dataMore.type == 1
					? "暂无数据"
					: this.props.dataMore.type == 2
					? "网络异常，请点击重试"
					: "")
			);
		};
		ZEmpty.prototype.refresh = function() {
			showProgress("刷新中");
			this.fire("refresh");
			setTimeout(function() {
				hideProgress();
			}, 2500);
		};
		ZEmpty.prototype.up = function() {
			if (this.props._this && isFunction(this.props._this.loadMore)) {
				this.props._this.loadMore({detail: {}});
			} else {
				this.fire("up", {});
			}
		};
		ZEmpty.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"view",
					null,
					this.props.dataMore.type &&
						this.props.dataMore.type != "load" &&
						apivm.h(
							"view",
							{
								id: "" + (this.props.id || ""),
								style: "margin:100px 0;" + (this.props.style || ""),
								class: "xy_center " + (this.props.class || "")
							},
							apivm.h("image", {
								style: "width:200px;height:200px;",
								src: this.getShowImg(),
								mode: "aspectFill"
							}),
							apivm.h(
								"text",
								{
									style:
										loadConfiguration(this.props.size || 0) +
										"color:#666;padding:10px 20px;text-align: center;"
								},
								this.getShowText()
							),
							apivm.h(
								"view",
								null,
								this.props.dataMore.type == 2 &&
									apivm.h(
										"z-button",
										{
											style: "width:132px;margin-top:20px;",
											color: G.appTheme,
											round: true,
											onClick: this.refresh
										},
										"刷新"
									)
							),
							this.props.children
						)
				),
				apivm.h(
					"view",
					null,
					!this.props.dataMore.type &&
						this.props.dataMore.text &&
						apivm.h(
							"view",
							{class: "xy_center", onClick: this.up},
							apivm.h(
								"text",
								{style: loadConfiguration(-4) + "color:#bbb;padding:15px;"},
								this.props.dataMore.text
							)
						)
				)
			);
		};

		return ZEmpty;
	})(Component);
	apivm.define("z-empty", ZEmpty);

	var ZSkeleton = /*@__PURE__*/ (function(Component) {
		function ZSkeleton(props) {
			Component.call(this, props);
		}

		if (Component) ZSkeleton.__proto__ = Component;
		ZSkeleton.prototype = Object.create(Component && Component.prototype);
		ZSkeleton.prototype.constructor = ZSkeleton;
		ZSkeleton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{style: "width:100%;"},
				(Array.isArray(dataForNum(this.props.cell || 3))
					? dataForNum(this.props.cell || 3)
					: Object.values(dataForNum(this.props.cell || 3))
				).map(function(item$1, index$1, list) {
					return apivm.h(
						"view",
						{class: "z_skeleton"},
						apivm.h(
							"view",
							null,
							(Array.isArray(dataForNum(this$1.props.row || 4))
								? dataForNum(this$1.props.row || 4)
								: Object.values(dataForNum(this$1.props.row || 4))
							).map(function(item$1, index$1, list) {
								return apivm.h("view", {
									class: "z_skeleton_row",
									style:
										"width:" +
										(!index$1 ? 40 : index$1 == list.length - 1 ? 60 : 100) +
										"%;"
								});
							})
						)
					);
				})
			);
		};

		return ZSkeleton;
	})(Component);
	ZSkeleton.css = {
		".z_skeleton": {width: "100%", padding: "10px 16px"},
		".z_skeleton_row": {
			marginTop: "12px",
			height: "16px",
			backgroundColor: "#f2f3f5"
		}
	};
	apivm.define("z-skeleton", ZSkeleton);

	var YScrollView = /*@__PURE__*/ (function(Component) {
		function YScrollView(props) {
			Component.call(this, props);
			this.data = {
				mId: "",
				scroll_view: "", //滚动到id
				scroll_animation: true, //滚动动画
				refreshEd: false
			};
			this.compute = {
				monitor: function() {
					if (!this.data.mId) {
						var nowId = this.props.id || "scroll_view" + getNum();
						this.data.mId = !document.getElementById(nowId)
							? nowId
							: "scroll_view" + getNum();
						(this.props.dataMore || {}).id = this.data.mId;
					}
					var dataMore = this.props.dataMore || {};
					if (this.data.scroll_view != dataMore.scroll_view) {
						this.data.scroll_animation = isParameters(dataMore.scroll_animation)
							? dataMore.scroll_animation
							: true;
						this.data.scroll_view = dataMore.scroll_view || "";
						if (this.data.scroll_view) {
							this.scrollTo(this.data.scroll_view);
						}
					}
				}
			};
		}

		if (Component) YScrollView.__proto__ = Component;
		YScrollView.prototype = Object.create(Component && Component.prototype);
		YScrollView.prototype.constructor = YScrollView;
		YScrollView.prototype.onrefresherrefresh = function(e) {
			var this$1 = this;

			if (this.props._this && isFunction(this.props._this.pageRefresh)) {
				this.props._this.pageRefresh({detail: {}});
			} else {
				this.fire("lower", {});
			}
			this.data.refreshEd = true;
			setTimeout(function() {
				this$1.data.refreshEd = false;
			}, 800);
		};
		YScrollView.prototype.onscrolltolower = function(e) {
			if (this.props._this && isFunction(this.props._this.loadMore)) {
				this.props._this.loadMore({detail: {}});
			} else {
				this.fire("up", {});
			}
		};
		YScrollView.prototype.onscroll = function(ref) {
			var detail = ref.detail;

			if (this.props._this && isFunction(this.props._this.pageScroll)) {
				this.props._this.pageScroll({detail: detail});
			} else {
				this.fire("scroll", detail);
			}
		};
		YScrollView.prototype.scrollTo = function(nowView) {
			var this$1 = this;

			var _animated = this.data.scroll_animation;
			if (document.getElementById(this.data.mId) && platform() != "mp") {
				var scrollTo =
					platform() == "app"
						? {view: nowView, animated: _animated}
						: this.props.dataMore.direction == "horizontal"
						? {
								left: this.getOffestValue(document.getElementById(nowView)).left,
								behavior: _animated ? "smooth" : "instant"
						  }
						: {
								top: this.getOffestValue(document.getElementById(nowView)).top,
								behavior: _animated ? "smooth" : "instant"
						  };
				document.getElementById(this.data.mId).scrollTo(scrollTo);
			}
			setTimeout(function() {
				this$1.props.dataMore.scroll_view = "";
			}, 500);
		};
		YScrollView.prototype.getOffestValue = function(elem) {
			var Far = null;
			var topValue = elem && elem.offsetTop;
			var leftValue = elem && elem.offsetLeft;
			var offsetFar = elem && elem.offsetParent;
			while (offsetFar) {
				if (offsetFar.id == this.data.mId) {
					break;
				}
				topValue += offsetFar.offsetTop;
				leftValue += offsetFar.offsetLeft;
				Far = offsetFar;
				offsetFar = offsetFar.offsetParent;
				if (offsetFar.id == this.data.mId) {
					break;
				}
			}
			return {top: topValue, left: leftValue, Far: Far};
		};
		YScrollView.prototype.render = function() {
			return apivm.h(
				"scroll-view",
				{
					s: this.monitor,
					id: "" + this.data.mId,
					style: "flex:1;" + (this.props.style || ""),
					class: "" + (this.props.class || ""),
					"refresher-background": "rgba(0,0,0,0)",
					"scroll-x": isParameters(this.props["scroll-x"])
						? this.props["scroll-x"]
						: false,
					"scroll-y": isParameters(this.props["scroll-y"])
						? this.props["scroll-y"]
						: true,
					bounces: isParameters(this.props["bounces"])
						? this.props["bounces"]
						: false,
					"scroll-into-view": platform() == "mp" ? this.data.scroll_view : null,
					"scroll-with-animation":
						platform() == "mp" ? this.data.scroll_animation : null,
					"refresher-enabled": isParameters(this.props["refresh"])
						? this.props["refresh"] &&
						  (platform() != "app" ? !this.props._this.props.dataMore : true)
						: false,
					"refresher-triggered": this.data.refreshEd,
					onScrolltolower: this.onscrolltolower,
					onRefresherrefresh: this.onrefresherrefresh,
					onScroll: this.onscroll
				},
				this.props.children
			);
		};

		return YScrollView;
	})(Component);
	apivm.define("y-scroll-view", YScrollView);

	var ZBreadcrumb = /*@__PURE__*/ (function(Component) {
		function ZBreadcrumb(props) {
			Component.call(this, props);
			this.data = {
				oldData: "",
				scrollView: {
					scroll_view: "",
					direction: "horizontal"
				}
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					var data = this.props.dataMore.data;
					if (isArray(data) && this.data.oldData != JSON.stringify(data)) {
						if (this.data.oldData) {
							setTimeout(function() {
								this$1.tabClick(data[data.length - 1]);
							}, 30);
						}
						this.data.oldData = JSON.stringify(data);
					}
				}
			};
		}

		if (Component) ZBreadcrumb.__proto__ = Component;
		ZBreadcrumb.prototype = Object.create(Component && Component.prototype);
		ZBreadcrumb.prototype.constructor = ZBreadcrumb;
		ZBreadcrumb.prototype.tabClick = function(_item) {
			if (this.props.dataMore.key == _item.key) {
				return;
			}
			this.props.dataMore.key = _item.key;

			var nLevel = [];
			for (var i = 0; i < this.props.dataMore.data.length; i++) {
				var item = this.props.dataMore.data[i];
				nLevel.push(item);
				if (item.key == _item.key) {
					break;
				}
			}
			this.props.dataMore.data = nLevel;

			this.fire("change", {key: this.props.dataMore.key});
			this.data.scrollView.scroll_view = "breadcrumb_" + _item.key;
		};
		ZBreadcrumb.prototype.nTouchmove = function() {
			touchmove();
		};
		ZBreadcrumb.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "" + (this.props.id || ""),
					style: "flex-direction:row;" + (this.props.style || ""),
					class: "" + (this.props.class || "")
				},
				apivm.h(
					"y-scroll-view",
					{
						style: "display: block;white-space: nowrap;",
						_this: this,
						dataMore: this.data.scrollView,
						"scroll-x": true,
						"scroll-y": false
					},
					(Array.isArray(this.props.dataMore.data || [])
						? this.props.dataMore.data || []
						: Object.values(this.props.dataMore.data || [])
					).map(function(item$1, index$1, list) {
						return apivm.h(
							"view",
							{
								id: "breadcrumb_" + item$1.key,
								style:
									"display: inline-block;margin-left:" +
									(!index$1 ? "16" : "0") +
									"px;margin-right:" +
									(index$1 == list.length - 1 ? "16" : "0") +
									"px;",
								onClick: function() {
									return this$1.tabClick(item$1);
								},
								onTouchStart: this$1.nTouchmove,
								onTouchMove: this$1.nTouchmove,
								onTouchEnd: this$1.nTouchmove
							},
							apivm.h(
								"view",
								{class: "flex_row"},
								apivm.h(
									"view",
									{style: "display:" + (index$1 ? "flex" : "none") + ";"},
									apivm.h("a-iconfont", {
										style: "margin:0 8px;transform: rotate(180deg) scale(0.7,0.7);",
										name: "fanhui1",
										color: index$1 ? "#999" : "rgba(0,0,0,0)",
										size: G.appFontSize - 4
									})
								),
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(this$1.props.size) +
											";color:" +
											(index$1 == list.length - 1 && index$1 ? "#333" : "#999")
									},
									item$1.value
								)
							)
						);
					})
				)
			);
		};

		return ZBreadcrumb;
	})(Component);
	apivm.define("z-breadcrumb", ZBreadcrumb);

	var CollectAdd = /*@__PURE__*/ (function(Component) {
		function CollectAdd(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				level: {key: "0", data: [{key: "0", value: "全部"}]},

				listData: [],
				favoriteFolder: [],
				emptyBox: {
					type: "load",
					text: ""
				}
			};
			this.compute = {
				monitor: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						this.data.level.key = "0";
						this.data.level.data = [this.data.level.data[0]];
						this.data.emptyBox.type = "load";
						if (this.data.show) {
							this.baseInit();
						}
					}
				},
				titleName: function() {
					var name = "";
					switch (this.props.dataMore.key) {
						case "long_add":
							name =
								"收藏到“" +
								this.data.level.data[this.data.level.data.length - 1].value +
								"”";
							break;
					}

					return name;
				}
			};
		}

		if (Component) CollectAdd.__proto__ = Component;
		CollectAdd.prototype = Object.create(Component && Component.prototype);
		CollectAdd.prototype.constructor = CollectAdd;
		CollectAdd.prototype.baseInit = function() {
			this.getData();
		};
		CollectAdd.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		CollectAdd.prototype.penetrate = function() {};
		CollectAdd.prototype.confirmBtn = function() {
			var this$1 = this;

			var data = this.props.dataMore.data;
			optionCollect(
				{
					code: data.code,
					id: data.id,
					collect: true,
					theme: data.title,
					folderId: this.data.level.key
				},
				function(ret) {
					if (ret && ret.code == 200) {
						this$1.closePage();
						this$1.fire("refresh");
					} else {
						toast(ret ? ret.message || ret.data : NET_ERR);
					}
				}
			);
		};
		CollectAdd.prototype.openFolder = function(_item) {
			this.data.level.data.push({key: _item.id, value: _item.name});
		};
		CollectAdd.prototype.getData = function(_type) {
			var this$1 = this;

			ajax(
				{u: appUrl() + "favoriteFolder/list"},
				"favoriteFolder",
				function(ret, err) {
					hideProgress();
					var code = ret ? ret.code : "";
					this$1.data.favoriteFolder =
						code == "200" && isArray(ret.data) ? ret.data : [];
					this$1.showFavoriteFolder(ret);
				},
				"收藏夹列表",
				"post",
				{
					body: JSON.stringify({})
				}
			);
		};
		CollectAdd.prototype.showFavoriteFolder = function(ret) {
			var newList = [];
			if (this.data.level.key == "0") {
				newList = this.data.favoriteFolder;
			} else {
				newList =
					(getItemForKey(this.data.level.key, this.data.favoriteFolder, "id") || {})
						.children || [];
			}
			this.data.listData = newList;
			if (!this.data.listData.length) {
				this.data.emptyBox.type = ret ? "1" : "2";
				this.data.emptyBox.text =
					ret && ret.code != 200 ? ret.message || ret.data : "";
			} else {
				this.data.emptyBox.type = "";
				this.data.emptyBox.text = "";
			}
		};
		CollectAdd.prototype.levelChange = function() {
			this.getData(0);
		};
		CollectAdd.prototype.addFolder = function() {
			var this$1 = this;

			alert(
				{
					title: "请输入文件夹名字",
					type: "input",
					msg: "",
					placeholder: "新建收藏夹",
					buttons: ["确定", "取消"]
				},
				function(ret) {
					if (ret.buttonIndex == 1) {
						ajaxProcess(
							{
								toast: "新建中",
								url: appUrl() + "favoriteFolder/add",
								param: {
									form: {
										parentId: this$1.data.level.key || "0",
										name: ret.content || ret.placeholder
									}
								},

								name: "新建收藏"
							},
							function(ret, err) {
								if (ret && ret.code == 200) {
									this$1.getData(0);
								}
							}
						);
					}
				}
			);
		};
		CollectAdd.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style: "height:20%;flex-shrink: 0;"
				}),
				this.props.dataMore.show && [
					apivm.h(
						"view",
						{
							class: "flex_h pages_warp",
							onClick: function() {
								return this$1.penetrate();
							}
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							{class: "header_warp flex_row"},
							apivm.h(
								"view",
								{class: "header_main xy_center"},
								apivm.h(
									"text",
									{
										style: loadConfiguration(1) + "font-weight: 600;color:" + G.headColor
									},
									this.titleName
								)
							),
							apivm.h(
								"view",
								{class: "header_left_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{style: loadConfiguration(1) + "color:#666;margin:0 4px;"},
										"关闭"
									)
								)
							),
							apivm.h(
								"view",
								{class: "header_right_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.confirmBtn();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{
											style:
												loadConfiguration(1) + "color:" + G.appTheme + ";margin:0 4px;"
										},
										"确定"
									)
								)
							)
						),
						apivm.h("z-divider", null),
						apivm.h(
							"view",
							{
								class: "flex_row",
								style: "padding:10px 16px 10px 10px;margin-top:10px;"
							},
							apivm.h(
								"view",
								{class: "flex_w"},
								apivm.h("z-breadcrumb", {
									size: -2,
									dataMore: this.data.level,
									onChange: this.levelChange
								})
							),
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.addFolder();
									},
									class: "flex_row",
									style: "margin-left:10px;"
								},
								apivm.h("a-iconfont", {
									style: "margin-right:6px;",
									name: "folder-add-fill",
									color: "#F6931C",
									size: G.appFontSize + 4
								}),
								apivm.h("text", {style: loadConfiguration(1) + "color:#333;"}, "新建")
							)
						),
						apivm.h(
							"view",
							{class: "flex_h"},
							apivm.h(
								"view",
								null,
								!this.data.emptyBox.type &&
									this.data.listData.map(function(item, index) {
										return [
											apivm.h(
												"view",
												{
													onClick: function() {
														return this$1.openFolder(item, index);
													}
												},
												apivm.h(
													"view",
													{class: "folder_item flex_row"},
													apivm.h("a-iconfont", {
														name: "folder-2-fill",
														color: "#ffd977",
														size: G.appFontSize + 20
													}),
													apivm.h(
														"view",
														{class: "folder_item_body flex_w"},
														apivm.h(
															"view",
															{class: "folder_item_warp flex_row"},
															apivm.h(
																"text",
																{style: loadConfiguration(1) + "color: #333;flex:1;"},
																item.name
															),
															apivm.h(
																"view",
																{style: "padding:8px;transform: rotate(-90deg);"},
																apivm.h("a-iconfont", {
																	name: "xiangxia1",
																	color: "#999999",
																	size: G.appFontSize
																})
															)
														),
														apivm.h("view", {class: "folder_item_line"})
													)
												)
											)
										];
									})
							),
							apivm.h(
								"view",
								null,
								this.data.emptyBox.type == "load" && apivm.h("z-skeleton", null)
							),
							apivm.h("z-empty", {
								_this: this,
								style: "margin:50px 0;",
								dataMore: this.data.emptyBox,
								onRefresh: this.getData
							})
						)
					)
				]
			);
		};

		return CollectAdd;
	})(Component);
	CollectAdd.css = {
		".pages_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		},
		".folder_item": {padding: "0 16px"},
		".folder_item_body": {marginLeft: "15px"},
		".folder_item_warp": {minHeight: "65px", padding: "5px 0"},
		".folder_item_line": {borderTop: "1px solid #EEEEEE"},
		".folder_item_more": {width: "auto", height: "auto", padding: "6px"}
	};
	apivm.define("collect-add", CollectAdd);

	var ItemFile = /*@__PURE__*/ (function(Component) {
		function ItemFile(props) {
			Component.call(this, props);
		}

		if (Component) ItemFile.__proto__ = Component;
		ItemFile.prototype = Object.create(Component && Component.prototype);
		ItemFile.prototype.constructor = ItemFile;
		ItemFile.prototype.openDetail = function(e) {
			if (this.props.item.fileInfo.type == "folder") {
				this.fire("folder", this.props.item);
				return;
			}
			if (this.props.select) {
				var nItem = getItemForKey(this.props.item.id, this.props.listSelect, "id");
				if (nItem) {
					if (!nItem.readonly) {
						delItemForKey(nItem, this.props.listSelect, "id");
					}
				} else {
					this.props.listSelect.push(this.props.item);
				}
			}
			stopBubble(e);
		};
		ItemFile.prototype.isSelectValue = function(_item, _type) {
			var selectItem = getItemForKey(_item.id, this.props.listSelect, "id");
			if (_type == "color") {
				return selectItem && !selectItem.readonly;
			}
			return selectItem;
		};
		ItemFile.prototype.showDot = function(_item) {
			return (
				_item.isRedDot == 1 ||
				getItemForKey(_item.id, this.props.dataMore.listUnread || [])
			);
		};
		ItemFile.prototype.openLeftMore = function(e, _item) {
			stopBubble(e);
			this.fire("leftMore", _item);
		};
		ItemFile.prototype.openRightMore = function(e, _item) {
			stopBubble(e);
			this.fire("rightMore", _item);
		};
		ItemFile.prototype.showType = function() {
			var dataMore = this.props.dataMore;
			var select = this.props.select;
			if (
				dataMore.type == "details" ||
				(select && this.props.item.fileInfo.type == "folder")
			) {
				return 1;
			}
			if (select) {
				return 2;
			}
			return 3;
		};
		ItemFile.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{onClick: this.openDetail, class: "item_file_item flex_row"},
				apivm.h(
					"view",
					null,
					this.showType() == 2 &&
						apivm.h(
							"view",
							{
								style: "padding:10px;",
								onClick: function(e) {
									return this$1.openLeftMore(e, this$1.props.item);
								}
							},
							apivm.h("a-iconfont", {
								name: "gengduo3",
								color: "#999",
								size: G.appFontSize + 10
							})
						)
				),
				apivm.h(
					"view",
					{style: "padding:4px;margin-right:10px;"},
					apivm.h("a-iconfont", {
						name: this.props.item.fileInfo.name,
						color: this.props.item.fileInfo.color,
						size: G.appFontSize + 20
					}),
					this.showDot(this.props.item)
						? apivm.h("view", {
								class: "item_file_redDot flex_row",
								style: "" + loadConfigurationSize(-6)
						  })
						: null
				),
				apivm.h(
					"view",
					{class: "flex_w"},
					apivm.h(
						"text",
						{
							class: "text_one",
							style:
								loadConfiguration() +
								"height:" +
								(G.appFontSize + 3) +
								"px;font-weight: 600;color:#333;"
						},
						this.props.item.name
					),
					this.props.dataMore.type != "details"
						? apivm.h(
								"view",
								{class: "flex_row", style: "margin-top:4px;"},
								apivm.h(
									"view",
									null,
									this.props.item.firstMsg &&
										apivm.h(
											"view",
											{class: "flex_row"},
											apivm.h(
												"text",
												{
													style:
														loadConfiguration(-4) +
														"color: " +
														(this.props.item.fristColor || "#999") +
														";"
												},
												this.props.item.firstMsg
											),
											apivm.h("view", {
												style:
													"width:1px;height:" +
													(G.appFontSize - 1) +
													"px;background:#eee;margin:0 10px;"
											})
										)
								),
								apivm.h(
									"view",
									null,
									this.props.item.firstIcon &&
										this.props.item.firstIcon.name &&
										apivm.h("a-iconfont", {
											style: "margin-right:6px;",
											name: this.props.item.firstIcon.name,
											color: this.props.item.firstIcon.color || "#999",
											size: G.appFontSize
										})
								),
								apivm.h(
									"text",
									{style: loadConfiguration(-4) + "color: #999;flex-shrink: 0;"},
									dayjs(this.props.item.time).format("YYYY-MM-DD HH:mm")
								),
								apivm.h(
									"view",
									null,
									isArray(this.props.item.addMsg) &&
										this.props.item.addMsg.length > 0 &&
										apivm.h(
											"view",
											{class: "flex_row"},
											(Array.isArray(this.props.item.addMsg)
												? this.props.item.addMsg
												: Object.values(this.props.item.addMsg)
											).map(function(nItem, nIndex) {
												return apivm.h(
													"view",
													{class: "flex_row"},
													apivm.h("view", {
														style:
															"width:1px;height:" +
															(G.appFontSize - 1) +
															"px;background:#eee;margin:0 10px;"
													}),
													apivm.h(
														"text",
														{
															style:
																loadConfiguration(-4) +
																"color: " +
																(nItem.color || "#999") +
																";"
														},
														nItem.text
													)
												);
											})
										)
								),
								apivm.h(
									"view",
									null,
									!isArray(this.props.item.addMsg) &&
										this.props.item.addMsg &&
										apivm.h(
											"view",
											{class: "flex_row"},
											apivm.h("view", {
												style:
													"width:1px;height:" +
													(G.appFontSize - 1) +
													"px;background:#eee;margin:0 10px;"
											}),
											apivm.h(
												"text",
												{
													style:
														loadConfiguration(-4) +
														"color: " +
														(this.props.item.addColor || "#999") +
														";"
												},
												this.props.item.addMsg
											)
										)
								)
						  )
						: null
				),
				apivm.h(
					"view",
					null,
					this.showType() == 1 &&
						apivm.h(
							"view",
							{style: "padding:8px;transform: rotate(-90deg);"},
							apivm.h("a-iconfont", {
								name: "xiangxia1",
								color: "#999999",
								size: G.appFontSize
							})
						)
				),
				apivm.h(
					"view",
					null,
					this.showType() == 2 &&
						apivm.h(
							"view",
							{style: "margin-left:5px;"},
							apivm.h("z-radio", {
								checked: this.isSelectValue(this.props.item),
								size: 4,
								color: this.isSelectValue(this.props.item, "color")
									? G.appTheme
									: "#999"
							})
						)
				),
				apivm.h(
					"view",
					null,
					this.showType() == 3 &&
						apivm.h(
							"view",
							{
								style: "padding:8px;margin: -8px 0;",
								onClick: function(e) {
									return this$1.openRightMore(e, this$1.props.item);
								}
							},
							apivm.h("a-iconfont", {
								name: "gengduo",
								color: "#999999",
								size: G.appFontSize
							})
						)
				)
			);
		};

		return ItemFile;
	})(Component);
	ItemFile.css = {
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden",
			display: "inline"
		},
		".item_file_item": {minHeight: "70px", padding: "5px 16px"},
		".item_file_redDot": {
			position: "absolute",
			zIndex: "1",
			top: "2px",
			right: "2px",
			background: "#f92323",
			borderRadius: "50%",
			whiteSpace: "nowrap",
			justifyContent: "center"
		}
	};
	apivm.define("item-file", ItemFile);

	var FileList = /*@__PURE__*/ (function(Component) {
		function FileList(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				inputBox: {
					show: true,
					value: "",
					placeholder: "请输入关键词"
				},

				level: {key: "0", data: [{key: "0", value: "全部"}]},

				listData: [],
				listSelect: [],
				itemFileMore: {
					type: "select"
				},

				emptyBox: {
					type: "load",
					text: ""
				},

				orderBys: {
					key: "updatedate",
					direction: 1 //0 顺序 1倒序
				},
				select: false,
				hasAddFolder: false //新建按钮
			};
			this.compute = {
				monitor: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						this.data.level.key = "0";
						this.data.level.data = [this.data.level.data[0]];
						this.data.emptyBox.type = "load";
						if (this.data.show) {
							this.baseInit();
						}
					}
				},
				titleName: function() {
					var name = "";
					switch (this.props.dataMore.key) {
						case "cloud_file":
							name = "云盘文件";
							break;
						case "chat_file":
							name = "聊天文件";
							break;
						case "cloud_resave":
							name =
								"转存到“" +
								(this.data.level.data.length == 1
									? "我的云盘"
									: this.data.level.data[this.data.level.data.length - 1].value) +
								"”";
							break;
					}

					return name;
				}
			};
		}

		if (Component) FileList.__proto__ = Component;
		FileList.prototype = Object.create(Component && Component.prototype);
		FileList.prototype.constructor = FileList;
		FileList.prototype.baseInit = function() {
			this.data.select = this.props.dataMore.select;
			this.data.hasAddFolder = this.props.dataMore.addFolder;
			this.data.listSelect = [];
			this.getData(0);
		};
		FileList.prototype.closePage = function() {
			this.props.dataMore.show = false;
		};
		FileList.prototype.penetrate = function() {};
		FileList.prototype.confirmBtn = function() {
			var dataMore = this.props.dataMore;
			dataMore.toId = this.data.level.key;
			dataMore.toItem = this.data.level.data[this.data.level.data.length - 1];
			dataMore.listSelect = this.data.listSelect;
			if (dataMore.callback) {
				dataMore.callback(dataMore);
			} else {
				this.fire("click", dataMore);
			}
			this.closePage();
		};
		FileList.prototype.openFolder = function(e) {
			this.data.level.data.push({key: e.detail.id, value: e.detail.name});
		};
		FileList.prototype.openLeftMore = function(ref) {
			var this$1 = this;
			var detail = ref.detail;

			var buttons = ["打开"];
			if (detail.createBy == G.uId || detail.createBy == G.userId) {
				buttons.push("删除");
			}
			actionSheet(
				{
					title: "提示",
					buttons: buttons
				},
				function(ret, err) {
					if (ret.name == "打开") {
						openWin_filePreviewer({
							id: detail.fileInfoId,
							suffix: detail.fileInfo.type,
							fileSource: "5"
						});
					} else if (ret.name == "删除") {
						var delKey = "groupFileId";
						var url = appUrl() + "chatGroupFile/dels";
						var param = {ids: [detail[delKey]]};
						if (this$1.props.dataMore.key == "cloud_file") {
							url = appUrl() + "panfile/recycleflag";
							delKey = "id";
							param = {ids: [detail[delKey]], isRecycle: 1};
						}
						ajaxAlert(
							{
								msg: "确定删除吗?",
								url: url,
								param: param,
								toast: "删除中"
							},
							function(ret) {
								delItemForKey(detail[delKey], this$1.data.listData, delKey);
								delItemForKey(detail[delKey], this$1.data.listSelect, delKey);
							}
						);
					}
				}
			);
		};
		FileList.prototype.getData = function(_type) {
			var this$1 = this;

			var dataMore = this.props.dataMore;
			var url =
				appUrl() +
				(dataMore.key == "long_shared" ? "pubsharefile" : "panfile") +
				"/list";
			var tableId =
				dataMore.key == "long_shared" ? "id_pan_pubshare_file" : "id_pan_file_list";
			var postParam = {
				pageNo: 1,
				pageSize: 9999,
				keyword: "",
				tableId: tableId,
				query: {fileMenu: "1", fileStatus: "0"},
				orderBys: [
					{
						columnId: tableId + "_" + this.data.orderBys.key,
						isDesc: this.data.orderBys.direction
					}
				],
				wheres: [
					{
						columnId: tableId + "_parentid",
						queryType: "EQ",
						value: this.data.level.key || "0"
					}
				]
			};

			if (dataMore.key == "cloud_file") {
				delete postParam.query;
			}
			if (dataMore.key == "chat_file") {
				url = appUrl() + "chatGroupFile/list";
				postParam = {
					pageNo: 1,
					pageSize: 9999,
					keyword: "",
					query: {chatGroupId: dataMore.id}
				};
			}
			ajax(
				{u: url},
				"file_list",
				function(ret, err) {
					hideProgress();
					var code = ret ? ret.code : "";
					var data = ret ? ret.data || [] : [];
					if (!isArray(data) || !data.length) {
						dealData(_type, this$1, ret);
						return;
					}
					var nowList = [];
					data.forEach(function(_eItem) {
						var item = {};
						if (dataMore.key == "chat_file") {
							item.groupFileId = _eItem.id;
							_eItem = _eItem.fileInfo || {};
						}
						item.id = _eItem.id || ""; //id
						item.name = _eItem.fileName || _eItem.originalFileName || ""; //标题
						item.time = _eItem.updateDate || _eItem.createDate;
						item.fileInfoId = _eItem.fileInfoId || _eItem.id;
						item.createBy = _eItem.createBy || _eItem.accountId;

						item.fileInfo = getFileInfo(_eItem.extName || "folder");
						if (dataMore.key == "cloud_file" || dataMore.key == "chat_file") {
							nowList.push(item);
						} else {
							if (!getItemForKey(item.id, this$1.data.listSelect, "id")) {
								nowList.push(item);
							}
						}
					});
					if (!_type) {
						this$1.data.listData = nowList;
					} else {
						this$1.data.listData = this$1.data.listData.concat(nowList);
					}
					this$1.data.emptyBox.type = "";
					this$1.data.emptyBox.text = LOAD_ALL;
				},
				"列表",
				"post",
				{
					body: JSON.stringify(postParam)
				}
			);
		};
		FileList.prototype.levelChange = function() {
			this.getData(0);
		};
		FileList.prototype.addFolder = function() {
			var this$1 = this;

			alert(
				{
					title: "请输入文件夹名字",
					type: "input",
					msg: "",
					placeholder: "新建文件夹",
					buttons: ["确定", "取消"]
				},
				function(ret) {
					if (ret.buttonIndex == 1) {
						ajaxProcess(
							{
								toast: "新建中",
								url:
									appUrl() +
									(this$1.props.dataMore.key == "cloud_shared"
										? "pubsharefile"
										: "panfile") +
									"/addmenu",
								param: {
									parentId: this$1.data.level.key || "0",
									fileName: ret.content || ret.placeholder,
									pubshareName: ret.content || ret.placeholder
								},

								name: "新建文件夹"
							},
							function(ret, err) {
								if (ret && ret.code == 200) {
									this$1.getData(0);
								}
							}
						);
					}
				}
			);
		};
		FileList.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.show ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style: "height:20%;flex-shrink: 0;"
				}),
				this.props.dataMore.show && [
					apivm.h(
						"view",
						{
							class: "flex_h pages_warp",
							onClick: function() {
								return this$1.penetrate();
							}
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							{class: "header_warp flex_row"},
							apivm.h(
								"view",
								{class: "header_main xy_center"},
								apivm.h(
									"text",
									{
										style: loadConfiguration(1) + "font-weight: 600;color:" + G.headColor
									},
									this.titleName
								)
							),
							apivm.h(
								"view",
								{class: "header_left_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{style: loadConfiguration(1) + "color:#666;margin:0 4px;"},
										"关闭"
									)
								)
							),
							apivm.h(
								"view",
								{class: "header_right_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.confirmBtn();
										},
										class: "header_btn"
									},
									apivm.h(
										"text",
										{
											style:
												loadConfiguration(1) + "color:" + G.appTheme + ";margin:0 4px;"
										},
										"确定"
									)
								)
							)
						),
						apivm.h("z-divider", null),
						apivm.h(
							"view",
							{
								class: "flex_row",
								style: "padding:10px 16px 10px 10px;margin-top:10px;"
							},
							apivm.h(
								"view",
								{class: "flex_w"},
								apivm.h("z-breadcrumb", {
									size: -2,
									dataMore: this.data.level,
									onChange: this.levelChange
								})
							),
							this.data.hasAddFolder &&
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.addFolder();
										},
										class: "flex_row",
										style: "margin-left:10px;"
									},
									apivm.h("a-iconfont", {
										style: "margin-right:6px;",
										name: "folder-add-fill",
										color: "#F6931C",
										size: G.appFontSize + 4
									}),
									apivm.h("text", {style: loadConfiguration(1) + "color:#333;"}, "新建")
								)
						),
						apivm.h(
							"y-scroll-view",
							{_this: this},
							apivm.h(
								"view",
								null,
								!this.data.emptyBox.type &&
									this.data.listData.map(function(item, index) {
										return [
											apivm.h("item-file", {
												dataMore: this$1.data.itemFileMore,
												search: this$1.data.inputBox,
												_this: this$1,
												select: this$1.data.select,
												listSelect: this$1.data.listSelect,
												item: item,
												onRefresh: function() {
													return this$1.getData(0);
												},
												onFolder: this$1.openFolder,
												onLeftMore: this$1.openLeftMore
											}),
											apivm.h(
												"view",
												{style: "padding-left:56px;"},
												apivm.h("z-divider", null)
											)
										];
									})
							),
							apivm.h(
								"view",
								null,
								this.data.emptyBox.type == "load" && apivm.h("z-skeleton", null)
							),
							apivm.h("z-empty", {
								_this: this,
								style: "margin:50px 0;",
								dataMore: this.data.emptyBox,
								onRefresh: this.getData
							})
						)
					)
				]
			);
		};

		return FileList;
	})(Component);
	FileList.css = {
		".pages_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px"
		},
		".folder_item": {padding: "0 16px"},
		".folder_item_body": {marginLeft: "15px"},
		".folder_item_warp": {minHeight: "65px", padding: "5px 0"},
		".folder_item_line": {borderTop: "1px solid #EEEEEE"},
		".folder_item_more": {width: "auto", height: "auto", padding: "6px"}
	};
	apivm.define("file-list", FileList);

	var YAttachments = /*@__PURE__*/ (function(Component) {
		function YAttachments(props) {
			Component.call(this, props);
			this.data = {
				oldData: "",
				imgList: [],
				fileList: []
			};
			this.compute = {
				monitor: function() {
					var data = this.props.data;
					if (isArray(data) && this.data.oldData != JSON.stringify(data)) {
						this.data.oldData = JSON.stringify(data);
						var imgList = [],
							fileList = [];
						data.forEach(function(_eItem, _eIndex, _eArr) {
							var fileInfo = getFileInfo(_eItem.extName);
							var item = {
								id: _eItem.id,
								name: _eItem.originalFileName,
								newName: _eItem.newFileName,
								url:
									fileInfo.type == "image"
										? appUrl() + "image/" + _eItem.newFileName
										: appUrl() + "file/preview/" + _eItem.id,
								fileInfo: fileInfo,
								dotSystemDel: _eItem.dotSystemDel
							};

							if (fileInfo.type != "image") {
								fileList.push(item);
							} else {
								imgList.push(item);
							}
						});
						this.data.imgList = imgList;
						this.data.fileList = fileList;
					}
				},
				id: function() {},
				name: function() {},
				newName: function() {},
				url: function() {},
				fileInfo: function() {},
				dotSystemDel: function() {}
			};
		}

		if (Component) YAttachments.__proto__ = Component;
		YAttachments.prototype = Object.create(Component && Component.prototype);
		YAttachments.prototype.constructor = YAttachments;
		YAttachments.prototype.openFile = function(e, _item) {
			stopBubble(e);
			var param = {};
			param.id = _item.id || _item.url;
			param.suffix = _item.fileInfo.type;
			param.fileSource = this.props.fileSource;
			openWin_filePreviewer(param);
		};
		YAttachments.prototype.openImages = function(e, _item, _index) {
			stopBubble(e);
			openWin_imgPreviewer({
				index: _index,
				imgs: this.data.imgList.map(function(obj) {
					return obj.url;
				})
			});
		};
		YAttachments.prototype.delFile = function(e, _item, _index) {
			stopBubble(e);
			delItemForKey(_item, this.props.data, "id");
			this.fire("change", this.props.data);
			if (!_item.dotSystemDel) {
				ajax(
					{u: appUrl() + "file/clear"},
					"clear" + _item.id,
					function(ret, err) {},
					"删除附件",
					"post",
					{
						body: JSON.stringify({fileIds: [_item.id]})
					}
				);
			}
		};
		YAttachments.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{s: this.monitor},
				(this.data.imgList.length > 0 || this.data.fileList.length > 0) &&
					apivm.h(
						"view",
						{style: this.props.style || ""},
						apivm.h(
							"view",
							null,
							this.data.imgList.length > 0 &&
								apivm.h(
									"view",
									{
										class: "attach_img_box",
										style:
											"margin-bottom:" +
											(this.data.fileList.length > 0 ? "1" : "") +
											"0px;"
									},
									(Array.isArray(this.data.imgList)
										? this.data.imgList
										: Object.values(this.data.imgList)
									).map(function(item$1, index$1) {
										return apivm.h(
											"view",
											{
												class: "attach_img_item",
												onClick: function(e) {
													return this$1.openImages(e, item$1, index$1);
												}
											},
											apivm.h("image", {
												style: "width:100%;" + loadConfigurationSize(44, "h"),
												src: showImg(item$1),
												mode: "aspectFill"
											}),
											this$1.props.type == "2" &&
												apivm.h(
													"view",
													{
														class: "attach_img_clean",
														onClick: function(e) {
															return this$1.delFile(e, item$1, index$1);
														}
													},
													apivm.h("a-iconfont", {
														name: "qingkong",
														color: "rgba(0,0,0,0.65)",
														size: G.appFontSize + 2
													})
												)
										);
									})
								)
						),
						apivm.h(
							"view",
							null,
							this.data.fileList.length > 0 &&
								apivm.h(
									"view",
									null,
									(Array.isArray(this.data.fileList)
										? this.data.fileList
										: Object.values(this.data.fileList)
									).map(function(item$1, index$1) {
										return apivm.h(
											"view",
											{
												class: "attach_item",
												style:
													"margin-top:" + (index$1 ? 1 : 0) + "0px;background:transparent;",
												onClick: function(e) {
													return this$1.openFile(e, item$1, index$1);
												}
											},
											apivm.h(
												"view",
												{style: "margin-right:10px;"},
												apivm.h("a-iconfont", {
													name: item$1.fileInfo.name,
													color: item$1.fileInfo.color,
													size: G.appFontSize + 6
												})
											),
											apivm.h(
												"view",
												{class: "flex_w"},
												apivm.h(
													"text",
													{
														class: "text_one",
														style: loadConfiguration() + "color: #666;word-break: break-all;"
													},
													item$1.name
												)
											),
											this$1.props.type == "2" &&
												apivm.h(
													"view",
													{
														style: "padding:5px;margin-right:-5px;",
														onClick: function(e) {
															return this$1.delFile(e, item$1, index$1);
														}
													},
													apivm.h("a-iconfont", {
														name: "qingkong",
														color: "#333",
														size: G.appFontSize + 4
													})
												)
										);
									})
								)
						)
					)
			);
		};

		return YAttachments;
	})(Component);
	YAttachments.css = {
		".attach_item": {
			borderRadius: "4px",
			border: "1px solid #F4F5F7",
			padding: "2px 10px",
			minHeight: "36px",
			flexDirection: "row",
			alignItems: "center"
		},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden",
			display: "inline"
		},
		".attach_img_box": {flexDirection: "row", flexWrap: "wrap"},
		".attach_img_item": {
			padding: "10px 10px 0 0",
			width: "25%",
			height: "auto",
			boxSizing: "border-box"
		},
		".attach_img_clean": {position: "absolute", top: "1px", right: "3px"}
	};
	apivm.define("y-attachments", YAttachments);

	var YCommentSendBig = /*@__PURE__*/ (function(Component) {
		function YCommentSendBig(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				isFocus: false,
				fileListPop: {
					//选择聊天 云盘文件
					show: false
				}
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;

					if (this.props.dataMore.bigShow != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							setTimeout(
								function() {
									this$1.baseInit();
								},
								platform() == "app" ? 50 : 0
							);
						}
					}
				}
			};
		}

		if (Component) YCommentSendBig.__proto__ = Component;
		YCommentSendBig.prototype = Object.create(Component && Component.prototype);
		YCommentSendBig.prototype.constructor = YCommentSendBig;
		YCommentSendBig.prototype.baseInit = function() {};
		YCommentSendBig.prototype.closePage = function() {
			var data = this.props.dataMore;
			data.bigShow = false;
			if (!data.value && !data.files.length) {
				data.replyId = "";
				data.replyPlaceholder = "";
			}
		};
		YCommentSendBig.prototype.penetrate = function() {};
		YCommentSendBig.prototype.textwarp = function() {
			$("#" + this.props.dataMore.textareaId).focus();
		};
		YCommentSendBig.prototype.focus = function(e) {
			this.data.isFocus = true;
		};
		YCommentSendBig.prototype.blur = function(e) {
			this.data.isFocus = false;
		};
		YCommentSendBig.prototype.send = function(_param) {
			var this$1 = this;

			var data = this.props.dataMore;
			var param = {
				form: {
					terminalName: "APP",
					businessCode: data.code,
					businessId: data.id,
					commentContent: data.value,
					attachmentIds: data.files
						.map(function(obj) {
							return obj.id;
						})
						.join(","),
					checkedStatus: data.checkedStatus,
					parentId: data.replyId
				}
			};

			if (_param && _param.isReplaceDirtyWord) {
				param.isReplaceDirtyWord = 1;
			}
			showProgress(data.placeholder + "中");
			ajax(
				{u: appUrl() + "comment/add"},
				"comment/add",
				function(ret, err) {
					hideProgress();
					if (ret && ret.data == "DirtyWordException") {
						alert(
							{
								title: "提示",
								msg: ret.message + "，是否继续提交？提交时会把敏感词替换成【*】",
								buttons: ["确定", "取消"]
							},
							function(ret) {
								if (ret.buttonIndex == 1) {
									this$1.send({isReplaceDirtyWord: 1});
								}
							}
						);
					} else {
						toast(
							ret
								? ret.code == 200
									? data.placeholder + "成功"
									: ret.message || ret.data
								: NET_ERR$1
						);
						if (ret && ret.code == 200) {
							data.value = "";
							data.files = [];
							this$1.closePage();
							this$1.fire("refresh");
						}
					}
				},
				"评论",
				"post",
				{
					body: JSON.stringify(param)
				}
			);
		};
		YCommentSendBig.prototype.chooseFile = function(_type) {
			var this$1 = this;

			var callback = function(ret) {
				if (ret.otherInfo) {
					this$1.props.dataMore.files.push(ret.otherInfo);
				} else {
					toast(ret.error);
				}
			};
			if (_type == 2) {
				//文件
				var buttons = ["我的云盘", "本机文件"];
				actionSheet(
					{
						title: "请选择",
						buttons: buttons
					},
					function(ret, err) {
						var _index = ret.buttonIndex;
						if (_index <= buttons.length) {
							switch (buttons[_index - 1]) {
								case "我的云盘":
									this$1.data.fileListPop = {
										key: "cloud_file",
										show: true,
										select: true,
										callback: function(ret) {
											this$1.fileCallback(ret.listSelect);
										}
									};

									break;
								case "本机文件":
									this$1.data.fileListPop.key = "local_file";
									chooseFile({showToast: true}, function(ret) {
										if (ret.otherInfo) {
											this$1.props.dataMore.files.push(ret.otherInfo);
										} else {
											toast(ret.error);
										}
									});
									break;
							}
						}
					}
				);
			} else {
				//相机相册
				getPicture(
					{sourceType: _type == 1 ? "library" : "camera", destinationType: "url"},
					function(ret, err) {
						var dataUrl = ret ? ret.data || ret.base64Data : "";
						if (dataUrl) {
							var _item = {showToast: true, url: dataUrl};
							uploadFile(_item, callback);
						}
					}
				);
			}
		};
		YCommentSendBig.prototype.fileCallback = function(_list) {
			var this$1 = this;

			_list.forEach(function(_eItem) {
				ajax(
					{u: appUrl() + "file/info/" + _eItem.fileInfoId},
					"info" + _eItem.fileInfoId,
					function(ret, err) {
						if (ret && ret.code == 200 && ret.data) {
							ret.data.dotSystemDel = true; //云盘增加的文件不能真删除
							this$1.props.dataMore.files.push(ret.data);
						}
					},
					"附件详情",
					"get"
				);
			});
		};
		YCommentSendBig.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					class: "page_box",
					style:
						"display:" +
						(this.props.dataMore.bigShow ? "flex" : "none") +
						";background:rgba(0,0,0,0.4);"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					class: "flex_h"
				}),
				this.props.dataMore.bigShow && [
					apivm.h(
						"view",
						{
							onClick: function() {
								return this$1.penetrate();
							},
							class: "commentBig_warp",
							style:
								"padding-bottom:" +
								((!this.data.isFocus && this.props.bottom ? footerBottom() : 0) + 16) +
								"px;"
						},
						apivm.h(
							"view",
							{class: "watermark_box"},
							G.watermark &&
								apivm.h("image", {
									class: "xy_100",
									src: G.watermark,
									mode: "aspectFill",
									thumbnail: "false"
								})
						),
						apivm.h(
							"view",
							{
								onClick: function() {
									return this$1.textwarp();
								},
								class: "commentBig_box"
							},
							apivm.h("z-textarea", {
								dataMore: this.props.dataMore,
								onInput: this.focus,
								onFocus: this.focus,
								onBlur: this.blur
							})
						),
						apivm.h("y-attachments", {
							style: "padding: 10px 0;",
							type: "2",
							data: this.props.dataMore.files
						}),
						apivm.h(
							"view",
							{class: "flex_row"},
							apivm.h(
								"view",
								null,
								platform() != "web" &&
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.chooseFile(0);
											},
											style: "margin-right:20px;"
										},
										apivm.h("a-iconfont", {
											name: "shurupaizhao",
											color: "#333",
											size: G.appFontSize + 12
										})
									)
							),
							apivm.h(
								"view",
								null,
								G.userId &&
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.chooseFile(1);
											},
											style: "margin-right:20px;"
										},
										apivm.h("a-iconfont", {
											name: "shuruxiangce",
											color: "#333",
											size: G.appFontSize + 12
										})
									)
							),
							apivm.h(
								"view",
								null,
								G.userId &&
									apivm.h(
										"view",
										{
											onClick: function() {
												return this$1.chooseFile(2);
											}
										},
										apivm.h("a-iconfont", {
											name: "shuruwenjian",
											color: "#333",
											size: G.appFontSize + 12
										})
									)
							),
							apivm.h("view", {style: "flex:1;"}),
							apivm.h(
								"text",
								{style: loadConfiguration(-2) + "color: #999;padding-top: 8px;"},
								this.props.dataMore.value.length,
								"/",
								this.props.dataMore.maxlength || 300
							),
							apivm.h("z-button", {
								onClick: function() {
									return this$1.send();
								},
								disabled:
									!this.props.dataMore.value &&
									(this.props.dataMore.files || []).length <= 0,
								text: this.props.dataMore.sendtext || "发送",
								style: "padding:3px 12px;margin-left:20px;",
								color: G.appTheme
							})
						)
					),
					apivm.h("file-list", {dataMore: this.data.fileListPop})
				]
			);
		};

		return YCommentSendBig;
	})(Component);
	YCommentSendBig.css = {
		".commentBig_warp": {background: "#FFF", width: "100%", padding: "16px"},
		".commentBig_box": {
			minHeight: "110px",
			maxHeight: "250px",
			marginBottom: "10px"
		}
	};
	apivm.define("y-comment-send-big", YCommentSendBig);

	var YCommentSend = /*@__PURE__*/ (function(Component) {
		function YCommentSend(props) {
			Component.call(this, props);
		}

		if (Component) YCommentSend.__proto__ = Component;
		YCommentSend.prototype = Object.create(Component && Component.prototype);
		YCommentSend.prototype.constructor = YCommentSend;
		YCommentSend.prototype.hintclick = function() {
			if (this.props.dataMore.disabledHint) {
				toast(this.props.dataMore.disabledHint);
				return;
			}
			this.props.dataMore.bigShow = true;
		};
		YCommentSend.prototype.cckComment = function() {
			this.fire("comment");
		};
		YCommentSend.prototype.cckLike = function() {
			if (this.props.dataMore.likeIs) {
				if (this.props.dataMore.likeNum > 0) {
					this.props.dataMore.likeNum--;
				}
			} else {
				this.props.dataMore.likeNum++;
			}
			this.props.dataMore.likeIs = !this.props.dataMore.likeIs;
			optionPraises(this.props.dataMore);
			this.fire("like");
		};
		YCommentSend.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					class: "commentSend_box",
					style:
						"display:" +
						(this.props.dataMore.sendShow && !G.isAppReview ? "flex" : "none") +
						";padding-bottom:" +
						((this.props.bottom ? footerBottom() : 0) + 6) +
						"px;"
				},
				this.props.dataMore.sendShow &&
					!G.isAppReview && [
						apivm.h(
							"view",
							{onClick: this.hintclick, class: "flex_w flex_row commentSend_hint_box"},
							apivm.h(
								"text",
								{
									style:
										loadConfiguration() +
										"color:" +
										(this.props.dataMore.value ? "#333" : "#999") +
										";",
									class: "text_one"
								},
								this.props.dataMore.value ||
									this.props.dataMore.replyPlaceholder ||
									this.props.dataMore.placeholder
							)
						)
					],
				(isNumber(this.props.dataMore.commentNum) ||
					isNumber(this.props.dataMore.likeNum)) &&
					apivm.h(
						"view",
						{class: "commentSend_btn_box"},
						isNumber(this.props.dataMore.commentNum) &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.cckComment();
									},
									class: "commentSend_btn_item"
								},
								apivm.h(
									"text",
									{style: loadConfiguration(-2) + "color:#666666;margin-right:5px;"},
									this.props.dataMore.commentNum
								),
								apivm.h("a-iconfont", {
									name: "pinglun1",
									color: "#666666",
									size: G.appFontSize + 4
								})
							),
						isNumber(this.props.dataMore.likeNum) &&
							apivm.h(
								"view",
								{
									onClick: function() {
										return this$1.cckLike();
									},
									class: "commentSend_btn_item"
								},
								apivm.h(
									"text",
									{
										style:
											loadConfiguration(-2) +
											"color:" +
											(this.props.dataMore.likeIs ? "#F6931C" : "#666666") +
											";margin-right:5px;"
									},
									this.props.dataMore.likeNum
								),
								apivm.h("a-iconfont", {
									name: this.props.dataMore.likeIs ? "dianzan" : "dianzan",
									color: this.props.dataMore.likeIs ? "#F6931C" : "#666666",
									size: G.appFontSize + 4
								})
							)
					)
			);
		};

		return YCommentSend;
	})(Component);
	YCommentSend.css = {
		".commentSend_box": {
			width: "100%",
			minHeight: "44px",
			flexDirection: "row",
			alignItems: "center",
			borderTop: "1px solid rgba(153,153,153,0.2)",
			padding: "6px 16px"
		},
		".commentSend_hint_box": {
			height: "32px",
			padding: "0px 8px",
			backgroundColor: "rgba(0, 0, 0, 0.05)",
			borderRadius: "2px"
		},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden",
			display: "inline"
		},
		".commentSend_btn_box": {
			marginLeft: "10px",
			flexDirection: "row",
			alignItems: "center",
			marginRight: "-10px"
		},
		".commentSend_btn_item": {
			padding: "5px 10px 5px 10px",
			flexDirection: "row",
			alignItems: "center"
		}
	};
	apivm.define("y-comment-send", YCommentSend);

	var YComment = /*@__PURE__*/ (function(Component) {
		function YComment(props) {
			Component.call(this, props);
			this.data = {};
		}

		if (Component) YComment.__proto__ = Component;
		YComment.prototype = Object.create(Component && Component.prototype);
		YComment.prototype.constructor = YComment;
		YComment.prototype.installed = function() {};
		YComment.prototype.hasExpand = function() {
			return getItemForKey(this.props.baseid, this.props.dataMore.listExpand);
		};
		YComment.prototype.openExpand = function(e) {
			if (getItemForKey(this.props.baseid, this.props.dataMore.listExpand)) {
				delItemForKey(this.props.baseid, this.props.dataMore.listExpand);
			} else {
				this.props.dataMore.listExpand.push(this.props.baseid);
			}
			this.props.scroll.scroll_view = "comment_" + this.props.baseid;
			stopBubble(e);
		};
		YComment.prototype.cckLike = function(e, _item) {
			if (_item.likeIs) {
				if (_item.likeNum > 0) {
					_item.likeNum--;
				}
			} else {
				_item.likeNum++;
			}
			_item.likeIs = !_item.likeIs;
			optionPraises({likeIs: _item.likeIs, code: "comment", id: _item.id});
			stopBubble(e);
		};
		YComment.prototype.cckReply = function(e, _item) {
			var this$1 = this;

			if (this.props.dataMore.disabledHint) {
				toast(this.props.dataMore.disabledHint);
				return;
			}
			if (_item.createBy == G.userId || _item.createBy == G.uId) {
				ajaxAlert(
					{
						msg: "确定删除所选的评论吗？",
						url: appUrl() + "comment/dels",
						param: {ids: [_item.id]},
						toast: "删除中"
					},
					function(ret) {
						if (ret && ret.code == 200) {
							setTimeout(function() {
								this$1.refresh();
							}, 300);
						}
					}
				);
			} else {
				this.props.dataMore.value = "";
				this.props.dataMore.files = [];
				this.props.dataMore.replyId = _item.id;
				this.props.dataMore.replyPlaceholder = "回复" + _item.name;
				this.props.dataMore.bigShow = true;
			}
			stopBubble(e);
		};
		YComment.prototype.refresh = function() {
			this.fire("refresh");
		};
		YComment.prototype.getTagColor = function(_item) {
			if (_item == "我" || _item == "我的单位" || _item == "单位") {
				return G.appTheme;
			} else if (_item == "群众") {
				return "#666666";
			} else if (_item == "普通用户") {
				return "#95d475";
			} else if (_item == "人大代表" || _item == "政协委员") {
				return "#e6a23c";
			}
			return "#F6931C";
		};
		YComment.prototype.itemClick = function(e, _item, _index) {
			var this$1 = this;

			actionSheet(
				{
					title: "提示",
					buttons: [
						"复制",
						_item.createBy == G.userId || _item.createBy == G.uId ? "删除" : "回复"
					]
				},
				function(ret) {
					switch (ret.name) {
						case "复制":
							copyText(_item.content, function(ret, err) {
								toast(ret ? "复制成功" : "复制失败");
							});
							break;
						case "删除":
						case "回复":
							this$1.cckReply(e, _item);
							break;
					}
				}
			);
			stopBubble(e);
		};
		YComment.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{id: this.props.id || ""},
				this.props.dataMore.listShow &&
					apivm.h(
						"view",
						{
							style:
								"" +
								(this.props.boxstyle ||
									"margin-top:10px;border-top: 10px solid #F8F8F8;")
						},
						apivm.h(
							"view",
							null,
							!this.props.ischildren &&
								!this.props.dataMore.dotHint &&
								apivm.h(
									"view",
									{class: "comment_hint_box flex_row"},
									apivm.h("view", {
										class: "comment_hint_line",
										style:
											loadConfigurationSize(-1, "h") + "background:" + G.appTheme + ";"
									}),
									apivm.h(
										"text",
										{class: "comment_hint_text", style: "" + loadConfiguration(2)},
										this.props.dataMore.listHint || "全部评论",
										this.props.dataMore.total ? "(" + this.props.dataMore.total + ")" : ""
									)
								)
						),
						apivm.h(
							"view",
							null,
							(Array.isArray(this.props.data)
								? this.props.data
								: Object.values(this.props.data)
							).map(function(item$1, index$1) {
								return apivm.h(
									"view",
									null,
									apivm.h(
										"view",
										{
											id: "comment_" + item$1.id,
											style:
												"display:" +
												(!this$1.props.ischildren || this$1.hasExpand(item$1) || index$1 < 1
													? "flex"
													: "none") +
												";padding: 0 " +
												(this$1.props.ischildren ? "10" : "16") +
												"px;"
										},
										apivm.h(
											"view",
											{
												class: "comment_item_warp",
												style:
													"padding:" +
													(this$1.props.ischildren ? "10" : "15") +
													"px 0;border-bottom: " +
													(this$1.props.ischildren ? "0" : "1") +
													"px solid rgba(153,153,153,0.2);",
												onClick: function(e) {
													return this$1.itemClick(e, item$1, index$1);
												}
											},
											apivm.h(
												"view",
												{style: loadConfigurationSize(14) + "margin-right:7px;"},
												apivm.h("z-avatar", {src: showImg(item$1)})
											),
											apivm.h(
												"view",
												{class: "flex_w"},
												apivm.h(
													"view",
													{class: "comment_item_top"},
													apivm.h(
														"view",
														{
															style:
																"flex:1;width:1px;flex-direction:row; align-items: flex-start;flex-wrap: wrap;"
														},
														apivm.h(
															"text",
															{class: "comment_item_name", style: "" + loadConfiguration()},
															item$1.name
														),
														apivm.h(
															"view",
															{class: "flex_row"},
															item$1.replyName && [
																apivm.h("a-iconfont", {
																	style: "margin:0 10px 10px 0;transform: rotate(180deg);",
																	name: "xiangzuo",
																	color: "#999999",
																	size: G.appFontSize - 6
																}),
																apivm.h(
																	"text",
																	{class: "comment_item_name", style: "" + loadConfiguration()},
																	item$1.replyName
																)
															]
														),
														!item$1.replyName &&
															item$1.userRoles.map(function(nItem) {
																return [
																	apivm.h(
																		"view",
																		{style: "margin:0 10px 10px 0;"},
																		apivm.h("z-tag", {color: this$1.getTagColor(nItem)}, nItem)
																	)
																];
															}),
														!item$1.replyName &&
															item$1.userArea &&
															apivm.h(
																"view",
																{style: "margin:0 10px 10px 0;"},
																apivm.h(
																	"z-tag",
																	{color: "#666"},
																	item$1.userArea.length > 8
																		? item$1.userArea.substring(0, 8) + "..."
																		: item$1.userArea
																)
															)
													),
													apivm.h(
														"view",
														{class: "flex_row"},
														item$1.checkedStatus != 1 &&
															apivm.h(
																"text",
																{
																	style:
																		loadConfiguration(-2) +
																		"color:" +
																		(item$1.checkedStatus != 2 ? "#e6a23c" : "#999") +
																		";"
																},
																item$1.checkedStatus != 2 ? "审核中" : "未通过"
															)
													),
													apivm.h(
														"view",
														null,
														item$1.checkedStatus == 1 &&
															!this$1.props.ischildren &&
															!this$1.props.dataMore.dotOption &&
															apivm.h(
																"view",
																{
																	class: "flex_row",
																	onClick: function(e) {
																		return this$1.cckLike(e, item$1);
																	}
																},
																apivm.h(
																	"text",
																	{
																		style:
																			loadConfiguration(-2) +
																			"color:" +
																			(item$1.likeIs ? "#F6931C" : "#666666") +
																			";margin-right:5px;"
																	},
																	item$1.likeNum
																),
																apivm.h("a-iconfont", {
																	name: item$1.likeIs ? "like-fill" : "like",
																	color: item$1.likeIs ? "#F6931C" : "#666666",
																	size: G.appFontSize + 3
																})
															)
													)
												),
												apivm.h(
													"view",
													{style: "margin-top: -5px;"},
													apivm.h("z-rich-text", {expand: 80, nodes: item$1.content})
												),
												apivm.h("y-attachments", {data: item$1.attachments}),
												apivm.h(
													"view",
													{class: "comment_item_add"},
													apivm.h(
														"text",
														{style: loadConfiguration(-2) + "color:#999;"},
														dayjs(item$1.time).format("YYYY-MM-DD HH:mm")
													),
													!this$1.props.dataMore.dotOption &&
														apivm.h(
															"text",
															{
																style: loadConfiguration(-2) + "margin-left:20px;color:#333;",
																onClick: function(e) {
																	return this$1.cckReply(e, item$1);
																}
															},
															item$1.createBy == G.userId || item$1.createBy == G.uId
																? "删除"
																: "回复"
														)
												),
												apivm.h(
													"view",
													null,
													item$1.replyList.length > 0 &&
														apivm.h("y-comment", {
															baseid: item$1.id,
															scroll: this$1.props.scroll,
															dataMore: this$1.props.dataMore,
															data: item$1.replyList,
															ischildren: true,
															onRefresh: this$1.refresh,
															boxstyle: "background:rgba(0,0,0,0.05);margin-top:10px;"
														})
												),
												apivm.h(
													"view",
													null,
													this$1.props.ischildren &&
														this$1.props.data.length > 1 &&
														(this$1.hasExpand(item$1)
															? index$1 == this$1.props.data.length - 1
															: true) &&
														apivm.h(
															"view",
															{
																style:
																	"flex-direction:row; align-items: center;margin-top:10px;",
																onClick: function(e) {
																	return this$1.openExpand(e);
																}
															},
															apivm.h(
																"text",
																{style: loadConfiguration(-2) + "color:#666;margin-right:5px;"},
																"—— ",
																this$1.hasExpand(item$1)
																	? "收起"
																	: "展开" + (this$1.props.data.length - 1) + "条回复"
															),
															apivm.h("a-iconfont", {
																style:
																	"transform: rotate(" +
																	(this$1.hasExpand(item$1) ? "180" : "0") +
																	"deg);",
																name: "xiangxia1",
																color: "#666666",
																size: G.appFontSize + 3
															})
														)
												)
											)
										)
									)
								);
							})
						)
					)
			);
		};

		return YComment;
	})(Component);
	YComment.css = {
		".comment_hint_box": {padding: "15px 15px 0"},
		".comment_hint_line": {width: "3px", borderRadius: "10px"},
		".comment_hint_text": {
			color: "#333333",
			fontWeight: "600",
			marginLeft: "5px",
			flex: "1"
		},
		".comment_item_warp": {flexDirection: "row"},
		".comment_item_top": {flexDirection: "row", alignItems: "flex-start"},
		".comment_item_name": {
			fontWeight: "600",
			color: "#333333",
			margin: "0 10px 10px 0"
		},
		".comment_item_add": {
			flexDirection: "row",
			alignItems: "center",
			marginTop: "10px"
		}
	};
	apivm.define("y-comment", YComment);

	var baoguo_hezi_o = "";
	var fuzhilianjiexian = "";
	var fuzhilianjiemian = "";
	var wenjian = "";
	var tianjia = "";
	var tianjiawenjian = "";
	var sousuowenjian = "";
	var wenjianshangchuan = "";
	var dalou = "";
	var wucan = "";
	var zengjia1 = "";
	var xinsui = "";
	var aixin = "";
	var jianshao2 = "";
	var yishoucang = "";
	var yuyinshibie = "";
	var shuruxiangce = "";
	var tupianshibie = "";
	var shuruwenjian = "";
	var shurupaizhao = "";
	var tongji4 = "";
	var pinglun1 = "";
	var gerentongxunlu = "";
	var fenxiang2 = "";
	var dianzan = "";
	var shoucang1 = "";
	var sousuo2 = "";
	var jiancexinbanben = "";
	var shezhi1 = "";
	var guanhuaimoshi = "";
	var xiugaimima = "";
	var anquantuichu = "";
	var dianhua = "";
	var shipintianchong = "";
	var xiangxiagengduo = "";
	var mubiao = "";
	var jianshao1 = "";
	var biaoqian = "";
	var duigou = "";
	var jinengbiaoqian = "";
	var jiantou_xiangyouliangci = "";
	var toupiao = "";
	var riqi = "";
	var gonggao = "";
	var tongji3 = "";
	var paihang = "";
	var mn_paiming_fill = "";
	var shaixuan = "";
	var envelope = "";
	var nvshangjia = "";
	var nan = "";
	var tongji1 = "";
	var tuceng = "";
	var tongji2 = "";
	var biaoqianlan_shouye = "";
	var shengyinjingyin = "";
	var shengyin = "";
	var shengyin1 = "";
	var tianxie = "";
	var erweima = "";
	var huanyuan = "";
	var lianjie = "";
	var changyonglogo28 = "";
	var pengyouquan = "";
	var zhixiangxia = "";
	var QQ = "";
	var quanxian = "";
	var baocun = "";
	var suoyouwenjian = "";
	var xiangxia1 = "";
	var xinzeng = "";
	var ziyuanxhdpi = "";
	var xiazai = "";
	var zhongmingming = "";
	var yunpan = "";
	var tongji = "";
	var kefu = "";
	var weibiaoti1 = "";
	var tongxunlu = "";
	var xinyongqia = "";
	var tuichu = "";
	var aixinxiantiao = "";
	var shezhi = "";
	var jianchaxinbanben = "";
	var yuyin = "";
	var shanchu = "";
	var remen = "";
	var tupian = "";
	var xiangji = "";
	var zan = "";
	var zan1 = "";
	var like = "";
	var unlike = "";
	var pinglun = "";
	var jushoucang = "";
	var jushoucanggift = "";
	var share = "";
	var shoucangfill = "";
	var fenxiang1 = "";
	var shoucang = "";
	var bofang = "";
	var daojishi = "";
	var cuohao = "";
	var duihao = "";
	var chakan = "";
	var shoujihaoma = "";
	var mima1 = "";
	var zhanghao1 = "";
	var yanzhengma1 = "";
	var kejian = "";
	var bukejian = "";
	var gengduo1 = "";
	var gengduogongneng = "";
	var gengduo2 = "";
	var gengduo3 = "";
	var fanhui1 = "";
	var fanhui2 = "";
	var sousuo = "";
	var sousuo1 = "";
	var saoyisao1 = "";
	var xiangxia = "";
	var xiangzuo = "";
	var fangxingweixuanzhong = "";
	var fangxingxuanzhongfill = "";
	var fangxingxuanzhong = "";
	var yuanxingweixuanzhong = "";
	var yuanxingxuanzhong = "";
	var yuanxingxuanzhongfill = "";
	var danxuan_xuanzhong = "";
	var danxuan_weixuanzhong = "";
	var gengduo = "";
	var fenxiang = "";
	var zhuanfa00 = "";
	var dingwei = "";
	var dingwei1 = "";
	var jianshao = "";
	var zengjia = "";
	var qingkong = "";
	var mima = "";
	var zhanghao = "";
	var yanzhengma = "";
	var icons = {
		baoguo_hezi_o: baoguo_hezi_o,
		fuzhilianjiexian: fuzhilianjiexian,
		fuzhilianjiemian: fuzhilianjiemian,
		wenjian: wenjian,
		tianjia: tianjia,
		tianjiawenjian: tianjiawenjian,
		sousuowenjian: sousuowenjian,
		wenjianshangchuan: wenjianshangchuan,
		"zuanshi-L": "",
		dalou: dalou,
		wucan: wucan,
		zengjia1: zengjia1,
		xinsui: xinsui,
		aixin: aixin,
		jianshao2: jianshao2,
		yishoucang: yishoucang,
		yuyinshibie: yuyinshibie,
		shuruxiangce: shuruxiangce,
		tupianshibie: tupianshibie,
		shuruwenjian: shuruwenjian,
		shurupaizhao: shurupaizhao,
		tongji4: tongji4,
		pinglun1: pinglun1,
		gerentongxunlu: gerentongxunlu,
		fenxiang2: fenxiang2,
		dianzan: dianzan,
		shoucang1: shoucang1,
		sousuo2: sousuo2,
		jiancexinbanben: jiancexinbanben,
		shezhi1: shezhi1,
		guanhuaimoshi: guanhuaimoshi,
		xiugaimima: xiugaimima,
		anquantuichu: anquantuichu,
		dianhua: dianhua,
		shipintianchong: shipintianchong,
		"31dianhua": "",
		xiangxiagengduo: xiangxiagengduo,
		mubiao: mubiao,
		jianshao1: jianshao1,
		biaoqian: biaoqian,
		"gantanhao-xianxingyuankuang": "",
		duigou: duigou,
		jinengbiaoqian: jinengbiaoqian,
		jiantou_xiangyouliangci: jiantou_xiangyouliangci,
		toupiao: toupiao,
		riqi: riqi,
		gonggao: gonggao,
		tongji3: tongji3,
		paihang: paihang,
		mn_paiming_fill: mn_paiming_fill,
		"line-084": "",
		"line-085": "",
		shaixuan: shaixuan,
		envelope: envelope,
		"envelope-open": "",
		"a-4_huaban1": "",
		nvshangjia: nvshangjia,
		nan: nan,
		tongji1: tongji1,
		"weibiaoti--": "",
		tuceng: tuceng,
		tongji2: tongji2,
		biaoqianlan_shouye: biaoqianlan_shouye,
		shengyinjingyin: shengyinjingyin,
		shengyin: shengyin,
		shengyin1: shengyin1,
		tianxie: tianxie,
		"file-download-fill": "",
		"file-damage-fill": "",
		"file-excel-fill": "",
		"file-copy-fill": "",
		"file-edit-fill": "",
		"file-music-fill": "",
		"file-gif-fill": "",
		"file-history-fill": "",
		"file-lock-fill": "",
		"file-info-fill": "",
		"file-pdf-fill": "",
		"file-text-fill": "",
		"file-ppt-fill": "",
		"file-upload-fill": "",
		"file-unknow-fill": "",
		"file-word-fill": "",
		"file-search-fill": "",
		"file-transfer-fill": "",
		"file-zip-fill": "",
		"folder-2-fill": "",
		"file-warning-fill": "",
		"folder-add-fill": "",
		erweima: erweima,
		"file-reduce-fill": "",
		huanyuan: huanyuan,
		lianjie: lianjie,
		changyonglogo28: changyonglogo28,
		pengyouquan: pengyouquan,
		zhixiangxia: zhixiangxia,
		QQ: QQ,
		quanxian: quanxian,
		baocun: baocun,
		suoyouwenjian: suoyouwenjian,
		xiangxia1: xiangxia1,
		"wj-gxwj": "",
		xinzeng: xinzeng,
		ziyuanxhdpi: ziyuanxhdpi,
		xiazai: xiazai,
		zhongmingming: zhongmingming,
		yunpan: yunpan,
		"file-code-fill": "",
		"file-add-fill": "",
		tongji: tongji,
		kefu: kefu,
		weibiaoti1: weibiaoti1,
		tongxunlu: tongxunlu,
		xinyongqia: xinyongqia,
		tuichu: tuichu,
		aixinxiantiao: aixinxiantiao,
		shezhi: shezhi,
		jianchaxinbanben: jianchaxinbanben,
		yuyin: yuyin,
		shanchu: shanchu,
		remen: remen,
		tupian: tupian,
		xiangji: xiangji,
		zan: zan,
		zan1: zan1,
		like: like,
		unlike: unlike,
		"like-fill": "",
		"unlike-fill": "",
		pinglun: pinglun,
		jushoucang: jushoucang,
		jushoucanggift: jushoucanggift,
		share: share,
		shoucangfill: shoucangfill,
		fenxiang1: fenxiang1,
		shoucang: shoucang,
		bofang: bofang,
		"bell-off": "",
		daojishi: daojishi,
		cuohao: cuohao,
		duihao: duihao,
		chakan: chakan,
		shoujihaoma: shoujihaoma,
		mima1: mima1,
		zhanghao1: zhanghao1,
		yanzhengma1: yanzhengma1,
		kejian: kejian,
		bukejian: bukejian,
		gengduo1: gengduo1,
		gengduogongneng: gengduogongneng,
		gengduo2: gengduo2,
		gengduo3: gengduo3,
		"a-14Bshanchu": "",
		fanhui1: fanhui1,
		fanhui2: fanhui2,
		sousuo: sousuo,
		sousuo1: sousuo1,
		saoyisao1: saoyisao1,
		xiangxia: xiangxia,
		xiangzuo: xiangzuo,
		fangxingweixuanzhong: fangxingweixuanzhong,
		fangxingxuanzhongfill: fangxingxuanzhongfill,
		fangxingxuanzhong: fangxingxuanzhong,
		yuanxingweixuanzhong: yuanxingweixuanzhong,
		yuanxingxuanzhong: yuanxingxuanzhong,
		yuanxingxuanzhongfill: yuanxingxuanzhongfill,
		danxuan_xuanzhong: danxuan_xuanzhong,
		danxuan_weixuanzhong: danxuan_weixuanzhong,
		gengduo: gengduo,
		fenxiang: fenxiang,
		zhuanfa00: zhuanfa00,
		dingwei: dingwei,
		dingwei1: dingwei1,
		jianshao: jianshao,
		zengjia: zengjia,
		qingkong: qingkong,
		mima: mima,
		zhanghao: zhanghao,
		yanzhengma: yanzhengma
	};

	var AMpcss = /*@__PURE__*/ (function(Component) {
		function AMpcss(props) {
			Component.call(this, props);
		}

		if (Component) AMpcss.__proto__ = Component;
		AMpcss.prototype = Object.create(Component && Component.prototype);
		AMpcss.prototype.constructor = AMpcss;
		AMpcss.prototype.render = function() {
			return;
		};

		return AMpcss;
	})(Component);
	AMpcss.css = {
		"@font-face": {
			fontFamily: '"iconfont_mp"',
			src:
				"url('https://at.alicdn.com/t/c/font_3560231_mff0m4knr.ttf') format('truetype')"
		}
	};

	apivm.define("a-mpcss", AMpcss);

	var AIconfont = /*@__PURE__*/ (function(Component) {
		function AIconfont(props) {
			Component.call(this, props);
		}

		if (Component) AIconfont.__proto__ = Component;
		AIconfont.prototype = Object.create(Component && Component.prototype);
		AIconfont.prototype.constructor = AIconfont;
		AIconfont.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"text",
					{
						style:
							"font-size:" +
							(this.props.size || 16) +
							"px;color:" +
							(this.props.color || "#999") +
							";\n\t\tfont-family: " +
							(api.platform == "mp" ? "iconfont_mp" : "iconfont;") +
							";\n\t\t" +
							(this.props.style || "") +
							";font-weight:400;",
						class: "" + (this.props.class || "")
					},
					icons[this.props.name + ""]
				),
				api.platform == "mp" && apivm.h("a-mpcss", null)
			);
		};

		return AIconfont;
	})(Component);
	AIconfont.css = {
		"@font-face": {
			fontFamily: '"iconfont"',
			src:
				"url('../../components/act/a-iconfont/fonts/iconfont.ttf') format('truetype')"
		}
	};
	apivm.define("a-iconfont", AIconfont);

	var YBasePage = /*@__PURE__*/ (function(Component) {
		function YBasePage(props) {
			Component.call(this, props);
			this.data = {
				show: false, //为组件时显示隐藏
				initFrist: true, //首次初始化
				title: "" //标题栏
			};
			this.compute = {
				monitor: function() {
					var this$1 = this;
					if (!this.props.dataMore) {
						if (this.headTitle != G.headTitle) {
							this.headTitle = G.headTitle;
							this.setHeader();
						}
						if (this.headTheme != (G.showHeadTheme || G.headTheme)) {
							this.headTheme = G.showHeadTheme || G.headTheme;
							this.setHeadTheme();
						}
					} else {
						if (this.props.dataMore.show != this.data.show) {
							this.data.show = this.props.dataMore.show;
							videoPlayRemoves();
							if (this.data.show) {
								if (this.data.initFrist) {
									setTimeout(function() {
										this$1.baseInit();
									}, 110);
								} else {
									this.pageRefresh();
								}
							} else {
								if (this.props._this && isFunction(this.props._this.baseClose)) {
									this.props._this.baseClose({detail: {}});
								} else {
									this.fire("baseClose");
								}
							}
						}
					}
					if (
						(!this.props.dataMore || this.data.show) &&
						G.onShowNum != this.onShowNum
					) {
						this.onShowNum = G.onShowNum;
						if (this.isShow && this.onShowNum > 0) {
							this.pageRefresh();
							if (!this.props.dataMore) {
								console.log(
									(api.winName || "") +
										"第" +
										G.onShowNum +
										"次返回：" +
										JSON.stringify(this.props._this.data.pageParam)
								);
							}
						}
						this.isShow = true;
					}
				},
				detail: function() {}
			};
		}

		if (Component) YBasePage.__proto__ = Component;
		YBasePage.prototype = Object.create(Component && Component.prototype);
		YBasePage.prototype.constructor = YBasePage;
		YBasePage.prototype.installed = function() {
			var this$1 = this;
			var that = this.props._this;
			that.data.pageParam = pageParam(that);
			that.data.pageType = that.data.pageParam.pageType || "page";
			clearTimeout(that.data.oneTask);
			that.data.oneTask = setTimeout(function() {
				console.log("当前页面参数：" + JSON.stringify(that.data.pageParam));
				G.chatInfos = [];
				G.chatInfoTask = [];
				setTimeout(function() {
					if (!this$1.props.dataMore) {
						if (!G.headTitle) {
							G.headTitle = that.data.pageParam.title || that.data.dTitle || "";
						}
					} else {
						this$1.data.title = that.data.pageParam.title || that.data.dTitle || "";
					}
				}, 400);
				if (!this$1.props.dataMore) {
					G._this = that;
					if (that.data.pageParam.token) {
						setPrefs("sys_token", decodeURIComponent(that.data.pageParam.token));
						if (!getPrefs("sys_Mobile")) {
							getLoginInfo({header: {"u-login-areaId": ""}}, function(ret, err) {});
						}
					}
					if (that.data.pageParam.areaId) {
						setPrefs("sys_aresId", that.data.pageParam.areaId);
					}
					addEventListener("sys_refresh", function(ret) {
						this$1.initConfiguration();
					});
					var traverseList = function(_obj, _option) {
						for (var key in _obj) {
							var item = _obj[key];
							if (
								isObject(item) &&
								!isArray(item) &&
								key.endsWith("Pop") &&
								isParameters(item.show) &&
								item.show
							) {
								if (_option) {
									item.show = false;
								}
								return false;
							}
						}
						return true;
					};
					addEventListener("keyback", function(ret, err) {
						if (G.alertPop.show) {
							if (G.alertPop.cancel.show) {
								G.alertPop.show = false;
							}
							return;
						}
						if (!traverseList(G, true) || !traverseList(that.data, true)) {
							return;
						}
						this$1.close();
					});
					addEventListener("swiperight", function(ret) {
						if (G.nTouchmove) {
							return;
						}
						if (!traverseList(G, false) || !traverseList(that.data, false)) {
							return;
						}
						this$1.close(1);
					});
					this$1.initConfiguration();
					this$1.baseInit();
				}
			}, 50);
		};
		YBasePage.prototype.baseInit = function() {
			var this$1 = this;

			setTimeout(
				function() {
					var detail = {first: this$1.data.initFrist};
					if (this$1.props._this && isFunction(this$1.props._this.baseInit)) {
						this$1.props._this.baseInit({detail: detail});
					} else {
						this$1.fire("init", detail);
					}
					this$1.data.initFrist = false;
				},
				!this.props.dataMore ? 300 : 0
			);
		};
		YBasePage.prototype.close = function(_type) {
			if (this.props._this && isFunction(this.props._this.close)) {
				this.props._this.close({detail: {type: _type}});
			} else {
				this.fire("close", {type: _type});
			}
		};
		YBasePage.prototype.cTitle = function() {
			this.fire("cTitle");
		};
		YBasePage.prototype.pageRefresh = function(_frist) {
			if (!this.props.dataMore) {
				this.setHeadTheme();
			}
			if (!_frist) {
				if (this.props._this && isFunction(this.props._this.pageRefresh)) {
					this.props._this.pageRefresh({detail: {back: true}});
				} else {
					this.fire("pageRefresh", {back: true});
				}
			}
		};
		YBasePage.prototype.initConfiguration = function() {
			G.pageWidth =
				platform() == "web"
					? api.winWidth > api.winHeight
						? 600
						: api.winWidth
					: api.winWidth;
			G.showCaptcha = getPrefs("enableShortAuth") == "true";
			G.appName = getPrefs("sys_systemName") || "";
			G.terminal = getPrefs("terminal") || "";
			G.appFont = getPrefs("appFont") || "heitiSimplified";
			G.appFontSize = Number(getPrefs("appFontSize") || "16");
			G.sysSign = sysSign();
			G.appTheme =
				this.props._this.data.pageParam.appTheme ||
				getPrefs("appTheme" + G.sysSign) ||
				(G.sysSign == "rd" ? "#C61414" : "#3088FE");
			var headTheme =
				this.props._this.data.headTheme ||
				this.props._this.data.pageParam.headTheme ||
				getPrefs("headTheme") ||
				"transparent";
			G.headTheme = headTheme == "appTheme" ? G.appTheme : headTheme;
			G.careMode = parseInt(G.appFontSize) > 16;
			G.systemtTypeIsPlatform = getPrefs("sys_systemType") == "platform"; //系统类型是否是平台版
			G.loginInfo = getPrefs("sys_systemLoginContact") || "";
			if (platform() == "app") {
				G.isAppReview =
					JSON.parse(getPrefs("sys_appReviewVersion") || "{}")[api.systemType] ==
					api.appVersion;
			}
			G.v = getPrefs("sys_appVersion") || "";

			G.uId = getPrefs("sys_Id") || "";
			G.userId = getPrefs("sys_UserID") || "";
			G.userName = getPrefs("sys_UserName") || "";
			G.userImg = getPrefs("sys_AppPhoto") || "";
			G.areaId = getPrefs("sys_aresId") || "";
			G.specialRoleKeys = JSON.parse(getPrefs("sys_SpecialRoleKeys") || "[]");
			G.isAdmin =
				G.userId == "1" ||
				getItemForKey("dc_admin", G.specialRoleKeys) ||
				getItemForKey("admin", G.specialRoleKeys);
			G.grayscale = getPrefs("sys_grayscale") || "";
			var watermark = getPrefs("sys_watermark") || "";
			if (platform() == "app") {
				api.screenLayerFilter({
					region: "window",
					sat: G.grayscale == "true" ? 0 : 1
				});
			}
			if (watermark && watermark != "false") {
				var t = watermark
					.replace("user", G.userName)
					.replace("phone", getPrefs("sys_Mobile") || "")
					.replace("true", G.appName);
				if (t) {
					G.watermark =
						tomcatAddress() +
						"utils2/watermark?s=" +
						G.appFontSize +
						"&t=" +
						t +
						"&w=" +
						G.pageWidth +
						"&h=" +
						api.winHeight;
				}
			} else {
				G.watermark = false;
			}

			this.pageRefresh(true);
			if (platform() == "web") {
				var fontStyleId = "fontStyle";
				if (document.getElementById(fontStyleId)) {
					//存在的时候先删除
					document
						.getElementById(fontStyleId)
						.parentNode.removeChild(document.getElementById(fontStyleId));
				}
				var fontStyle = document.createElement("style");
				fontStyle.id = fontStyleId;
				switch (G.appFont) {
					case "shusongSimplified":
						fontStyle.innerText =
							"@font-face{font-family: shusongSimplified;src: url('../../res/fz_shusong_simplified.ttf')}";
						break;
					case "kaitiSimplified":
						fontStyle.innerText =
							"@font-face{font-family: kaitiSimplified;src: url('../../res/fz_kaiti_simplified.ttf')}";
						break;
					case "heitiTraditional":
						fontStyle.innerText =
							"@font-face{font-family: heitiTraditional;src: url('../../res/fz_heiti_traditional.ttf')}";
						break;
				}

				document.getElementsByTagName("head")[0].appendChild(fontStyle);
				this.fitWidth();
			}
		};
		YBasePage.prototype.fitWidth = function() {
			if (platform() == "web") {
				$("body").style.width = "100%";
				$("body").style.maxWidth = G.pageWidth + "px";
				$("body").style.minWidth = "300px";
				$("body").style.margin = "auto";
				$("body").style.position = "relative";
			}
		};
		YBasePage.prototype.isColorDarkOrLight = function(hexcolor) {
			try {
				var colors = colorRgba(hexcolor).match(/\d+\.?\d*/g);
				var red = colors[1],
					green = colors[2],
					blue = colors[3],
					brightness;
				brightness = (red * 299 + green * 587 + blue * 114) / 255000;
				return brightness >= 0.5 ? "light" : "dark";
			} catch (e) {
				return "";
			}
		};
		YBasePage.prototype.setHeadTheme = function() {
			var colorDarkOrLight = this.isColorDarkOrLight(
				this.headTheme == "transparent" ? "#FFF" : this.headTheme
			);
			G.headColor = colorDarkOrLight == "dark" ? "#FFF" : "#333";
			setStatusBarStyle({
				style: colorDarkOrLight == "light" ? "dark" : "light",
				color: "rgba(0,0,0,0)"
			});
		};
		YBasePage.prototype.setHeader = function() {
			if (this.props.dataMore) {
				return;
			}
			this.data.title = G.headTitle;
			if (platform() == "web") {
				if (window.parent) {
					window.parent.document.title = this.data.title;
				} else {
					document.title = this.data.title;
				}
			} else if (platform() == "mp") {
				wx.setNavigationBarTitle({title: this.data.title});
			}
		};
		YBasePage.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.monitor,
					id: "page_box",
					class:
						"page_box page_bg " +
						(G.grayscale == "true" ? "filterGray" : "filterNone"),
					style: {display: !this.props.dataMore || this.data.show ? "flex" : "none"}
				},
				apivm.h(
					"view",
					{class: "watermark_box"},
					G.watermark &&
						apivm.h("image", {
							class: "xy_100",
							src: G.watermark,
							mode: "aspectFill",
							thumbnail: "false"
						})
				),
				G.appTheme && [
					apivm.h(
						"view",
						{class: "xy_100"},

						apivm.h(
							"view",
							{
								class: "flex_shrink",
								style: {
									display:
										!this.props.dataMore || !this.props.dataMore.closeH ? "flex" : "none"
								}
							},
							!this.props.closeH &&
								(this.props.titleBox ||
									this.props.back ||
									this.props.more ||
									showHeader()) &&
								apivm.h(
									"view",
									{
										style:
											"height:auto;padding-top:" +
											headerTop() +
											"px;background:" +
											(this.props._this.data.headTheme || G.headTheme)
									},
									apivm.h(
										"view",
										{class: "header_warp flex_row"},
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.cTitle();
												},
												class: "header_main xy_center",
												style: this.props.titleStyle || null
											},
											this.props.titleBox && this.props.children.length >= 3
												? [this.props.children[2]]
												: [
														apivm.h(
															"text",
															{
																style:
																	loadConfiguration(4) + "font-weight: 600;color:" + G.headColor
															},
															this.data.title
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "header_left_box"},
											this.props.back && this.props.children.length >= 1
												? [this.props.children[0]]
												: [
														apivm.h(
															"view",
															{
																onClick: function() {
																	return this$1.close();
																},
																class: "header_btn",
																style: {
																	display:
																		showHeader() && this.props._this.data.pageType == "page"
																			? "flex"
																			: "none"
																}
															},
															apivm.h("a-iconfont", {
																name: "fanhui1",
																color: G.headColor,
																size: G.appFontSize + 1
															})
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "header_right_box"},
											this.props.more &&
												this.props.children.length >= 2 &&
												this.props.children[1]
										)
									)
								)
						),
						this.props.children.length >= 4 && this.props.children[3]
					),
					this.props.children.length >= 5
						? this.props.children.filter(function(item, index) {
								return index >= 4;
						  })
						: null,
					!this.props.dataMore && [
						apivm.h("previewer-img", {dataMore: G.imgPreviewerPop}),
						apivm.h("areas", {dataMore: G.areasPop}),
						apivm.h("select-item", {dataMore: G.selectPop}),
						apivm.h("z-actionSheet", {dataMore: G.actionSheetPop}),
						apivm.h("z-alert", {dataMore: G.alertPop})
					]
				]
			);
		};

		return YBasePage;
	})(Component);
	YBasePage.css = {
		".avm-toast,.avm-confirm-mask": {zIndex: "999"},
		element: {width: "auto", height: "auto"},
		".flex_shrink,div": {flexShrink: "0", WebkitOverflowScrolling: "touch"},
		".flex_w": {flex: "1", width: "1px"},
		".flex_h": {flex: "1", height: "1px"},
		".flex_row": {flexDirection: "row", alignItems: "center"},
		".xy_center": {alignItems: "center", justifyContent: "center"},
		".xy_100": {width: "100%", height: "100%"},
		".page_box": {
			position: "absolute",
			zIndex: "999",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		},
		".page_bg": {background: "#FFF"},
		".header_warp": {height: "44px"},
		".header_main": {
			height: "100%",
			flex: "1",
			flexDirection: "row",
			padding: "0 44px"
		},
		".header_left_box": {
			position: "absolute",
			zIndex: "999",
			left: "0",
			width: "auto",
			height: "44px"
		},
		".header_right_box": {
			position: "absolute",
			zIndex: "999",
			right: "0",
			width: "auto",
			height: "44px"
		},
		".header_btn": {
			width: "auto",
			height: "100%",
			minWidth: "36px",
			padding: "0 12px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".header_btn:active": {background: "rgba(0,0,0,0.05)"},
		".filterNone": {filter: "none"},
		".filterGray": {filter: "grayscale(1)"},
		".watermark_box": {
			position: "absolute",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0"
		}
	};
	apivm.define("y-base-page", YBasePage);

	var Root = /*@__PURE__*/ (function(Component) {
		function Root(props) {
			Component.call(this, props);
			this.data = {
				pageParam: {},
				pageType: "",
				MG: !this.props.dataMore ? G : null,
				dTitle: "",
				emptyBox: {
					type: "load",
					text: ""
				},

				showData: null,
				pageNo: 1,
				pageSize: 15,
				refreshPageSize: 0,
				listData: [],

				scrollView: {
					scroll_view: ""
				},

				collect: {has: false, is: false, title: ""},
				share: {
					has: false,
					show: false
				},

				suspendedBtn: {
					show: true,
					data: [
						{
							show: false,
							key: "listen",
							value: "播报",
							bg: "#FFF",
							color: "appTheme",
							type: "icon",
							src: "shengyin1",
							size: 5
						}
					]
				},

				floatModule: {},
				comment: {
					sendShow: false,
					bigShow: false,
					listShow: false,
					listHint: "全部评论",
					placeholder: "发表评论",
					replyPlaceholder: "",
					disabledHint: "", //禁用提示
					value: "",
					files: [],
					height: 150,
					commentNum: 0,
					likeNum: 0,
					likeIs: false,
					maxlength: 300,
					autoFocus: true,
					listExpand: [], //展开收起暂存
					checkedStatus: "1"
				},

				favoriteOkBox: {
					show: false
				},

				bu_50_option: {
					show: false
				},

				bu_50_3_users: {
					show: false
				},

				addData: [],

				addMore: {},
				submitText: "提交"
			};
		}

		if (Component) Root.__proto__ = Component;
		Root.prototype = Object.create(Component && Component.prototype);
		Root.prototype.constructor = Root;
		Root.prototype.onShow = function() {
			G.onShowNum++;
		};
		Root.prototype.baseInit = function(ref) {
			var this$1 = this;
			var detail = ref.detail;

			this.code = this.data.pageParam.code || "50_4"; //"39_4"
			this.id = this.data.pageParam.id || "1811313151648493569"; //"1772536731304751106"
			this.data.comment.id = this.id;
			//接收播报状态监听
			addEventListener("playState_details", function(ret, err) {
				this$1.data.floatModule = ret.value;
				var playItem = getItemForKey("listen", this$1.data.suspendedBtn.data);
				if (this$1.id == this$1.data.floatModule.id) {
					switch (this$1.data.floatModule.state + "") {
						case "0":
							playItem.src = "shengyin1";
							break;
						case "1":
							playItem.src = "shengyin";
							break;
						case "2":
							playItem.src = "shengyinjingyin";
							break;
						case "3":
							playItem.src = "shengyin1";
							break;
					}
				} else {
					playItem.src = "shengyin1";
				}
			});
			this.pageRefresh();
		};
		Root.prototype.collectRefresh = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.data.favoriteOkBox.show = true;
			}, 300);
			clearTimeout(this.data.favoriteOkBox.task);
			this.data.favoriteOkBox.task = setTimeout(function() {
				this$1.data.favoriteOkBox.show = false;
			}, 5000);
			this.pageRefresh();
		};
		Root.prototype.pageRefresh = function(e) {
			if (e === void 0) e = {};

			if (!e.detail || !e.detail.back || !this.backDotRefresh) {
				this.getPageData();
			}
		};
		Root.prototype.close = function() {
			api.setScreenSecure && api.setScreenSecure({secure: false});
			if (this.props.dataMore) {
				this.props.dataMore.show = false;
			} else {
				closeWin();
			}
		};
		Root.prototype.getModuleDetails = function(_param, _callback) {
			var this$1 = this;

			switch (this.code) {
				case "searchAll": //全局搜索
					_param.dirId = this.data.pageParam.typeId || "57";
					if (!this.showAllModel) {
						getSearchAllModel({param: _param.dirId}, function(ret, err) {
							var data = ret ? ret.dealWith || [] : [];
							if (!isArray(data) || !data.length) {
								this$1.data.emptyBox.type = ret ? "1" : "2";
								this$1.data.emptyBox.text =
									ret && ret.code != 200 ? ret.message || ret.data : "";
								return;
							}
							this$1.showAllModel = data;
							this$1.getModuleDetails(_param, _callback);
						});
						return;
					}
					_param.param.id = _param.param.detailId;
					_param.showAllModel = this.showAllModel;
					getDetailsSearchAll(_param, _callback);
					break;
				case "5":
					this.data.comment.code = module5.businessCode;
					this.data.collect.has = true;
					getDetails5(_param, _callback);
					break;
				case "apptext":
					_param.pt = this.data.pageParam.pt || "yszc";
					getDetailsAppText(_param, _callback);
					break;
				case "39_3":
				case "39_4":
					getDetails39_3(_param, _callback);
					break;
				case "50_1":
					getDetails50_1(_param, _callback);
					break;
				case "50_2":
					getDetails50_2(_param, _callback);
					break;
				case "50_3":
					if (this.data.pageParam.qr) {
						_param.param.qrInfo = 1;
					}
					getDetails50_3(_param, _callback);
					break;
				case "50_4":
					getDetails50_4(_param, _callback);
					break;
				case "50_5":
					getList50_5({param: {query: {id: this.id}}}, function(ret, err) {
						var data = ret ? ret.dealWith || [] : [];
						if (!isArray(data) || !data.length) {
							this$1.data.emptyBox.type = ret ? "1" : "2";
							this$1.data.emptyBox.text =
								ret && ret.code != 200 ? ret.message || ret.data : "";
							return;
						}
						var sData = {duty: data};
						this$1.data.emptyBox.type = "";
						this$1.data.emptyBox.text = "";
						getDetails50_5_feedback(_param, function(ret, err) {
							var data = ret ? ret.dealWith || {} : {};
							this$1.data.showData = setNewJSON(sData, data);
						});
					});
					break;
				case "50_7":
					this.data.comment.code = module50_7.businessCode;
					getDetails50_7(_param, _callback);
					break;
				case "50_7_re":
					getDetails50_7_re(_param, _callback);
					break;
			}
		};
		Root.prototype.getPageData = function() {
			var this$1 = this;

			this.getModuleDetails(
				{
					code: this.code,
					param: {detailId: this.id},
					pageParam: this.data.pageParam
				},
				function(ret, err) {
					hideProgress();
					var data = ret ? ret.dealWith || "" : "";
					this$1.data.emptyBox.type = !data ? (ret ? "1" : "2") : "";
					this$1.data.emptyBox.text = !data
						? ret && ret.code != 200
							? ret.message || ret.data
							: ""
						: "";
					this$1.data.showData = data;

					this$1.data.share.has = data && data.share;
					G.sharePosterPop.data = data;
					this$1.data.comment.sendShow =
						data && data.comment == "1" && !G.isAppReview;
					this$1.data.comment.listShow = data && data.commentList && !G.isAppReview;

					this$1.floatModuleContent = removeTagAll(data ? data.content || "" : "");
					getItemForKey("listen", this$1.data.suspendedBtn.data).show =
						(trimAll(this$1.floatModuleContent) ? true : false) &&
						platform() == "app" &&
						api.require("voiceRecognizer");
					sendEvent({name: "index", extra: {type: "getPlayState"}});
					switch (this$1.code) {
						case "5":
							this$1.data.share.param = {
								n: "mo_details_s",
								u: "../mo_details_s/mo_details_s.stml",
								p: {}
							};
							api.setScreenSecure &&
								api.setScreenSecure({secure: data && data.screen == "0"});
							if (data && data.module == 7) {
								this$1.getState();
							}
							break;
						case "50_2": //代表留言
							if (!data) {
								return;
							}
							// var review = G.isAdmin || getItemForKey('station_worker',G.specialRoleKeys) || getItemForKey('station_admin',G.specialRoleKeys);
							var review = data.showCheckStatus == 0;
							var isReview =
								this$1.data.pageParam.option && !this$1.data.pageParam.mine && review
									? true
									: false;
							var isWorker =
								this$1.data.pageParam.option &&
								!this$1.data.pageParam.mine &&
								(review || data.userId != G.userId)
									? true
									: false;
							var isMember =
								this$1.data.pageParam.option &&
								this$1.data.pageParam.mine &&
								data.userId == G.userId
									? true
									: false;

							var reviews =
								getItemForKey("station_worker", G.specialRoleKeys) ||
								getItemForKey("station_member", G.specialRoleKeys);

							if (data.checkStatus == 0 || data.checkStatus == 1) {
								if (data.checkStatus == 0) {
									if (isReview && reviews) {
										data.optionBtn = [
											{
												key: "50_2_review_ok",
												value: "审核通过",
												status: 1,
												plain: false,
												width: "50%"
											},
											{
												key: "50_2_review_no",
												value: "审核不通过",
												status: 2,
												plain: true,
												width: "50%"
											}
										];
									}
								}
								var answerItem = {title: "答复办理情况", data: []};
								var evaluateItem = {title: "评价结果", data: [], type: "evaluate"};
								if (isWorker && reviews) {
									data.optionBtn.push({
										key: "50_2_answer",
										value: "去回复",
										plain: false,
										width: "100%"
									});
								}
								data.otherAdd.push(answerItem);
								if (data.hasAnswer) {
									if (
										this$1.data.pageParam.option &&
										this$1.data.pageParam.mine &&
										data.userId == G.userId
									) {
										data.optionBtn.push({
											key: "50_2_evaluate",
											value: "去评价",
											plain: false,
											width: "100%"
										});
									}
									getList50_2_ans({param: {query: {letterId: this$1.id}}}, function(
										ret
									) {
										answerItem.data = ret ? ret.dealWith || [] : [];
									});
									data.otherAdd.push(evaluateItem);
									if (data.hasEvaluate && data.hasEvaluate != "0") {
										this$1.getEvaList = function() {
											var myEvaluateStatus = data.myEvaluateStatus == 0;
											getList50_2_eva(
												{
													param: {
														query: {
															letterId: this$1.id,
															checkStatus: isReview || isMember || myEvaluateStatus ? null : 1
														}
													},
													isWorker: myEvaluateStatus
												},
												function(ret) {
													evaluateItem.data = ret ? ret.dealWith || [] : [];
													if (!evaluateItem.data.length) {
														this$1.data.emptyBox.type = "1";
														this$1.data.emptyBox.text = "评价审核中或未通过";
													}
												}
											);
										};
										this$1.getEvaList();
									} else {
										this$1.data.emptyBox.type = "1";
										this$1.data.emptyBox.text = "暂无评价";
									}
								} else {
									this$1.data.emptyBox.type = "1";
									this$1.data.emptyBox.text = "暂无回复";
								}
								if (this$1.data.pageParam.open && data.optionBtn.length) {
									this$1.data.pageParam.open = null;
									if (data.checkStatus != 0) {
										this$1.cOptionBtn(data.optionBtn[data.optionBtn.length - 1]);
									}
								}
							}
							break;
						case "50_3":
							if (!data) {
								return;
							}
							if (data.userStatus == 0 || data.userStatus == 1) {
								data.optionBtn = [
									{
										key: "50_3_signup",
										value: (data.userStatus == 1 ? "取消" : "") + "报名",
										status: data.userStatus == 1 ? 1 : 0,
										plain: data.userStatus == 1,
										width: "100%"
									}
								];
							}
							ajax(
								{u: appUrl() + "feedback/info"},
								"feedback",
								function(ret, err) {
									var fData = ret ? ret.data || {} : {};
									this$1.backDotRefresh = fData.content ? true : false;
									if (fData.content) {
										var pictures = fData.pictures || "",
											picAttr = [];
										if (pictures) {
											pictures.split(",").forEach(function(_eItem) {
												picAttr.push({extName: "png", newFileName: _eItem});
											});
										}
										data.otherAdd.push({
											title: "活动开展情况",
											data: [
												{
													content: fData.content,
													attachments: picAttr.concat(fData.attachments || [])
												}
											]
										});
									}
								},
								"活动记录",
								"post",
								{
									body: JSON.stringify({detailId: this$1.id})
								}
							);

							break;
						case "50_4":
							if (!data) {
								return;
							}
							var receiveStations = data.receiveStations || [];
							var receiveMembers = data.receiveMembers || [];
							var readStations = data.readStations || []; //已读
							getWorkerStation({}, function(ret) {
								var myStation = ret ? ret.dealWith || [] : [];
								var myReceSta = [];
								myStation.forEach(function(_eItem) {
									if (getItemForKey(_eItem.id, receiveStations, "stationId")) {
										myReceSta.push(_eItem);
										if (!getItemForKey(_eItem.id, readStations, "receiveStationId")) {
											//没读 设置已读
											ajax(
												{u: appUrl() + "stationNoticeCallback/read"},
												"redDot" + _eItem.id,
												function(ret, err) {},
												"设置已读" + _eItem.name,
												"post",
												{
													body: JSON.stringify({
														form: {taskId: this$1.id, receiveStationId: _eItem.id}
													})
												}
											);
										}
									}
								});
								if (getItemForKey(G.userId, receiveMembers, "accountId")) {
									myReceSta.push({id: G.userId, name: G.userName, type: "accountId"});
									if (!getItemForKey(G.userId, readStations, "receiveAccountId")) {
										//没读 设置已读
										ajax(
											{u: appUrl() + "stationNoticeCallback/read"},
											"redDot" + G.userId,
											function(ret, err) {},
											"设置已读" + G.userName,
											"post",
											{
												body: JSON.stringify({
													form: {taskId: this$1.id, receiveAccountId: G.userId}
												})
											}
										);
									}
								}
								if (!myReceSta.length) {
									return;
								}
								this$1.myReceSta = myReceSta;
								if (data.hasCallback == 1) {
									//回执
									var callbackStartT = dayjs(data.callbackStartTime),
										callbackEndT = dayjs(data.callbackEndTime);
									data.contentAdd.push({
										text:
											"回执时间：" +
											callbackStartT.format("YYYY-MM-DD HH:mm") +
											"至" +
											callbackEndT.format("YYYY-MM-DD HH:mm")
									});
									data.callbackStatus = dayjs().isBefore(callbackStartT)
										? "未开始"
										: callbackEndT.isBefore(dayjs())
										? "已结束"
										: "";
									data.otherAdd.push({
										title: "回执" + data.callbackStatus,
										data: [],
										key: myReceSta.length == 1 ? "50_4_receipt" : "50_4_receipt_list",
										right: true,
										dt: true
									});
									if (myReceSta.length == 1) {
										var reqParam = {taskId: this$1.id};
										reqParam[
											myReceSta[0].type == "accountId"
												? "receiveAccountId"
												: "receiveStationId"
										] = myReceSta[0].id;
										getList50_4_callback({param: {query: reqParam}}, function(ret) {
											var code = ret ? ret.code || 0 : 0;
											var data = ret ? ret.data || [] : [];
											this$1.receiptItem = isArray(data) && data.length ? data[0] : null;
											if (this$1.receiptItem && this$1.receiptItem.hasCallback) {
												this$1.data.showData.otherAdd[0].title += "【已回执】";
											}
										});
									}
								}
							});
							break;
						case "50_7_re":
							if (!data.passStatus) {
								this$1.addParam = {id: this$1.id};
								this$1.data.addData = [
									{
										type: "radio",
										title: "审核结果",
										twa: 1,
										key: "passStatus",
										value: 1,
										defaultValue: 1,
										noRequired: true,
										line: 10,
										data: [
											{key: 1, value: "通过"},
											{key: 2, value: "不通过"}
										]
									},
									{
										type: "switch",
										title: "是否短信通知",
										twa: 1,
										key: "needTextMessageNotice",
										value: false,
										defaultValue: false,
										noRequired: true,
										line: 1
									},
									{
										type: "textarea",
										html: false,
										title: "审核意见",
										titleHint: "",
										max: 2000,
										hint: "请输入审核意见",
										key: "passReason",
										value: this$1.data.showData.passReason || "",
										defaultValue: "",
										noRequired: true,
										height: "200",
										line: 1
									}
								];
							} else {
								this$1.data.addData = [];
							}
							break;
					}

					//是否收藏
					if (data && this$1.data.comment.code && this$1.data.collect.has) {
						var param = {
							code: this$1.getNCode(),
							id: this$1.id
						};

						getIsCollect(param, function(is) {
							this$1.data.collect.is = is;
						});
					}
					//获取评论点赞数量
					if (
						this$1.data.comment.sendShow &&
						(isNumber(this$1.data.comment.commentNum) ||
							isNumber(this$1.data.comment.likeNum))
					) {
						getCommentCount(this$1.data.comment);
					}
					//获取评论
					if (this$1.data.comment.listShow) {
						this$1.getData(0);
					}
					if (this$1.data.comment.sendShow) {
						getAppConfig(["performCommentPass"], function(ret) {
							var data = ret ? ret.data || {} : {};
							this$1.data.comment.checkedStatus = data.performCommentPass || "1"; //默认审核通过
						});
					}
				}
			);
		};
		Root.prototype.getNCode = function() {
			if (this.code == "5") {
				return {
					"1": module5.businessCode,
					"2": "contentInformation",
					"6": "learningmaterials",
					"7": "perform_promote"
				}[this.data.showData.module];
			} else {
				return this.data.comment.code;
			}
		};
		Root.prototype.getData = function(_type) {
			var this$1 = this;

			var url = appUrl() + "comment/twoLevelTree";
			var postParam = {
				pageNo: !_type ? 1 : this.data.pageNo,
				pageSize:
					!_type && this.data.refreshPageSize > this.data.pageSize
						? this.data.refreshPageSize
						: this.data.pageSize,
				businessCode: this.data.comment.code,
				businessId: this.id,
				query: {}
			};

			if (this.data.comment.onlyMe) {
				postParam.isSelectForMyself = 1;
			}
			getListComment({param: postParam}, function(ret) {
				var data = ret ? ret.dealWith || [] : [];
				if (!isArray(data) || !data.length) {
					dealData(_type, this$1, ret);
					return;
				}
				if (!_type) {
					this$1.data.listData = data;
				} else {
					this$1.data.listData = this$1.data.listData.concat(data);
				}
				this$1.data.emptyBox.type = "";
				this$1.data.emptyBox.text =
					data.length >= postParam.pageSize ? LOAD_MORE : LOAD_ALL;
				this$1.data.pageNo =
					Math.ceil(this$1.data.listData.length / this$1.data.pageSize) + 1;
				this$1.data.refreshPageSize =
					Math.ceil(this$1.data.listData.length / this$1.data.pageSize) *
					this$1.data.pageSize;
			});
		};
		Root.prototype.loadMore = function() {
			if (
				(this.data.emptyBox.text == LOAD_MORE ||
					this.data.emptyBox.text == NET_ERR$1) &&
				this.data.pageNo != 1
			) {
				this.data.emptyBox.text = LOAD_ING;
				this.getData(this.data.listData.length ? 1 : 0);
			}
		};
		Root.prototype.collectBtn = function() {
			var optionItem = JSON.parse(JSON.stringify(this.data.comment));
			optionItem.code = this.getNCode();
			if (this.data.collect.is) {
				this.data.collect.is = !this.data.collect.is;
				optionCollect({
					id: optionItem.id,
					code: optionItem.code,
					collect: this.data.collect.is
				});
			} else {
				optionItem.title = this.data.showData.title;
				G.favoritePop = {
					show: true,
					key: "long_add",
					data: optionItem
				};
			}
		};
		Root.prototype.shareBtn = function() {
			this.data.share.param.p.id = this.id;
			this.data.share.param.p.code = this.code;
			this.data.share.param.p.areaId = areaId();

			G.sharePop = {
				show: true,
				data: this.data.showData,
				param: this.data.share.param,
				btns: this.data.share.btns
			};
		};
		Root.prototype.cckComment = function() {
			this.data.scrollView.scroll_view = "details_comment_box";
		};
		Root.prototype.recordPoints = function() {
			var this$1 = this;

			ajax(
				{u: appUrl() + "promotecert/addscore"},
				"promotecert/addscore",
				function(ret, err) {
					var data = ret ? ret.data || {} : {};
					if (ret.code == 200) {
						this$1.getCode(); //多次？
					}
				},
				"给分",
				"post",
				{
					body: JSON.stringify({
						form: {businessId: this.data.showData.id, beginTime: this.beginTime}
					})
				}
			);
		};
		Root.prototype.getState = function() {
			var this$1 = this;

			ajax(
				{u: appUrl() + "config/read"},
				"config/read",
				function(ret, err) {
					var data = ret ? ret.data || {} : {};
					if (data) {
						this$1.promoteReadTime = data.promote_read_time;
						if (this$1.promoteReadTime) {
							this$1.getCode();
						}
					}
				},
				"配置",
				"post",
				{
					body: JSON.stringify({codes: ["promote_read_time"]})
				}
			);
		};
		Root.prototype.getCode = function() {
			var this$1 = this;

			this.beginTime = dayjs().valueOf();
			var TIME_COUNT = Number(this.promoteReadTime);
			if (!this.timer && isNumber(TIME_COUNT)) {
				this.count = TIME_COUNT;
				this.timer = setInterval(function() {
					if (this$1.count > 0 && this$1.count <= TIME_COUNT) {
						this$1.count--;
					} else {
						clearInterval(this$1.timer);
						this$1.timer = null;
						this$1.recordPoints();
					}
				}, 1000);
			}
		};
		Root.prototype.cOptionBtn = function(_item, _nItem) {
			var this$1 = this;

			switch (_item.key) {
				case "participants": //点击活动参与人员
					this.data.bu_50_3_users = {
						show: true,
						title: "参与人员(" + this.data.showData.users.length + ")",
						listData: this.data.showData.users
					};

					break;
				case "50_2_review_ok":
				case "50_2_review_no":
					ajaxAlert(
						{
							msg: "确定" + _item.value + "吗?",
							url: appUrl() + "stationLetter/pass",
							param: {ids: [this.id], checkStatus: _item.status},
							toast: "审核中"
						},
						function(ret) {
							if (ret && ret.code == "200") {
								this$1.pageRefresh();
							}
						}
					);
					break;
				case "50_2_evaluatereview_ok":
				case "50_2_evaluatereview_no":
					ajaxAlert(
						{
							msg:
								"确定审核" +
								(_item.key == "50_2_evaluatereview_ok" ? "" : "不") +
								"通过吗?",
							url: appUrl() + "stationLetterEvaluate/pass",
							param: {
								ids: [_nItem.id],
								checkStatus: _item.key == "50_2_evaluatereview_ok" ? "1" : "2"
							},
							toast: "审核中"
						},
						function(ret) {
							if (ret && ret.code == "200") {
								this$1.getEvaList();
							}
						}
					);
					break;
				case "50_2_answer":
					this.data.bu_50_option = {
						show: true,
						title: "回复",
						param: function() {
							return {
								letterId: this$1.id,
								answerTime: dayjs().valueOf(),
								answerUser: G.userName
							};
						},
						form: true,
						listData: [
							{
								type: "textarea",
								html: false,
								title: "回复",
								titleHint: "",
								max: 2000,
								hint: "请输入回复内容",
								key: "content",
								value: "",
								noRequired: false,
								height: "200"
							},
							{
								type: "picture",
								show: true,
								title: "上传图片",
								hint: "请上传图片",
								key: "attachmentIds",
								value: [],
								max: 9,
								valueKey: "id",
								valueType: ",",
								noRequired: true,
								line: "10"
							}
						],

						callback: function(param) {
							ajaxAlert(
								{
									msg: "确定回复吗?",
									url: appUrl() + "stationLetterAnswer/add",
									param: param,
									toast: "回复中"
								},
								function(ret) {
									if (ret && ret.code == "200") {
										this$1.data.bu_50_option.show = false;
										this$1.pageRefresh();
									}
								}
							);
						}
					};

					break;
				case "50_2_evaluate":
					this.data.bu_50_option = {
						show: true,
						title: "评价",
						param: function() {
							return {
								letterId: this$1.id,
								evaluateTime: dayjs().valueOf(),
								evaluateUser: G.userName
							};
						},
						form: true,
						listData: [
							{
								type: "radio",
								title: "满意度",
								hint: "请选择满意度",
								key: "evaluateResult",
								dictKey: "has_evaluate",
								value: "",
								noRequired: false,
								line: 0,
								data: [],
								dIndex: 0
							},
							{
								type: "textarea",
								html: false,
								title: "评价内容",
								titleHint: "",
								max: 2000,
								hint: "请输入评价内容",
								key: "content",
								value: "",
								noRequired: false,
								height: "200",
								line: 10
							}
						],

						callback: function(param) {
							ajaxAlert(
								{
									msg: "确定评价吗?",
									url: appUrl() + "stationLetterEvaluate/add",
									param: param,
									toast: "评价中"
								},
								function(ret) {
									if (ret && ret.code == "200") {
										this$1.data.bu_50_option.show = false;
										this$1.pageRefresh();
									}
								}
							);
						}
					};

					break;
				case "50_3_signup":
					ajaxAlert(
						{
							msg: "确定" + _item.value + "吗?",
							url: appUrl() + "stationActivity/signup",
							param: {
								form: {id: this.id},
								userIds: [G.userId],
								controlsType: _item.status
							},
							toast: _item.value + "中"
						},
						function(ret) {
							if (ret && ret.code == "200") {
								this$1.pageRefresh();
							}
						}
					);
					break;
				case "50_4_receipt":
					var receiptItem = this.receiptItem || {};
					var readonly = receiptItem.hasCallback;
					if (this.data.showData.callbackStatus && !readonly) {
						toast("回执" + this.data.showData.callbackStatus);
						return;
					}
					var listD = [];
					var optionIds = receiptItem.optionIds || "";
					switch (this.data.showData.noticeCallbackType) {
						case "1": //文本
							listD.push({
								type: "textarea",
								html: false,
								title: "回执内容",
								titleHint: "",
								max: 2000,
								hint: "请输入回执内容",
								key: "callbackContent",
								value: receiptItem.callbackContent || "",
								noRequired: false,
								height: "200",
								line: 0
							});
							break;
						case "2": //单
							listD.push({
								type: "radio",
								title: "回执",
								hint: "请选择回执选项",
								key: "optionIds",
								value: optionIds,
								noRequired: false,
								line: 0,
								data: this.data.showData.options.map(function(obj) {
									return {key: obj.optionName, value: obj.optionName};
								}),
								dIndex: 0
							});
							break;
						case "3": //多
							listD.push({
								type: "radio",
								title: "回执",
								hint: "请选择回执选项",
								key: "optionIds",
								value: optionIds ? optionIds.split(",") : [],
								noRequired: false,
								line: 0,
								data: this.data.showData.options.map(function(obj) {
									return {key: obj.optionName, value: obj.optionName};
								}),
								dIndex: 0
							});
							break;
					}

					this.data.bu_50_option = {
						show: true,
						readonly: readonly,
						title: "回执",
						param: function() {
							return {
								id: receiptItem.id,
								taskId: this$1.id,
								receiveStationId: this$1.data.pageParam.stationId,
								hasCallback: 1,
								stationCallbackType: this$1.data.showData.noticeCallbackType
							};
						},
						form: true,
						listData: listD,
						callback: function(param) {
							ajaxAlert(
								{
									msg: "确定回执吗?",
									url:
										appUrl() + "stationNoticeCallback/" + (receiptItem ? "edit" : "add"),
									param: param,
									toast: "回执中"
								},
								function(ret) {
									if (ret && ret.code == "200") {
										this$1.data.bu_50_option.show = false;
										this$1.pageRefresh();
									}
								}
							);
						}
					};

					break;
				case "50_4_receipt_list": //多个站点回执
					showProgress();
					getList50_4_callback({param: {query: {taskId: this.id}}}, function(ret) {
						hideProgress();
						var code = ret ? ret.code || 0 : 0;
						var data = ret ? ret.data || [] : [];
						if (!isArray(data) || !data.length) {
							toast(ret ? ret.message || ret.data : NET_ERR$1);
							return;
						}
						this$1.myReceSta.forEach(function(_eItem) {
							var nowCallback = getItemForKey(
								_eItem.id,
								data,
								_eItem.type == "accountId" ? "receiveAccountId" : "receiveStationId"
							);
							if (nowCallback) {
								_eItem.callback = nowCallback;
								_eItem.hasCallback = nowCallback.hasCallback;
							}
						});
						actionSheet(
							{
								title: "请选择",
								buttons: this$1.myReceSta.map(function(obj) {
									var nObj = JSON.parse(JSON.stringify(obj));
									nObj.name = nObj.name + (nObj.hasCallback ? "【已回执】" : "");
									return nObj;
								})
							},
							function(ret) {
								console.log(JSON.stringify(ret));
								this$1.receiptItem = ret.callback;
								this$1.cOptionBtn({key: "50_4_receipt"});
							}
						);
					});
					break;
			}
		};
		Root.prototype.suspendedBtnClick = function(ref) {
			var detail = ref.detail;

			switch (detail.key) {
				case "listen":
					if (getPrefs("isFirstOpen") != "true") {
						toast("同意隐私政协后方可使用朗读功能");
						return;
					}
					if (this.id == this.data.floatModule.id) {
						if (this.data.floatModule.state == "1") {
							sendEvent({name: "index", extra: {type: "pauseStartPlay"}});
						} else {
							sendEvent({name: "index", extra: {type: "reStartPlay"}});
						}
					} else {
						sendEvent({
							name: "index",
							extra: {
								type: "startPlay",
								floatType: this.code,
								id: this.id,
								src: this.floatModuleContent
							}
						});
					}
					break;
				default:
					toast(detail.value);
					break;
			}
		};
		Root.prototype.submit = function(_draft) {
			var this$1 = this;

			var url = "";
			var param = this.addParam || {};
			var getPostParam = addPostParam(this.data.addData);
			if (!getPostParam) {
				return;
			}
			param = setNewJSON(param, getPostParam);
			switch (this.paramType || this.code) {
				case "50_7_re":
					url = appUrl() + "stationdutiesroom/pass";
					param = {form: param};
					break;
			}

			setParamToFirst(this.data.addData, param);
			var hintText = _draft ? this.submitDraftText : this.data.submitText;
			ajaxAlert(
				{
					msg: "确定" + hintText + "吗?",
					url: url,
					param: param,
					toast: hintText + "中"
				},
				function(ret) {
					if (ret && ret.code == "200") {
						setTimeout(function() {
							this$1.pageRefresh();
						}, 300);
					}
				}
			);
		};
		Root.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"y-base-page",
				{
					_this: this,
					dataMore: this.props.dataMore,
					more: this.data.collect.has || this.data.share.has
				},
				apivm.h("view", null),
				apivm.h(
					"view",
					{style: "height:100%;flex-direction:row-reverse;"},
					apivm.h(
						"view",
						{
							onClick: function() {
								return this$1.shareBtn();
							},
							class: "header_btn",
							style: {display: this.data.share.has ? "flex" : "none"}
						},
						apivm.h("a-iconfont", {
							name: "fenxiang2",
							color: G.headColor,
							size: G.appFontSize + 3
						})
					),
					apivm.h(
						"view",
						{
							onClick: function() {
								return this$1.collectBtn();
							},
							class: "header_btn",
							style: {display: this.data.collect.has ? "flex" : "none"}
						},
						apivm.h("a-iconfont", {
							name: this.data.collect.is ? "yishoucang" : "shoucang1",
							color: this.data.collect.is ? "#F6931C" : G.headColor,
							size: G.appFontSize + 4
						})
					)
				),
				apivm.h("view", null),
				apivm.h(
					"view",
					{class: "flex_h"},
					apivm.h(
						"y-scroll-view",
						{_this: this, dataMore: this.data.scrollView, refresh: true},
						apivm.h(
							"view",
							null,
							this.data.showData &&
								apivm.h(
									"view",
									null,
									apivm.h(
										"view",
										{style: "padding: 0 16px;"},
										this.data.showData.duty &&
											this.data.showData.duty.length > 0 &&
											this.data.showData.duty.map(function(item, index) {
												return [
													apivm.h("item50-5-1", {
														flat: true,
														detail: true,
														_this: this$1,
														list: this$1.data.showData.duty,
														item: item,
														index: index
													})
												];
											})
									),
									apivm.h(
										"view",
										null,
										this.data.showData.title &&
											apivm.h(
												"view",
												{style: "padding: 10px 16px 0;"},
												apivm.h(
													"text",
													{
														style: loadConfiguration(6) + "font-weight: 600; color: #333333;"
													},
													this.data.showData.title
												),
												apivm.h(
													"view",
													null,
													this.data.showData.subTitle &&
														apivm.h(
															"text",
															{style: loadConfiguration(1) + "margin-top:10px;color:#666;"},
															this.data.showData.subTitle
														)
												)
											)
									),
									apivm.h(
										"view",
										{class: "flex_row"},
										apivm.h(
											"view",
											{class: "left_box flex_w"},
											(Array.isArray(this.data.showData.leftAdd || [])
												? this.data.showData.leftAdd || []
												: Object.values(this.data.showData.leftAdd || [])
											).map(function(item$1, index$1) {
												return apivm.h(
													"view",
													{style: "margin:10px 16px 0 0;"},
													item$1.type == "tag"
														? apivm.h(
																"z-tag",
																{color: item$1.color, type: item$1.tType},
																item$1.text
														  )
														: apivm.h(
																"text",
																{
																	style:
																		loadConfiguration(-2) +
																		"color: " +
																		(item$1.color || "#666") +
																		";padding: 2px 0px;"
																},
																item$1.text
														  )
												);
											})
										),
										apivm.h(
											"view",
											{class: "right_box"},
											(Array.isArray(this.data.showData.rightAdd || [])
												? this.data.showData.rightAdd || []
												: Object.values(this.data.showData.rightAdd || [])
											).map(function(item$1, index$1) {
												return apivm.h(
													"view",
													{style: "margin:10px 0 0 16px;"},
													item$1.type == "tag"
														? apivm.h(
																"z-tag",
																{color: item$1.color, type: item$1.tType},
																item$1.text
														  )
														: apivm.h(
																"text",
																{
																	style:
																		loadConfiguration(-2) +
																		"color: " +
																		(item$1.color || "#666") +
																		";padding: 2px 0px;"
																},
																item$1.text
														  )
												);
											})
										)
									),
									apivm.h(
										"view",
										{style: "padding: 0 16px 0;"},
										(this.data.showData.contentBefore || []).map(function(item, index) {
											return [
												apivm.h(
													"view",
													{style: "flex-direction:row;align-items: start;margin-top:10px;"},
													apivm.h(
														"view",
														null,
														item.hint &&
															apivm.h(
																"text",
																{
																	style:
																		loadConfiguration(-2) +
																		"color: " +
																		(item.color || "#333") +
																		";width:80px;"
																},
																item.hint,
																"："
															)
													),
													apivm.h(
														"view",
														{class: "flex_w"},
														item.type == "richText"
															? apivm.h("z-rich-text", {
																	size: 0,
																	detail: true,
																	nodes: item.text
															  })
															: apivm.h(
																	"text",
																	{
																		style:
																			loadConfiguration(-2) +
																			"color: " +
																			(item.color || "#333") +
																			";"
																	},
																	item.text
															  )
													)
												)
											];
										})
									),
									apivm.h(
										"view",
										{style: "padding: 10px 16px;"},
										apivm.h("z-rich-text", {
											size: 2,
											detail: true,
											nodes: this.data.showData.content
										})
									),
									apivm.h("y-attachments", {
										style: "padding: 15px 16px;",
										data: this.data.showData.attachments || []
									}),
									apivm.h(
										"view",
										{style: "padding: 0 16px 0;"},
										(this.data.showData.contentAdd || []).map(function(item, index) {
											return [
												apivm.h(
													"view",
													{style: "margin-top:10px;"},
													apivm.h(
														"text",
														{
															style:
																loadConfiguration(-2) + "color: " + (item.color || "#999") + ";"
														},
														item.text
													)
												)
											];
										})
									),
									apivm.h(
										"view",
										null,
										(this.data.showData.optionBtn || []).length > 0 &&
											apivm.h(
												"view",
												{style: "padding: 15px 16px 0;flex-direction:row;flex-wrap: wrap;"},
												this.data.showData.optionBtn.map(function(nItem, nIndex) {
													return apivm.h(
														"view",
														{style: "width:" + nItem.width + ";padding:5px 15px;"},
														apivm.h("z-button", {
															onClick: function() {
																return this$1.cOptionBtn(nItem);
															},
															disabled: nItem.disabled,
															round: true,
															plain: nItem.plain,
															style: "padding:7px 7px;width:100%;",
															color: G.appTheme,
															text: nItem.value
														})
													);
												})
											)
									),
									apivm.h(
										"view",
										null,
										(this.data.showData.otherAdd || []).map(function(item, index) {
											return [
												apivm.h(
													"view",
													{
														style: "margin-top:10px;border-top: 10px solid rgba(0,0,0,0.04);"
													},
													apivm.h(
														"view",
														{
															onClick: function() {
																return this$1.cOptionBtn(item);
															},
															class: "flex_row",
															style: "padding: 15px;"
														},
														apivm.h("view", {
															style:
																loadConfigurationSize(-1, "h") +
																"width:3px; border-radius: 10px;background:" +
																G.appTheme +
																";"
														}),
														apivm.h(
															"text",
															{
																style:
																	loadConfiguration(1) +
																	"flex:1;font-weight: 600;color: #333333;margin-left:5px;"
															},
															item.title
														),
														item.right &&
															apivm.h("a-iconfont", {
																style: "transform: rotate(-90deg)",
																name: "xiangxia1",
																color: "#333333",
																size: G.appFontSize
															})
													),
													apivm.h(
														"view",
														{
															style:
																"border-top: " +
																(item.dt ? 1 : 0) +
																"0px solid rgba(0,0,0,0.04);"
														},
														(item.data || []).map(function(nItem, nIndex) {
															return apivm.h(
																"view",
																{style: "padding: 0 15px 15px;"},
																apivm.h(
																	"view",
																	null,
																	item.type == "evaluate" && [
																		apivm.h(
																			"view",
																			{class: "flex_row", style: "margin-bottom: 6px;"},
																			apivm.h("view", {
																				style:
																					"width:14px;height:14px;border: 2px solid " +
																					G.appTheme +
																					";border-radius: 50%;margin-right:11px;"
																			}),
																			apivm.h(
																				"text",
																				{
																					style:
																						loadConfiguration() +
																						"font-weight: 600;color:" +
																						G.appTheme +
																						";flex:1;"
																				},
																				nItem.title
																			),
																			apivm.h(
																				"text",
																				{style: loadConfiguration(-2) + "color: #999;"},
																				nItem.addText
																			),
																			nItem.tag &&
																				apivm.h(
																					"z-tag",
																					{
																						color: nItem.tag.color,
																						style: "margin-left:10px;",
																						type: nItem.tag.tType
																					},
																					nItem.tag.text
																				)
																		),
																		apivm.h("z-rich-text", {
																			style: "padding:5px 0 0 26px;",
																			nodes: nItem.content
																		}),
																		!nItem.checkStatus &&
																			nItem.isWorker &&
																			apivm.h(
																				"view",
																				{style: "padding: 20px;", class: "flex_row"},
																				apivm.h("z-button", {
																					onClick: function() {
																						return this$1.cOptionBtn(
																							{key: "50_2_evaluatereview_ok"},
																							nItem
																						);
																					},
																					round: true,
																					style: "padding:7px 7px;width:50%;",
																					color: G.appTheme,
																					text: "审核通过"
																				}),
																				apivm.h("z-button", {
																					onClick: function() {
																						return this$1.cOptionBtn(
																							{key: "50_2_evaluatereview_no"},
																							nItem
																						);
																					},
																					round: true,
																					style: "padding:7px 7px;width:50%;margin-left:20px;",
																					color: G.appTheme,
																					text: "审核不通过"
																				})
																			)
																	]
																),
																apivm.h(
																	"view",
																	null,
																	item.type != "evaluate" && [
																		apivm.h(
																			"view",
																			null,
																			nItem.title &&
																				apivm.h(
																					"text",
																					{style: loadConfiguration() + "margin-bottom: 6px;"},
																					nItem.title
																				)
																		),
																		apivm.h("z-rich-text", {nodes: nItem.content}),
																		apivm.h("y-attachments", {
																			style: "padding: 10px 0px;",
																			data: nItem.attachments
																		}),
																		apivm.h(
																			"text",
																			{
																				style:
																					loadConfiguration(-2) + "color: #999; margin-top: 6px;"
																			},
																			nItem.addText
																		)
																	]
																)
															);
														})
													)
												)
											];
										})
									),
									apivm.h(
										"view",
										null,
										this.data.addData.length > 0 && [
											apivm.h(
												"view",
												null,
												this.data.addData.map(function(item, index) {
													return [
														item.type == "textarea"
															? [
																	apivm.h("item-textarea", {
																		dataMore: this$1.data.addMore,
																		item: item,
																		index: index
																	})
															  ]
															: item.type == "radio"
															? [
																	apivm.h("item-radio", {
																		dataMore: this$1.data.addMore,
																		item: item,
																		index: index
																	})
															  ]
															: item.type == "switch"
															? [
																	apivm.h("item-switch", {
																		dataMore: this$1.data.addMore,
																		item: item,
																		index: index
																	})
															  ]
															: []
													];
												})
											),
											apivm.h(
												"view",
												{class: "flex_row", style: "margin:20px 0;padding:0 40px;"},
												apivm.h("z-button", {
													onClick: function() {
														return this$1.submit();
													},
													round: true,
													style: "flex:1;padding:7px 10px;",
													color: G.appTheme,
													text: this.data.submitText
												})
											)
										]
									),

									apivm.h("y-comment", {
										id: "details_comment_box",
										scroll: this.data.scrollView,
										dataMore: this.data.comment,
										data: this.data.listData,
										onRefresh: this.pageRefresh
									})
								)
						),
						apivm.h(
							"view",
							null,
							this.data.emptyBox.type == "load" && apivm.h("z-skeleton", null)
						),
						apivm.h("z-empty", {
							_this: this,
							dataMore: this.data.emptyBox,
							onRefresh: this.pageRefresh
						}),
						apivm.h("view", {
							style:
								"padding-bottom:" +
								(!this.data.pageParam.footerH ? safeArea().bottom : 0) +
								"px;"
						})
					),
					apivm.h("y-comment-send", {
						dataMore: this.data.comment,
						bottom: this.data.pageType == "page",
						onComment: this.cckComment
					})
				),

				apivm.h("y-suspended-btns", {
					dataMore: this.data.suspendedBtn,
					onClick: this.suspendedBtnClick
				}),

				apivm.h("y-comment-send-big", {
					dataMore: this.data.comment,
					bottom: this.data.pageType == "page",
					onRefresh: this.pageRefresh
				}),

				apivm.h("collect-add", {
					dataMore: G.favoritePop,
					onRefresh: this.collectRefresh
				}),

				apivm.h("collect-ok", {dataMore: this.data.favoriteOkBox}),

				apivm.h("share", {dataMore: G.sharePop}),

				apivm.h("share-poster-news", {dataMore: G.sharePosterPop}),

				apivm.h("bu-50-option", {dataMore: this.data.bu_50_option}),

				apivm.h("bu-50-3-users", {dataMore: this.data.bu_50_3_users})
			);
		};

		return Root;
	})(Component);
	Root.css = {
		".left_box": {paddingLeft: "16px", flexDirection: "row"},
		".right_box": {paddingRight: "16px", flexDirection: "row"}
	};
	apivm.define("root", Root);
	apivm.render(apivm.h("root", null), "body");
})();
