(function() {
	var G = {
		pageWidth:
			api.platform == "web"
				? api.winWidth > api.winHeight
					? 600
					: api.winWidth
				: api.winWidth,
		refreshPageSize: 0, //返回当前页刷新列表的条数
		dotRefsresh: false, // 返回当前页是否不刷新
		showSkeleton: true, //是否展示骨架屏
		seachText: "", //搜索词
		seachPlaceholder: "请输入搜索内容", //搜索提示
		firstAjax: false, //首次网络请求是否成功
		dotCloseListener: false, //当前页面不要划动返回
		hasCloseListener: false, //不管其它页面 直接添加关闭监听

		appName: "",
		appFont: "", //app全局字体
		appFontSize: 0, //app全局字体大小
		appTheme: "", //app全局主题色
		headTheme: "", //head全局主题色
		careMode: false, //是否启用了关怀模式
		htmlStyle: "", //html级别设置style 置灰等操作
		htmlClass: "", //html级别设置class
		systemtTypeIsPlatform: false, //系统类型是否是平台版
		v: "", //缓存版本号

		uId: "", //普通用户id
		userId: "", //当前用户id 账号id
		userName: "", //当前用户名字
		userImg: "", //当前用户头像
		areaId: "", //当前地区id
		specialRoleKeys: [], //当前用户角色集合
		isAdmin: false, //是否管理员 拥有所有权限
		viewappearFrist: true, //是否首次进入页面

		touchmoveTask: null, //划动元素时 禁用页面划动返回事件
		nTouchmove: false,

		isAppReview: false, //app是否上架期间 隐藏和显示部分功能

		touchmove: function touchmove() {
			G.nTouchmove = true;
			G.touchmoveTask && clearTimeout(G.touchmoveTask);
			G.touchmoveTask = setTimeout(function() {
				G.nTouchmove = false;
			}, 1000);
		},
		//通用组件 start=====================================================

		imagePreviewer: {
			//全局图片预览组件
			show: false,
			imgs: [],
			activeIndex: 0,
			type: 1
		},

		openImgPreviewer: function openImgPreviewer(_param) {
			if (_param === void 0) {
				_param = {};
			}
			if ((_param.imgs || []).length <= 0) {
				return;
			}
			G.imagePreviewer.activeIndex = _param.index || 0;
			G.imagePreviewer.imgs = _param.imgs;
			G.imagePreviewer.show = true;
			T.sendEvent("updatePage");
		},

		areasBox: {
			//地区切换弹窗组件
			show: false,
			type: "half", //full全屏打开  half半屏打开
			pageParam: null
		},

		openAreas: function openAreas(_param, _callback) {
			if (_param === void 0) {
				_param = {};
			}
			G.areasBox.pageParam = _param;
			G.areasBox.show = true;
			T.addEventListener("base_areas_callback", function(ret) {
				_callback && _callback(ret.value);
				T.removeEventListener("base_areas_callback");
			});
			T.sendEvent("updatePage");
		},

		alertBox: {
			// 确认提示框
			show: false,
			title: "",
			content: "",
			richText: false,
			input: false,
			textarea: false,
			placeholder: "",
			cancel: {show: false, text: "取消", color: "#333333"},
			sure: {show: true, text: "确定", color: "appTheme"}
		},

		alert: function alert(_param, _callback) {
			var o = {title: "", msg: "", buttons: ["确定"]};
			if (T.isObject(_param)) {
				o = T.setNewJSON(o, _param);
			} else {
				o.msg = T.isParameters(_param) ? _param : "";
			}
			G.alertBox.title = o.title;
			G.alertBox.content = (o.msg || o.content || "").toString();
			G.alertBox.input = o.input;
			G.alertBox.textarea = o.textarea;
			G.alertBox.placeholder = o.placeholder;
			G.alertBox.richText = o.richText;
			G.alertBox.cancel.show = o.buttons.length > 1;
			G.alertBox.cancel.text = o.buttons[1];
			G.alertBox.sure.text = o.buttons[0];
			G.alertBox.show = true;
			T.addEventListener("base_alert_callback", function(ret) {
				_callback && _callback(ret.value);
				T.removeEventListener("base_alert_callback");
			});
			T.sendEvent("updatePage");
		},

		actionSheetBox: {
			show: false,
			cancel: false,
			title: "",
			active: null,
			data: []
		},

		actionSheet: function actionSheet(_param, _callback) {
			var o = {title: "", cancelTitle: "取消", destructiveTitle: ""};
			o = T.setNewJSON(o, _param);
			G.actionSheetBox.title = o.title;
			G.actionSheetBox.cancel = o.cancelTitle;
			var oldButton = o.buttons || [],
				newButton = [];
			oldButton.forEach(function(item) {
				newButton.push(T.isObject(item) ? item : {value: item});
			});
			G.actionSheetBox.data = newButton;
			G.actionSheetBox.active = _param.active;
			G.actionSheetBox.dotClose = _param.dotClose;
			G.actionSheetBox.show = true;
			T.addEventListener("base_actionSheet_callback", function(ret) {
				_callback && _callback(ret.value);
				T.removeEventListener("base_actionSheet_callback");
			});
			T.sendEvent("updatePage");
		},
		//通用组件 end=====================================================

		installed: function installed(_this) {
			var _this2 = this;
			if (_this.props && _this.props.dataMore);
			else {
				G.fitWidth();
				G.changeConfiguration(_this);
				G.appGrayscale();
				G.initOther();
				T.addEventListener("index_login_ok", function(ret, err) {
					G.initOther();
				});
				//字体刷新
				T.addEventListener("changeConfiguration", function(ret, err) {
					G.changeConfiguration(_this);
				});
				//地区刷新监听
				T.addEventListener("areaChange", function(ret, err) {
					var notifas = ["module", "news", "my", "negotiable", "area"];
					notifas.forEach(function(_eItem) {
						T.sendEvent({name: "areaChange_" + _eItem, extra: ret.value});
					});
				});
				//红点刷新
				T.addEventListener("unreadChange", function(ret, err) {
					var notifas = ["module", "my"];
					notifas.forEach(function(_eItem) {
						T.sendEvent({name: "unreadChange_" + _eItem, extra: ret.value});
					});
				});
				if (
					T.isFunction(_this.close) &&
					!T.isParameters(_this.props.pageParam) &&
					!T.isParameters(_this.props.dataMore)
				) {
					T.addEventListener("keyback", function(ret, err) {
						if (G.imagePreviewer.show) {
							G.imagePreviewer.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G.areasBox.show) {
							G.areasBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G.alertBox.show) {
							if (G.alertBox.cancel.show) {
								G.alertBox.show = false;
								T.sendEvent("updatePage");
							}
							return;
						}
						if (G.actionSheetBox.show) {
							if (G.actionSheetBox.cancel) {
								G.actionSheetBox.show = false;
								T.sendEvent("updatePage");
							}
							return;
						}
						_this2.close(_this);
					});
					T.addEventListener("swiperight", function(ret, err) {
						if (G.nTouchmove) {
							return;
						}
						if (G.imagePreviewer.show) {
							return;
						}
						if (G.areasBox.show) {
							G.areasBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G.alertBox.show && G.alertBox.cancel.show) {
							G.alertBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						if (G.actionSheetBox.show) {
							G.actionSheetBox.show = false;
							T.sendEvent("updatePage");
							return;
						}
						_this2.close(_this);
					});
				}
				setTimeout(function() {
					_this2.setHeader(_this);
				}, 10);
			}
			try {
				_this.init();
			} catch (e) {
				console.log(e);
			}
		},
		close: function close(_this) {
			_this.close();
		},
		setHeader: function setHeader(_this) {
			var title = _this.data.title;
			// console.log("=================="+title);
			// console.log(_this.props);
			// console.log(_this.data);
			if (!title) {
				return;
			}
			if (T.platform() == "web") {
				if (window.parent) {
					window.parent.document.title = title;
				} else {
					document.title = title;
				}
			} else if (T.platform() == "mp") {
				wx.setNavigationBarTitle({
					title: title
				});
			}
		},
		//多端页面显示回调 app、h5、小程序
		onShow: function onShow(_this) {
			var _this3 = this;
			if (_this.props.dataMore) {
				return;
			}
			if (G.viewappearFrist) {
				G.viewappearFrist = false;
				return;
			}
			console.log("返回了当前页面：");
			T.sendEvent({name: "changeConfiguration"});
			if (G.areaId != T.getPrefs("sys_aresId")) {
				G.areaId = T.getPrefs("sys_aresId") || "";
				T.sendEvent({name: "areaChange", extra: {key: G.areaId}});
			}
			setTimeout(function() {
				_this3.setHeader(_this);
			}, 10);
			if (_this.getData) {
				//返回刷新一下
				_this.getData(false, {refresh: 1});
			}
		},
		//初始化后其它配置
		initOther: function initOther() {
			G.areaId = T.getPrefs("sys_aresId") || "";
			G.systemtTypeIsPlatform = T.getPrefs("sys_systemType") == "platform"; //系统类型是否是平台版
			G.appName = T.getPrefs("sys_systemName") || "";
			G.uId = T.getPrefs("sys_Id") || "";
			G.userId = T.getPrefs("sys_UserID") || "";
			G.userName = T.getPrefs("sys_UserName") || "";
			G.userImg = T.getPrefs("sys_AppPhoto") || "";
			G.specialRoleKeys = JSON.parse(T.getPrefs("sys_SpecialRoleKeys") || "[]");
			G.isAdmin =
				G.userId == "1" ||
				G.getItemForKey("dc_admin", G.specialRoleKeys) ||
				G.getItemForKey("admin", G.specialRoleKeys);
			G.v = T.getPrefs("sys_appVersion") || "";
			if (T.platform() == "app") {
				G.isAppReview =
					JSON.parse(T.getPrefs("sys_appReviewVersion") || "{}")[api.systemType] ==
					api.appVersion;
			}
		},
		//全局配置
		changeConfiguration: function changeConfiguration(_this) {
			G.appFont =
				T.getPrefs("appFont") && T.getPrefs("appFont") != "0"
					? T.getPrefs("appFont")
					: "heitiSimplified";
			G.appFontSize = Number(
				T.getPrefs("appFontSize") && T.getPrefs("appFontSize") != "0"
					? T.getPrefs("appFontSize")
					: "16"
			);
			G.appTheme =
				T.pageParam(_this).appTheme ||
				T.getPrefs("appTheme" + (myjs.iszx ? "zx" : "rd")) ||
				(myjs.iszx ? "#3088FE" : "#C61414");
			var headTheme =
				_this.data.headTheme ||
				T.pageParam(_this).headTheme ||
				T.getPrefs("headTheme") ||
				"#FFF";
			G.headTheme = headTheme == "appTheme" ? G.appTheme : headTheme;
			G.careMode = parseInt(G.appFontSize) > 16;
			if (T.platform() == "web") {
				var fontStyleId = "fontStyle";
				if (document.getElementById(fontStyleId)) {
					//存在的时候先删除
					document
						.getElementById(fontStyleId)
						.parentNode.removeChild(document.getElementById(fontStyleId));
				}
				var fontStyle = document.createElement("style");
				fontStyle.id = fontStyleId;
				switch (G.appFont) {
					case "shusongSimplified":
						fontStyle.innerText =
							"@font-face{font-family: shusongSimplified;src: url('../../res/fz_shusong_simplified.ttf')}";
						break;
					case "kaitiSimplified":
						fontStyle.innerText =
							"@font-face{font-family: kaitiSimplified;src: url('../../res/fz_kaiti_simplified.ttf')}";
						break;
					case "heitiTraditional":
						fontStyle.innerText =
							"@font-face{font-family: heitiTraditional;src: url('../../res/fz_heiti_traditional.ttf')}";
						break;
				}

				document.getElementsByTagName("head")[0].appendChild(fontStyle);
			}
			_this.update();
			T.sendEvent("updatePage");
		},
		//是否全局置灰
		appGrayscale: function appGrayscale() {
			var appGrayscale = T.getPrefs("appGrayscale") || "0";
			if (T.platform() == "app");
			else {
				// G.htmlStyle = "filter:"+(appGrayscale == 1?'grayscale(1)':'none')+";";//小程序不知道为啥style没用
				G.htmlClass = appGrayscale == 1 ? "filterGray" : "filterNone";
			}
		},
		//展示图片
		showImg: function showImg(_item) {
			var baseUrl = T.isObject(_item) ? _item.url || "" : _item || "";
			baseUrl = G.showAllSystemImg(baseUrl); //先显示系统图片
			if (
				baseUrl.indexOf("http") == 0 &&
				baseUrl.indexOf("http://127.0.0.1") != 0 &&
				baseUrl.indexOf(myjs.tomcatAddress()) != 0
			) {
				//是链接 不是小程序本地链接 不是处理过的链接
				if (myjs.proxy && T.platform() != "app" && baseUrl.indexOf("https") != 0) {
					baseUrl = myjs.tomcatAddress() + "utils/proxyPic?" + baseUrl;
				}
			}
			return baseUrl + (baseUrl.indexOf("?") != -1 ? "&" : "?") + "v=" + G.v;
		},
		showAllSystemImg: function showAllSystemImg(_url) {
			return !_url ||
				_url.indexOf("http") == 0 ||
				_url.indexOf("/") == 0 ||
				_url.indexOf("../") == 0
				? _url
				: myjs.appUrl() + "image/" + _url;
		},
		//图片处理
		cacheImg: function cacheImg(_item, _thumbnail, _url, _priority) {
			if (!T.isObject(_item) || !T.isParameters(_item.url)) return; //没有传对象 或者没有url的时候不处理
			var baseUrl = _item.webImg || _url || _item.url || ""; //存储当前缓存地址
		},
		//字体配置
		loadConfiguration: function loadConfiguration(_changeSize) {
			return (
				"font-size:" +
				((G.appFontSize || 0) + (_changeSize || 0)) +
				"px;font-family:" +
				G.appFont +
				";"
			);
		},
		//宽度配置
		loadConfigurationSize: function loadConfigurationSize(_changeSize, _who) {
			var changeSize = _changeSize || 0,
				returnCss = "",
				cssWidth,
				cssHeight;
			if (T.isArray(_changeSize)) {
				cssWidth = "width:" + (G.appFontSize + (_changeSize[0] || 0)) + "px;";
				cssHeight = "height:" + (G.appFontSize + (_changeSize[1] || 0)) + "px;";
			} else {
				cssWidth = "width:" + (G.appFontSize + changeSize) + "px;";
				cssHeight = "height:" + (G.appFontSize + changeSize) + "px;";
			}
			if (!_who) {
				returnCss = cssWidth + cssHeight;
			} else {
				returnCss = _who == "w" ? cssWidth : cssHeight;
			}
			return returnCss;
		},
		//获取item	只有一层级的时候 会返回 当前index	_i
		getItemForKey: function getItemForKey(_value, _list, _key, _child) {
			var hasChild = false;
			if (!T.isParameters(_list)) return;
			var listLength = _list.length;
			for (var i = 0; i < listLength; i++) {
				var listItem = _list[i];
				if (T.isArray(listItem)) {
					hasChild = true;
					var result = G.getItemForKey(_value, listItem, _key, true);
					if (result) return result;
				} else {
					if (!T.isObject(listItem)) {
						if (listItem === _value) {
							return listItem;
						}
					} else {
						if (T.isArray(listItem[_key || "key"])) {
							hasChild = true;
							var result = G.getItemForKey(
								_value,
								listItem[_key || "key"],
								_key,
								true
							);
							if (result) {
								listItem["_i"] = i;
								return listItem;
							}
						} else if (listItem[_key || "key"] === _value) {
							listItem["_i"] = i;
							return listItem;
						}
					}
					if (
						T.isObject(listItem) &&
						listItem.children &&
						T.isArray(listItem.children)
					) {
						hasChild = true;
						var result = G.getItemForKey(_value, listItem.children, _key, true);
						if (result) return result;
					}
				}
			}
			if (!_child && !hasChild) return false;
		},
		//在集合中删除第一个参数obj和index都可以或对比字符串	第二个传入集合	第三个为对比key
		delItemForKey: function delItemForKey(_obj, _list, _key) {
			if (T.isTargetType(_obj, "number") && _obj < _list.length) {
				_list.splice(_obj, 1);
			} else {
				var contrastObj = !T.isObject(_obj) ? _obj : _obj[_key || "key"];
				for (var i = 0; i < _list.length; i++) {
					if (
						(!T.isObject(_list[i]) ? _list[i] : _list[i][_key || "key"]) ==
						contrastObj
					) {
						_list.splice(i, 1);
						G.delItemForKey(_obj, _list, _key);
						break;
					}
				}
			}
		},
		//是否显示顶部
		showHeader: function showHeader(_this) {
			return T.platform() == "app" || T.pageParam(_this).showHeader;
		},
		//适配多端状态栏
		headerTop: function headerTop() {
			return T.platform() == "app" ? T.safeArea().top : 0;
		},
		//底部可视区域
		footerBottom: function footerBottom(_bottom) {
			return _bottom ? T.safeArea().bottom : 0;
		},
		//适配pc页打开宽度
		fitWidth: function fitWidth() {
			if (
				T.platform() == "web" &&
				document.documentElement.clientWidth > document.documentElement.clientHeight
			) {
				$("body").style.width = "100%";
				$("body").style.maxWidth = "600px";
				$("body").style.minWidth = "300px";
				$("body").style.margin = "auto";
				$("body").style.position = "relative";
			}
		},
		//获取当前主题色相对应前景色
		getHeadThemeRelatively: function getHeadThemeRelatively(_this) {
			var theme =
				(_this && _this.data && _this.data.headTheme
					? _this.data.headTheme || ""
					: "") || G.headTheme;
			return theme && T.isColorDarkOrLight(theme) == "dark" ? "#FFF" : "#333";
		},
		//转换成html格式
		convertRichText: function convertRichText(value) {
			value = (T.isParameters(value) ? value : "") + "";
			if (T.isObject(value) || T.isArray(value)) {
				value = JSON.stringify(value);
			}
			var textList = value.split("\n");
			var str = "";
			for (var i = 0; i < textList.length; i++) {
				var addText = textList[i].replace(/&amp;/g, "&").replace(/ /g, "&nbsp;");
				if (addText) {
					str += "<p>" + addText + "</p>";
				}
			}
			return str;
		},
		//清空html格式
		clearRichText: function clearRichText(value) {
			value = (T.isParameters(value) ? value : "") + "";
			if (T.isObject(value) || T.isArray(value)) {
				value = JSON.stringify(value);
			}
			//坑爹的后台管理了 & 符号
			value = value.replace(/&amp;/g, "&");
			// 空格处理
			value = value.replace(/(&nbsp;)/g, " ");
			// 换行处理
			value = value.replace(/<br\/?[^>]*>/g, "\n");

			value = value.replace(/<\/[p|div|h1|h2|h3|h4|h5|h6]>/g, "\n");
			value = value.replace(/<\/?[^>]*>/g, "");
			return value;
		},
		//阻止冒泡事件
		stopBubble: function stopBubble(e) {
			if (!e) return;
			if (T.platform() == "web") {
				e.preventDefault();
				e.stopPropagation();
			} else if (T.platform() == "mp") {
				e.$_canBubble = false;
			}
		},
		getTagColor: function getTagColor(_key) {
			if (_key === void 0) {
				_key = "";
			}
			var tagColors = [
				{key: "未处理", value: "#F6631C"},
				{key: "未开始", value: "#F6631C"},

				{key: "签到中", value: "#F6931C"},
				{key: "报名中", value: "#F6931C"},
				{key: "进行中", value: "#F6931C"},

				{key: "请假通过", value: "#50C614"},
				{key: "请假待审批", value: "#F6931C"},
				{key: "请假中", value: "#F6931C"},
				{key: "已参与", value: G.appTheme},
				{key: "待审核", value: "#F6931C"},
				{key: "已通过", value: "#50C614"},
				{key: "有效", value: G.appTheme},
				{key: "待审查", value: "#F6931C"},
				{key: "人大交办中", value: "#F6931C"},
				{key: "政协交办中", value: "#F6931C"},
				{key: "政府交办中", value: "#F6931C"},
				{key: "党委交办中", value: "#F6931C"},
				{key: "两院交办中", value: "#F6931C"},
				{key: "法院交办中", value: "#F6931C"},
				{key: "检察院交办中", value: "#F6931C"},
				{key: "转参阅件", value: "#C61414"},
				{key: "办理中", value: "#F6931C"},
				{key: "重新办理", value: "#F6931C"},
				{key: "已答复", value: "#50C614"},
				{key: "已办结", value: "#559FFF"},
				{key: "A类", value: "#F6931C"},
				{key: "B类", value: "#1A74DA"},
				{key: "待受理", value: "#F6931C"},
				{key: "已受理", value: "#50C614"},
				{key: "已回复", value: "#50C614"},
				{key: "待交付审议", value: "#F6931C"},
				{key: "专委会审议中", value: "#F6931C"},
				{key: "已上传相关资料", value: "#50C614"},
				{key: "留存", value: "#F6931C"},
				{key: "采用", value: "#50C614"}
			];

			var tagColor = G.getItemForKey(_key, tagColors);
			return tagColor ? tagColor.value : "#666666";
		},
		//获取文件类型 并返回数据
		getFileInfo: function getFileInfo(_name) {
			if (_name === void 0) {
				_name = "";
			}
			var name = _name.toLocaleLowerCase(),
				fileInfo = {name: "file-unknow-fill", color: "#bccbd7", type: "unknown"};
			try {
				if (name.indexOf(".") != -1)
					name = name.split(".")[name.split(".").length - 1];
				switch (name) {
					case "xlsx":
					case "xlsm":
					case "xlsb":
					case "xltx":
					case "xltm":
					case "xls":
					case "xlt":
					case "et":
					case "csv":
					case "uos": //excel格式
						fileInfo.name = "file-excel-fill";
						fileInfo.color = "#00bd76";
						fileInfo.type = "excel";
						fileInfo.convertType = "0";
						break;
					case "doc":
					case "docx":
					case "docm":
					case "dotx":
					case "dotm":
					case "dot":
					case "xps":
					case "rtf":
					case "wps":
					case "wpt":
					case "uot": //word格式
						fileInfo.name = "file-word-fill";
						fileInfo.color = "#387efa";
						fileInfo.type = "word";
						fileInfo.convertType = "0";
						break;
					case "pdf": //pdf格式
						fileInfo.name = "file-pdf-fill";
						fileInfo.color = "#e9494a";
						fileInfo.type = "pdf";
						fileInfo.convertType = "20";
						break;
					case "ppt":
					case "pptx":
					case "pps":
					case "pot":
					case "pptm":
					case "potx":
					case "potm":
					case "ppsx":
					case "ppsm":
					case "ppa":
					case "ppam":
					case "dps":
					case "dpt":
					case "uop": //ppt
						fileInfo.name = "file-ppt-fill";
						fileInfo.color = "#ff7440";
						fileInfo.type = "ppt";
						fileInfo.convertType = "0";
						break;
					case "bmp":
					case "gif":
					case "jpg":
					case "pic":
					case "png":
					case "tif":
					case "jpeg":
					case "jpe":
					case "icon":
					case "jfif":
					case "dib": //图片格式 case 'webp':
						fileInfo.name = "file-text-fill";
						fileInfo.color = "#ff7440";
						fileInfo.type = "image";
						fileInfo.convertType = "440";
						break;
					case "txt": //文本
						fileInfo.name = "file-text-fill";
						fileInfo.color = "#2696ff";
						fileInfo.type = "txt";
						fileInfo.convertType = "0";
						break;
					case "rar":
					case "zip":
					case "7z":
					case "tar":
					case "gz":
					case "jar":
					case "ios": //压缩格式
						fileInfo.name = "file-zip-fill";
						fileInfo.color = "#a5b0c0";
						fileInfo.type = "compression";
						fileInfo.convertType = "19";
						break;
					case "mp4":
					case "avi":
					case "flv":
					case "f4v":
					case "webm":
					case "m4v":
					case "mov":
					case "3gp":
					case "rm":
					case "rmvb":
					case "mkv":
					case "mpeg":
					case "wmv": //视频格式
						fileInfo.name = "file-music-fill";
						fileInfo.color = "#e14a4a";
						fileInfo.type = "video";
						fileInfo.convertType = "450";
						break;
					case "mp3":
					case "m4a":
					case "amr":
					case "pcm":
					case "wav":
					case "aiff":
					case "aac":
					case "ogg":
					case "wma":
					case "flac":
					case "alac":
					case "wma":
					case "cda": //音频格式
						fileInfo.name = "file-music-fill";
						fileInfo.color = "#8043ff";
						fileInfo.type = "voice";
						fileInfo.convertType = "660";
						break;
					case "folder": //文件夹
						fileInfo.name = "folder-2-fill";
						fileInfo.color = "#ffd977";
						fileInfo.type = "folder";
						break;
				}
			} catch (e) {
				console.log(e.message);
			}
			return fileInfo;
		},
		//获取文件大小
		getFileSize: function getFileSize(_fileSize) {
			if (!_fileSize && _fileSize != 0) return "";
			try {
				var size1 = parseFloat((_fileSize / 1024 / 1024).toFixed(1));
				var size2 = parseFloat((_fileSize / 1024).toFixed(1));
				if (size1 >= 1) {
					return size1 + "MB";
				} else if (size2 >= 1) {
					return size2 + "KB";
				} else {
					return parseInt(_fileSize) + "B";
				}
			} catch (e) {
				return _fileSize;
			}
		},
		//选择文件并上传
		chooseFile: function chooseFile(_this, _item, callback) {
			var max = T.isNumber(_item.max) ? _item.max : 0;
			if (T.platform() == "app") {
				if (T.systemType() == "ios") {
					if (!T.confirmPer("storage", "chooseFile")) {
						//存储权限
						T.addEventListener("storage" + "Per_" + "chooseFile", function(ret, err) {
							T.removeEventListener("storage" + "Per_" + "chooseFile");
							if (ret.value.granted) {
								G.chooseFile(_this, _item, callback);
							}
						});
						return;
					}
				} else {
					if (!api.require("zyRongCloud").hasAllFilesPermission()) {
						T.alert(
							{
								title: "提示",
								msg: "选择本机文件需要您授权访问所有文件权限，是否继续?",
								buttons: ["确定", "取消"]
							},
							function(ret) {
								if (ret.buttonIndex == "1") {
									api.require("zyRongCloud").requestAllFilesPermission(function(ret) {
										if (ret.status) {
											G.chooseFile(_this, _item, callback);
										}
									});
								}
							}
						);
						return;
					}
				}
				var fileBrowser = api.require("fileBrowser");
				fileBrowser.open({}, function(ret, err) {
					fileBrowser.close();
					setTimeout(function() {
						_item.url = ret.url;
						G.uploadFile(_this, _item, function(ret) {
							callback && callback(ret);
						});
					}, 500);
				});
			} else if (T.platform() == "web") {
				var h5Input = document.createElement("input");
				h5Input.type = "file";
				h5Input.accept = "";
				var ua = navigator.userAgent.toLowerCase();
				var version = "";
				if (ua.indexOf("android") > 0) {
					var reg = /android [\d._]+/gi;
					var v_info = ua.match(reg);
					version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, ".");
					version = parseInt(version.split(".")[0]);
				}
				if (!version || Number(version) <= 13) {
					h5Input.multiple = "multiple";
				}
				h5Input.click();
				h5Input.onchange = function() {
					var listLength =
						max != 0 && h5Input.files.length > max ? max : h5Input.files.length;
					for (var i = 0; i < listLength; i++) {
						(function(j) {
							var nItem = JSON.parse(JSON.stringify(_item));
							nItem.url = h5Input.files[j];
							G.uploadFile(_this, nItem, function(ret) {
								callback && callback(ret);
							});
						})(i);
					}
				};
			} else if (T.platform() == "mp") {
				wx.chooseMessageFile({
					count: max != 0 ? max : 9,
					type: "file",
					success: function success(res) {
						for (var i = 0; i < res.tempFiles.length; i++) {
							(function(j) {
								var nItem = JSON.parse(JSON.stringify(_item));
								nItem.url = res.tempFiles[j];
								G.uploadFile(_this, nItem, function(ret) {
									callback && callback(ret);
								});
							})(i);
						}
					}
				});
			}
		},
		//通用上传附件 item格式  url本地地址路径 uploadId上传后的id	state状态【1上传中2完成3失败】
		uploadFile: function uploadFile(_this, _item, callback) {
			if (_item._fileAjax || _item.module == "-noUpload")
				//有传过 或者明确不传
				return;
			_item._fileAjax = true; //是否请求过	有就不再请求
			_item.state = 1;
			if (_item.showToast) {
				T.showProgress("上传中");
			}
			var nCallack = function nCallack(ret, err) {
				T.hideProgress();
				var code = ret ? ret.code : "";
				if (code == 200) {
					var data = ret.data || {};
					_item.state = 2;
					_item.uploadId = data.id;
					_item.otherInfo = data;
				} else {
					_item.state = 3;
					_item.error = ret ? ret.message || ret.data : err.data || "";
				}
				callback && callback(_item);
			};
			if (T.platform() == "mp") {
				wx.uploadFile({
					url: myjs.tomcatAddress() + "utils/proxy",
					filePath: _item.url.path,
					name: "file",
					header: {
						"Content-Type": "multipart/form-data",
						"u-login-areaId": myjs.areaId(_this),
						Authorization: T.getPrefs("sys_token") || ""
					},

					formData: {
						BASE_URL: myjs.appUrl() + "file/upload",
						BASE_TYPE: "file",
						fileName:
							_item.url.name ||
							_item.url.path.substring(_item.url.path.lastIndexOf("/") + 1)
					},

					success: function success(res) {
						nCallack(JSON.parse(res.data), null);
					},
					fail: function fail(err) {
						nCallack(null, JSON.parse(err.data));
					}
				});
			} else {
				T.ajax(
					{u: myjs.appUrl() + "file/upload", _this: _this, web: _item.web},
					"file/upload" + _item.url,
					nCallack,
					"上传附件",
					"post",
					{
						files: {file: _item.url},
						values: {a: 1}
					},
					{
						"content-type": "file" //安卓附件不能传 得用默认的
					}
				);
			}
		},
		getAreaForKey: function getAreaForKey(_key, _dotAll) {
			var rItem = null;
			var areas = JSON.parse(T.getPrefs("sys_areas") || "[]");
			if (!_dotAll || !areas.length) {
				areas = JSON.parse(T.getPrefs("sys_allAreas") || "[]");
			}
			if (areas.length) {
				rItem = G.getItemForKey(_key, areas, "id");
				if (rItem) {
					rItem.name =
						rItem.name.length > 4 ? rItem.name.substring(0, 4) + "..." : rItem.name;
				}
			}
			return rItem || {};
		},
		showTextSize: function showTextSize(_text, _size, _middle) {
			if (_size && _text) {
				_text =
					_text.length > _size
						? _middle
							? _text.substring(0, _size / 2) +
							  "..." +
							  _text.substring(_text.length - _size / 2)
							: _text.substring(0, _size) + "..."
						: _text;
			}
			return _text;
		},
		ajaxAlert: function ajaxAlert(_param, _this, _callback) {
			var _this4 = this;
			var param = {
				title: "提示",
				msg: _param.msg || "",
				buttons: _param.buttons || ["确定", "取消"]
			};

			if (_param.alertParam) {
				param = T.setNewJSON(param, _param.alertParam);
			}
			T.alert(param, function(ret) {
				if (ret.buttonIndex == "1") {
					_this4.ajaxProcess(_param, _this, _callback);
				}
			});
		},
		ajaxProcess: function ajaxProcess(_param, _this, _callback) {
			if (!_param.dotProgress) T.showProgress(_param.toast);
			T.ajax(
				{u: _param.url, _this: _this},
				"ajaxProcess",
				function(ret) {
					if (!_param.dotProgress) T.hideProgress();
					if (!_param.dotToast) T.toast(ret ? ret.message || ret.data : T.NET_ERR);
					if (ret && ret.code == "200") {
						_callback && _callback(ret);
					}
				},
				"\u64CD\u4F5C",
				"post",
				{
					body: JSON.stringify(_param.param)
				}
			);
		}
	};

	//参数是否为空
	function isParameters(_obj) {
		return _obj != null && _obj != undefined;
	}
	//是否数组
	function isArray(_obj) {
		return isParameters(_obj) && toString.apply(_obj) === "[object Array]";
	}
	//是否对象
	function isObject(_obj) {
		return isParameters(_obj) && typeof _obj === "object";
	}
	//是否方法
	function isFunction(_obj) {
		return isParameters(_obj) && typeof _obj === "function";
	}

	//合并json
	function setNewJSON(_obj, _newobj, _dotReplace) {
		_obj = _obj || {};
		_newobj = _newobj || {};
		var returnObj = {};
		for (var key in _obj) {
			returnObj[key] = _obj[key];
		}
		for (var key in _newobj) {
			if (
				(_dotReplace && isParameters(returnObj[key])) ||
				isFunction(isParameters(returnObj[key]))
			)
				continue;
			returnObj[key] =
				isArray(returnObj[key]) || !isObject(returnObj[key])
					? _newobj[key]
					: setNewJSON(returnObj[key], _newobj[key]);
		}
		return returnObj;
	}

	//获取平台类型
	function platform() {
		return api.platform;
	}

	//页面参数对象
	function pageParam(_this) {
		try {
			var pageParam =
				(_this && _this.props
					? _this.props.pageParam || (_this.props.dataMore || {}).pageParam
					: null) ||
				api.pageParam ||
				{};
			if (pageParam.paramSaveKey) {
				pageParam = JSON.parse(getPrefs(pageParam.paramSaveKey) || "{}");
			}
			if (platform() == "web" && JSON.stringify(pageParam) == "{}") {
				pageParam = getOtherParam(window.location.href);
			}
			return pageParam;
		} catch (e) {
			return {};
		}
	}

	//获取缓存
	function getPrefs(key) {
		try {
			return api.getPrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				return window.parent[key];
			} else {
				return window[key];
			}
		}
	}

	//设置缓存
	function setPrefs(key, value) {
		if (!isParameters(value)) {
			removePrefs(key);
			return;
		}
		try {
			api.setPrefs({sync: true, key: key, value: value});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				window.parent[key] = value;
			} else {
				window[key] = value;
			}
		}
	}

	//删除缓存
	function removePrefs(key) {
		try {
			api.removePrefs({sync: true, key: key});
		} catch (e) {
			if (window.parent.document.getElementsByTagName("iframe").length > 0) {
				delete window.parent[key];
			} else {
				delete window[key];
			}
		}
	}

	//发送监听
	function sendEvent(name, extra) {
		try {
			if (
				platform() == "web" &&
				window.parent.document.getElementsByTagName("iframe").length > 0
			) {
				var pageframes = window.parent.document.getElementsByTagName("iframe");
				for (var i = 0; i < pageframes.length; i++) {
					if (isArray(pageframes[i].contentWindow.baseEventList)) {
						var sendItem = getItemForKey(
							isObject(name) ? name.name : name,
							pageframes[i].contentWindow.baseEventList
						);
						if (sendItem)
							sendItem.value({value: isObject(name) ? name.extra : extra});
					}
				}
			} else {
				api.sendEvent(isObject(name) ? name : {name: name, extra: extra});
			}
		} catch (e) {}
	}
	//网络请求
	function ajax(url, tag, callback, logText, method, data, header) {
		var getUrl = url,
			dataType = "json",
			cacheType = "",
			paramData = {},
			aId = areaId(),
			isWeb = "";
		if (isObject(url)) {
			getUrl = url.u;
			dataType = url.dt || "json";
			cacheType = url.t || "";
			paramData = url.paramData || {};
			aId = url.areaId || areaId();
			isWeb = url.web || "";
		}
		var o = {
			url: getUrl,
			tag: tag,
			method: method || "get",
			cache: false,
			timeout: 120,
			dataType: dataType,
			data: isObject(data) ? data : {},
			headers: setNewJSON(
				{
					"u-login-areaId": aId,
					Authorization: getPrefs("sys_token") || "",
					"content-type": "application/json",
					"u-terminal": "APP"
				},
				header || {}
			)
		};

		o = setNewJSON(o, paramData);
		if (o.url.indexOf("push/rongCloud") != -1) {
			if (o.method == "get") {
				o.url +=
					(o.url.indexOf("?") != -1 ? "&" : "?") +
					"environment=" +
					chatEnvironment();
			} else if (o.data.values) {
				o.data.values.environment = chatEnvironment();
			}
		}
		if (isWeb) {
			(o.headers.Authorization =
				(header || {}).Authorization || getPrefs("public_token") || ""),
				(o.headers["u-terminal"] = "PUBLIC");
		}
		var oldContentType = o.headers["content-type"];
		if (oldContentType == "file") {
			delete o.headers["content-type"];
		}
		if (platform() == "app" && logText) {
			console.log(logText + o.method + "：" + JSON.stringify(o));
		}
		var cbFun = function cbFun(ret, err) {
			if (isFunction(callback)) {
				// if(isObject(err)){
				// 	try{
				// 		ret = JSON.parse(err.msg);
				// 	}catch(e){
				// 		ret = JSON.parse(JSON.stringify(err));
				// 	}
				// 	err = null;
				// }
				if (platform() == "app" && logText) {
					console.log(
						"得到" + logText + "返回结果：" + JSON.stringify(ret ? ret : err)
					);
				}
				if (isObject(ret)) {
					ret.message = ret.message || ret.msg || "";
					var errcode = ret.code || "";
					if ((errcode == 302 || errcode == 2) && cacheType != "login") {
						cleanAllMsg();
						sendEvent({
							name: "index",
							extra: {type: "verificationToken", errmsg: ret.message}
						});
					}
					// if(!isObject(ret.data)){
					// 	ret.data = "";
					// }
				}
				callback(ret, err);
			}
		};
		if (platform() == "web") {
			var xhr = new XMLHttpRequest();
			xhr.open(o.method, o.url);
			for (var header in o.headers) {
				xhr.setRequestHeader(header, o.headers[header]);
			}
			var sendValue = "";
			if (oldContentType.indexOf("x-www-form-urlencoded") != -1) {
				var dValue = o.data.values || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue +=
						(!sendValue ? "" : "&") + vItem + "=" + encodeURIComponent(dValue[vItem]);
				}
			} else if (oldContentType.indexOf("file") != -1) {
				sendValue = new FormData();
				var dValue = o.data.values || {};
				var fileValue = o.data.files || {};
				var sendBody = o.data.body || "";
				if (sendBody) {
					dValue = JSON.parse(sendBody);
				}
				for (var vItem in dValue) {
					sendValue.append(vItem, encodeURIComponent(dValue[vItem]));
				}
				for (var vItem in fileValue) {
					sendValue.append(vItem, fileValue[vItem]);
				}
			} else {
				sendValue = o.data.body || JSON.stringify(o.data.values);
			}
			xhr.onreadystatechange = function() {
				if (xhr.readyState === XMLHttpRequest.DONE) {
					var ret, err;
					if (!xhr.responseText) {
						err = {};
					} else {
						var response = this.responseText;
						if (o.dataType == "json") {
							try {
								ret = JSON.parse(response);
							} catch (e) {
								err = {msg: response};
							}
						} else {
							ret = response;
						}
					}
					cbFun(ret, err);
				}
			};
			xhr.send(sendValue);
		} else {
			api.cancelAjax({tag: tag});
			api.ajax(o, cbFun);
		}
	}

	//人大政协标识  rd人大 zx政协
	function sysSign() {
		return getPrefs("sys_sign") || "rd";
	}

	//配置地址
	function appUrl() {
		var prot = sysSign() == "rd" ? "20169" : "20170";
		return (
			getPrefs("sys_appUrl") || "https://productpc.cszysoft.com:" + prot + "/lzt/"
		);
	}
	//融云唯一前缀
	function chatHeader() {
		return getPrefs("sys_chatHeader") || "platform5" + sysSign();
	}
	//融云正测
	function chatEnvironment() {
		return getPrefs("sys_chatEnvironment") || "1";
	}
	//当前页面地区id
	function areaId() {
		return (
			pageParam().areaId ||
			getPrefs("sys_aresId") ||
			getPrefs("sys_platform") ||
			""
		);
	}

	var SECONDS_A_MINUTE = 60;
	var SECONDS_A_HOUR = SECONDS_A_MINUTE * 60;
	var SECONDS_A_DAY = SECONDS_A_HOUR * 24;
	var SECONDS_A_WEEK = SECONDS_A_DAY * 7;
	var MILLISECONDS_A_SECOND = 1e3;
	var MILLISECONDS_A_MINUTE = SECONDS_A_MINUTE * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_HOUR = SECONDS_A_HOUR * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_DAY = SECONDS_A_DAY * MILLISECONDS_A_SECOND;
	var MILLISECONDS_A_WEEK = SECONDS_A_WEEK * MILLISECONDS_A_SECOND; // English locales

	var MS = "millisecond";
	var S = "second";
	var MIN = "minute";
	var H = "hour";
	var D = "day";
	var W = "week";
	var M = "month";
	var Q = "quarter";
	var Y = "year";
	var DATE = "date";
	var FORMAT_DEFAULT = "YYYY-MM-DDTHH:mm:ssZ";
	var INVALID_DATE_STRING = "Invalid Date"; // regex

	var REGEX_PARSE = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/;
	var REGEX_FORMAT = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;

	var en = {
		name: "en",
		weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split(
			"_"
		),
		months: "January_February_March_April_May_June_July_August_September_October_November_December".split(
			"_"
		)
	};

	var padStart = function padStart(string, length, pad) {
		var s = String(string);
		if (!s || s.length >= length) return string;
		return "" + Array(length + 1 - s.length).join(pad) + string;
	};
	var padZoneStr = function padZoneStr(instance) {
		var negMinutes = -instance.utcOffset();
		var minutes = Math.abs(negMinutes);
		var hourOffset = Math.floor(minutes / 60);
		var minuteOffset = minutes % 60;
		return (
			"" +
			(negMinutes <= 0 ? "+" : "-") +
			padStart(hourOffset, 2, "0") +
			":" +
			padStart(minuteOffset, 2, "0")
		);
	};
	var monthDiff = function monthDiff(a, b) {
		if (a.date() < b.date()) return -monthDiff(b, a);
		var wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month());
		var anchor = a.clone().add(wholeMonthDiff, M);
		var c = b - anchor < 0;
		var anchor2 = a.clone().add(wholeMonthDiff + (c ? -1 : 1), M);
		return +(
			-(
				wholeMonthDiff +
				(b - anchor) / (c ? anchor - anchor2 : anchor2 - anchor)
			) || 0
		);
	};
	var absFloor = function absFloor(n) {
		return n < 0 ? Math.ceil(n) || 0 : Math.floor(n);
	};
	var prettyUnit = function prettyUnit(u) {
		var special = {
			M: M,
			y: Y,
			w: W,
			d: D,
			D: DATE,
			h: H,
			m: MIN,
			s: S,
			ms: MS,
			Q: Q
		};

		return (
			special[u] ||
			String(u || "")
				.toLowerCase()
				.replace(/s$/, "")
		);
	};
	var isUndefined = function isUndefined(s) {
		return s === undefined;
	};
	var U = {
		s: padStart,
		z: padZoneStr,
		m: monthDiff,
		a: absFloor,
		p: prettyUnit,
		u: isUndefined
	};

	var L = "en";
	var Ls = {};
	Ls[L] = en;
	var isDayjs = function isDayjs(d) {
		return d instanceof Dayjs;
	};
	var parseLocale = function parseLocale(preset, object, isLocal) {
		var l;
		if (!preset) return L;
		if (typeof preset === "string") {
			var presetLower = preset.toLowerCase();
			if (Ls[presetLower]) {
				l = presetLower;
			}
			if (object) {
				Ls[presetLower] = object;
				l = presetLower;
			}
			var presetSplit = preset.split("-");
			if (!l && presetSplit.length > 1) {
				return parseLocale(presetSplit[0]);
			}
		} else {
			var name = preset.name;
			Ls[name] = preset;
			l = name;
		}
		if (!isLocal && l) L = l;
		return l || (!isLocal && L);
	};
	var dayjs = function dayjs(date, c) {
		if (isDayjs(date)) {
			return date.clone();
		}
		var cfg = typeof c === "object" ? c : {};
		cfg.date = date;
		cfg.args = arguments;
		return new Dayjs(cfg);
	};
	var wrapper = function wrapper(date, instance) {
		return dayjs(date, {
			locale: instance.$L,
			utc: instance.$u,
			x: instance.$x,
			$offset: instance.$offset
		});
	};
	var Utils = U;
	Utils.l = parseLocale;
	Utils.i = isDayjs;
	Utils.w = wrapper;
	var parseDate = function parseDate(cfg) {
		var date = cfg.date,
			utc = cfg.utc;
		if (date === null) return new Date(NaN);
		if (Utils.u(date)) return new Date();
		if (date instanceof Date) return new Date(date);
		if (typeof date === "string" && !/Z$/i.test(date)) {
			var d = date.match(REGEX_PARSE);
			if (d) {
				var m = d[2] - 1 || 0;
				var ms = (d[7] || "0").substring(0, 3);
				if (utc) {
					return new Date(
						Date.UTC(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms)
					);
				}
				return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);
			}
		}
		return new Date(date);
	};
	var Dayjs = (function() {
		function Dayjs(cfg) {
			this.$L = parseLocale(cfg.locale, null, true);
			this.parse(cfg);
		}
		var _proto = Dayjs.prototype;
		_proto.parse = function parse(cfg) {
			this.$d = parseDate(cfg);
			this.$x = cfg.x || {};
			this.init();
		};
		_proto.init = function init() {
			var $d = this.$d;
			this.$y = $d.getFullYear();
			this.$M = $d.getMonth();
			this.$D = $d.getDate();
			this.$W = $d.getDay();
			this.$H = $d.getHours();
			this.$m = $d.getMinutes();
			this.$s = $d.getSeconds();
			this.$ms = $d.getMilliseconds();
		};
		_proto.$utils = function $utils() {
			return Utils;
		};
		_proto.isValid = function isValid() {
			return !(this.$d.toString() === INVALID_DATE_STRING);
		};
		_proto.isSame = function isSame(that, units) {
			var other = dayjs(that);
			return this.startOf(units) <= other && other <= this.endOf(units);
		};
		_proto.isAfter = function isAfter(that, units) {
			return dayjs(that) < this.startOf(units);
		};
		_proto.isBefore = function isBefore(that, units) {
			return this.endOf(units) < dayjs(that);
		};
		_proto.$g = function $g(input, get, set) {
			if (Utils.u(input)) return this[get];
			return this.set(set, input);
		};
		_proto.unix = function unix() {
			return Math.floor(this.valueOf() / 1000);
		};
		_proto.valueOf = function valueOf() {
			return this.$d.getTime();
		};
		_proto.startOf = function startOf(units, _startOf) {
			var _this = this;
			var isStartOf = !Utils.u(_startOf) ? _startOf : true;
			var unit = Utils.p(units);
			var instanceFactory = function instanceFactory(d, m) {
				var ins = Utils.w(
					_this.$u ? Date.UTC(_this.$y, m, d) : new Date(_this.$y, m, d),
					_this
				);
				return isStartOf ? ins : ins.endOf(D);
			};
			var instanceFactorySet = function instanceFactorySet(method, slice) {
				var argumentStart = [0, 0, 0, 0];
				var argumentEnd = [23, 59, 59, 999];
				return Utils.w(
					_this
						.toDate()
						[method].apply(
							_this.toDate("s"),
							(isStartOf ? argumentStart : argumentEnd).slice(slice)
						),
					_this
				);
			};
			var $W = this.$W,
				$M = this.$M,
				$D = this.$D;
			var utcPad = "set" + (this.$u ? "UTC" : "");
			switch (unit) {
				case Y:
					return isStartOf ? instanceFactory(1, 0) : instanceFactory(31, 11);
				case M:
					return isStartOf ? instanceFactory(1, $M) : instanceFactory(0, $M + 1);
				case W: {
					var weekStart = this.$locale().weekStart || 0;
					var gap = ($W < weekStart ? $W + 7 : $W) - weekStart;
					return instanceFactory(isStartOf ? $D - gap : $D + (6 - gap), $M);
				}
				case D:
				case DATE:
					return instanceFactorySet(utcPad + "Hours", 0);
				case H:
					return instanceFactorySet(utcPad + "Minutes", 1);
				case MIN:
					return instanceFactorySet(utcPad + "Seconds", 2);
				case S:
					return instanceFactorySet(utcPad + "Milliseconds", 3);
				default:
					return this.clone();
			}
		};
		_proto.endOf = function endOf(arg) {
			return this.startOf(arg, false);
		};
		_proto.$set = function $set(units, _int) {
			var _C$D$C$DATE$C$M$C$Y$C;
			var unit = Utils.p(units);
			var utcPad = "set" + (this.$u ? "UTC" : "");
			var name = ((_C$D$C$DATE$C$M$C$Y$C = {}),
			(_C$D$C$DATE$C$M$C$Y$C[D] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[DATE] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[M] = utcPad + "Month"),
			(_C$D$C$DATE$C$M$C$Y$C[Y] = utcPad + "FullYear"),
			(_C$D$C$DATE$C$M$C$Y$C[H] = utcPad + "Hours"),
			(_C$D$C$DATE$C$M$C$Y$C[MIN] = utcPad + "Minutes"),
			(_C$D$C$DATE$C$M$C$Y$C[S] = utcPad + "Seconds"),
			(_C$D$C$DATE$C$M$C$Y$C[MS] = utcPad + "Milliseconds"),
			_C$D$C$DATE$C$M$C$Y$C)[unit];
			var arg = unit === D ? this.$D + (_int - this.$W) : _int;
			if (unit === M || unit === Y) {
				var date = this.clone().set(DATE, 1);
				date.$d[name](arg);
				date.init();
				this.$d = date.set(DATE, Math.min(this.$D, date.daysInMonth())).$d;
			} else if (name) this.$d[name](arg);
			this.init();
			return this;
		};
		_proto.set = function set(string, _int2) {
			return this.clone().$set(string, _int2);
		};
		_proto.get = function get(unit) {
			return this[Utils.p(unit)]();
		};
		_proto.add = function add(number, units) {
			var _this2 = this,
				_C$MIN$C$H$C$S$unit;
			number = Number(number);
			var unit = Utils.p(units);
			var instanceFactorySet = function instanceFactorySet(n) {
				var d = dayjs(_this2);
				return Utils.w(d.date(d.date() + Math.round(n * number)), _this2);
			};
			if (unit === M) {
				return this.set(M, this.$M + number);
			}
			if (unit === Y) {
				return this.set(Y, this.$y + number);
			}
			if (unit === D) {
				return instanceFactorySet(1);
			}
			if (unit === W) {
				return instanceFactorySet(7);
			}
			var step =
				((_C$MIN$C$H$C$S$unit = {}),
				(_C$MIN$C$H$C$S$unit[MIN] = MILLISECONDS_A_MINUTE),
				(_C$MIN$C$H$C$S$unit[H] = MILLISECONDS_A_HOUR),
				(_C$MIN$C$H$C$S$unit[S] = MILLISECONDS_A_SECOND),
				_C$MIN$C$H$C$S$unit)[unit] || 1;
			var nextTimeStamp = this.$d.getTime() + number * step;
			return Utils.w(nextTimeStamp, this);
		};
		_proto.subtract = function subtract(number, string) {
			return this.add(number * -1, string);
		};
		_proto.format = function format(formatStr) {
			var _this3 = this;
			var locale = this.$locale();
			if (!this.isValid()) return locale.invalidDate || INVALID_DATE_STRING;
			var str = formatStr || FORMAT_DEFAULT;
			var zoneStr = Utils.z(this);
			var $H = this.$H,
				$m = this.$m,
				$M = this.$M;
			var weekdays = locale.weekdays,
				months = locale.months,
				meridiem = locale.meridiem;
			var getShort = function getShort(arr, index, full, length) {
				return (
					(arr && (arr[index] || arr(_this3, str))) || full[index].slice(0, length)
				);
			};
			var get$H = function get$H(num) {
				return Utils.s($H % 12 || 12, num, "0");
			};
			var meridiemFunc =
				meridiem ||
				function(hour, minute, isLowercase) {
					var m = hour < 12 ? "AM" : "PM";
					return isLowercase ? m.toLowerCase() : m;
				};
			var matches = {
				YY: String(this.$y).slice(-2),
				YYYY: this.$y,
				M: $M + 1,
				MM: Utils.s($M + 1, 2, "0"),
				MMM: getShort(locale.monthsShort, $M, months, 3),
				MMMM: getShort(months, $M),
				D: this.$D,
				DD: Utils.s(this.$D, 2, "0"),
				d: String(this.$W),
				dd: getShort(locale.weekdaysMin, this.$W, weekdays, 2),
				ddd: getShort(locale.weekdaysShort, this.$W, weekdays, 3),
				dddd: weekdays[this.$W],
				H: String($H),
				HH: Utils.s($H, 2, "0"),
				h: get$H(1),
				hh: get$H(2),
				a: meridiemFunc($H, $m, true),
				A: meridiemFunc($H, $m, false),
				m: String($m),
				mm: Utils.s($m, 2, "0"),
				s: String(this.$s),
				ss: Utils.s(this.$s, 2, "0"),
				SSS: Utils.s(this.$ms, 3, "0"),
				Z: zoneStr
			};

			return str.replace(REGEX_FORMAT, function(match, $1) {
				return $1 || matches[match] || zoneStr.replace(":", "");
			});
		};
		_proto.utcOffset = function utcOffset() {
			return -Math.round(this.$d.getTimezoneOffset() / 15) * 15;
		};
		_proto.diff = function diff(input, units, _float) {
			var _C$Y$C$M$C$Q$C$W$C$D$;
			var unit = Utils.p(units);
			var that = dayjs(input);
			var zoneDelta =
				(that.utcOffset() - this.utcOffset()) * MILLISECONDS_A_MINUTE;
			var diff = this - that;
			var result = Utils.m(this, that);
			result =
				((_C$Y$C$M$C$Q$C$W$C$D$ = {}),
				(_C$Y$C$M$C$Q$C$W$C$D$[Y] = result / 12),
				(_C$Y$C$M$C$Q$C$W$C$D$[M] = result),
				(_C$Y$C$M$C$Q$C$W$C$D$[Q] = result / 3),
				(_C$Y$C$M$C$Q$C$W$C$D$[W] = (diff - zoneDelta) / MILLISECONDS_A_WEEK),
				(_C$Y$C$M$C$Q$C$W$C$D$[D] = (diff - zoneDelta) / MILLISECONDS_A_DAY),
				(_C$Y$C$M$C$Q$C$W$C$D$[H] = diff / MILLISECONDS_A_HOUR),
				(_C$Y$C$M$C$Q$C$W$C$D$[MIN] = diff / MILLISECONDS_A_MINUTE),
				(_C$Y$C$M$C$Q$C$W$C$D$[S] = diff / MILLISECONDS_A_SECOND),
				_C$Y$C$M$C$Q$C$W$C$D$)[unit] || diff;
			return _float ? result : Utils.a(result);
		};
		_proto.daysInMonth = function daysInMonth() {
			return this.endOf(M).$D;
		};
		_proto.$locale = function $locale() {
			return Ls[this.$L];
		};
		_proto.locale = function locale(preset, object) {
			if (!preset) return this.$L;
			var that = this.clone();
			var nextLocaleName = parseLocale(preset, object, true);
			if (nextLocaleName) that.$L = nextLocaleName;
			return that;
		};
		_proto.clone = function clone() {
			return Utils.w(this.$d, this);
		};
		_proto.toDate = function toDate() {
			return new Date(this.valueOf());
		};
		_proto.toJSON = function toJSON() {
			return this.isValid() ? this.toISOString() : null;
		};
		_proto.toISOString = function toISOString() {
			return this.$d.toISOString();
		};
		_proto.toString = function toString() {
			return this.$d.toUTCString();
		};
		return Dayjs;
	})();
	var proto = Dayjs.prototype;
	dayjs.prototype = proto;
	[
		["$ms", MS],
		["$s", S],
		["$m", MIN],
		["$H", H],
		["$W", D],
		["$M", M],
		["$y", Y],
		["$D", DATE]
	].forEach(function(g) {
		proto[g[1]] = function(input) {
			return this.$g(input, g[0], g[1]);
		};
	});
	dayjs.extend = function(plugin, option) {
		if (!plugin.$i) {
			plugin(option, Dayjs, dayjs);
			plugin.$i = true;
		}
		return dayjs;
	};
	dayjs.locale = parseLocale;
	dayjs.isDayjs = isDayjs;
	dayjs.unix = function(timestamp) {
		return dayjs(timestamp * 1e3);
	};
	dayjs.en = Ls[L];
	dayjs.Ls = Ls;
	dayjs.p = {};

	var module6 = {
		name: sysSign() == "rd" ? "意见征集" : "网络议政",
		code: "6",
		businessCode: "opinioncollect"
	};
	var module9 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息",
		code: "9",
		businessCode: "cppcc_member"
	};
	var module9_1 = {
		name: (sysSign() == "rd" ? "代表" : "委员") + "信息变更申请",
		code: "9_1",
		businessCode: "cppccMemberCheckPrepare"
	};
	var module12 = {
		name: sysSign() == "rd" ? "圈子" : "委员说",
		code: "12",
		businessCode: "styleCircle"
	};

	//获取登录信息
	function getLoginInfo(_param, _callback) {
		ajax(
			{u: appUrl() + "login/user"},
			"login/user",
			function(ret, err) {
				if (ret && ret.code == 200 && ret.data.id != "anonymous") {
					saveLogin(ret.data);
				}
				_callback && _callback(ret, err);
			},
			"用户信息",
			"post",
			{
				values: _param.param
			},
			_param.header
		);
	}

	function getAllChatInfo() {
		var chatInfos = JSON.parse(getPrefs("chatInfos") || "[]"),
			addInfo = [];
		chatInfos.forEach(function(_eItem) {
			if (_eItem.id) {
				if (/^(?!h|(\.)|(\/)).*/.test(_eItem.url)) {
					_eItem.url = showImg(_eItem.url);
				}
				addInfo.push(_eItem);
			}
		});
		G$1.chatInfos = addInfo;
		setPrefs("chatInfos", JSON.stringify(G$1.chatInfos));
		return G$1.chatInfos;
	}

	function setAllChatInfo(_item) {
		getAllChatInfo();
		var nowInfo = JSON.parse(JSON.stringify(G$1.chatInfos));
		if (isArray(_item)) {
			_item.forEach(function(_eItem) {
				if (_eItem.id) {
					delItemForKey(_eItem.id, nowInfo, "id");
					nowInfo.push(_eItem);
				}
			});
		} else {
			if (!_item.id) {
				return;
			}
			delItemForKey(_item.id, nowInfo, "id");
			nowInfo.push(_item);
		}
		setPrefs("chatInfos", JSON.stringify(nowInfo));
		setRongChatInfo();
	}

	function setRongChatInfo() {
		if (platform() == "app" && api.require("zyRongCloudRTC")) {
			getAllChatInfo();
			var setUser = {
				apply: 1,
				users: G$1.chatInfos.map(function(obj) {
					return {id: chatHeader() + obj.id, name: obj.name, url: obj.url};
				}),
				groupUser: JSON.parse(getPrefs("rongGroupUser") || "[]"), //当前通话的群组成员列表id集合
				totalNumber: 10 //语音或视频最大人数
			};
			// console.log("融云设置信息："+JSON.stringify(setUser));
			api.require("zyRongCloudRTC").setUsers(setUser, function(ret) {
				removePrefs("rongGroupUser");
				setRongChatInfo();
				setTimeout(
					function() {
						sendEvent({name: "chat_refresh"});
					},
					api.systemType == "ios" ? 600 : 1500
				);
			});
		}
	}

	//全局页面引用变量
	var G$1 = {
		sysSign: sysSign(), //人大政协标识
		pageWidth: "", //页面总宽度
		onShowNum: -1, //当前页面展示次数 1为首次
		appName: "", //app名字
		appFont: "", //app全局字体
		appFontSize: 0, //app全局字体大小
		appTheme: "", //app全局主题色
		headTheme: "", //head全局主题色
		headColor: "", //标题前景色
		headTitle: "", //标题栏文字
		loginInfo: "",

		careMode: false, //是否启用了关怀模式
		systemtTypeIsPlatform: false, //系统类型是否是平台版
		isAppReview: false, //app是否上架期间 隐藏和显示部分功能
		v: "", //缓存版本号

		uId: "", //普通用户id
		userId: "", //当前用户id 账号id
		userName: "", //当前用户名字
		userImg: "", //当前用户头像
		areaId: "", //当前地区id
		specialRoleKeys: [], //当前用户角色集合
		isAdmin: false, //是否管理员 拥有所有权限
		grayscale: "", //全局置灰
		watermark: "", //全局水印

		// chatInfos:[],
		// chatInfoTask:[],

		alertPop: {
			// alert提示框
			show: false
		},

		actionSheetPop: {
			//actionSheet弹出框
			show: false
		},

		imgPreviewerPop: {
			//图片预览
			show: false
		},

		areasPop: {
			// 全局地区弹窗
			show: false
		},

		numInputPop: {
			// 全局数字弹窗
			show: false
		},

		favoritePop: {
			//收藏弹窗
			show: false
		},

		sharePosterPop: {
			//分享海报
			show: false
		},

		sharePop: {
			//分享
			show: false
		},

		identifyAudioPop: {
			//语音输入
			show: false
		},

		selectPop: {
			//单多选
			show: false
		},

		qrcodePop: {
			//h5扫码
			show: false
		},

		addressBookPop: {
			//通讯录
			show: false
		},

		fileListPop: {
			//选择文件
			show: false
		}
	};

	//获取item参数
	function getItemForKey(_value, _list, _key, _child) {
		if (!isParameters(_list)) return;
		var hasChild = false,
			listLength = _list.length;
		for (var i = 0; i < listLength; i++) {
			var listItem = _list[i];
			if (isArray(listItem)) {
				hasChild = true;
				var result = getItemForKey(_value, listItem, _key, true);
				if (result) return result;
			} else {
				if (!isObject(listItem)) {
					if (listItem === _value) return listItem;
				} else {
					var listItemKey = listItem[_key || "key"];
					if (isArray(listItemKey)) {
						hasChild = true;
						var result = getItemForKey(_value, listItemKey, _key, true);
						if (result) {
							return listItem;
						}
					} else if (!isObject(listItemKey) && listItemKey === _value) {
						listItem["_i"] = i;
						return listItem;
					}
				}
				if (isObject(listItem) && isArray(listItem.children)) {
					hasChild = true;
					var result = getItemForKey(_value, listItem.children, _key, true);
					if (result) return result;
				}
			}
		}
		if (!_child && !hasChild) return false;
	}

	//删除item中的元素
	function delItemForKey(_obj, _list, _key) {
		var contrastObj = !isObject(_obj) ? _obj : _obj[_key || "key"];
		for (var i = 0; i < _list.length; i++) {
			if (
				(!isObject(_list[i]) ? _list[i] : _list[i][_key || "key"]) === contrastObj
			) {
				_list.splice(i, 1);
				delItemForKey(_obj, _list, _key);
				break;
			}
		}
	}

	//适配图片
	function showImg(_item, _add) {
		if (_add === void 0) {
			_add = "";
		}
		var baseUrl = isObject(_item) ? _item.url || "" : _item || "";
		if (!baseUrl) return;
		if (/^(?!h|(\.)|(\/)).*/.test(baseUrl)) {
			baseUrl =
				appUrl() +
				"image/" +
				_add +
				(baseUrl.indexOf("-compress-") > -1
					? baseUrl.split("-compress-")[1]
					: baseUrl);
		}
		return baseUrl + (baseUrl.indexOf("?") != -1 ? "&" : "?") + "v=" + G$1.v;
	}

	//删除所有缓存
	function cleanAllMsg() {
		var exitPrefs = [
			"isAutoLogin",
			"loginPassword",
			"sys_token",
			"sys_Id",
			"sys_UserID",
			"sys_UserName",
			"sys_AppPhoto",
			"sys_Mobile",
			"sys_Position",
			"sys_aresId",
			"sys_OfficeId",
			"sys_SpecialRoleKeys",
			"sdt_signin_phone",
			"public_token",
			"last14Msg",
			"sys_unread"
		];
		exitPrefs.forEach(function(_eItem) {
			removePrefs(_eItem);
		});
	}

	//保存登录信息
	function saveLogin(_info) {
		var infolist = _info || {};
		setPrefs("sys_Id", infolist.id);
		setPrefs("sys_UserID", infolist.accountId);
		setPrefs("sys_UserName", infolist.userName);
		setPrefs("sys_AppPhoto", infolist.headImg || infolist.photo); //headImg用户头像 photo代表头像
		setPrefs("sys_Mobile", infolist.mobile);
		setPrefs("sys_Position", infolist.position);
		if (!getPrefs("sys_aresId")) {
			setPrefs("sys_aresId", infolist.areaId);
		}
		setPrefs("sys_OfficeId", infolist.officeId);
		setPrefs(
			"sys_SpecialRoleKeys",
			JSON.stringify(infolist.specialRoleKeys || [])
		);
		if (infolist.id) {
			// //修改头像后保存下缓存数据
			setAllChatInfo({
				id: infolist.accountId,
				name: infolist.userName,
				url: infolist.headImg || infolist.photo
			});
		}
		sendEvent({name: "sys_refresh"});
	}

	//获取链接中参数
	function getOtherParam(_url) {
		if (_url.indexOf("?") != -1) {
			_url = _url.substring(_url.indexOf("?") + 1);
		}
		var params = _url.split("&"),
			rp = {};
		for (var j = 0; j < params.length; j++) {
			if (params[j]) {
				var data_key = params[j].substring(0, params[j].indexOf("="));
				if (!data_key) {
					continue;
				}
				var data_value = decodeURIComponent(
					params[j].substring(params[j].indexOf("=") + 1)
				);
				if (data_value.indexOf("{") == 0 || data_value.indexOf("[") == 0)
					data_value = JSON.parse(data_value);
				rp[data_key] = data_value;
			}
		}
		return rp;
	}

	// import dayjs from "./dayjs";
	// import { MD5 } from './crypto-ts.js';
	/**
	 * 封装和适配 api相关所有接口 和一些别的
	 */
	var T = {};
	T.NET_ERR = "网络不小心断开了";
	T.NET_OK = "操作成功";
	T.NET_NO = "操作失败，请重试";
	T.JK_ERR = "接口异常，请联系技术";
	T.LOAD_ING = "加载中，请稍候...";
	T.LOAD_MORE = "点击加载更多";
	T.LOAD_ALL = "已加载完";
	T.LOAD_NO = "暂无数据";
	T.LOAD_NOT = "页面尚未加载完成，请刷新重试";

	T.isParameters = function(obj) {
		return obj != null && obj != undefined;
	};
	T.isTargetType = function(obj, typeString) {
		return typeof obj === typeString;
	};
	T.isNumber = function(obj) {
		return T.isParameters(obj) && T.isTargetType(obj, "number");
	};
	T.isObject = function(obj) {
		return T.isParameters(obj) && T.isTargetType(obj, "object");
	};
	T.isArray = function(obj) {
		return T.isParameters(obj) && toString.apply(obj) === "[object Array]";
	};
	T.isFunction = function(obj) {
		return T.isParameters(obj) && T.isTargetType(obj, "function");
	};

	T.setNewJSON = function(obj, newobj, _ifReplace) {
		obj = obj || {};
		newobj = newobj || {};
		var returnObj = {};
		for (var key in obj) {
			returnObj[key] = obj[key];
		}
		for (var key in newobj) {
			if (_ifReplace && returnObj.hasOwnProperty(key)) {
				//是否不替换前者 默认替换
				continue;
			}
			returnObj[key] = newobj[key];
		}
		return returnObj;
	};

	T.getNum = function() {
		return Math.round(Math.random() * 1000000000);
	};

	T.trim = function(str) {
		if (String.prototype.trim) {
			return str == null ? "" : String.prototype.trim.call(str);
		} else {
			return str.replace(/(^\s*)|(\s*$)/g, "");
		}
	};
	T.trimAll = function(str) {
		return str.replace(/\s*/g, "");
	};
	T.removeTag = function(str) {
		if (!str) return str;
		return T.decodeCharacter(
			str
				.replace(/<!--[\w\W\r\n]*?-->/gim, "")
				.replace(/(<[^\s\/>]+)\b[^>]*>/gi, "$1>")
				.replace(/<[^>]+>/g, "")
				.replace(/\s*/g, "")
		);
	};
	T.decodeCharacter = function(str) {
		if (!str) return str;
		return str
			.replace(/&amp;/g, "&")
			.replace(/(&nbsp;|&ensp;)/g, " ")
			.replace(/&mdash;/g, "—")
			.replace(/&ldquo;/g, "“")
			.replace(/&rsquo;/g, "’")
			.replace(/&lsquo;/g, "‘")
			.replace(/&rdquo;/g, "”")
			.replace(/&middot;/g, "·")
			.replace(/&hellip;/g, "…")
			.replace(/&quot;/g, '"')
			.replace(/&lt;/g, "<")
			.replace(/&gt;/g, ">");
	};

	T.platform = function() {
		try {
			return api.platform || "";
		} catch (e) {
			return "";
		}
	};

	T.rebootApp = function() {
		if (T.platform() == "web") {
			window.location.reload();
		} else if (T.platform() == "mp") {
			wx.reLaunch({url: "/pages/index/index"});
		} else {
			api.rebootApp();
		}
	};

	T.appName = function() {
		try {
			return myjs.appName || "";
		} catch (e) {
			return "";
		}
	};

	T.systemType = function() {
		try {
			return api.systemType;
		} catch (e) {
			return "";
		}
	};

	T.pageParam = function(_this) {
		try {
			var pageParam =
				(_this && _this.props ? _this.props.pageParam : null) ||
				api.pageParam ||
				{};
			if (pageParam.paramSaveKey) {
				pageParam = JSON.parse(T.getPrefs(pageParam.paramSaveKey) || "{}");
			}
			if (T.platform() == "web" && JSON.stringify(pageParam) == "{}") {
				pageParam = getOtherParam(window.location.href);
			}
			return pageParam;
		} catch (e) {
			return {};
		}
	};

	T.safeArea = function() {
		try {
			return api.safeArea;
		} catch (e) {
			return {top: 0, left: 0, bottom: 0, right: 0};
		}
	};

	T.setPrefs = function(key, value) {
		if (!T.isParameters(value)) {
			T.removePrefs(key);
			return;
		}
		try {
			api.setPrefs({sync: true, key: key, value: value});
		} catch (e) {}
	};

	T.getPrefs = function(key) {
		try {
			return api.getPrefs({sync: true, key: key});
		} catch (e) {
			return "";
		}
	};

	T.removePrefs = function(key) {
		try {
			api.removePrefs({sync: true, key: key});
		} catch (e) {}
	};

	T.addEventListener = function(name, callback) {
		var keyback = function keyback(ret, err) {
			T.isFunction(callback) && callback(ret, err);
		};
		if (
			T.platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			if (!window.baseEventList) {
				window.baseEventList = [];
			}
			if (G.getItemForKey(name, window.baseEventList)) {
				G.delItemForKey(name, window.baseEventList);
			}
			window.baseEventList.push({
				key: name,
				value: keyback
			});
		} else {
			api.addEventListener({name: name}, keyback);
		}
	};

	T.removeEventListener = function(name) {
		if (
			T.platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			G.delItemForKey(name, window.baseEventList);
		} else {
			api.removeEventListener({name: name});
		}
	};

	T.sendEvent = function(name, extra) {
		if (
			T.platform() == "web" &&
			window.parent.document.getElementsByTagName("iframe").length > 0
		) {
			var pageframes = window.parent.document.getElementsByTagName("iframe");
			for (var i = 0; i < pageframes.length; i++) {
				if (T.isArray(pageframes[i].contentWindow.baseEventList)) {
					var sendItem = G.getItemForKey(
						T.isObject(name) ? name.name : name,
						pageframes[i].contentWindow.baseEventList
					);
					if (sendItem) {
						sendItem.value({value: T.isObject(name) ? name.extra : extra});
					}
				}
			}
		} else {
			try {
				api.sendEvent(T.isObject(name) ? name : {name: name, extra: extra});
			} catch (e) {}
		}
	};

	T.removeLaunchView = function() {
		try {
			api.removeLaunchView();
		} catch (e) {}
	};

	T.setScreenOrientation = function(orientation) {
		try {
			api.setScreenOrientation({orientation: orientation});
		} catch (e) {}
	};

	T.cancelAjax = function(name) {
		try {
			api.cancelAjax({tag: name});
		} catch (e) {}
	};

	T.ajax = function(url, tag, callback, logText, method, data, header) {
		if (header === void 0) {
			header = {};
		}
		T.cancelAjax(tag);
		var getUrl = url; //请求链接
		var frequency = 0; //网络异常 重复请求次数
		var dataType = "json"; //返回数据类型
		var cacheType = ""; //请求类型
		var paramData = {};
		var areaId = "";
		var timeout = 0;
		var isWeb = "";
		if (T.isObject(url)) {
			getUrl = url.u; //请求链接
			dataType = url.dt || "json"; //
			cacheType = url.t || ""; //
			frequency = url.frequency || 0;
			paramData = url.paramData || {};
			areaId = url.areaId || myjs.areaId(url._this);
			timeout = url.timeout || 0;
			isWeb = url.web || "";
		}
		var o = {
			url: getUrl,
			tag: tag,
			method: method ? method : "get",
			cache: false,
			timeout: 50,
			dataType: dataType,
			data: T.isObject(data) ? data : {},
			headers: T.setNewJSON(
				{
					"u-login-areaId": areaId,
					Authorization: T.getPrefs("sys_token") || "",
					"content-type": "application/json",
					"u-terminal": "APP"
				},
				header
			)
		};

		o = T.setNewJSON(o, paramData);
		if (T.platform() == "web") {
			delete o.tag;
		}
		if (o.url.indexOf("push/rongCloud") != -1) {
			//融云接口
			if (o.method == "get") {
				o.url +=
					(o.url.indexOf("?") != -1 ? "&" : "?") +
					"environment=" +
					myjs.chatEnvironment();
			} else if (o.data.values) {
				o.data.values.environment = myjs.chatEnvironment();
			}
		}
		if (
			isWeb &&
			JSON.stringify(o.data) != "{}" &&
			(o.data.body || JSON.stringify(o.data.values) != "{}")
		) {
			//公众端通用 过期时间不传token且置空public_token
			var webToken = "";
			if (
				T.getPrefs("tokenEndTime") &&
				new Date().getTime() < T.getPrefs("tokenEndTime")
			) {
				webToken = T.getPrefs("public_token") || "";
			} else {
				T.removePrefs("public_token");
			}
			(o.headers.Authorization = header.Authorization || webToken || ""),
				(o.headers["u-terminal"] = "PUBLIC");
			// var isBody = o.data.body?true:false;
			// var postParam = isBody?JSON.parse(o.data.body):o.data.values;
			// var signParam = {};
			// function getParam(_obj){
			// 	// console.log(JSON.stringify(_obj));
			// 	for (var key in _obj) {
			// 		var kValue = _obj[key];
			// 		if(T.isObject(kValue) && !T.isArray(kValue)){
			// 			getParam(kValue);
			// 		}else{
			// 			kValue = T.isArray(kValue)?kValue.join(","):kValue;
			// 			if (signParam.hasOwnProperty(key)) {
			// 				signParam[key] += (signParam[key]?',':'') + kValue;
			// 			}else{
			// 				signParam[key] = kValue;
			// 			}
			// 		}
			// 	}
			// }
			// getParam(postParam);
			// var signStr = T.sort_ascii(signParam,"#");
			// postParam.clientId = M.clientId;
			// postParam.token = isWeb;
			// postParam.timestamp = dayjs().valueOf();
			// postParam.nonce = "zyrd";
			// postParam.sign = MD5(signStr + "#" + M.clientId + isWeb + postParam.timestamp + postParam.nonce).toString().toUpperCase()
			// if(isBody){
			// 	o.data.body = JSON.stringify(postParam);
			// }
		}
		var oldContentType = o.headers["content-type"];
		if (myjs.proxy && T.platform() != "app" && o.url.indexOf("https") != 0) {
			//小程序 使用代理 T.platform() == "mp" &&
			var oldUrl = o.url;
			var proxyUrl = myjs.tomcatAddress() + "utils/proxy";
			if (oldUrl.indexOf("?") != -1) {
				o.url = proxyUrl + oldUrl.substring(oldUrl.indexOf("?"));
				oldUrl = oldUrl.substring(0, oldUrl.indexOf("?"));
			} else {
				o.url = proxyUrl;
			}
			o.url +=
				(o.url.indexOf("?") != -1
					? o.url.substring(o.url.indexOf("?")) == "?"
						? ""
						: "&"
					: "?") +
				"BASE_URL=" +
				oldUrl;
			o.url += "&BASE_TYPE=" + oldContentType;
			o.headers["content-type"] = "application/x-www-form-urlencoded";
		}
		if (oldContentType == "file") {
			delete o.headers["content-type"];
		}
		if (T.platform() == "app" && logText) {
			if (o.method == "post") {
				console.log(logText + "post【" + frequency + "】：" + JSON.stringify(o));
			} else {
				console.log(logText + "get【" + frequency + "】：" + JSON.stringify(o));
			}
		}
		try {
			var cbFun = function cbFun(ret, err) {
				// if(T.isObject(url) && T.isParameters(url._this.ajax)){
				// 	url._this.ajax = true;
				// }
				if (T.isFunction(callback)) {
					if (err) {
						try {
							ret = JSON.parse(err.msg);
							err = null;
						} catch (e) {
							ret = JSON.parse(JSON.stringify(err));
							err = null;
						}
					}
					if (err) {
						// if (frequency > 0) {
						// 	var frequencyUrl = url;
						// 	frequencyUrl.frequency--;
						// 	T.ajax(frequencyUrl, tag, callback, logText, method, data, header);
						// 	return;
						// }
					}
					if (T.platform() == "app" && logText) {
						if (ret)
							console.log("得到" + logText + "返回结果ret：" + JSON.stringify(ret));
						if (err)
							console.log("得到" + logText + "返回结果err：" + JSON.stringify(err));
					}
					if (ret) {
						ret.message = ret.message || ret.msg || "";
						var errcode = ret.code || "";
						if ((errcode == 302 || errcode == 2) && cacheType != "login") {
							//令牌失效
							T.hideProgress();
							T.sendEvent({
								name: "index",
								extra: {type: "verificationToken", errmsg: ret.message}
							});
							// return;
						}
					}
					callback(ret, err, true);
				}
			};
			setTimeout(function() {
				if (T.platform() == "web") {
					var xhr = new XMLHttpRequest();
					xhr.open(o.method, o.url);
					for (var header in o.headers) {
						xhr.setRequestHeader(header, o.headers[header]);
					}
					var sendValue = "";
					if (oldContentType.indexOf("x-www-form-urlencoded") != -1) {
						var dValue = o.data.values || {};
						var sendBody = o.data.body || "";
						if (sendBody) {
							dValue = JSON.parse(sendBody);
						}
						for (var vItem in dValue) {
							sendValue +=
								(!sendValue ? "" : "&") +
								vItem +
								"=" +
								encodeURIComponent(dValue[vItem]);
						}
					} else if (oldContentType.indexOf("file") != -1) {
						sendValue = new FormData();
						var dValue = o.data.values || {};
						var fileValue = o.data.files || {};
						var sendBody = o.data.body || "";
						if (sendBody) {
							dValue = JSON.parse(sendBody);
						}
						for (var vItem in dValue) {
							sendValue.append(vItem, encodeURIComponent(dValue[vItem]));
						}
						for (var vItem in fileValue) {
							sendValue.append(vItem, fileValue[vItem]);
						}
					} else {
						sendValue = o.data.body || JSON.stringify(o.data.values); //encodeURIComponent web加了之后 不能传递json了
					}
					xhr.send(sendValue);
					xhr.onreadystatechange = function() {
						if (xhr.readyState === XMLHttpRequest.DONE) {
							if (xhr.responseText) {
								var response = this.responseText;
								if (o.dataType == "json") {
									var isJSON = false;
									try {
										response = JSON.parse(response);
										isJSON = true;
									} catch (e) {
										isJSON = false;
									}
									if (isJSON) {
										cbFun(response, null);
									} else {
										cbFun(null, response);
									}
								} else {
									cbFun(response, null);
								}
							} else {
								cbFun(null, {});
							}
						}
					};
				} else {
					api.ajax(o, cbFun);
				}
			}, timeout);
		} catch (e) {
			console.log(e);
		}
	};

	T.openWin = function(name, url, pageParam, _this, allowEdit, _more) {
		var delay = 0;
		url = T.handleSYSLink(url, _this); //先处理跳转链接
		var o = {
			name: name,
			url: T.platform() == "web" ? url.substring(url.lastIndexOf("/") + 1) : url,
			pageParam: pageParam ? pageParam : {},
			bounces: false,
			bgColor: "#FFF",
			slidBackEnabled: false, //ios滑动返回
			vScrollBarEnabled: true,
			hScrollBarEnabled: true,
			scaleEnabled: true,
			animation: {
				type: "push",
				subType: "from_right",
				duration: 300
			},

			reload: true, // 去除设置
			allowEdit: true, //去除设置 默认都可以复制粘贴
			delay: delay,
			overScrollMode: "scrolls",
			defaultRefreshHeader: "swipe"
		};

		if (T.isObject(_more)) {
			o = T.setNewJSON(o, _more);
		}
		o.pageParam.headTheme =
			(_this && _this.data && _this.data.headTheme
				? _this.data.headTheme || ""
				: "") ||
			G.headTheme ||
			"";
		o.pageParam.appTheme = G.appTheme || "";
		o.pageParam.areaId = o.pageParam.areaId || myjs.areaId(_this);
		o.pageParam.v = G.v;
		if (o.pageParam.paramSaveKey) {
			T.setPrefs(o.pageParam.paramSaveKey, JSON.stringify(o.pageParam));
			o.pageParam = {paramSaveKey: o.pageParam.paramSaveKey};
		}
		api.openWin(o);
	};

	T.closeWin = function(_param) {
		var o = {};
		if (T.isObject(_param)) {
			o = T.setNewJSON(o, _param);
		} else {
			o.name = _param;
		}
		try {
			if (api.pageParam.paramSaveKey) {
				T.removePrefs(api.pageParam.paramSaveKey);
			}
			api.closeWin(o);
		} catch (e) {}
	};

	T.clearCache = function(callback) {
		var o = {};
		try {
			api.clearCache(o, function(ret, err) {
				T.isFunction(callback) && callback(ret, err);
			});
		} catch (e) {}
	};

	T.toast = function(_param, location, global) {
		var o = {
			msg: "",
			duration: 2000,
			location: "middle",
			global: false
		};

		if (T.isObject(_param)) {
			o = T.setNewJSON(o, _param);
		} else {
			o.msg = T.isParameters(_param) ? _param : "";
			o.location = location || "middle";
			o.global = global;
		}
		o.msg = o.msg.toString();
		try {
			api.toast(o);
		} catch (e) {}
	};

	T.showProgress = function(_param, modal) {
		var o = {
			style: "default",
			animationType: "fade",
			title: "加载中",
			text: "请稍候...",
			modal: true //是否模态，模态时整个页面将不可交互
		};
		if (T.isObject(_param)) {
			o = T.setNewJSON(o, _param);
		} else {
			o.title = T.isParameters(_param) ? _param : "";
			o.modal = !modal; //是否可以交互 反过来了
		}
		o.title = o.title.toString();
		try {
			api.showProgress(o);
		} catch (e) {}
	};

	T.hideProgress = function() {
		try {
			api.hideProgress();
		} catch (e) {}
	};

	T.alert = function(_param, callback) {
		G.alert(_param, callback);
		// var o = {
		// 	title: '提示',
		// 	msg: "",
		// 	buttons: ["确定"]
		// };
		// if (T.isObject(_param)) {
		// 	o = T.setNewJSON(o, _param);
		// } else {
		// 	o.msg = T.isParameters(_param)?_param:"";
		// }
		// o.msg = o.msg.toString();
		// try{
		// 	api.alert(o, (ret, err)=> {
		// 		T.isFunction(callback) && callback(ret, err);
		// 	});
		// }catch(e){}
	};

	T.confirm = function(_param, callback) {
		G.alert(_param, callback);
		// var o = {
		// 	title: '提示',
		// 	msg: "",
		// 	buttons: ['确定', '取消']
		// };
		// if (T.isObject(_param)) {
		// 	o = T.setNewJSON(o, _param);
		// } else {
		// 	o.msg = _param;
		// }
		// o = T.setNewJSON(o, _param);
		// try{
		// 	api.confirm(o, (ret, err)=> {
		// 		T.isFunction(callback) && callback(ret, err);
		// 	});
		// }catch(e){}
	};

	T.actionSheet = function(_param, _callback) {
		G.actionSheet(_param, _callback);
		// var o = {
		// 	title: '请选择',
		// 	cancelTitle: '取消',
		// 	destructiveTitle: "",
		// };
		// o = T.setNewJSON(o, _param);
		// try{
		// 	api.actionSheet(o, (ret, err)=> {
		// 		T.isFunction(callback) && callback(ret, err);
		// 	});
		// }catch(e){}
	};

	T.getPicture = function(_param, callback) {
		if (!callback) {
			T.toast("请先设置callback");
			return;
		}
		var o = {
			sourceType: "camera",
			encodingType: "jpg",
			mediaValue: "pic",
			targetWidth: 720,
			count: 1,
			max: 0
		};

		o = T.setNewJSON(o, _param);
		try {
			if (T.platform() == "web") {
				var h5Input = document.createElement("input");
				h5Input.style = "display: none;";
				h5Input.type = "file";
				h5Input.accept = "image/*";
				var ua = navigator.userAgent.toLowerCase();
				var version = "";
				if (ua.indexOf("android") > 0) {
					var reg = /android [\d._]+/gi;
					var v_info = ua.match(reg);
					version = (v_info + "").replace(/[^0-9|_.]/gi, "").replace(/_/gi, ".");
					version = parseInt(version.split(".")[0]);
				}
				if (!version || Number(version) <= 13) {
					h5Input.multiple = "multiple";
				}
				h5Input.onchange = function() {
					var listLength =
						o.max != 0 && h5Input.files.length > o.max ? o.max : h5Input.files.length;
					for (var i = 0; i < listLength; i++) {
						callback({data: h5Input.files[i]}, null);
					}
				};
				//ios拍照需要加到真实dom能才进onchange
				document.body.appendChild(h5Input);
				h5Input.click();
			} else if (T.platform() == "mp") {
				wx.chooseImage({
					count: o.max != 0 ? o.max : 9,
					sourceType: [o.sourceType == "camera" ? "camera" : "album"],
					success: function success(res) {
						for (var i = 0; i < res.tempFiles.length; i++) {
							callback({data: res.tempFiles[i]}, null);
						}
					}
				});
			} else if (T.platform() == "app") {
				var preName = o.sourceType == "camera" ? "camera" : "photos";
				if (
					!T.confirmPer(
						preName,
						"getPicture",
						o.reason || "用于上传并使用图片的功能，若取消将无法使用图片功能"
					)
				) {
					//相机相册权限
					T.addEventListener(preName + "Per_" + "getPicture", function(ret, err) {
						T.removeEventListener(preName + "Per_" + "getPicture");
						if (ret.value.granted) {
							T.getPicture(_param, callback);
						}
					});
					return;
				}
				if (o.sourceType == "camera" || o.userOne) {
					api.getPicture(o, function(ret, err) {
						T.isFunction(callback) && callback(ret, err);
					});
				} else {
					api.require("WXPhotoPicker").open(
						{
							max: o.max != 0 ? o.max : 9,
							styles: {
								mark: {checked: G.appTheme},
								bottomTabBar: {sendText: "确定", sendBgColor: G.appTheme}
							},
							type: "image"
						},
						function(ret) {
							var eventType = ret ? ret.eventType : "cancel";
							if (eventType == "cancel") return;
							if (eventType == "confirm" && ret.list.length != 0) {
								for (var i = 0; i < ret.list.length; i++) {
									callback({data: ret.list[i].path}, null);
								}
							}
						}
					);
				}
			}
		} catch (e) {}
	};

	T.hasPermission = function(one_per) {
		if (T.platform() == "app") {
			if (!one_per) return;
			var rets = api.hasPermission({list: one_per.split(",")});
			if (one_per.indexOf(",") != -1) {
				//判断一堆时 就自己看	一般是一个一个判断
				T.alert("判断结果：" + JSON.stringify(rets));
				return;
			} else {
				return rets;
			}
		}
	};

	T.requestPermission = function(one_per, callback, _fName) {
		if (T.platform() == "app") {
			if (!one_per) return;
			api.requestPermission({list: one_per.split(",")}, function(ret) {
				console.log(JSON.stringify(ret));
				//把结果 发监听过去
				ret.list.forEach(function(_eItem, _eIndex, _eArr) {
					T.sendEvent(_eItem.name + "Per_" + _fName, {granted: _eItem.granted});
				});
				T.isFunction(callback) && callback(ret, err);
			});
		}
	};

	T.confirmPer = function(perm, _fName, _reason) {
		if (T.platform() == "app") {
			var has = T.hasPermission(perm);
			if (!has || !has[0] || !has[0].granted) {
				var hintWord = {
					camera: "相机",
					storage: "存储",
					photos: "照片",
					microphone: "麦克风",
					location: "位置",
					phone: "电话",
					"phone-r": "通话状态",
					"phone-r-log": "通话记录"
				};

				var iosHint =
					"请在" +
					(api.uiMode == "phone" ? "iPhone" : "iPad") +
					"的“设置-隐私-" +
					hintWord[perm] +
					"”中请允许访问" +
					hintWord[perm] +
					(_reason ? "，" + _reason : "。");
				var androidHint =
					"使用该功能需要" +
					hintWord[perm] +
					"权限，" +
					(_reason || "请前往系统设置开启权限。");
				T.confirm(
					{
						title: "无法使用" + hintWord[perm],
						msg: T.systemType() == "ios" ? iosHint : androidHint,
						buttons: ["下一步", "取消"]
					},
					function(ret, err) {
						if (1 == ret.buttonIndex) {
							T.requestPermission(perm, null, _fName);
						} else {
							T.sendEvent(perm + "Per_" + _fName, {granted: false});
						}
					}
				);
				return false;
			}
			return true;
		}
		return true;
	};

	//处理app链接 可以不带app会拼接上 或者需要带上特定参数
	T.handleSYSLink = function(_link, _this, myParam) {
		if (_link === void 0) {
			_link = "";
		}
		// if(_link.indexOf("http") != 0){
		// 	return _link;
		// }
		myParam = myParam || {};
		//index.html?token={{token}}&userId={{userId}}
		_link = _link.replace("{{tomcatAddress}}", myjs.tomcatAddress());
		_link = _link.replace("{{shareAddress}}", myjs.shareAddress());
		_link = _link.replace(
			"{{token}}",
			encodeURIComponent(T.getPrefs("sys_token"))
		); //当前app登录用户的token，例如：bearer eyJhbGciOiJ...
		_link = _link.replace("{{sysUrl}}", myjs.appUrl()); //当前app请求系统地址，例如：http://**************:54386/lzt/
		_link = _link.replace("{{areaId}}", myParam.areaId || myjs.areaId(_this)); //当前跳转页面的地区id，例如：430000
		_link = _link.replace("{{userId}}", G.userId); //当前用户id，例如：1
		_link = _link.replace("{{iszx}}", myjs.iszx); //当前系统类型，例如：true (true为政协，flase为人大)
		_link = _link.replace("{{appTheme}}", G.appTheme); //当前app主题颜色，例如：#3657C0
		_link = _link.replace("{{careMode}}", G.careMode); //当前是否为关怀模式：例如：true (关怀模式下字体大4px)
		if (_link.indexOf("?ndata=") != -1) {
			//是app内页面 带上特有参数如果没有
			if (_link.indexOf("sysUrl-zy-") == -1) {
				//没有带地址
				_link += "-zyz-sysUrl-zy-" + myjs.appUrl();
			}
			if (_link.indexOf("sysAreaId-zy-") == -1) {
				//没有带地区
				_link += "-zyz-sysAreaId-zy-" + (myParam.areaId || myjs.areaId(_this));
			}
			if (_link.indexOf("iszx-zy-") == -1) {
				//没有带人大政协判断
				_link += "-zyz-iszx-zy-" + myjs.iszx;
			}
			if (_link.indexOf("appTheme-zy-") == -1) {
				//没有带主题
				_link += "-zyz-appTheme-zy-" + G.appTheme;
			}
			if (_link.indexOf("careMode-zy-") == -1) {
				//没有唯一标识
				_link += "-zyz-careMode-zy-" + G.careMode;
			}
		}
		return _link;
	};

	//按照ascii 排序
	T.sort_ascii = function(obj, _default) {
		var arr = [];
		var num = 0;
		for (var i in obj) {
			arr[num] = i;
			num++;
		}
		var sortArr = arr.sort();
		var str = "";
		for (var _i = 0; _i < sortArr.length; _i++) {
			var sValue = obj[sortArr[_i]];
			str +=
				sortArr[_i] +
				"=" +
				(T.isTargetType(sValue, "number")
					? sValue
					: T.isObject(sValue)
					? JSON.stringify(sValue)
					: sValue || _default) +
				"&";
		}
		var char = "&";
		str = str.replace(new RegExp("^\\" + char + "+|\\" + char + "+$", "g"), "");
		return str;
	};

	/** 判断颜色属于深色还是浅色*/
	T.isColorDarkOrLight = function(hexcolor) {
		try {
			var colorrgb = T.colorRgb(hexcolor);
			var colors = colorrgb.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
			var red = colors[1];
			var green = colors[2];
			var blue = colors[3];
			var brightness;
			brightness = red * 299 + green * 587 + blue * 114;
			brightness = brightness / 255000;
			if (brightness >= 0.5) {
				return "light";
			} else {
				return "dark";
			}
		} catch (e) {
			return "";
		}
	};

	//16进制颜色转化为RGB颜色
	T.colorRgb = function(str) {
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		var sColor = str.toLowerCase();
		if (sColor && reg.test(sColor)) {
			if (sColor.length === 4) {
				var sColorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1));
				}
				sColor = sColorNew;
			}
			var sColorChange = [];
			for (var i = 1; i < 7; i += 2) {
				sColorChange.push(parseInt("0x" + sColor.slice(i, i + 2)));
			}
			return "rgb(" + sColorChange.join(",") + ")";
		} else {
			return sColor;
		}
	};

	/** 16进制颜色 转换成rgba颜色	可设置透明 */
	T.colorRgba = function(_color, _alpha) {
		if (!_color) return;
		// 16进制颜色值的正则
		var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
		// 把颜色值变成小写
		var color = _color.toLowerCase();
		if (reg.test(color)) {
			if (color.length === 4) {
				var colorNew = "#";
				for (var i = 1; i < 4; i += 1) {
					colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
				}
				color = colorNew;
			}
			var colorChange = [];
			for (var i = 1; i < 7; i += 2) {
				colorChange.push(parseInt("0x" + color.slice(i, i + 2)));
			}
			return (
				"rgba(" +
				colorChange.join(",") +
				"," +
				(T.isParameters(_alpha) ? _alpha : "1") +
				")"
			);
		} else {
			return color;
		}
	};

	var myjs = {};

	//修改时 需同步修改_zy/myjs.js
	myjs.iszx = false;

	myjs.proxy = false;

	myjs.appName = (myjs.iszx ? "政协" : "人大") + "平台版";

	myjs.appUrl = function() {
		// if(T.platform() == "web"){//适配正式测试 不用重复切换
		// 	switch(location.hostname){
		// 		case "*************":
		// 			return "http://*************:810/lzt/";
		// 		case "***************":
		// 			return "http://***************:54386/lzt/";
		// 	}
		// }
		return (
			T.getPrefs("sys_appUrl") ||
			(myjs.iszx
				? "https://productpc.cszysoft.com:20170/lzt/"
				: "https://productpc.cszysoft.com:20169/lzt/")
		);
	};

	myjs.chatHeader = function() {
		return (
			T.getPrefs("sys_chatHeader") || "platform5" + (myjs.iszx ? "zx" : "rd")
		);
	};

	myjs.chatEnvironment = function() {
		return T.getPrefs("sys_chatEnvironment") || "1";
	};

	myjs.tomcatAddress = function() {
		return (
			T.getPrefs("sys_tomcatAddress") ||
			(T.platform() == "web"
				? window.location.protocol == "https:"
					? "https://cszysoft.com:9091/"
					: "http://cszysoft.com:9090/"
				: "https://cszysoft.com:9091/")
		); //http://**********:8080/ http://cszysoft.com:9090/ https://cszysoft.com:9091/
	};

	myjs.shareAddress = function(_type) {
		if (_type == 1 && T.platform() != "mp") {
			return "../../";
		}
		return (
			T.getPrefs("sys_shareAddress") ||
			(T.platform() == "web"
				? window.location.protocol.indexOf("http") == 0
					? window.location.protocol
					: "http:"
				: "https:") +
				"//cszysoft.com/appShare/" +
				(myjs.iszx ? "platform5zx/" : "platform5rd/")
		);
	};

	//默认当前页面 area下的地区 无则传参地区
	(myjs.areaId = function(_this) {
		return (
			(_this && _this.data && _this.data.area ? _this.data.area.key || "" : "") ||
			T.pageParam(_this).areaId ||
			T.getPrefs("sys_aresId") ||
			T.getPrefs("sys_platform") ||
			""
		);
	}),
		//系统类型：（平台版：platform）（标准版：standard）
		(myjs.systemType = function(_this) {
			return (
				T.pageParam(_this).platform || T.getPrefs("sys_systemType") || "platform"
			);
		});

	myjs.clientId = "zyrdV5TestAccount";

	myjs.clientSecret = "zyrdV5TestPassword";

	myjs.hw_project_id = "0611d8333100251b2fc1c01937b8e6d9";

	myjs.hw_bucket = "zy-soft";

	myjs.hw_header = "ZY";

	var SECONDS_A_MINUTE$1 = 60;
	var SECONDS_A_HOUR$1 = SECONDS_A_MINUTE$1 * 60;
	var SECONDS_A_DAY$1 = SECONDS_A_HOUR$1 * 24;
	var SECONDS_A_WEEK$1 = SECONDS_A_DAY$1 * 7;
	var MILLISECONDS_A_SECOND$1 = 1e3;
	var MILLISECONDS_A_MINUTE$1 = SECONDS_A_MINUTE$1 * MILLISECONDS_A_SECOND$1;
	var MILLISECONDS_A_HOUR$1 = SECONDS_A_HOUR$1 * MILLISECONDS_A_SECOND$1;
	var MILLISECONDS_A_DAY$1 = SECONDS_A_DAY$1 * MILLISECONDS_A_SECOND$1;
	var MILLISECONDS_A_WEEK$1 = SECONDS_A_WEEK$1 * MILLISECONDS_A_SECOND$1; // English locales

	var MS$1 = "millisecond";
	var S$1 = "second";
	var MIN$1 = "minute";
	var H$1 = "hour";
	var D$1 = "day";
	var W$1 = "week";
	var M$1 = "month";
	var Q$1 = "quarter";
	var Y$1 = "year";
	var DATE$1 = "date";
	var FORMAT_DEFAULT$1 = "YYYY-MM-DDTHH:mm:ssZ";
	var INVALID_DATE_STRING$1 = "Invalid Date"; // regex

	var REGEX_PARSE$1 = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/;
	var REGEX_FORMAT$1 = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;

	var en$1 = {
		name: "en",
		weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split(
			"_"
		),
		months: "January_February_March_April_May_June_July_August_September_October_November_December".split(
			"_"
		)
	};

	var padStart$1 = function padStart(string, length, pad) {
		var s = String(string);
		if (!s || s.length >= length) return string;
		return "" + Array(length + 1 - s.length).join(pad) + string;
	};
	var padZoneStr$1 = function padZoneStr(instance) {
		var negMinutes = -instance.utcOffset();
		var minutes = Math.abs(negMinutes);
		var hourOffset = Math.floor(minutes / 60);
		var minuteOffset = minutes % 60;
		return (
			"" +
			(negMinutes <= 0 ? "+" : "-") +
			padStart$1(hourOffset, 2, "0") +
			":" +
			padStart$1(minuteOffset, 2, "0")
		);
	};
	var monthDiff$1 = function monthDiff(a, b) {
		if (a.date() < b.date()) return -monthDiff(b, a);
		var wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month());
		var anchor = a.clone().add(wholeMonthDiff, M$1);
		var c = b - anchor < 0;
		var anchor2 = a.clone().add(wholeMonthDiff + (c ? -1 : 1), M$1);
		return +(
			-(
				wholeMonthDiff +
				(b - anchor) / (c ? anchor - anchor2 : anchor2 - anchor)
			) || 0
		);
	};
	var absFloor$1 = function absFloor(n) {
		return n < 0 ? Math.ceil(n) || 0 : Math.floor(n);
	};
	var prettyUnit$1 = function prettyUnit(u) {
		var special = {
			M: M$1,
			y: Y$1,
			w: W$1,
			d: D$1,
			D: DATE$1,
			h: H$1,
			m: MIN$1,
			s: S$1,
			ms: MS$1,
			Q: Q$1
		};

		return (
			special[u] ||
			String(u || "")
				.toLowerCase()
				.replace(/s$/, "")
		);
	};
	var isUndefined$1 = function isUndefined(s) {
		return s === undefined;
	};
	var U$1 = {
		s: padStart$1,
		z: padZoneStr$1,
		m: monthDiff$1,
		a: absFloor$1,
		p: prettyUnit$1,
		u: isUndefined$1
	};

	var L$1 = "en";
	var Ls$1 = {};
	Ls$1[L$1] = en$1;
	var isDayjs$1 = function isDayjs(d) {
		return d instanceof Dayjs$1;
	};
	var parseLocale$1 = function parseLocale(preset, object, isLocal) {
		var l;
		if (!preset) return L$1;
		if (typeof preset === "string") {
			var presetLower = preset.toLowerCase();
			if (Ls$1[presetLower]) {
				l = presetLower;
			}
			if (object) {
				Ls$1[presetLower] = object;
				l = presetLower;
			}
			var presetSplit = preset.split("-");
			if (!l && presetSplit.length > 1) {
				return parseLocale(presetSplit[0]);
			}
		} else {
			var name = preset.name;
			Ls$1[name] = preset;
			l = name;
		}
		if (!isLocal && l) L$1 = l;
		return l || (!isLocal && L$1);
	};
	var dayjs$1 = function dayjs(date, c) {
		if (isDayjs$1(date)) {
			return date.clone();
		}
		var cfg = typeof c === "object" ? c : {};
		cfg.date = date;
		cfg.args = arguments;
		return new Dayjs$1(cfg);
	};
	var wrapper$1 = function wrapper(date, instance) {
		return dayjs$1(date, {
			locale: instance.$L,
			utc: instance.$u,
			x: instance.$x,
			$offset: instance.$offset
		});
	};
	var Utils$1 = U$1;
	Utils$1.l = parseLocale$1;
	Utils$1.i = isDayjs$1;
	Utils$1.w = wrapper$1;
	var parseDate$1 = function parseDate(cfg) {
		var date = cfg.date,
			utc = cfg.utc;
		if (date === null) return new Date(NaN);
		if (Utils$1.u(date)) return new Date();
		if (date instanceof Date) return new Date(date);
		if (typeof date === "string" && !/Z$/i.test(date)) {
			var d = date.match(REGEX_PARSE$1);
			if (d) {
				var m = d[2] - 1 || 0;
				var ms = (d[7] || "0").substring(0, 3);
				if (utc) {
					return new Date(
						Date.UTC(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms)
					);
				}
				return new Date(d[1], m, d[3] || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms);
			}
		}
		return new Date(date);
	};
	var Dayjs$1 = (function() {
		function Dayjs(cfg) {
			this.$L = parseLocale$1(cfg.locale, null, true);
			this.parse(cfg);
		}
		var _proto = Dayjs.prototype;
		_proto.parse = function parse(cfg) {
			this.$d = parseDate$1(cfg);
			this.$x = cfg.x || {};
			this.init();
		};
		_proto.init = function init() {
			var $d = this.$d;
			this.$y = $d.getFullYear();
			this.$M = $d.getMonth();
			this.$D = $d.getDate();
			this.$W = $d.getDay();
			this.$H = $d.getHours();
			this.$m = $d.getMinutes();
			this.$s = $d.getSeconds();
			this.$ms = $d.getMilliseconds();
		};
		_proto.$utils = function $utils() {
			return Utils$1;
		};
		_proto.isValid = function isValid() {
			return !(this.$d.toString() === INVALID_DATE_STRING$1);
		};
		_proto.isSame = function isSame(that, units) {
			var other = dayjs$1(that);
			return this.startOf(units) <= other && other <= this.endOf(units);
		};
		_proto.isAfter = function isAfter(that, units) {
			return dayjs$1(that) < this.startOf(units);
		};
		_proto.isBefore = function isBefore(that, units) {
			return this.endOf(units) < dayjs$1(that);
		};
		_proto.$g = function $g(input, get, set) {
			if (Utils$1.u(input)) return this[get];
			return this.set(set, input);
		};
		_proto.unix = function unix() {
			return Math.floor(this.valueOf() / 1000);
		};
		_proto.valueOf = function valueOf() {
			return this.$d.getTime();
		};
		_proto.startOf = function startOf(units, _startOf) {
			var _this = this;
			var isStartOf = !Utils$1.u(_startOf) ? _startOf : true;
			var unit = Utils$1.p(units);
			var instanceFactory = function instanceFactory(d, m) {
				var ins = Utils$1.w(
					_this.$u ? Date.UTC(_this.$y, m, d) : new Date(_this.$y, m, d),
					_this
				);
				return isStartOf ? ins : ins.endOf(D$1);
			};
			var instanceFactorySet = function instanceFactorySet(method, slice) {
				var argumentStart = [0, 0, 0, 0];
				var argumentEnd = [23, 59, 59, 999];
				return Utils$1.w(
					_this
						.toDate()
						[method].apply(
							_this.toDate("s"),
							(isStartOf ? argumentStart : argumentEnd).slice(slice)
						),
					_this
				);
			};
			var $W = this.$W,
				$M = this.$M,
				$D = this.$D;
			var utcPad = "set" + (this.$u ? "UTC" : "");
			switch (unit) {
				case Y$1:
					return isStartOf ? instanceFactory(1, 0) : instanceFactory(31, 11);
				case M$1:
					return isStartOf ? instanceFactory(1, $M) : instanceFactory(0, $M + 1);
				case W$1: {
					var weekStart = this.$locale().weekStart || 0;
					var gap = ($W < weekStart ? $W + 7 : $W) - weekStart;
					return instanceFactory(isStartOf ? $D - gap : $D + (6 - gap), $M);
				}
				case D$1:
				case DATE$1:
					return instanceFactorySet(utcPad + "Hours", 0);
				case H$1:
					return instanceFactorySet(utcPad + "Minutes", 1);
				case MIN$1:
					return instanceFactorySet(utcPad + "Seconds", 2);
				case S$1:
					return instanceFactorySet(utcPad + "Milliseconds", 3);
				default:
					return this.clone();
			}
		};
		_proto.endOf = function endOf(arg) {
			return this.startOf(arg, false);
		};
		_proto.$set = function $set(units, _int) {
			var _C$D$C$DATE$C$M$C$Y$C;
			var unit = Utils$1.p(units);
			var utcPad = "set" + (this.$u ? "UTC" : "");
			var name = ((_C$D$C$DATE$C$M$C$Y$C = {}),
			(_C$D$C$DATE$C$M$C$Y$C[D$1] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[DATE$1] = utcPad + "Date"),
			(_C$D$C$DATE$C$M$C$Y$C[M$1] = utcPad + "Month"),
			(_C$D$C$DATE$C$M$C$Y$C[Y$1] = utcPad + "FullYear"),
			(_C$D$C$DATE$C$M$C$Y$C[H$1] = utcPad + "Hours"),
			(_C$D$C$DATE$C$M$C$Y$C[MIN$1] = utcPad + "Minutes"),
			(_C$D$C$DATE$C$M$C$Y$C[S$1] = utcPad + "Seconds"),
			(_C$D$C$DATE$C$M$C$Y$C[MS$1] = utcPad + "Milliseconds"),
			_C$D$C$DATE$C$M$C$Y$C)[unit];
			var arg = unit === D$1 ? this.$D + (_int - this.$W) : _int;
			if (unit === M$1 || unit === Y$1) {
				var date = this.clone().set(DATE$1, 1);
				date.$d[name](arg);
				date.init();
				this.$d = date.set(DATE$1, Math.min(this.$D, date.daysInMonth())).$d;
			} else if (name) this.$d[name](arg);
			this.init();
			return this;
		};
		_proto.set = function set(string, _int2) {
			return this.clone().$set(string, _int2);
		};
		_proto.get = function get(unit) {
			return this[Utils$1.p(unit)]();
		};
		_proto.add = function add(number, units) {
			var _this2 = this,
				_C$MIN$C$H$C$S$unit;
			number = Number(number);
			var unit = Utils$1.p(units);
			var instanceFactorySet = function instanceFactorySet(n) {
				var d = dayjs$1(_this2);
				return Utils$1.w(d.date(d.date() + Math.round(n * number)), _this2);
			};
			if (unit === M$1) {
				return this.set(M$1, this.$M + number);
			}
			if (unit === Y$1) {
				return this.set(Y$1, this.$y + number);
			}
			if (unit === D$1) {
				return instanceFactorySet(1);
			}
			if (unit === W$1) {
				return instanceFactorySet(7);
			}
			var step =
				((_C$MIN$C$H$C$S$unit = {}),
				(_C$MIN$C$H$C$S$unit[MIN$1] = MILLISECONDS_A_MINUTE$1),
				(_C$MIN$C$H$C$S$unit[H$1] = MILLISECONDS_A_HOUR$1),
				(_C$MIN$C$H$C$S$unit[S$1] = MILLISECONDS_A_SECOND$1),
				_C$MIN$C$H$C$S$unit)[unit] || 1;
			var nextTimeStamp = this.$d.getTime() + number * step;
			return Utils$1.w(nextTimeStamp, this);
		};
		_proto.subtract = function subtract(number, string) {
			return this.add(number * -1, string);
		};
		_proto.format = function format(formatStr) {
			var _this3 = this;
			var locale = this.$locale();
			if (!this.isValid()) return locale.invalidDate || INVALID_DATE_STRING$1;
			var str = formatStr || FORMAT_DEFAULT$1;
			var zoneStr = Utils$1.z(this);
			var $H = this.$H,
				$m = this.$m,
				$M = this.$M;
			var weekdays = locale.weekdays,
				months = locale.months,
				meridiem = locale.meridiem;
			var getShort = function getShort(arr, index, full, length) {
				return (
					(arr && (arr[index] || arr(_this3, str))) || full[index].slice(0, length)
				);
			};
			var get$H = function get$H(num) {
				return Utils$1.s($H % 12 || 12, num, "0");
			};
			var meridiemFunc =
				meridiem ||
				function(hour, minute, isLowercase) {
					var m = hour < 12 ? "AM" : "PM";
					return isLowercase ? m.toLowerCase() : m;
				};
			var matches = {
				YY: String(this.$y).slice(-2),
				YYYY: this.$y,
				M: $M + 1,
				MM: Utils$1.s($M + 1, 2, "0"),
				MMM: getShort(locale.monthsShort, $M, months, 3),
				MMMM: getShort(months, $M),
				D: this.$D,
				DD: Utils$1.s(this.$D, 2, "0"),
				d: String(this.$W),
				dd: getShort(locale.weekdaysMin, this.$W, weekdays, 2),
				ddd: getShort(locale.weekdaysShort, this.$W, weekdays, 3),
				dddd: weekdays[this.$W],
				H: String($H),
				HH: Utils$1.s($H, 2, "0"),
				h: get$H(1),
				hh: get$H(2),
				a: meridiemFunc($H, $m, true),
				A: meridiemFunc($H, $m, false),
				m: String($m),
				mm: Utils$1.s($m, 2, "0"),
				s: String(this.$s),
				ss: Utils$1.s(this.$s, 2, "0"),
				SSS: Utils$1.s(this.$ms, 3, "0"),
				Z: zoneStr
			};

			return str.replace(REGEX_FORMAT$1, function(match, $1) {
				return $1 || matches[match] || zoneStr.replace(":", "");
			});
		};
		_proto.utcOffset = function utcOffset() {
			return -Math.round(this.$d.getTimezoneOffset() / 15) * 15;
		};
		_proto.diff = function diff(input, units, _float) {
			var _C$Y$C$M$C$Q$C$W$C$D$;
			var unit = Utils$1.p(units);
			var that = dayjs$1(input);
			var zoneDelta =
				(that.utcOffset() - this.utcOffset()) * MILLISECONDS_A_MINUTE$1;
			var diff = this - that;
			var result = Utils$1.m(this, that);
			result =
				((_C$Y$C$M$C$Q$C$W$C$D$ = {}),
				(_C$Y$C$M$C$Q$C$W$C$D$[Y$1] = result / 12),
				(_C$Y$C$M$C$Q$C$W$C$D$[M$1] = result),
				(_C$Y$C$M$C$Q$C$W$C$D$[Q$1] = result / 3),
				(_C$Y$C$M$C$Q$C$W$C$D$[W$1] = (diff - zoneDelta) / MILLISECONDS_A_WEEK$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[D$1] = (diff - zoneDelta) / MILLISECONDS_A_DAY$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[H$1] = diff / MILLISECONDS_A_HOUR$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[MIN$1] = diff / MILLISECONDS_A_MINUTE$1),
				(_C$Y$C$M$C$Q$C$W$C$D$[S$1] = diff / MILLISECONDS_A_SECOND$1),
				_C$Y$C$M$C$Q$C$W$C$D$)[unit] || diff;
			return _float ? result : Utils$1.a(result);
		};
		_proto.daysInMonth = function daysInMonth() {
			return this.endOf(M$1).$D;
		};
		_proto.$locale = function $locale() {
			return Ls$1[this.$L];
		};
		_proto.locale = function locale(preset, object) {
			if (!preset) return this.$L;
			var that = this.clone();
			var nextLocaleName = parseLocale$1(preset, object, true);
			if (nextLocaleName) that.$L = nextLocaleName;
			return that;
		};
		_proto.clone = function clone() {
			return Utils$1.w(this.$d, this);
		};
		_proto.toDate = function toDate() {
			return new Date(this.valueOf());
		};
		_proto.toJSON = function toJSON() {
			return this.isValid() ? this.toISOString() : null;
		};
		_proto.toISOString = function toISOString() {
			return this.$d.toISOString();
		};
		_proto.toString = function toString() {
			return this.$d.toUTCString();
		};
		return Dayjs;
	})();
	var proto$1 = Dayjs$1.prototype;
	dayjs$1.prototype = proto$1;
	[
		["$ms", MS$1],
		["$s", S$1],
		["$m", MIN$1],
		["$H", H$1],
		["$W", D$1],
		["$M", M$1],
		["$y", Y$1],
		["$D", DATE$1]
	].forEach(function(g) {
		proto$1[g[1]] = function(input) {
			return this.$g(input, g[0], g[1]);
		};
	});
	dayjs$1.extend = function(plugin, option) {
		if (!plugin.$i) {
			plugin(option, Dayjs$1, dayjs$1);
			plugin.$i = true;
		}
		return dayjs$1;
	};
	dayjs$1.locale = parseLocale$1;
	dayjs$1.isDayjs = isDayjs$1;
	dayjs$1.unix = function(timestamp) {
		return dayjs$1(timestamp * 1e3);
	};
	dayjs$1.en = Ls$1[L$1];
	dayjs$1.Ls = Ls$1;
	dayjs$1.p = {};

	// 定义各模块共用参数
	function baseModule(_value, _key) {
		switch (_value) {
			case "contentInformation": //内容资料其实是资讯
			case "learningmaterials": //学习资料其实是资讯
			case "perform_promote": //履职技巧
				_value = "informationContent";
				break;
			case "activityPublish": //活动
			case "activitySign":
			case "activityJoin":
			case "activitySignNotice":
			case "activityJoinNotice":
				_value = "servantActivity";
				break;
			case "npcMemberCheckPrepare": //变更申请
			case "npcMemberCheckPass":
			case "npcMemberCheckFalse":
			case "cppccMemberCheckPass":
			case "cppccMemberCheckFalse":
				_value = "cppccMemberCheckPrepare";
				break;
			case "npc_member":
				_value = "cppcc_member";
				break;
			case "LegislationBusinessCode":
			case "submitArchivePassiveSuggest":
				_value = "legislationOpinion";
				break;
			case "study_online_1":
			case "study_publish":
				_value = "study_online";
				break;
			case "schedule_daily_notice_staff":
				_value = "schedule_daily_notice";
				break;
			case "micro_advice_reject":
			case "micro_advice_reply":
			case "micro_advice_submit":
			case "micro_advice_report":
			case "micro_advice_push_group":
			case "micro_advice_transfer":
			case "micro_advice_invite":
				_value = "min_suggest";
				break;
			case "schedule_reception_notice":
				_value = "schedule_reception";
				break;
			case "contactInteractSend":
				_value = "double_interaction";
				break;
			case "sendNodeProcessInfo":
				_value = "legislative_process";
				break;
		}

		var mySeModule = null;
		//code【模块管理中code,app中唯一】	appType【展示在business-item组件name】	businessCode【后台接口需要的code】	behaviorCode【点击详情时增加阅读量】	dotComment【通用详情不评论】
		var mybaseModule = [
			{
				name: "资讯",
				code: "5",
				appType: "news",
				businessCode: "informationContent",
				behaviorCode: "information_content"
			},
			{
				name: myjs.iszx ? "网络议政" : "意见征集",
				code: "6",
				appType: "solicitation",
				businessCode: "opinioncollect"
			},
			{name: "专题", code: "7", appType: "topic", businessCode: "infoSubject"},
			{
				name: "专题资讯",
				code: "8",
				appType: "news",
				businessCode: "informationSubject",
				dotComment: true
			},
			{
				name: (myjs.iszx ? "委员" : "代表") + "信息",
				code: "9",
				appType: "npcInfo",
				businessCode: "cppcc_member"
			},
			{
				name: (myjs.iszx ? "委员" : "代表") + "信息变更申请",
				code: "9_1",
				appType: "npcReview",
				businessCode: "cppccMemberCheckPrepare"
			},
			{
				name: "活动",
				code: "10",
				appType: "activity",
				businessCode: "servantActivity"
			},
			{name: "活动资料", code: "10_1", appType: "material", businessCode: ""},
			{
				name: "活动请假申请",
				code: "10_2",
				appType: "",
				businessCode: "activityLeavepass"
			},
			{
				name: "活动请假审批",
				code: "10_3",
				appType: "",
				businessCode: "activityLeavesub"
			},
			{name: "履职档案", code: "11", appType: "performance", businessCode: ""},
			{name: "履职足迹", code: "11_1", appType: "material", businessCode: ""},
			{
				name: "履职补录",
				code: "11_2",
				appType: "material",
				businessCode: "dutiesadditional",
				dotComment: true
			},
			{name: "履职调整", code: "11_3", appType: "", businessCode: ""},
			{
				name: myjs.iszx ? "委员说" : "圈子",
				code: "12",
				appType: "circle",
				businessCode: "styleCircle"
			},
			{
				name: "待办",
				code: "13",
				appType: "upcoming",
				businessCode: "pendingMessage"
			},
			{name: "消息", code: "14", appType: "upcoming", businessCode: "box_message"},
			{
				name: "系统消息",
				code: "14_1",
				appType: "",
				businessCode: "system",
				dotComment: true
			},
			{name: "扫一扫", code: "15", appType: "", businessCode: ""},
			{name: "云盘", code: "16", appType: "", businessCode: "pan_pubshare"},
			{
				name: "云盘共享",
				code: "16_1",
				appType: "",
				businessCode: "pan_pubshare_1"
			},
			{
				name: "个人收藏",
				code: "17",
				appType: "upcoming",
				businessCode: "",
				listFlat: true
			},
			{name: "帮助中心", code: "18", appType: "", businessCode: ""},
			{name: "分享应用", code: "19", appType: "", businessCode: ""},
			{name: "有事好商量", code: "20", appType: "negotiable", businessCode: ""},
			{
				name: "协商活动",
				code: "21",
				appType: "negotiable_activity",
				businessCode: ""
			},
			{
				name: "通知公告",
				code: "22",
				appType: "news",
				businessCode: "notification",
				dotComment: true
			},
			{name: "制度文件", code: "23", appType: "news", businessCode: ""},
			{
				name: "协商征集",
				code: "24",
				appType: "negotiable_solicitation",
				businessCode: "opinioncollect",
				onlyBusinessCode: "discussioncollect"
			},
			{name: "远程协商", code: "25", appType: "videoConference", businessCode: ""},
			{
				name: "法规规章规范性文件库",
				code: "26",
				appType: "regulations",
				businessCode: ""
			},
			{
				name: "双联互动",
				code: "27",
				appType: "double_interaction",
				businessCode: "double_interaction",
				sendBusinessCode: "voter_npc"
			},
			{
				name: "双联互动H5",
				code: "27_1",
				appType: "double_interaction",
				businessCode: "double_interaction",
				sendBusinessCode: "voter_npc"
			},
			{
				name: "委员值班",
				code: "28",
				appType: "schedule_daily_notice",
				businessCode: "schedule_daily_notice",
				doubleCode: "schedule_daily"
			},
			{
				name: "委员会客厅",
				code: "29",
				appType: "schedule_reception",
				businessCode: "schedule_reception",
				doubleCode: "schedule_reception"
			},
			{name: "投票", code: "30", appType: "vote", businessCode: "vote"},
			{
				name: "培训考试",
				code: "31",
				appType: "training",
				businessCode: "study_online"
			},
			{
				name: "成绩单",
				code: "31_1",
				appType: "transcript",
				businessCode: "study_online_1"
			},
			{
				name: "题集训练",
				code: "31_2",
				appType: "training",
				businessCode: "promote_paper"
			},
			{
				name: "训练任务",
				code: "31_3",
				appType: "training",
				businessCode: "promote_task"
			},
			{
				name: "掌上建议",
				code: "32",
				appType: "suggestion",
				businessCode: "suggestion"
			},
			{
				name: "沟通情况记录",
				code: "32_communication",
				appType: "suggestion_communication",
				listFlat: true,
				dotSearch: true,
				dotDetails: true,
				businessCode: ""
			},
			{
				name: "答复信息",
				code: "32_reply",
				appType: "suggestion_reply",
				listFlat: true,
				dotSearch: true,
				dotDetails: true,
				businessCode: ""
			},
			{
				name: "单位调整记录",
				code: "32_adjustRecord",
				appType: "suggestion_adjustRecord",
				listFlat: true,
				dotSearch: true,
				dotDetails: true,
				businessCode: ""
			},
			{
				name: "申请调整记录",
				code: "32_adjustStopRecord",
				appType: "suggestion_adjustStopRecord",
				listFlat: true,
				dotSearch: true,
				dotDetails: true,
				businessCode: ""
			},
			{
				name: "申请延期记录",
				code: "32_answerStopRecord",
				appType: "suggestion_answerStopRecord",
				listFlat: true,
				dotSearch: true,
				dotDetails: true,
				businessCode: ""
			},
			{
				name: "掌上提案",
				code: "33",
				appType: "suggestion",
				businessCode: "proposal"
			},
			{name: "问卷调查", code: "34", appType: "survey", businessCode: "survey"},
			{
				name: "立法征询",
				code: "35",
				appType: "legislative_consultation",
				businessCode: "legislationOpinion"
			},
			{
				name: "规范性备案审查建议平台",
				code: "36",
				appType: "normative",
				businessCode: "archive"
			},
			{
				name: "掌上议案",
				code: "37",
				appType: "suggestion",
				businessCode: "motion"
			},
			{
				name: "代表参与情况",
				code: "37_communication",
				appType: "suggestion_communication",
				listFlat: true,
				dotSearch: true,
				dotDetails: true,
				businessCode: ""
			},
			{
				name: "审议结果与报告",
				code: "37_reply",
				appType: "suggestion_reply",
				listFlat: true,
				dotSearch: true,
				dotDetails: true,
				businessCode: ""
			},
			{
				name: "提案线索征集",
				code: "38",
				appType: "proposal_clue",
				businessCode: "proposal_clue"
			},
			{
				name: "立法联系点",
				code: "39",
				appType: "contact_point",
				businessCode: "contact_point",
				onlyBusinessCode: "legis_contact_point",
				doubleCode: "law_contact_point"
			},
			{
				name: "微建议",
				code: "40",
				appType: "min_suggest",
				businessCode: "min_suggest"
			},
			{
				name: "社情民意",
				code: "41",
				appType: "suggestion",
				businessCode: "social"
			},
			{
				name: "立法规划计划",
				code: "42",
				appType: "legislative_planning",
				businessCode: "legislative_planning"
			},
			{
				name: "立法项目库",
				code: "43",
				appType: "legislative_project_library",
				businessCode: "legislative_project_library"
			},
			{
				name: "立法进程",
				code: "44",
				appType: "legislative_process",
				businessCode: "legislative_process"
			},
			{
				name: "政协书院",
				code: "45",
				appType: "classical_learning",
				businessCode: "classical_learning"
			},
			{
				name: "联网监督",
				code: "46",
				appType: "superviseOnline",
				businessCode: "superviseOnline"
			},
			{
				name: "党建工作",
				code: "47",
				appType: "partyActivity",
				businessCode: "partyActivity"
			},
			{
				name: "履职大厅",
				code: "48",
				appType: "dutiesroom",
				businessCode: "dutiesroom"
			},
			{
				name: "履职大厅审核",
				code: "48_1",
				appType: "dutiesroomsub",
				businessCode: "dutiesroomsub"
			},
			{
				name: "履职大厅审核",
				code: "52",
				appType: "conference",
				businessCode: "conference"
			}
		];

		//有保存先获取
		var saveModule = T.getPrefs("baseModule_" + _value);
		if (saveModule) {
			mySeModule = JSON.parse(saveModule);
		} else {
			if (T.isArray(_key)) {
				for (var i = 0; i < _key.length; i++) {
					mySeModule = G.getItemForKey(_value, mybaseModule, _key[i]);
					if (mySeModule) {
						break;
					}
				}
			} else {
				mySeModule = G.getItemForKey(_value, mybaseModule, _key);
			}
		}
		return mySeModule ? mySeModule : {};
	}

	// 打开聊天 type=PRIVATE/GROUP id=userId
	function openChat(_param, _this) {
		if (_param === void 0) {
			_param = {};
		}
		var openPage = "mo_chat";
		var myParam = {
			conversationType: _param.conversationType,
			targetId: _param.targetId || _param.id
		};

		if (_param.paramMore) {
			myParam = T.setNewJSON(myParam, _param.paramMore);
		}
		T.openWin(
			openPage + myParam.targetId,
			"../" + openPage + "/" + openPage + ".stml",
			myParam,
			_this
		);
	}

	// 打开详情共用方法
	function openDetails(_param, _addParam, _this) {
		if (_param === void 0) {
			_param = {};
		}
		if (_addParam === void 0) {
			_addParam = {};
		}
		console.log(JSON.stringify(_param));
		if (_param.isDelete) {
			T.toast("该条目已删除");
			return;
		}
		var myParam = {};
		// myParam.pageType = "page";
		// myParam.title = _param.nTitle || "详情";//页面名字
		// myParam.areaId = _param.areaId || "";
		myParam.id = _param.id;
		myParam.code = _param.code;
		myParam.businessCode = _param.businessCode;
		myParam.url = _param.link; //不要写url 图片用到了这个
		if (myParam.url) {
			myParam.url = T.handleSYSLink(myParam.url, _this);
		}
		var title = _param.title;
		myParam.title =
			title && title.length > 7 ? title.substring(0, 7) + "..." : title;
		myParam = T.setNewJSON(myParam, _addParam);
		if (_param.addParam) {
			myParam = T.setNewJSON(myParam, _param.addParam);
		}
		var openPage = "mo_details";
		console.log("myParam：" + JSON.stringify(myParam));
		var nowModule = baseModule(_param.code, "code");
		switch (myParam.code) {
			case "area": //地区切换
				openPage = "mo_areas";
				break;
			case "add": //通用新增
				openPage = "mo_add";
				break;
			case "search": //通用搜索
				openPage = "mo_search";
				break;
			case "newsList": //资讯列表
				openPage = "mo_news_list";
				break;
			case "business":
				openPage = "mo_business_list";
				break;
			case "showuser":
				openPage = "mo_showuser_list";
				break;
			case "file":
				openPage = "mo_preview_file";
				break;
			case "5":
			case "8":
				break;
			case "6":
			case "24":
				break;
			case "7": //资讯专题详情
				openPage = "mo_news_topic";
				break;
			case "9":
				openPage = myParam.id ? "mo_npcinfo_details" : "mo_npcinfo_list";
				break;
			case "9_1": //代表/委员 信息审核
				openPage = myParam.id ? "mo_npcinfo_review_details" : "mo_npcinfo_review";
				break;
			case "10":
				openPage = myParam.id ? "mo_activity_details" : "mo_business_list_n";
				break;
			case "10_1": //活动资料
				break;
			case "10_2": //活动请假详情
				openPage = "mo_activity_leaveDetail";
				break;
			case "10_3": //活动请假审批 通过了就进详情
				openPage =
					_param.hasComplete == 1
						? "mo_activity_leaveDetail"
						: "mo_activity_leaveReview";
				break;
			case "11":
				openPage = myParam.id ? "mo_performance_file" : "mo_performance_file_list";
				break;
			case "11_2":
				openPage = myParam.id ? openPage : "mo_performance_repair";
				break;
			case "12":
				openPage = myParam.id ? openPage : "mo_circle";
				break;
			case "14_1":
				break;
			case "16_1": //云盘共享
				openPage = "mo_cloud_disk";
				myParam.defaultType = "share";
				break;
			case "20":
				openPage = myParam.id ? "mo_negotiable_details" : "mo_negotiable";
				break;
			case "21":
				openPage = myParam.id
					? "mo_negotiableActivity_details"
					: "mo_business_list";
				break;
			case "22": //通知公告
				openPage = myParam.id ? "mo_notice_detail" : "mo_business_list_n";
				break;
			case "23":
				break;
			case "25":
				openPage = myParam.id
					? "mo_negotiableConference_details"
					: "mo_business_list";
				break;
			case "26": //规范性文件库
				openPage = myParam.id ? "mo_regulations_details" : "mo_regulations";
				break;
			case "27": //规范性文件库
				openPage = "mo_DoubleInteractive_detail";
				break;
			case "28": //规范性文件库
				openPage = "mo_member_duty_content";
				break;
			case "29": //规范性文件库
				openPage = "mo_member_duty_content";
				myParam.type = "schedule_reception";
				break;
			case "30": //投票
				openPage = myParam.id ? "mo_vote_details" : "mo_business_list";
				break;
			case "31": //培训考试
			case "31_1": //成绩单
				openPage = myParam.id ? "mo_exam_answers" : "mo_business_list";
				if (myParam.id && myParam.id.indexOf(",") != -1) {
					var dataId = myParam.id.split(",");
					myParam.id = dataId[0];
					myParam.topicId = dataId[1];
				}
				break;
			case "31_2": //题集训练
			case "31_3": //训练任务
				openPage = myParam.id ? "mo_duty_promotion_transcript_exam_answers" : "";
				if (myParam.id && myParam.id.indexOf(",") != -1) {
					var dataId = myParam.id.split(",");
					myParam.id = dataId[0];
					myParam.topicId = dataId[1];
				}
				break;
			case "32": //建议
				openPage = myParam.id ? "mo_suggestion_details" : "mo_suggestion";
				break;
			case "33": //提案
				openPage = myParam.id ? "mo_proposal_details" : "mo_proposal";
				break;
			case "34": //问卷调查
				openPage = myParam.id ? "mo_survey_details" : "mo_survey_list";
				break;
			case "35": //立法征询
				openPage = myParam.id
					? "mo_legislative_consultation_detail"
					: "mo_legislative_consultation";
				break;
			case "36": //备案审查
				openPage = myParam.id ? "mo_normative_details" : "mo_normative";
				break;
			case "36_1": //备案审查-我的建议
				openPage = "mo_normative_suggest";
				break;
			case "37": //掌上议案
				openPage = myParam.id ? "mo_motion_details" : "mo_business_list";
				break;
			case "38": //提案线索征集
				openPage = myParam.id ? "mo_proposal_clue_details" : "mo_proposal_clue";
				break;
			case "39": //立法联系点
				openPage = myParam.id
					? "mo_legislative_point_detail"
					: "mo_legislative_point";
				break;
			case "40": //微建议
				openPage = myParam.id ? "mo_min_suggest_details" : "mo_min_suggest";
				break;
			case "41": //社情民意
				openPage = myParam.id ? "mo_social_details" : "mo_business_list";
				break;
			case "43": //立法项目库
				openPage = myParam.id
					? "mo_legislative_project_library_details"
					: "mo_legislative_project_library";
				break;
			case "44": //立法进程
				openPage = myParam.id
					? "mo_legislative_process_details"
					: "mo_legislative_process";
				break;
			case "47": //党建工作
				openPage = myParam.id ? "mo_normal_details" : "mo_party_building";
				break;
			case "48": // 履职大厅
				openPage = myParam.id
					? "mo_performance_dynamics_details"
					: "mo_performance_dynamics";
				break;
			case "48_1": // 履职大厅审核
				openPage = myParam.id
					? "mo_performance_dynamics_examine_details"
					: "mo_performance_dynamics_examine";
				break;
			case "videoconferencing_details": //视频会议详情
				openPage = "mo_videoconferencing_details";
				break;
			case "videoconferencing_Join": //加入视频会议
				openPage = "mo_videoconferencing_Join";
				break;
			case "8897": //会议系统扫码相关操作
				break;
			default:
				T.toast("请使用电脑登录系统进行处理");
				return;
		}

		if (nowModule && myParam.id) delete myParam.title;
		myParam.code = _param.ncode || _param.code;
		if (myParam.url) {
			openPage = "mo_details_url";
		}
		if (!openPage) {
			T.toast("请使用电脑登录系统进行处理");
			return;
		}
		if (T.platform() == "web" && myParam.url) {
			window.open(myParam.url);
		} else {
			T.openWin(
				openPage + (myParam.id || myParam.code),
				"../" + openPage + "/" + openPage + ".stml",
				myParam,
				_this
			);
		}

		//增加阅读量
		if (nowModule && nowModule.behaviorCode) {
			addBehaviorRecord(_param, _this);
		}
		//需要已读
		if (_param.isRedDot == 1) {
			addRedDot(_param, _this);
			if (_param.oldId) {
				//消息中心已读
				addRedDot(_param, _this, null, _param.oldBusinessCode, _param.oldId);
			}
		}
	}

	// 增加已读
	function addRedDot(_param, _this, _callback, _businessCode, _businessId) {
		var businessCode = "",
			businessId = "";
		if (_businessCode) {
			businessCode = _businessCode;
			businessId = _businessId;
		} else {
			var nowModule = baseModule(_param.code, "code");
			businessCode = nowModule.businessCode;
			businessId = _param.id;
		}
		T.ajax(
			{u: myjs.appUrl() + "redDot/sign", _this: _this},
			"redDot/sign" + businessId,
			function(ret, err) {
				_callback && _callback(ret, err);
			},
			"设置已读",
			"post",
			{
				body: JSON.stringify({businessCode: businessCode, businessId: businessId})
			}
		);

		_param.isRedDot = "0";
	}

	// 增加阅读量
	function addBehaviorRecord(_param, _this) {
		var nowModule = baseModule(_param.code, "code");
		T.ajax(
			{
				u:
					myjs.appUrl() +
					("behavior/record/" + nowModule.behaviorCode + "/" + _param.id),
				_this: _this
			},
			"behavior/record",
			function(ret, err) {},
			"阅读",
			"post",
			{
				body: JSON.stringify({})
			}
		);
	}

	// 获取各模块详情接口
	function getModuleDetails(_param, _this, _callback) {
		var url = "";
		var postParam = {};

		postParam.detailId = _param.id;
		switch (_param.code) {
			case "file": //文件详情
				url = myjs.appUrl() + "file/info/" + _param.id;
				break;
			case "5":
			case "8":
				url = myjs.appUrl() + "newsContent/info";
				break;
			case "6": //意见征集
			case "24": //协商征集
				url = myjs.appUrl() + "opinioncollect/info";
				break;
			case "10": //活动详情
				url = myjs.appUrl() + "servantactivity/info";
				break;
			case "10_1": //活动资料
				url = myjs.appUrl() + "activitydoc/info";
				break;
			case "11_2": //履职补录
				url = myjs.appUrl() + "performdutiesaddit/info";
				break;
			case "12": //圈子
				url = myjs.appUrl() + "styleCircle/info";
				break;
			case "14_1": //系统消息
				url = myjs.appUrl() + "boxMessage/info";
				break;
			case "20": //协商计划
				url = myjs.appUrl() + "consultPlan/info";
				break;
			case "21": //协商活动
				url = myjs.appUrl() + "consultActivity/info";
				break;
			case "22": //通知公告
				url = myjs.appUrl() + "notification/info";
				break;
			case "23": //协商制度
				url = myjs.appUrl() + "consultRegulation/info";
				break;
			case "25": //远程协商
				url = myjs.appUrl() + "consultActivityConference/info";
				break;
			case "26": //规范性文件库
				url = myjs.appUrl() + "open_api/openStatute/app/info";
				break;
			case "27": //规范性文件库
				url = myjs.appUrl() + "officeOnlineTopic/info";
				break;
			case "28": //委员值班
				url = myjs.appUrl() + "officeOnlineTopic/info";
				break;
			case "29": //委员会客厅
				url = myjs.appUrl() + "officeOnlineTopic/info";
				break;
			case "30": //投票
				url = myjs.appUrl() + "voteTopic/info";
				break;
			case "32": //建议
				url = myjs.appUrl() + "suggestion/info";
				postParam.isOpenWithLock = _param.isOpenWithLock;
				break;
			case "33": //提案
				url = myjs.appUrl() + "proposal/info";
				postParam.isOpenWithLock = _param.isOpenWithLock;
				break;
			case "34": //问卷调查
				url = myjs.appUrl() + "survey/info";
				break;
			case "35": //立法征询
				url = myjs.appUrl() + "legislationOpinion/info";
				break;
			case "36": //备案审查
				url = myjs.appUrl() + "open_api/openStatute/app/info";
				break;
			case "37": //掌上议案
				url = myjs.appUrl() + "motion/info";
				break;
			case "38": //提案线索征集
				url = myjs.appUrl() + "proposalClue/info";
				break;
			case "39": //立法联系点
				url = myjs.appUrl() + "lawConsultRegulation/info";
				break;
			case "40": //微建议
				url = myjs.appUrl() + "microAdvice/info";
				break;
			case "41": //社情民意
				url = myjs.appUrl() + "socialInfo/info";
				break;
			case "42": //立法规划计划详情
				url = myjs.appUrl() + "lawPlanningPlan/info";
				break;
			case "43": //立法项目库
				url = myjs.appUrl() + "lawProject/info";
				break;
			case "44": //立法进程
				url = myjs.appUrl() + "lawProcessInfo/info";
				break;
			case "45": //政协书院
				url = myjs.appUrl() + "syReadingNotes/info";
				break;
			case "47": //党建工作
				url = myjs.appUrl() + "partyActivity/info";
				break;
			case "48": //履职动态
				url = myjs.appUrl() + "performdutiesroom/info";
				break;
			case "48_1": //履职动态审核
				url = myjs.appUrl() + "performdutiesroom/info";
				break;
			default:
				_callback(null, "未携带code");
				return;
		}

		T.ajax(
			{u: url, _this: _this},
			_param.tag || "details" + _param.id,
			function(ret, err) {
				var data = ret && ret.code == 200 ? ret.data || {} : {};
				if (data.id) {
					//得出外部链接
					_param.link =
						data.contentType == 2 ? data.linkUrl || data.externalLinks || "" : "";
				}
				_callback(ret, err);
			},
			"详情",
			"post",
			{
				body: JSON.stringify(postParam)
			},
			_param.header
		);
	}

	//打开扫码
	function openScan(_param, _this, _callback) {
		if (_param === void 0) {
			_param = {};
		}
		function scanResult(_content) {
			if (_content.indexOf(myjs.chatHeader() + "|") != -1) {
				//如果是内部扫码 HeNanZXJavaapp|login|http://aa.com/
				var params = _content.split("|");
				if (params.length < 3) {
					T.alert(
						{title: "识别结果为：", msg: _content, buttons: ["关闭"]},
						function(ret, err) {}
					);
					return;
				}
				switch (params[1]) {
					case "login": //登录
						T.showProgress("登录中");
						T.ajax(
							{
								u:
									myjs.appUrl() + "scanCodeLogin/receipt/appToken?qrCodeId=" + params[2],
								_this: _this
							},
							"appToken",
							function(ret, err) {
								T.hideProgress();
								T.toast(ret ? ret.message || ret.data : T.NET_ERR);
								if (ret && ret.code == 200) {
									_callback && _callback();
								}
							},
							params[1]
						);
						break;
					case "activityCode": //活动签到码	platform5zx|activityCode|580930599619919872|7961|430000
						var url = "",
							param = {
								form: {
									activityId: params[2]
								},

								signInCommand: params[3]
							};

						url = myjs.appUrl() + "activityperson/join";
						T.showProgress("签到中");
						T.ajax(
							{u: url, _this: _this},
							"activityperson/join",
							function(ret, err) {
								T.hideProgress();
								T.toast(ret ? ret.message || ret.data : T.NET_ERR);
								if (ret && ret.code == 200) {
									_callback && _callback();
								}
							},
							params[1],
							"post",
							{
								body: JSON.stringify(param)
							}
						);

						break; //扫群二维码
					case "groupQr":
						var _id = params[2];
						T.showProgress();
						T.ajax(
							{u: myjs.appUrl() + "chatGroup/info", _this: _this},
							"chatGroup/info" + _id,
							function(ret, err) {
								T.hideProgress();
								var data = ret ? ret.data || {} : {};
								if (!ret || ret.code != "200" || !data.id) {
									T.toast("获取群信息失败，请稍候重试");
									return;
								}
								var memberUserIds = data.memberUserIds || [];
								if (G.getItemForKey(G.userId, memberUserIds)) {
									openChat(
										{conversationType: "GROUP", targetId: myjs.chatHeader() + data.id},
										_this
									);
									return;
								}
								T.showProgress("加入中");
								T.ajax(
									{u: myjs.appUrl() + "chatGroup/edit", _this: _this},
									"chatGroup/edit" + _id,
									function(ret, err) {
										T.hideProgress();
										if (!ret || ret.code != "200") {
											T.toast("加入群组失败，请稍候重试");
											return;
										}
										openChat(
											{
												conversationType: "GROUP",
												targetId: myjs.chatHeader() + data.id,
												paramMore: {joinType: "groupQr"}
											},
											_this
										);
									},
									"\u52A0\u5165\u7FA4\u7EC4" + _id,
									"post",
									{
										body: JSON.stringify({
											form: {groupName: data.groupName, id: data.id},
											memberUserIds: memberUserIds.concat([G.userId]),
											ownerUserId: data.ownerUserId
										})
									}
								);
							},
							"\u83B7\u53D6\u7FA4\u4FE1\u606F" + _id,
							"post",
							{
								body: JSON.stringify({detailId: _id})
							}
						);

						break;
					case "toWygzsSignApp": //代表联络站/委员工作室 活动扫码签到
						var url = "";
						if (params.length > 4) {
							url = params[2] + "app/wygzsApp/operationActivityUserByUser?";
						} else {
							T.alert(
								{title: "提示", msg: "后台未配置有误,请联系管理员", buttons: ["关闭"]},
								function(ret, err) {}
							);
							return;
						}

						var paramObj = {activityId: params[4], type: 2, status: 1}; //type:2 签到操作 , status: 1签到
						T.showProgress("签到中");
						T.ajax(
							{u: url, _this: _this},
							"app/wygzsApp/operationActivityUserByUser?",
							function(ret, err) {
								T.hideProgress();
								T.toast(ret ? ret.message || ret.data : T.NET_ERR);
								if (ret && ret.code == 200) {
									var workstationsUrl =
										params[3] +
										"/#/activitiesDetail?id=" +
										params[4] +
										"&token={{token}}&areaId={{areaId}}";
									setTimeout(function() {
										openDetails(
											{code: "8897", link: workstationsUrl, title: "详情"},
											null,
											_this
										);
									}, 1500);
								}
							},
							"代表联络站扫码签到接口",
							"post",
							{
								values: paramObj
							},
							{
								"content-type": "application/x-www-form-urlencoded"
							}
						);

						break;
					case "zhtmeetingSignIn": //会议签到--智会通
						var url = "";
						if (params.length > 3) {
							url = params[2] + "appMeetSign/tosignin?";
						} else {
							T.alert(
								{title: "提示", msg: "后台未配置有误,请联系管理员", buttons: ["关闭"]},
								function(ret, err) {}
							);
							return;
						}
						var paramObj = {
							meetId: params[3],
							activityId: params[3],
							conferenceId: params[3],
							dataId: params[3],
							signInType: "qrCode",
							userId: G.userId,
							type: "signIn"
						};

						T.showProgress("签到中");
						T.ajax(
							{u: url, _this: _this},
							"appMeetSign/tosignin?",
							function(ret, err) {
								T.hideProgress();
								T.toast(ret ? ret.message || ret.data : T.NET_ERR);
								// if(ret&&ret.code==200){

								// }
							},
							"会议系统签到接口",
							"post",
							{
								values: paramObj
							},
							{
								"content-type": "application/x-www-form-urlencoded"
							}
						);

						break;
					default:
						T.alert(
							{title: "识别结果为：", msg: _content, buttons: ["关闭"]},
							function(ret, err) {}
						);
						break;
				}
			} else if (_content.indexOf("http") == 0) {
				openDetails({code: "5", link: _content, title: ""}, null, _this);
			} else {
				T.alert({title: "识别结果为：", msg: _content, buttons: ["关闭"]}, function(
					ret,
					err
				) {});
			}
		}
		if (T.platform() == "app") {
			var zyHmsScan = api.require("zyHmsScan");
			if (!zyHmsScan) {
				T.toast("未绑定扫码模块，请联系开发");
				return;
			}
			var preName = "camera";
			if (
				!T.confirmPer(
					preName,
					"getPicture",
					"用于打开摄像头并扫码，若取消将无法使用扫一扫功能"
				)
			) {
				//相机权限
				T.addEventListener(preName + "Per_" + "getPicture", function(ret, err) {
					T.removeEventListener(preName + "Per_" + "getPicture");
					if (ret.value.granted) {
						openScan(_param, _this, _callback);
					}
				});
				return;
			}
			zyHmsScan.openDefaultView({}, function(ret) {
				if (ret.status) {
					setTimeout(function() {
						scanResult(ret.result);
					}, 300);
				}
			});
		} else if (T.platform() == "mp") {
			wx.scanCode({
				success: function success(ret) {
					setTimeout(function() {
						scanResult(ret.result);
					}, 300);
				}
			});
		}
	}

	// 打开后台配置的应用
	function openModuleDetails(_item, _this) {
		console.log(JSON.stringify(_item));
		var pageParam = {};
		pageParam.title = _item.value;
		pageParam.code = _item.moduleCode;
		if (_item.remarks) {
			var remarkList = _item.remarks.split("&");
			remarkList.forEach(function(_eItem) {
				pageParam[_eItem.split("=")[0]] = _eItem.split("=")[1];
			});
		}
		var moduleUrl = T.handleSYSLink(_item.moduleUrl, _this); //先处理跳转链接
		if (moduleUrl && moduleUrl != "/") {
			if (_item.moduleCode == "15" || moduleUrl == "scan") {
				openScan(null, _this, function(ret) {});
				return;
			}
			if (moduleUrl.indexOf("mo_") == 0) {
				if (
					moduleUrl == "mo_performance_file_list" &&
					!(
						G.isAdmin ||
						G.getItemForKey("worker", G.specialRoleKeys) ||
						G.getItemForKey(
							(myjs.iszx ? "cppcc" : "npc") + "_contact_committee",
							G.specialRoleKeys
						) ||
						G.getItemForKey("delegation_manager", G.specialRoleKeys)
					)
				) {
					moduleUrl = "mo_performance_file";
				}
				T.openWin(
					moduleUrl + pageParam.code,
					"../" + moduleUrl + "/" + moduleUrl + ".stml",
					pageParam,
					_this
				);
			} else if (moduleUrl.indexOf("http") == 0) {
				openDetails(
					{code: "5", link: moduleUrl, title: pageParam.title},
					null,
					_this
				);
			} else {
				T.toast("加急开发中");
			}
		} else {
			T.toast("加急开发中");
		}
	}

	function _inheritsLoose(subClass, superClass) {
		subClass.prototype = Object.create(superClass.prototype);
		subClass.prototype.constructor = subClass;

		_setPrototypeOf(subClass, superClass);
	}

	function _setPrototypeOf(o, p) {
		_setPrototypeOf = Object.setPrototypeOf
			? Object.setPrototypeOf.bind()
			: function _setPrototypeOf(o, p) {
					o.__proto__ = p;
					return o;
			  };
		return _setPrototypeOf(o, p);
	}

	var Hex = /*#__PURE__*/ (function() {
		function Hex() {}
		Hex.stringify = function stringify(wordArray) {
			var hexChars = [];
			for (var i = 0; i < wordArray.sigBytes; i++) {
				var bite = (wordArray.words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
				hexChars.push((bite >>> 4).toString(16));
				hexChars.push((bite & 0x0f).toString(16));
			}
			return hexChars.join("");
		};
		Hex.parse = function parse(hexStr) {
			var hexStrLength = hexStr.length;
			var words = [];
			for (var i = 0; i < hexStrLength; i += 2) {
				words[i >>> 3] |= parseInt(hexStr.substr(i, 2), 16) << (24 - (i % 8) * 4);
			}
			return new WordArray(words, hexStrLength / 2);
		};
		return Hex;
	})();
	var WordArray = /*#__PURE__*/ (function() {
		WordArray.random = function random(nBytes) {
			var words = [];
			var r = function r(m_w) {
				var m_z = 0x3ade68b1;
				var mask = 0xffffffff;
				return function() {
					m_z = (0x9069 * (m_z & 0xffff) + (m_z >> 0x10)) & mask;
					m_w = (0x4650 * (m_w & 0xffff) + (m_w >> 0x10)) & mask;
					var result = ((m_z << 0x10) + m_w) & mask;
					result /= 0x100000000;
					result += 0.5;
					return result * (Math.random() > 0.5 ? 1 : -1);
				};
			};
			for (var i = 0, rcache; i < nBytes; i += 4) {
				var _r = r((rcache || Math.random()) * 0x100000000);
				rcache = _r() * 0x3ade67b7;
				words.push((_r() * 0x100000000) | 0);
			}
			return new WordArray(words, nBytes);
		};
		function WordArray(words, sigBytes) {
			this.words = words || [];
			if (sigBytes !== undefined) {
				this.sigBytes = sigBytes;
			} else {
				this.sigBytes = this.words.length * 4;
			}
		}
		var _proto = WordArray.prototype;
		_proto.toString = function toString(encoder) {
			return (encoder || Hex).stringify(this);
		};
		_proto.concat = function concat(wordArray) {
			this.clamp();
			if (this.sigBytes % 4) {
				for (var i = 0; i < wordArray.sigBytes; i++) {
					var thatByte = (wordArray.words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
					this.words[(this.sigBytes + i) >>> 2] |=
						thatByte << (24 - ((this.sigBytes + i) % 4) * 8);
				}
			} else {
				for (var _i = 0; _i < wordArray.sigBytes; _i += 4) {
					this.words[(this.sigBytes + _i) >>> 2] = wordArray.words[_i >>> 2];
				}
			}
			this.sigBytes += wordArray.sigBytes;
			return this;
		};
		_proto.clamp = function clamp() {
			this.words[this.sigBytes >>> 2] &=
				0xffffffff << (32 - (this.sigBytes % 4) * 8);
			this.words.length = Math.ceil(this.sigBytes / 4);
		};
		_proto.clone = function clone() {
			return new WordArray(this.words.slice(0), this.sigBytes);
		};
		return WordArray;
	})();
	var Latin1 = /*#__PURE__*/ (function() {
		function Latin1() {}
		Latin1.stringify = function stringify(wordArray) {
			var latin1Chars = [];
			for (var i = 0; i < wordArray.sigBytes; i++) {
				var bite = (wordArray.words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
				latin1Chars.push(String.fromCharCode(bite));
			}
			return latin1Chars.join("");
		};
		Latin1.parse = function parse(latin1Str) {
			var latin1StrLength = latin1Str.length;
			var words = [];
			for (var i = 0; i < latin1StrLength; i++) {
				words[i >>> 2] |= (latin1Str.charCodeAt(i) & 0xff) << (24 - (i % 4) * 8);
			}
			return new WordArray(words, latin1StrLength);
		};
		return Latin1;
	})();
	var Utf8 = /*#__PURE__*/ (function() {
		function Utf8() {}
		Utf8.stringify = function stringify(wordArray) {
			try {
				return decodeURIComponent(escape(Latin1.stringify(wordArray)));
			} catch (e) {
				throw new Error("Malformed UTF-8 data");
			}
		};
		Utf8.parse = function parse(utf8Str) {
			return Latin1.parse(unescape(encodeURIComponent(utf8Str)));
		};
		return Utf8;
	})();
	var BufferedBlockAlgorithm = /*#__PURE__*/ (function() {
		function BufferedBlockAlgorithm(cfg) {
			this._minBufferSize = 0;
			this.cfg = Object.assign({blockSize: 1}, cfg);
			this._data = new WordArray();
			this._nDataBytes = 0;
		}
		var _proto2 = BufferedBlockAlgorithm.prototype;
		_proto2.reset = function reset() {
			this._data = new WordArray();
			this._nDataBytes = 0;
		};
		_proto2._append = function _append(data) {
			if (typeof data === "string") {
				data = Utf8.parse(data);
			}
			this._data.concat(data);
			this._nDataBytes += data.sigBytes;
		};
		_proto2._process = function _process(doFlush) {
			if (!this.cfg.blockSize) {
				throw new Error("missing blockSize in config");
			}
			var blockSizeBytes = this.cfg.blockSize * 4;
			var nBlocksReady = this._data.sigBytes / blockSizeBytes;
			if (doFlush) {
				nBlocksReady = Math.ceil(nBlocksReady);
			} else {
				nBlocksReady = Math.max((nBlocksReady | 0) - this._minBufferSize, 0);
			}
			var nWordsReady = nBlocksReady * this.cfg.blockSize;
			var nBytesReady = Math.min(nWordsReady * 4, this._data.sigBytes);
			var processedWords;
			if (nWordsReady) {
				for (var offset = 0; offset < nWordsReady; offset += this.cfg.blockSize) {
					this._doProcessBlock(this._data.words, offset);
				}
				processedWords = this._data.words.splice(0, nWordsReady);
				this._data.sigBytes -= nBytesReady;
			}
			return new WordArray(processedWords, nBytesReady);
		};
		_proto2.clone = function clone() {
			var clone = this.constructor();
			for (var attr in this) {
				if (this.hasOwnProperty(attr)) {
					clone[attr] = this[attr];
				}
			}
			clone._data = this._data.clone();
			return clone;
		};
		return BufferedBlockAlgorithm;
	})();
	var Base = function Base() {};
	var CipherParams = /*#__PURE__*/ (function(_Base) {
		_inheritsLoose(CipherParams, _Base);
		function CipherParams(cipherParams) {
			var _this;
			_this = _Base.call(this) || this;
			_this.ciphertext = cipherParams.ciphertext;
			_this.key = cipherParams.key;
			_this.iv = cipherParams.iv;
			_this.salt = cipherParams.salt;
			_this.algorithm = cipherParams.algorithm;
			_this.mode = cipherParams.mode;
			_this.padding = cipherParams.padding;
			_this.blockSize = cipherParams.blockSize;
			_this.formatter = cipherParams.formatter;
			return _this;
		}
		var _proto3 = CipherParams.prototype;
		_proto3.extend = function extend(additionalParams) {
			if (additionalParams.ciphertext !== undefined) {
				this.ciphertext = additionalParams.ciphertext;
			}
			if (additionalParams.key !== undefined) {
				this.key = additionalParams.key;
			}
			if (additionalParams.iv !== undefined) {
				this.iv = additionalParams.iv;
			}
			if (additionalParams.salt !== undefined) {
				this.salt = additionalParams.salt;
			}
			if (additionalParams.algorithm !== undefined) {
				this.algorithm = additionalParams.algorithm;
			}
			if (additionalParams.mode !== undefined) {
				this.mode = additionalParams.mode;
			}
			if (additionalParams.padding !== undefined) {
				this.padding = additionalParams.padding;
			}
			if (additionalParams.blockSize !== undefined) {
				this.blockSize = additionalParams.blockSize;
			}
			if (additionalParams.formatter !== undefined) {
				this.formatter = additionalParams.formatter;
			}
			return this;
		};
		_proto3.toString = function toString(formatter) {
			if (formatter) {
				return formatter.stringify(this);
			} else if (this.formatter) {
				return this.formatter.stringify(this);
			} else {
				throw new Error(
					"cipher needs a formatter to be able to convert the result into a string"
				);
			}
		};
		return CipherParams;
	})(Base);
	var Base64 = /*#__PURE__*/ (function() {
		function Base64() {}
		Base64.stringify = function stringify(wordArray) {
			wordArray.clamp();
			var base64Chars = [];
			for (var i = 0; i < wordArray.sigBytes; i += 3) {
				var byte1 = (wordArray.words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
				var byte2 =
					(wordArray.words[(i + 1) >>> 2] >>> (24 - ((i + 1) % 4) * 8)) & 0xff;
				var byte3 =
					(wordArray.words[(i + 2) >>> 2] >>> (24 - ((i + 2) % 4) * 8)) & 0xff;
				var triplet = (byte1 << 16) | (byte2 << 8) | byte3;
				for (var j = 0; j < 4 && i + j * 0.75 < wordArray.sigBytes; j++) {
					base64Chars.push(this._map.charAt((triplet >>> (6 * (3 - j))) & 0x3f));
				}
			}
			var paddingChar = this._map.charAt(64);
			if (paddingChar) {
				while (base64Chars.length % 4) {
					base64Chars.push(paddingChar);
				}
			}
			return base64Chars.join("");
		};
		Base64.parse = function parse(base64Str) {
			var base64StrLength = base64Str.length;
			if (this._reverseMap === undefined) {
				this._reverseMap = [];
				for (var j = 0; j < this._map.length; j++) {
					this._reverseMap[this._map.charCodeAt(j)] = j;
				}
			}
			var paddingChar = this._map.charAt(64);
			if (paddingChar) {
				var paddingIndex = base64Str.indexOf(paddingChar);
				if (paddingIndex !== -1) {
					base64StrLength = paddingIndex;
				}
			}
			return this.parseLoop(base64Str, base64StrLength, this._reverseMap);
		};
		Base64.parseLoop = function parseLoop(
			base64Str,
			base64StrLength,
			reverseMap
		) {
			var words = [];
			var nBytes = 0;
			for (var i = 0; i < base64StrLength; i++) {
				if (i % 4) {
					var bits1 = reverseMap[base64Str.charCodeAt(i - 1)] << ((i % 4) * 2);
					var bits2 = reverseMap[base64Str.charCodeAt(i)] >>> (6 - (i % 4) * 2);
					words[nBytes >>> 2] |= (bits1 | bits2) << (24 - (nBytes % 4) * 8);
					nBytes++;
				}
			}
			return new WordArray(words, nBytes);
		};
		return Base64;
	})();
	Base64._map =
		"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
	Base64._reverseMap = undefined;
	var OpenSSL = /*#__PURE__*/ (function() {
		function OpenSSL() {}
		OpenSSL.stringify = function stringify(cipherParams) {
			if (!cipherParams.ciphertext) {
				throw new Error("missing ciphertext in params");
			}
			var ciphertext = cipherParams.ciphertext;
			var salt = cipherParams.salt;
			var wordArray;
			if (salt) {
				if (typeof salt === "string") {
					throw new Error("salt is expected to be a WordArray");
				}
				wordArray = new WordArray([0x53616c74, 0x65645f5f])
					.concat(salt)
					.concat(ciphertext);
			} else {
				wordArray = ciphertext;
			}
			return wordArray.toString(Base64);
		};
		OpenSSL.parse = function parse(openSSLStr) {
			var ciphertext = Base64.parse(openSSLStr);
			var salt;
			if (
				ciphertext.words[0] === 0x53616c74 &&
				ciphertext.words[1] === 0x65645f5f
			) {
				salt = new WordArray(ciphertext.words.slice(2, 4));
				ciphertext.words.splice(0, 4);
				ciphertext.sigBytes -= 16;
			}
			return new CipherParams({ciphertext: ciphertext, salt: salt});
		};
		return OpenSSL;
	})();
	var SerializableCipher = /*#__PURE__*/ (function() {
		function SerializableCipher() {}
		SerializableCipher.encrypt = function encrypt(cipher, message, key, cfg) {
			var config = Object.assign({}, this.cfg, cfg);
			var encryptor = cipher.createEncryptor(key, config);
			var ciphertext = encryptor.finalize(message);
			return new CipherParams({
				ciphertext: ciphertext,
				key: key,
				iv: encryptor.cfg.iv,
				algorithm: cipher,
				mode: encryptor.cfg.mode,
				padding: encryptor.cfg.padding,
				blockSize: encryptor.cfg.blockSize,
				formatter: config.format
			});
		};
		SerializableCipher.decrypt = function decrypt(
			cipher,
			ciphertext,
			key,
			optionalCfg
		) {
			var cfg = Object.assign({}, this.cfg, optionalCfg);
			if (!cfg.format) {
				throw new Error("could not determine format");
			}
			ciphertext = this._parse(ciphertext, cfg.format);
			if (!ciphertext.ciphertext) {
				throw new Error("could not determine ciphertext");
			}
			var plaintext = cipher
				.createDecryptor(key, cfg)
				.finalize(ciphertext.ciphertext);
			return plaintext;
		};
		SerializableCipher._parse = function _parse(ciphertext, format) {
			if (typeof ciphertext === "string") {
				return format.parse(ciphertext);
			} else {
				return ciphertext;
			}
		};
		return SerializableCipher;
	})();
	SerializableCipher.cfg = {
		blockSize: 4,
		iv: new WordArray([]),
		format: OpenSSL
	};
	var Hasher = /*#__PURE__*/ (function(_BufferedBlockAlgorit) {
		_inheritsLoose(Hasher, _BufferedBlockAlgorit);
		Hasher._createHelper = function _createHelper(hasher) {
			function helper(message, cfg) {
				var hasherClass = hasher;
				var hasherInstance = new hasherClass(cfg);
				return hasherInstance.finalize(message);
			}
			return helper;
		};
		function Hasher(cfg) {
			var _this2;
			_this2 =
				_BufferedBlockAlgorit.call(
					this,
					Object.assign({blockSize: 512 / 32}, cfg)
				) || this;
			_this2.reset();
			return _this2;
		}
		var _proto4 = Hasher.prototype;
		_proto4.update = function update(messageUpdate) {
			this._append(messageUpdate);
			this._process();
			return this;
		};
		_proto4.finalize = function finalize(messageUpdate) {
			if (messageUpdate) {
				this._append(messageUpdate);
			}
			var hash = this._doFinalize();
			return hash;
		};
		return Hasher;
	})(BufferedBlockAlgorithm);
	var T$1 = [];
	for (var i = 0; i < 64; i++) {
		T$1[i] = (Math.abs(Math.sin(i + 1)) * 0x100000000) | 0;
	}
	var MD5 = /*#__PURE__*/ (function(_Hasher) {
		_inheritsLoose(MD5, _Hasher);
		function MD5() {
			return _Hasher.apply(this, arguments) || this;
		}
		MD5.FF = function FF(a, b, c, d, x, s, t) {
			var n = a + ((b & c) | (~b & d)) + x + t;
			return ((n << s) | (n >>> (32 - s))) + b;
		};
		MD5.GG = function GG(a, b, c, d, x, s, t) {
			var n = a + ((b & d) | (c & ~d)) + x + t;
			return ((n << s) | (n >>> (32 - s))) + b;
		};
		MD5.HH = function HH(a, b, c, d, x, s, t) {
			var n = a + (b ^ c ^ d) + x + t;
			return ((n << s) | (n >>> (32 - s))) + b;
		};
		MD5.II = function II(a, b, c, d, x, s, t) {
			var n = a + (c ^ (b | ~d)) + x + t;
			return ((n << s) | (n >>> (32 - s))) + b;
		};
		var _proto5 = MD5.prototype;
		_proto5.reset = function reset() {
			_Hasher.prototype.reset.call(this);
			this._hash = new WordArray([0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476]);
		};
		_proto5._doProcessBlock = function _doProcessBlock(M, offset) {
			for (var _i2 = 0; _i2 < 16; _i2++) {
				var offset_i = offset + _i2;
				var M_offset_i = M[offset_i];
				M[offset_i] =
					(((M_offset_i << 8) | (M_offset_i >>> 24)) & 0x00ff00ff) |
					(((M_offset_i << 24) | (M_offset_i >>> 8)) & 0xff00ff00);
			}
			var H = this._hash.words;
			var M_offset_0 = M[offset + 0];
			var M_offset_1 = M[offset + 1];
			var M_offset_2 = M[offset + 2];
			var M_offset_3 = M[offset + 3];
			var M_offset_4 = M[offset + 4];
			var M_offset_5 = M[offset + 5];
			var M_offset_6 = M[offset + 6];
			var M_offset_7 = M[offset + 7];
			var M_offset_8 = M[offset + 8];
			var M_offset_9 = M[offset + 9];
			var M_offset_10 = M[offset + 10];
			var M_offset_11 = M[offset + 11];
			var M_offset_12 = M[offset + 12];
			var M_offset_13 = M[offset + 13];
			var M_offset_14 = M[offset + 14];
			var M_offset_15 = M[offset + 15];
			var a = H[0];
			var b = H[1];
			var c = H[2];
			var d = H[3];
			a = MD5.FF(a, b, c, d, M_offset_0, 7, T$1[0]);
			d = MD5.FF(d, a, b, c, M_offset_1, 12, T$1[1]);
			c = MD5.FF(c, d, a, b, M_offset_2, 17, T$1[2]);
			b = MD5.FF(b, c, d, a, M_offset_3, 22, T$1[3]);
			a = MD5.FF(a, b, c, d, M_offset_4, 7, T$1[4]);
			d = MD5.FF(d, a, b, c, M_offset_5, 12, T$1[5]);
			c = MD5.FF(c, d, a, b, M_offset_6, 17, T$1[6]);
			b = MD5.FF(b, c, d, a, M_offset_7, 22, T$1[7]);
			a = MD5.FF(a, b, c, d, M_offset_8, 7, T$1[8]);
			d = MD5.FF(d, a, b, c, M_offset_9, 12, T$1[9]);
			c = MD5.FF(c, d, a, b, M_offset_10, 17, T$1[10]);
			b = MD5.FF(b, c, d, a, M_offset_11, 22, T$1[11]);
			a = MD5.FF(a, b, c, d, M_offset_12, 7, T$1[12]);
			d = MD5.FF(d, a, b, c, M_offset_13, 12, T$1[13]);
			c = MD5.FF(c, d, a, b, M_offset_14, 17, T$1[14]);
			b = MD5.FF(b, c, d, a, M_offset_15, 22, T$1[15]);
			a = MD5.GG(a, b, c, d, M_offset_1, 5, T$1[16]);
			d = MD5.GG(d, a, b, c, M_offset_6, 9, T$1[17]);
			c = MD5.GG(c, d, a, b, M_offset_11, 14, T$1[18]);
			b = MD5.GG(b, c, d, a, M_offset_0, 20, T$1[19]);
			a = MD5.GG(a, b, c, d, M_offset_5, 5, T$1[20]);
			d = MD5.GG(d, a, b, c, M_offset_10, 9, T$1[21]);
			c = MD5.GG(c, d, a, b, M_offset_15, 14, T$1[22]);
			b = MD5.GG(b, c, d, a, M_offset_4, 20, T$1[23]);
			a = MD5.GG(a, b, c, d, M_offset_9, 5, T$1[24]);
			d = MD5.GG(d, a, b, c, M_offset_14, 9, T$1[25]);
			c = MD5.GG(c, d, a, b, M_offset_3, 14, T$1[26]);
			b = MD5.GG(b, c, d, a, M_offset_8, 20, T$1[27]);
			a = MD5.GG(a, b, c, d, M_offset_13, 5, T$1[28]);
			d = MD5.GG(d, a, b, c, M_offset_2, 9, T$1[29]);
			c = MD5.GG(c, d, a, b, M_offset_7, 14, T$1[30]);
			b = MD5.GG(b, c, d, a, M_offset_12, 20, T$1[31]);
			a = MD5.HH(a, b, c, d, M_offset_5, 4, T$1[32]);
			d = MD5.HH(d, a, b, c, M_offset_8, 11, T$1[33]);
			c = MD5.HH(c, d, a, b, M_offset_11, 16, T$1[34]);
			b = MD5.HH(b, c, d, a, M_offset_14, 23, T$1[35]);
			a = MD5.HH(a, b, c, d, M_offset_1, 4, T$1[36]);
			d = MD5.HH(d, a, b, c, M_offset_4, 11, T$1[37]);
			c = MD5.HH(c, d, a, b, M_offset_7, 16, T$1[38]);
			b = MD5.HH(b, c, d, a, M_offset_10, 23, T$1[39]);
			a = MD5.HH(a, b, c, d, M_offset_13, 4, T$1[40]);
			d = MD5.HH(d, a, b, c, M_offset_0, 11, T$1[41]);
			c = MD5.HH(c, d, a, b, M_offset_3, 16, T$1[42]);
			b = MD5.HH(b, c, d, a, M_offset_6, 23, T$1[43]);
			a = MD5.HH(a, b, c, d, M_offset_9, 4, T$1[44]);
			d = MD5.HH(d, a, b, c, M_offset_12, 11, T$1[45]);
			c = MD5.HH(c, d, a, b, M_offset_15, 16, T$1[46]);
			b = MD5.HH(b, c, d, a, M_offset_2, 23, T$1[47]);
			a = MD5.II(a, b, c, d, M_offset_0, 6, T$1[48]);
			d = MD5.II(d, a, b, c, M_offset_7, 10, T$1[49]);
			c = MD5.II(c, d, a, b, M_offset_14, 15, T$1[50]);
			b = MD5.II(b, c, d, a, M_offset_5, 21, T$1[51]);
			a = MD5.II(a, b, c, d, M_offset_12, 6, T$1[52]);
			d = MD5.II(d, a, b, c, M_offset_3, 10, T$1[53]);
			c = MD5.II(c, d, a, b, M_offset_10, 15, T$1[54]);
			b = MD5.II(b, c, d, a, M_offset_1, 21, T$1[55]);
			a = MD5.II(a, b, c, d, M_offset_8, 6, T$1[56]);
			d = MD5.II(d, a, b, c, M_offset_15, 10, T$1[57]);
			c = MD5.II(c, d, a, b, M_offset_6, 15, T$1[58]);
			b = MD5.II(b, c, d, a, M_offset_13, 21, T$1[59]);
			a = MD5.II(a, b, c, d, M_offset_4, 6, T$1[60]);
			d = MD5.II(d, a, b, c, M_offset_11, 10, T$1[61]);
			c = MD5.II(c, d, a, b, M_offset_2, 15, T$1[62]);
			b = MD5.II(b, c, d, a, M_offset_9, 21, T$1[63]);
			H[0] = (H[0] + a) | 0;
			H[1] = (H[1] + b) | 0;
			H[2] = (H[2] + c) | 0;
			H[3] = (H[3] + d) | 0;
		};
		_proto5._doFinalize = function _doFinalize() {
			var data = this._data;
			var dataWords = data.words;
			var nBitsTotal = this._nDataBytes * 8;
			var nBitsLeft = data.sigBytes * 8;
			dataWords[nBitsLeft >>> 5] |= 0x80 << (24 - (nBitsLeft % 32));
			var nBitsTotalH = Math.floor(nBitsTotal / 0x100000000);
			var nBitsTotalL = nBitsTotal;
			dataWords[(((nBitsLeft + 64) >>> 9) << 4) + 15] =
				(((nBitsTotalH << 8) | (nBitsTotalH >>> 24)) & 0x00ff00ff) |
				(((nBitsTotalH << 24) | (nBitsTotalH >>> 8)) & 0xff00ff00);
			dataWords[(((nBitsLeft + 64) >>> 9) << 4) + 14] =
				(((nBitsTotalL << 8) | (nBitsTotalL >>> 24)) & 0x00ff00ff) |
				(((nBitsTotalL << 24) | (nBitsTotalL >>> 8)) & 0xff00ff00);
			data.sigBytes = (dataWords.length + 1) * 4;
			this._process();
			var hash = this._hash;
			var H = hash.words;
			for (var _i3 = 0; _i3 < 4; _i3++) {
				var H_i = H[_i3];
				H[_i3] =
					(((H_i << 8) | (H_i >>> 24)) & 0x00ff00ff) |
					(((H_i << 24) | (H_i >>> 8)) & 0xff00ff00);
			}
			return hash;
		};
		return MD5;
	})(Hasher);
	var EvpKDF = /*#__PURE__*/ (function() {
		function EvpKDF(cfg) {
			this.cfg = Object.assign(
				{keySize: 128 / 32, hasher: MD5, iterations: 1},
				cfg
			);
		}
		var _proto6 = EvpKDF.prototype;
		_proto6.compute = function compute(password, salt) {
			var hasher = new this.cfg.hasher();
			var derivedKey = new WordArray();
			var block;
			while (derivedKey.words.length < this.cfg.keySize) {
				if (block) {
					hasher.update(block);
				}
				block = hasher.update(password).finalize(salt);
				hasher.reset();
				for (var _i4 = 1; _i4 < this.cfg.iterations; _i4++) {
					block = hasher.finalize(block);
					hasher.reset();
				}
				derivedKey.concat(block);
			}
			derivedKey.sigBytes = this.cfg.keySize * 4;
			return derivedKey;
		};
		return EvpKDF;
	})();
	var OpenSSLKdf = /*#__PURE__*/ (function() {
		function OpenSSLKdf() {}
		OpenSSLKdf.execute = function execute(password, keySize, ivSize, salt) {
			if (!salt) {
				salt = WordArray.random(64 / 8);
			}
			var key = new EvpKDF({keySize: keySize + ivSize}).compute(password, salt);
			var iv = new WordArray(key.words.slice(keySize), ivSize * 4);
			key.sigBytes = keySize * 4;
			return new CipherParams({key: key, iv: iv, salt: salt});
		};
		return OpenSSLKdf;
	})();
	var PasswordBasedCipher = /*#__PURE__*/ (function() {
		function PasswordBasedCipher() {}
		PasswordBasedCipher.encrypt = function encrypt(
			cipher,
			message,
			password,
			cfg
		) {
			var config = Object.assign({}, this.cfg, cfg);
			if (config.kdf === undefined) {
				throw new Error("missing kdf in config");
			}
			var derivedParams = config.kdf.execute(
				password,
				cipher.keySize,
				cipher.ivSize
			);
			if (derivedParams.iv !== undefined) {
				config.iv = derivedParams.iv;
			}
			var ciphertext = SerializableCipher.encrypt.call(
				this,
				cipher,
				message,
				derivedParams.key,
				config
			);
			return ciphertext.extend(derivedParams);
		};
		PasswordBasedCipher.decrypt = function decrypt(
			cipher,
			ciphertext,
			password,
			cfg
		) {
			var config = Object.assign({}, this.cfg, cfg);
			if (config.format === undefined) {
				throw new Error("missing format in config");
			}
			ciphertext = this._parse(ciphertext, config.format);
			if (config.kdf === undefined) {
				throw new Error("the key derivation function must be set");
			}
			var derivedParams = config.kdf.execute(
				password,
				cipher.keySize,
				cipher.ivSize,
				ciphertext.salt
			);
			if (derivedParams.iv !== undefined) {
				config.iv = derivedParams.iv;
			}
			var plaintext = SerializableCipher.decrypt.call(
				this,
				cipher,
				ciphertext,
				derivedParams.key,
				config
			);
			return plaintext;
		};
		PasswordBasedCipher._parse = function _parse(ciphertext, format) {
			if (typeof ciphertext === "string") {
				return format.parse(ciphertext);
			} else {
				return ciphertext;
			}
		};
		return PasswordBasedCipher;
	})();
	PasswordBasedCipher.cfg = {
		blockSize: 4,
		iv: new WordArray([]),
		format: OpenSSL,
		kdf: OpenSSLKdf
	};
	var Cipher = /*#__PURE__*/ (function(_BufferedBlockAlgorit2) {
		_inheritsLoose(Cipher, _BufferedBlockAlgorit2);
		function Cipher(xformMode, key, cfg) {
			var _this3;
			_this3 =
				_BufferedBlockAlgorit2.call(this, Object.assign({blockSize: 1}, cfg)) ||
				this;
			_this3._xformMode = xformMode;
			_this3._key = key;
			_this3.reset();
			return _this3;
		}
		Cipher.createEncryptor = function createEncryptor(key, cfg) {
			var thisClass = this;
			return new thisClass(this._ENC_XFORM_MODE, key, cfg);
		};
		Cipher.createDecryptor = function createDecryptor(key, cfg) {
			var thisClass = this;
			return new thisClass(this._DEC_XFORM_MODE, key, cfg);
		};
		Cipher._createHelper = function _createHelper(cipher) {
			function encrypt(message, key, cfg) {
				if (typeof key === "string") {
					return PasswordBasedCipher.encrypt(cipher, message, key, cfg);
				} else {
					return SerializableCipher.encrypt(cipher, message, key, cfg);
				}
			}
			function decrypt(ciphertext, key, cfg) {
				if (typeof key === "string") {
					return PasswordBasedCipher.decrypt(cipher, ciphertext, key, cfg);
				} else {
					return SerializableCipher.decrypt(cipher, ciphertext, key, cfg);
				}
			}
			return {encrypt: encrypt, decrypt: decrypt};
		};
		var _proto7 = Cipher.prototype;
		_proto7.process = function process(dataUpdate) {
			this._append(dataUpdate);
			return this._process();
		};
		_proto7.finalize = function finalize(dataUpdate) {
			if (dataUpdate) {
				this._append(dataUpdate);
			}
			var finalProcessedData = this._doFinalize();
			return finalProcessedData;
		};
		return Cipher;
	})(BufferedBlockAlgorithm);
	Cipher._ENC_XFORM_MODE = 1;
	Cipher._DEC_XFORM_MODE = 2;
	Cipher.keySize = 4;
	Cipher.ivSize = 4;
	var BlockCipherModeAlgorithm = /*#__PURE__*/ (function() {
		function BlockCipherModeAlgorithm(cipher, iv) {
			this.init(cipher, iv);
		}
		var _proto8 = BlockCipherModeAlgorithm.prototype;
		_proto8.init = function init(cipher, iv) {
			this._cipher = cipher;
			this._iv = iv;
		};
		return BlockCipherModeAlgorithm;
	})();
	var BlockCipherMode = /*#__PURE__*/ (function() {
		function BlockCipherMode() {}
		BlockCipherMode.createEncryptor = function createEncryptor(cipher, iv) {
			var encryptorClass = this.Encryptor;
			return new encryptorClass(cipher, iv);
		};
		BlockCipherMode.createDecryptor = function createDecryptor(cipher, iv) {
			var decryptorClass = this.Decryptor;
			return new decryptorClass(cipher, iv);
		};
		return BlockCipherMode;
	})();
	BlockCipherMode.Encryptor = BlockCipherModeAlgorithm;
	BlockCipherMode.Decryptor = BlockCipherModeAlgorithm;
	var CBCEncryptor = /*#__PURE__*/ (function(_BlockCipherModeAlgor) {
		_inheritsLoose(CBCEncryptor, _BlockCipherModeAlgor);
		function CBCEncryptor() {
			return _BlockCipherModeAlgor.apply(this, arguments) || this;
		}
		var _proto9 = CBCEncryptor.prototype;
		_proto9.processBlock = function processBlock(words, offset) {
			if (this._cipher.cfg.blockSize === undefined) {
				throw new Error("missing blockSize in cipher config");
			}
			this.xorBlock(words, offset, this._cipher.cfg.blockSize);
			this._cipher.encryptBlock(words, offset);
			this._prevBlock = words.slice(offset, offset + this._cipher.cfg.blockSize);
		};
		_proto9.xorBlock = function xorBlock(words, offset, blockSize) {
			var block;
			if (this._iv) {
				block = this._iv;
				this._iv = undefined;
			} else {
				block = this._prevBlock;
			}
			if (block !== undefined) {
				for (var _i5 = 0; _i5 < blockSize; _i5++) {
					words[offset + _i5] ^= block[_i5];
				}
			}
		};
		return CBCEncryptor;
	})(BlockCipherModeAlgorithm);
	var CBCDecryptor = /*#__PURE__*/ (function(_BlockCipherModeAlgor2) {
		_inheritsLoose(CBCDecryptor, _BlockCipherModeAlgor2);
		function CBCDecryptor() {
			return _BlockCipherModeAlgor2.apply(this, arguments) || this;
		}
		var _proto10 = CBCDecryptor.prototype;
		_proto10.processBlock = function processBlock(words, offset) {
			if (this._cipher.cfg.blockSize === undefined) {
				throw new Error("missing blockSize in cipher config");
			}
			var thisBlock = words.slice(offset, offset + this._cipher.cfg.blockSize);
			this._cipher.decryptBlock(words, offset);
			this.xorBlock(words, offset, this._cipher.cfg.blockSize);
			this._prevBlock = thisBlock;
		};
		_proto10.xorBlock = function xorBlock(words, offset, blockSize) {
			var block;
			if (this._iv) {
				block = this._iv;
				this._iv = undefined;
			} else {
				block = this._prevBlock;
			}
			if (block !== undefined) {
				for (var _i6 = 0; _i6 < blockSize; _i6++) {
					words[offset + _i6] ^= block[_i6];
				}
			}
		};
		return CBCDecryptor;
	})(BlockCipherModeAlgorithm);
	var CBC = /*#__PURE__*/ (function(_BlockCipherMode) {
		_inheritsLoose(CBC, _BlockCipherMode);
		function CBC() {
			return _BlockCipherMode.apply(this, arguments) || this;
		}
		return CBC;
	})(BlockCipherMode);
	CBC.Encryptor = CBCEncryptor;
	CBC.Decryptor = CBCDecryptor;
	var PKCS7 = /*#__PURE__*/ (function() {
		function PKCS7() {}
		PKCS7.pad = function pad(data, blockSize) {
			var blockSizeBytes = blockSize * 4;
			var nPaddingBytes = blockSizeBytes - (data.sigBytes % blockSizeBytes);
			var paddingWord =
				(nPaddingBytes << 24) |
				(nPaddingBytes << 16) |
				(nPaddingBytes << 8) |
				nPaddingBytes;
			var paddingWords = [];
			for (var _i7 = 0; _i7 < nPaddingBytes; _i7 += 4) {
				paddingWords.push(paddingWord);
			}
			var padding = new WordArray(paddingWords, nPaddingBytes);
			data.concat(padding);
		};
		PKCS7.unpad = function unpad(data) {
			var nPaddingBytes = data.words[(data.sigBytes - 1) >>> 2] & 0xff;
			data.sigBytes -= nPaddingBytes;
		};
		return PKCS7;
	})();
	var BlockCipher = /*#__PURE__*/ (function(_Cipher) {
		_inheritsLoose(BlockCipher, _Cipher);
		function BlockCipher(xformMode, key, cfg) {
			return (
				_Cipher.call(
					this,
					xformMode,
					key,
					Object.assign({blockSize: 4, mode: CBC, padding: PKCS7}, cfg)
				) || this
			);
		}
		var _proto11 = BlockCipher.prototype;
		_proto11.reset = function reset() {
			_Cipher.prototype.reset.call(this);
			if (this.cfg.mode === undefined) {
				throw new Error("missing mode in config");
			}
			var modeCreator;
			if (this._xformMode === this.constructor._ENC_XFORM_MODE) {
				modeCreator = this.cfg.mode.createEncryptor;
			} else {
				modeCreator = this.cfg.mode.createDecryptor;
				this._minBufferSize = 1;
			}
			if (this._mode && this._mode.__creator === modeCreator) {
				this._mode.init(this, this.cfg.iv && this.cfg.iv.words);
			} else {
				this._mode = modeCreator.call(
					this.cfg.mode,
					this,
					this.cfg.iv && this.cfg.iv.words
				);
				this._mode.__creator = modeCreator;
			}
		};
		_proto11._doProcessBlock = function _doProcessBlock(words, offset) {
			this._mode.processBlock(words, offset);
		};
		_proto11._doFinalize = function _doFinalize() {
			if (this.cfg.padding === undefined) {
				throw new Error("missing padding in config");
			}
			var finalProcessedBlocks;
			if (this._xformMode === this.constructor._ENC_XFORM_MODE) {
				if (this.cfg.blockSize === undefined) {
					throw new Error("missing blockSize in config");
				}
				this.cfg.padding.pad(this._data, this.cfg.blockSize);
				finalProcessedBlocks = this._process(!!"flush");
			} else {
				finalProcessedBlocks = this._process(!!"flush");
				this.cfg.padding.unpad(finalProcessedBlocks);
			}
			return finalProcessedBlocks;
		};
		return BlockCipher;
	})(Cipher);
	var SBOX = [];
	var INV_SBOX = [];
	var SUB_MIX_0 = [];
	var SUB_MIX_1 = [];
	var SUB_MIX_2 = [];
	var SUB_MIX_3 = [];
	var INV_SUB_MIX_0 = [];
	var INV_SUB_MIX_1 = [];
	var INV_SUB_MIX_2 = [];
	var INV_SUB_MIX_3 = [];
	(function() {
		var d = [];
		for (var _i8 = 0; _i8 < 256; _i8++) {
			if (_i8 < 128) {
				d[_i8] = _i8 << 1;
			} else {
				d[_i8] = (_i8 << 1) ^ 0x11b;
			}
		}
		var x = 0;
		var xi = 0;
		for (var _i9 = 0; _i9 < 256; _i9++) {
			var sx = xi ^ (xi << 1) ^ (xi << 2) ^ (xi << 3) ^ (xi << 4);
			sx = (sx >>> 8) ^ (sx & 0xff) ^ 0x63;
			SBOX[x] = sx;
			INV_SBOX[sx] = x;
			var x2 = d[x];
			var x4 = d[x2];
			var x8 = d[x4];
			var t = (d[sx] * 0x101) ^ (sx * 0x1010100);
			SUB_MIX_0[x] = (t << 24) | (t >>> 8);
			SUB_MIX_1[x] = (t << 16) | (t >>> 16);
			SUB_MIX_2[x] = (t << 8) | (t >>> 24);
			SUB_MIX_3[x] = t;
			t = (x8 * 0x1010101) ^ (x4 * 0x10001) ^ (x2 * 0x101) ^ (x * 0x1010100);
			INV_SUB_MIX_0[sx] = (t << 24) | (t >>> 8);
			INV_SUB_MIX_1[sx] = (t << 16) | (t >>> 16);
			INV_SUB_MIX_2[sx] = (t << 8) | (t >>> 24);
			INV_SUB_MIX_3[sx] = t;
			if (!x) {
				x = xi = 1;
			} else {
				x = x2 ^ d[d[d[x8 ^ x2]]];
				xi ^= d[d[xi]];
			}
		}
	})();
	var RCON = [0x00, 0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80, 0x1b, 0x36];
	var AES = /*#__PURE__*/ (function(_BlockCipher) {
		_inheritsLoose(AES, _BlockCipher);
		function AES(xformMode, key, cfg) {
			return _BlockCipher.call(this, xformMode, key, cfg) || this;
		}
		var _proto12 = AES.prototype;
		_proto12.reset = function reset() {
			_BlockCipher.prototype.reset.call(this);
			if (this._nRounds && this._keyPriorReset === this._key) {
				return;
			}
			var key = (this._keyPriorReset = this._key);
			var keyWords = key.words;
			var keySize = key.sigBytes / 4;
			var nRounds = (this._nRounds = keySize + 6);
			var ksRows = (nRounds + 1) * 4;
			var keySchedule = (this._keySchedule = []);
			for (var ksRow = 0; ksRow < ksRows; ksRow++) {
				if (ksRow < keySize) {
					keySchedule[ksRow] = keyWords[ksRow];
				} else {
					var t = keySchedule[ksRow - 1];
					if (!(ksRow % keySize)) {
						t = (t << 8) | (t >>> 24);
						t =
							(SBOX[t >>> 24] << 24) |
							(SBOX[(t >>> 16) & 0xff] << 16) |
							(SBOX[(t >>> 8) & 0xff] << 8) |
							SBOX[t & 0xff];
						t ^= RCON[(ksRow / keySize) | 0] << 24;
					} else if (keySize > 6 && ksRow % keySize === 4) {
						t =
							(SBOX[t >>> 24] << 24) |
							(SBOX[(t >>> 16) & 0xff] << 16) |
							(SBOX[(t >>> 8) & 0xff] << 8) |
							SBOX[t & 0xff];
					}
					keySchedule[ksRow] = keySchedule[ksRow - keySize] ^ t;
				}
			}
			var invKeySchedule = (this._invKeySchedule = []);
			for (var invKsRow = 0; invKsRow < ksRows; invKsRow++) {
				var _ksRow = ksRows - invKsRow;
				var _t = void 0;
				if (invKsRow % 4) {
					_t = keySchedule[_ksRow];
				} else {
					_t = keySchedule[_ksRow - 4];
				}
				if (invKsRow < 4 || _ksRow <= 4) {
					invKeySchedule[invKsRow] = _t;
				} else {
					invKeySchedule[invKsRow] =
						INV_SUB_MIX_0[SBOX[_t >>> 24]] ^
						INV_SUB_MIX_1[SBOX[(_t >>> 16) & 0xff]] ^
						INV_SUB_MIX_2[SBOX[(_t >>> 8) & 0xff]] ^
						INV_SUB_MIX_3[SBOX[_t & 0xff]];
				}
			}
		};
		_proto12.encryptBlock = function encryptBlock(M, offset) {
			this._doCryptBlock(
				M,
				offset,
				this._keySchedule,
				SUB_MIX_0,
				SUB_MIX_1,
				SUB_MIX_2,
				SUB_MIX_3,
				SBOX
			);
		};
		_proto12.decryptBlock = function decryptBlock(M, offset) {
			var t = M[offset + 1];
			M[offset + 1] = M[offset + 3];
			M[offset + 3] = t;
			this._doCryptBlock(
				M,
				offset,
				this._invKeySchedule,
				INV_SUB_MIX_0,
				INV_SUB_MIX_1,
				INV_SUB_MIX_2,
				INV_SUB_MIX_3,
				INV_SBOX
			);
			t = M[offset + 1];
			M[offset + 1] = M[offset + 3];
			M[offset + 3] = t;
		};
		_proto12._doCryptBlock = function _doCryptBlock(
			M,
			offset,
			keySchedule,
			sub_mix_0,
			sub_mix_1,
			sub_mix_2,
			sub_mix_3,
			sbox
		) {
			var s0 = M[offset] ^ keySchedule[0];
			var s1 = M[offset + 1] ^ keySchedule[1];
			var s2 = M[offset + 2] ^ keySchedule[2];
			var s3 = M[offset + 3] ^ keySchedule[3];
			var ksRow = 4;
			for (var round = 1; round < this._nRounds; round++) {
				var t0 =
					sub_mix_0[s0 >>> 24] ^
					sub_mix_1[(s1 >>> 16) & 0xff] ^
					sub_mix_2[(s2 >>> 8) & 0xff] ^
					sub_mix_3[s3 & 0xff] ^
					keySchedule[ksRow++];
				var t1 =
					sub_mix_0[s1 >>> 24] ^
					sub_mix_1[(s2 >>> 16) & 0xff] ^
					sub_mix_2[(s3 >>> 8) & 0xff] ^
					sub_mix_3[s0 & 0xff] ^
					keySchedule[ksRow++];
				var t2 =
					sub_mix_0[s2 >>> 24] ^
					sub_mix_1[(s3 >>> 16) & 0xff] ^
					sub_mix_2[(s0 >>> 8) & 0xff] ^
					sub_mix_3[s1 & 0xff] ^
					keySchedule[ksRow++];
				var t3 =
					sub_mix_0[s3 >>> 24] ^
					sub_mix_1[(s0 >>> 16) & 0xff] ^
					sub_mix_2[(s1 >>> 8) & 0xff] ^
					sub_mix_3[s2 & 0xff] ^
					keySchedule[ksRow++];
				s0 = t0;
				s1 = t1;
				s2 = t2;
				s3 = t3;
			}
			var t0g =
				((sbox[s0 >>> 24] << 24) |
					(sbox[(s1 >>> 16) & 0xff] << 16) |
					(sbox[(s2 >>> 8) & 0xff] << 8) |
					sbox[s3 & 0xff]) ^
				keySchedule[ksRow++];
			var t1g =
				((sbox[s1 >>> 24] << 24) |
					(sbox[(s2 >>> 16) & 0xff] << 16) |
					(sbox[(s3 >>> 8) & 0xff] << 8) |
					sbox[s0 & 0xff]) ^
				keySchedule[ksRow++];
			var t2g =
				((sbox[s2 >>> 24] << 24) |
					(sbox[(s3 >>> 16) & 0xff] << 16) |
					(sbox[(s0 >>> 8) & 0xff] << 8) |
					sbox[s1 & 0xff]) ^
				keySchedule[ksRow++];
			var t3g =
				((sbox[s3 >>> 24] << 24) |
					(sbox[(s0 >>> 16) & 0xff] << 16) |
					(sbox[(s1 >>> 8) & 0xff] << 8) |
					sbox[s2 & 0xff]) ^
				keySchedule[ksRow++];
			M[offset] = t0g;
			M[offset + 1] = t1g;
			M[offset + 2] = t2g;
			M[offset + 3] = t3g;
		};
		return AES;
	})(BlockCipher);
	AES.keySize = 8;
	var H$2 = [];
	var K = [];
	var W$2 = [];
	var SHA256 = /*#__PURE__*/ (function(_Hasher2) {
		_inheritsLoose(SHA256, _Hasher2);
		function SHA256() {
			return _Hasher2.apply(this, arguments) || this;
		}
		var _proto13 = SHA256.prototype;
		_proto13.reset = function reset() {
			_Hasher2.prototype.reset.call(this);
			this._hash = new WordArray(H$2.slice(0));
		};
		_proto13._doProcessBlock = function _doProcessBlock(M, offset) {
			var Hl = this._hash.words;
			var a = Hl[0];
			var b = Hl[1];
			var c = Hl[2];
			var d = Hl[3];
			var e = Hl[4];
			var f = Hl[5];
			var g = Hl[6];
			var h = Hl[7];
			for (var _i10 = 0; _i10 < 64; _i10++) {
				if (_i10 < 16) {
					W$2[_i10] = M[offset + _i10] | 0;
				} else {
					var gamma0x = W$2[_i10 - 15];
					var gamma0 =
						((gamma0x << 25) | (gamma0x >>> 7)) ^
						((gamma0x << 14) | (gamma0x >>> 18)) ^
						(gamma0x >>> 3);
					var gamma1x = W$2[_i10 - 2];
					var gamma1 =
						((gamma1x << 15) | (gamma1x >>> 17)) ^
						((gamma1x << 13) | (gamma1x >>> 19)) ^
						(gamma1x >>> 10);
					W$2[_i10] = gamma0 + W$2[_i10 - 7] + gamma1 + W$2[_i10 - 16];
				}
				var ch = (e & f) ^ (~e & g);
				var maj = (a & b) ^ (a & c) ^ (b & c);
				var sigma0 =
					((a << 30) | (a >>> 2)) ^
					((a << 19) | (a >>> 13)) ^
					((a << 10) | (a >>> 22));
				var sigma1 =
					((e << 26) | (e >>> 6)) ^
					((e << 21) | (e >>> 11)) ^
					((e << 7) | (e >>> 25));
				var t1 = h + sigma1 + ch + K[_i10] + W$2[_i10];
				var t2 = sigma0 + maj;
				h = g;
				g = f;
				f = e;
				e = (d + t1) | 0;
				d = c;
				c = b;
				b = a;
				a = (t1 + t2) | 0;
			}
			Hl[0] = (Hl[0] + a) | 0;
			Hl[1] = (Hl[1] + b) | 0;
			Hl[2] = (Hl[2] + c) | 0;
			Hl[3] = (Hl[3] + d) | 0;
			Hl[4] = (Hl[4] + e) | 0;
			Hl[5] = (Hl[5] + f) | 0;
			Hl[6] = (Hl[6] + g) | 0;
			Hl[7] = (Hl[7] + h) | 0;
		};
		_proto13._doFinalize = function _doFinalize() {
			var nBitsTotal = this._nDataBytes * 8;
			var nBitsLeft = this._data.sigBytes * 8;
			this._data.words[nBitsLeft >>> 5] |= 0x80 << (24 - (nBitsLeft % 32));
			this._data.words[(((nBitsLeft + 64) >>> 9) << 4) + 14] = Math.floor(
				nBitsTotal / 0x100000000
			);
			this._data.words[(((nBitsLeft + 64) >>> 9) << 4) + 15] = nBitsTotal;
			this._data.sigBytes = this._data.words.length * 4;
			this._process();
			return this._hash;
		};
		return SHA256;
	})(Hasher);
	var lib = {
		BlockCipher: BlockCipher,
		WordArray: WordArray,
		CipherParams: CipherParams,
		Hasher: Hasher,
		SerializableCipher: SerializableCipher,
		PasswordBasedCipher: PasswordBasedCipher
	};
	var algo = {AES: AES, SHA256: SHA256, MD5: MD5};
	var AES$1 = lib.BlockCipher._createHelper(algo.AES);
	var SHA256$1 = lib.Hasher._createHelper(algo.SHA256);
	var MD5$1 = lib.Hasher._createHelper(algo.MD5);

	// 复制
	function copyText(_text, _callback) {
		console.log("copy:" + _text);
		if (T.platform() == "app") {
			api.require("clipBoard").set(
				{
					value: _text
				},
				function(ret, err) {
					_callback && _callback(ret, err);
				}
			);
		} else if (T.platform() == "web") {
			htmlCopyText(_text, function(ret) {
				_callback && _callback(ret, null);
			});
		} else if (T.platform() == "mp") {
			wx.setClipboardData({
				data: _text,
				success: function success(ret) {
					_callback && _callback(ret, null);
				},
				fail: function fail(err) {
					_callback && _callback(null, err);
				}
			});
		}
	}

	// web复制

	// web复制
	function htmlCopyText(_text, callback) {
		var success = true;
		var textarea = document.createElement("textarea");
		textarea.value = _text;
		document.body.appendChild(textarea);
		textarea.select();
		textarea.setSelectionRange(0, textarea.value.length); // 兼容 iOS 设备
		try {
			if (navigator.clipboard) {
				navigator.clipboard.writeText(_text).then(
					function() {
						success = true;
					},
					function(err) {
						success = false;
					}
				);
			} else {
				var input = document.createElement("input");
				input.setAttribute("value", _text);
				document.body.appendChild(input);
				input.select();
				input.setSelectionRange(0, input.value.length); // 兼容 iOS 设备
				success = document.execCommand("copy");
				document.body.removeChild(input);
			}
		} catch (err) {
			success = false;
		} finally {
			document.body.removeChild(textarea);
		}
		callback && callback(success);
	}

	/**
	 * @fileoverview
	 * - Using the 'QRCode for Javascript library'
	 * - Fixed dataset of 'QRCode for Javascript library' for support full-spec.
	 * - this library has no dependencies.
	 *
	 * <AUTHOR>
	 * @see <a href="http://www.d-project.com/" target="_blank">http://www.d-project.com/</a>
	 * @see <a href="http://jeromeetienne.github.com/jquery-qrcode/" target="_blank">http://jeromeetienne.github.com/jquery-qrcode/</a>
	 */
	var QRCode;

	(function() {
		//---------------------------------------------------------------------
		// QRCode for JavaScript
		//
		// Copyright (c) 2009 Kazuhiko Arase
		//
		// URL: http://www.d-project.com/
		//
		// Licensed under the MIT license:
		//   http://www.opensource.org/licenses/mit-license.php
		//
		// The word "QR Code" is registered trademark of
		// DENSO WAVE INCORPORATED
		//   http://www.denso-wave.com/qrcode/faqpatent-e.html
		//
		//---------------------------------------------------------------------
		function QR8bitByte(data) {
			this.mode = QRMode.MODE_8BIT_BYTE;
			this.data = data;
			this.parsedData = [];

			// Added to support UTF-8 Characters
			for (var i = 0, l = this.data.length; i < l; i++) {
				var byteArray = [];
				var code = this.data.charCodeAt(i);

				if (code > 0x10000) {
					byteArray[0] = 0xf0 | ((code & 0x1c0000) >>> 18);
					byteArray[1] = 0x80 | ((code & 0x3f000) >>> 12);
					byteArray[2] = 0x80 | ((code & 0xfc0) >>> 6);
					byteArray[3] = 0x80 | (code & 0x3f);
				} else if (code > 0x800) {
					byteArray[0] = 0xe0 | ((code & 0xf000) >>> 12);
					byteArray[1] = 0x80 | ((code & 0xfc0) >>> 6);
					byteArray[2] = 0x80 | (code & 0x3f);
				} else if (code > 0x80) {
					byteArray[0] = 0xc0 | ((code & 0x7c0) >>> 6);
					byteArray[1] = 0x80 | (code & 0x3f);
				} else {
					byteArray[0] = code;
				}

				this.parsedData.push(byteArray);
			}

			this.parsedData = Array.prototype.concat.apply([], this.parsedData);

			if (this.parsedData.length != this.data.length) {
				this.parsedData.unshift(191);
				this.parsedData.unshift(187);
				this.parsedData.unshift(239);
			}
		}

		QR8bitByte.prototype = {
			getLength: function getLength(buffer) {
				return this.parsedData.length;
			},
			write: function write(buffer) {
				for (var i = 0, l = this.parsedData.length; i < l; i++) {
					buffer.put(this.parsedData[i], 8);
				}
			}
		};

		function QRCodeModel(typeNumber, errorCorrectLevel) {
			this.typeNumber = typeNumber;
			this.errorCorrectLevel = errorCorrectLevel;
			this.modules = null;
			this.moduleCount = 0;
			this.dataCache = null;
			this.dataList = [];
		}

		QRCodeModel.prototype = {
			addData: function addData(data) {
				var newData = new QR8bitByte(data);
				this.dataList.push(newData);
				this.dataCache = null;
			},
			isDark: function isDark(row, col) {
				if (
					row < 0 ||
					this.moduleCount <= row ||
					col < 0 ||
					this.moduleCount <= col
				) {
					throw new Error(row + "," + col);
				}
				return this.modules[row][col];
			},
			getModuleCount: function getModuleCount() {
				return this.moduleCount;
			},
			make: function make() {
				this.makeImpl(false, this.getBestMaskPattern());
			},
			makeImpl: function makeImpl(test, maskPattern) {
				this.moduleCount = this.typeNumber * 4 + 17;
				this.modules = new Array(this.moduleCount);
				for (var row = 0; row < this.moduleCount; row++) {
					this.modules[row] = new Array(this.moduleCount);
					for (var col = 0; col < this.moduleCount; col++) {
						this.modules[row][col] = null;
					}
				}
				this.setupPositionProbePattern(0, 0);
				this.setupPositionProbePattern(this.moduleCount - 7, 0);
				this.setupPositionProbePattern(0, this.moduleCount - 7);
				this.setupPositionAdjustPattern();
				this.setupTimingPattern();
				this.setupTypeInfo(test, maskPattern);
				if (this.typeNumber >= 7) {
					this.setupTypeNumber(test);
				}
				if (this.dataCache == null) {
					this.dataCache = QRCodeModel.createData(
						this.typeNumber,
						this.errorCorrectLevel,
						this.dataList
					);
				}
				this.mapData(this.dataCache, maskPattern);
			},
			setupPositionProbePattern: function setupPositionProbePattern(row, col) {
				for (var r = -1; r <= 7; r++) {
					if (row + r <= -1 || this.moduleCount <= row + r) continue;
					for (var c = -1; c <= 7; c++) {
						if (col + c <= -1 || this.moduleCount <= col + c) continue;
						if (
							(0 <= r && r <= 6 && (c == 0 || c == 6)) ||
							(0 <= c && c <= 6 && (r == 0 || r == 6)) ||
							(2 <= r && r <= 4 && 2 <= c && c <= 4)
						) {
							this.modules[row + r][col + c] = true;
						} else {
							this.modules[row + r][col + c] = false;
						}
					}
				}
			},
			getBestMaskPattern: function getBestMaskPattern() {
				var minLostPoint = 0;
				var pattern = 0;
				for (var i = 0; i < 8; i++) {
					this.makeImpl(true, i);
					var lostPoint = QRUtil.getLostPoint(this);
					if (i == 0 || minLostPoint > lostPoint) {
						minLostPoint = lostPoint;
						pattern = i;
					}
				}
				return pattern;
			},
			createMovieClip: function createMovieClip(target_mc, instance_name, depth) {
				var qr_mc = target_mc.createEmptyMovieClip(instance_name, depth);
				var cs = 1;
				this.make();
				for (var row = 0; row < this.modules.length; row++) {
					var y = row * cs;
					for (var col = 0; col < this.modules[row].length; col++) {
						var x = col * cs;
						var dark = this.modules[row][col];
						if (dark) {
							qr_mc.beginFill(0, 100);
							qr_mc.moveTo(x, y);
							qr_mc.lineTo(x + cs, y);
							qr_mc.lineTo(x + cs, y + cs);
							qr_mc.lineTo(x, y + cs);
							qr_mc.endFill();
						}
					}
				}
				return qr_mc;
			},
			setupTimingPattern: function setupTimingPattern() {
				for (var r = 8; r < this.moduleCount - 8; r++) {
					if (this.modules[r][6] != null) {
						continue;
					}
					this.modules[r][6] = r % 2 == 0;
				}
				for (var c = 8; c < this.moduleCount - 8; c++) {
					if (this.modules[6][c] != null) {
						continue;
					}
					this.modules[6][c] = c % 2 == 0;
				}
			},
			setupPositionAdjustPattern: function setupPositionAdjustPattern() {
				var pos = QRUtil.getPatternPosition(this.typeNumber);
				for (var i = 0; i < pos.length; i++) {
					for (var j = 0; j < pos.length; j++) {
						var row = pos[i];
						var col = pos[j];
						if (this.modules[row][col] != null) {
							continue;
						}
						for (var r = -2; r <= 2; r++) {
							for (var c = -2; c <= 2; c++) {
								if (r == -2 || r == 2 || c == -2 || c == 2 || (r == 0 && c == 0)) {
									this.modules[row + r][col + c] = true;
								} else {
									this.modules[row + r][col + c] = false;
								}
							}
						}
					}
				}
			},
			setupTypeNumber: function setupTypeNumber(test) {
				var bits = QRUtil.getBCHTypeNumber(this.typeNumber);
				for (var i = 0; i < 18; i++) {
					var mod = !test && ((bits >> i) & 1) == 1;
					this.modules[Math.floor(i / 3)][(i % 3) + this.moduleCount - 8 - 3] = mod;
				}
				for (var i = 0; i < 18; i++) {
					var mod = !test && ((bits >> i) & 1) == 1;
					this.modules[(i % 3) + this.moduleCount - 8 - 3][Math.floor(i / 3)] = mod;
				}
			},
			setupTypeInfo: function setupTypeInfo(test, maskPattern) {
				var data = (this.errorCorrectLevel << 3) | maskPattern;
				var bits = QRUtil.getBCHTypeInfo(data);
				for (var i = 0; i < 15; i++) {
					var mod = !test && ((bits >> i) & 1) == 1;
					if (i < 6) {
						this.modules[i][8] = mod;
					} else if (i < 8) {
						this.modules[i + 1][8] = mod;
					} else {
						this.modules[this.moduleCount - 15 + i][8] = mod;
					}
				}
				for (var i = 0; i < 15; i++) {
					var mod = !test && ((bits >> i) & 1) == 1;
					if (i < 8) {
						this.modules[8][this.moduleCount - i - 1] = mod;
					} else if (i < 9) {
						this.modules[8][15 - i - 1 + 1] = mod;
					} else {
						this.modules[8][15 - i - 1] = mod;
					}
				}
				this.modules[this.moduleCount - 8][8] = !test;
			},
			mapData: function mapData(data, maskPattern) {
				var inc = -1;
				var row = this.moduleCount - 1;
				var bitIndex = 7;
				var byteIndex = 0;
				for (var col = this.moduleCount - 1; col > 0; col -= 2) {
					if (col == 6) col--;
					while (true) {
						for (var c = 0; c < 2; c++) {
							if (this.modules[row][col - c] == null) {
								var dark = false;
								if (byteIndex < data.length) {
									dark = ((data[byteIndex] >>> bitIndex) & 1) == 1;
								}
								var mask = QRUtil.getMask(maskPattern, row, col - c);
								if (mask) {
									dark = !dark;
								}
								this.modules[row][col - c] = dark;
								bitIndex--;
								if (bitIndex == -1) {
									byteIndex++;
									bitIndex = 7;
								}
							}
						}
						row += inc;
						if (row < 0 || this.moduleCount <= row) {
							row -= inc;
							inc = -inc;
							break;
						}
					}
				}
			}
		};
		QRCodeModel.PAD0 = 0xec;
		QRCodeModel.PAD1 = 0x11;
		QRCodeModel.createData = function(typeNumber, errorCorrectLevel, dataList) {
			var rsBlocks = QRRSBlock.getRSBlocks(typeNumber, errorCorrectLevel);
			var buffer = new QRBitBuffer();
			for (var i = 0; i < dataList.length; i++) {
				var data = dataList[i];
				buffer.put(data.mode, 4);
				buffer.put(data.getLength(), QRUtil.getLengthInBits(data.mode, typeNumber));
				data.write(buffer);
			}
			var totalDataCount = 0;
			for (var i = 0; i < rsBlocks.length; i++) {
				totalDataCount += rsBlocks[i].dataCount;
			}
			if (buffer.getLengthInBits() > totalDataCount * 8) {
				throw new Error(
					"code length overflow. (" +
						buffer.getLengthInBits() +
						">" +
						totalDataCount * 8 +
						")"
				);
			}
			if (buffer.getLengthInBits() + 4 <= totalDataCount * 8) {
				buffer.put(0, 4);
			}
			while (buffer.getLengthInBits() % 8 != 0) {
				buffer.putBit(false);
			}
			while (true) {
				if (buffer.getLengthInBits() >= totalDataCount * 8) {
					break;
				}
				buffer.put(QRCodeModel.PAD0, 8);
				if (buffer.getLengthInBits() >= totalDataCount * 8) {
					break;
				}
				buffer.put(QRCodeModel.PAD1, 8);
			}
			return QRCodeModel.createBytes(buffer, rsBlocks);
		};
		QRCodeModel.createBytes = function(buffer, rsBlocks) {
			var offset = 0;
			var maxDcCount = 0;
			var maxEcCount = 0;
			var dcdata = new Array(rsBlocks.length);
			var ecdata = new Array(rsBlocks.length);
			for (var r = 0; r < rsBlocks.length; r++) {
				var dcCount = rsBlocks[r].dataCount;
				var ecCount = rsBlocks[r].totalCount - dcCount;
				maxDcCount = Math.max(maxDcCount, dcCount);
				maxEcCount = Math.max(maxEcCount, ecCount);
				dcdata[r] = new Array(dcCount);
				for (var i = 0; i < dcdata[r].length; i++) {
					dcdata[r][i] = 0xff & buffer.buffer[i + offset];
				}
				offset += dcCount;
				var rsPoly = QRUtil.getErrorCorrectPolynomial(ecCount);
				var rawPoly = new QRPolynomial(dcdata[r], rsPoly.getLength() - 1);
				var modPoly = rawPoly.mod(rsPoly);
				ecdata[r] = new Array(rsPoly.getLength() - 1);
				for (var i = 0; i < ecdata[r].length; i++) {
					var modIndex = i + modPoly.getLength() - ecdata[r].length;
					ecdata[r][i] = modIndex >= 0 ? modPoly.get(modIndex) : 0;
				}
			}
			var totalCodeCount = 0;
			for (var i = 0; i < rsBlocks.length; i++) {
				totalCodeCount += rsBlocks[i].totalCount;
			}
			var data = new Array(totalCodeCount);
			var index = 0;
			for (var i = 0; i < maxDcCount; i++) {
				for (var r = 0; r < rsBlocks.length; r++) {
					if (i < dcdata[r].length) {
						data[index++] = dcdata[r][i];
					}
				}
			}
			for (var i = 0; i < maxEcCount; i++) {
				for (var r = 0; r < rsBlocks.length; r++) {
					if (i < ecdata[r].length) {
						data[index++] = ecdata[r][i];
					}
				}
			}
			return data;
		};
		var QRMode = {
			MODE_NUMBER: 1 << 0,
			MODE_ALPHA_NUM: 1 << 1,
			MODE_8BIT_BYTE: 1 << 2,
			MODE_KANJI: 1 << 3
		};
		var QRErrorCorrectLevel = {L: 1, M: 0, Q: 3, H: 2};
		var QRMaskPattern = {
			PATTERN000: 0,
			PATTERN001: 1,
			PATTERN010: 2,
			PATTERN011: 3,
			PATTERN100: 4,
			PATTERN101: 5,
			PATTERN110: 6,
			PATTERN111: 7
		};
		var QRUtil = {
			PATTERN_POSITION_TABLE: [
				[],
				[6, 18],
				[6, 22],
				[6, 26],
				[6, 30],
				[6, 34],
				[6, 22, 38],
				[6, 24, 42],
				[6, 26, 46],
				[6, 28, 50],
				[6, 30, 54],
				[6, 32, 58],
				[6, 34, 62],
				[6, 26, 46, 66],
				[6, 26, 48, 70],
				[6, 26, 50, 74],
				[6, 30, 54, 78],
				[6, 30, 56, 82],
				[6, 30, 58, 86],
				[6, 34, 62, 90],
				[6, 28, 50, 72, 94],
				[6, 26, 50, 74, 98],
				[6, 30, 54, 78, 102],
				[6, 28, 54, 80, 106],
				[6, 32, 58, 84, 110],
				[6, 30, 58, 86, 114],
				[6, 34, 62, 90, 118],
				[6, 26, 50, 74, 98, 122],
				[6, 30, 54, 78, 102, 126],
				[6, 26, 52, 78, 104, 130],
				[6, 30, 56, 82, 108, 134],
				[6, 34, 60, 86, 112, 138],
				[6, 30, 58, 86, 114, 142],
				[6, 34, 62, 90, 118, 146],
				[6, 30, 54, 78, 102, 126, 150],
				[6, 24, 50, 76, 102, 128, 154],
				[6, 28, 54, 80, 106, 132, 158],
				[6, 32, 58, 84, 110, 136, 162],
				[6, 26, 54, 82, 110, 138, 166],
				[6, 30, 58, 86, 114, 142, 170]
			],
			G15:
				(1 << 10) | (1 << 8) | (1 << 5) | (1 << 4) | (1 << 2) | (1 << 1) | (1 << 0),
			G18:
				(1 << 12) |
				(1 << 11) |
				(1 << 10) |
				(1 << 9) |
				(1 << 8) |
				(1 << 5) |
				(1 << 2) |
				(1 << 0),
			G15_MASK: (1 << 14) | (1 << 12) | (1 << 10) | (1 << 4) | (1 << 1),
			getBCHTypeInfo: function getBCHTypeInfo(data) {
				var d = data << 10;
				while (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G15) >= 0) {
					d ^=
						QRUtil.G15 << (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G15));
				}
				return ((data << 10) | d) ^ QRUtil.G15_MASK;
			},
			getBCHTypeNumber: function getBCHTypeNumber(data) {
				var d = data << 12;
				while (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G18) >= 0) {
					d ^=
						QRUtil.G18 << (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G18));
				}
				return (data << 12) | d;
			},
			getBCHDigit: function getBCHDigit(data) {
				var digit = 0;
				while (data != 0) {
					digit++;
					data >>>= 1;
				}
				return digit;
			},
			getPatternPosition: function getPatternPosition(typeNumber) {
				return QRUtil.PATTERN_POSITION_TABLE[typeNumber - 1];
			},
			getMask: function getMask(maskPattern, i, j) {
				switch (maskPattern) {
					case QRMaskPattern.PATTERN000:
						return (i + j) % 2 == 0;
					case QRMaskPattern.PATTERN001:
						return i % 2 == 0;
					case QRMaskPattern.PATTERN010:
						return j % 3 == 0;
					case QRMaskPattern.PATTERN011:
						return (i + j) % 3 == 0;
					case QRMaskPattern.PATTERN100:
						return (Math.floor(i / 2) + Math.floor(j / 3)) % 2 == 0;
					case QRMaskPattern.PATTERN101:
						return ((i * j) % 2) + ((i * j) % 3) == 0;
					case QRMaskPattern.PATTERN110:
						return (((i * j) % 2) + ((i * j) % 3)) % 2 == 0;
					case QRMaskPattern.PATTERN111:
						return (((i * j) % 3) + ((i + j) % 2)) % 2 == 0;
					default:
						throw new Error("bad maskPattern:" + maskPattern);
				}
			},
			getErrorCorrectPolynomial: function getErrorCorrectPolynomial(
				errorCorrectLength
			) {
				var a = new QRPolynomial([1], 0);
				for (var i = 0; i < errorCorrectLength; i++) {
					a = a.multiply(new QRPolynomial([1, QRMath.gexp(i)], 0));
				}
				return a;
			},
			getLengthInBits: function getLengthInBits(mode, type) {
				if (1 <= type && type < 10) {
					switch (mode) {
						case QRMode.MODE_NUMBER:
							return 10;
						case QRMode.MODE_ALPHA_NUM:
							return 9;
						case QRMode.MODE_8BIT_BYTE:
							return 8;
						case QRMode.MODE_KANJI:
							return 8;
						default:
							throw new Error("mode:" + mode);
					}
				} else if (type < 27) {
					switch (mode) {
						case QRMode.MODE_NUMBER:
							return 12;
						case QRMode.MODE_ALPHA_NUM:
							return 11;
						case QRMode.MODE_8BIT_BYTE:
							return 16;
						case QRMode.MODE_KANJI:
							return 10;
						default:
							throw new Error("mode:" + mode);
					}
				} else if (type < 41) {
					switch (mode) {
						case QRMode.MODE_NUMBER:
							return 14;
						case QRMode.MODE_ALPHA_NUM:
							return 13;
						case QRMode.MODE_8BIT_BYTE:
							return 16;
						case QRMode.MODE_KANJI:
							return 12;
						default:
							throw new Error("mode:" + mode);
					}
				} else {
					throw new Error("type:" + type);
				}
			},
			getLostPoint: function getLostPoint(qrCode) {
				var moduleCount = qrCode.getModuleCount();
				var lostPoint = 0;
				for (var row = 0; row < moduleCount; row++) {
					for (var col = 0; col < moduleCount; col++) {
						var sameCount = 0;
						var dark = qrCode.isDark(row, col);
						for (var r = -1; r <= 1; r++) {
							if (row + r < 0 || moduleCount <= row + r) {
								continue;
							}
							for (var c = -1; c <= 1; c++) {
								if (col + c < 0 || moduleCount <= col + c) {
									continue;
								}
								if (r == 0 && c == 0) {
									continue;
								}
								if (dark == qrCode.isDark(row + r, col + c)) {
									sameCount++;
								}
							}
						}
						if (sameCount > 5) {
							lostPoint += 3 + sameCount - 5;
						}
					}
				}
				for (var row = 0; row < moduleCount - 1; row++) {
					for (var col = 0; col < moduleCount - 1; col++) {
						var count = 0;
						if (qrCode.isDark(row, col)) count++;
						if (qrCode.isDark(row + 1, col)) count++;
						if (qrCode.isDark(row, col + 1)) count++;
						if (qrCode.isDark(row + 1, col + 1)) count++;
						if (count == 0 || count == 4) {
							lostPoint += 3;
						}
					}
				}
				for (var row = 0; row < moduleCount; row++) {
					for (var col = 0; col < moduleCount - 6; col++) {
						if (
							qrCode.isDark(row, col) &&
							!qrCode.isDark(row, col + 1) &&
							qrCode.isDark(row, col + 2) &&
							qrCode.isDark(row, col + 3) &&
							qrCode.isDark(row, col + 4) &&
							!qrCode.isDark(row, col + 5) &&
							qrCode.isDark(row, col + 6)
						) {
							lostPoint += 40;
						}
					}
				}
				for (var col = 0; col < moduleCount; col++) {
					for (var row = 0; row < moduleCount - 6; row++) {
						if (
							qrCode.isDark(row, col) &&
							!qrCode.isDark(row + 1, col) &&
							qrCode.isDark(row + 2, col) &&
							qrCode.isDark(row + 3, col) &&
							qrCode.isDark(row + 4, col) &&
							!qrCode.isDark(row + 5, col) &&
							qrCode.isDark(row + 6, col)
						) {
							lostPoint += 40;
						}
					}
				}
				var darkCount = 0;
				for (var col = 0; col < moduleCount; col++) {
					for (var row = 0; row < moduleCount; row++) {
						if (qrCode.isDark(row, col)) {
							darkCount++;
						}
					}
				}
				var ratio =
					Math.abs((100 * darkCount) / moduleCount / moduleCount - 50) / 5;
				lostPoint += ratio * 10;
				return lostPoint;
			}
		};
		var QRMath = {
			glog: function glog(n) {
				if (n < 1) {
					throw new Error("glog(" + n + ")");
				}
				return QRMath.LOG_TABLE[n];
			},
			gexp: function gexp(n) {
				while (n < 0) {
					n += 255;
				}
				while (n >= 256) {
					n -= 255;
				}
				return QRMath.EXP_TABLE[n];
			},
			EXP_TABLE: new Array(256),
			LOG_TABLE: new Array(256)
		};
		for (var i = 0; i < 8; i++) {
			QRMath.EXP_TABLE[i] = 1 << i;
		}
		for (var i = 8; i < 256; i++) {
			QRMath.EXP_TABLE[i] =
				QRMath.EXP_TABLE[i - 4] ^
				QRMath.EXP_TABLE[i - 5] ^
				QRMath.EXP_TABLE[i - 6] ^
				QRMath.EXP_TABLE[i - 8];
		}
		for (var i = 0; i < 255; i++) {
			QRMath.LOG_TABLE[QRMath.EXP_TABLE[i]] = i;
		}
		function QRPolynomial(num, shift) {
			if (num.length == undefined) {
				throw new Error(num.length + "/" + shift);
			}
			var offset = 0;
			while (offset < num.length && num[offset] == 0) {
				offset++;
			}
			this.num = new Array(num.length - offset + shift);
			for (var i = 0; i < num.length - offset; i++) {
				this.num[i] = num[i + offset];
			}
		}
		QRPolynomial.prototype = {
			get: function get(index) {
				return this.num[index];
			},
			getLength: function getLength() {
				return this.num.length;
			},
			multiply: function multiply(e) {
				var num = new Array(this.getLength() + e.getLength() - 1);
				for (var i = 0; i < this.getLength(); i++) {
					for (var j = 0; j < e.getLength(); j++) {
						num[i + j] ^= QRMath.gexp(
							QRMath.glog(this.get(i)) + QRMath.glog(e.get(j))
						);
					}
				}
				return new QRPolynomial(num, 0);
			},
			mod: function mod(e) {
				if (this.getLength() - e.getLength() < 0) {
					return this;
				}
				var ratio = QRMath.glog(this.get(0)) - QRMath.glog(e.get(0));
				var num = new Array(this.getLength());
				for (var i = 0; i < this.getLength(); i++) {
					num[i] = this.get(i);
				}
				for (var i = 0; i < e.getLength(); i++) {
					num[i] ^= QRMath.gexp(QRMath.glog(e.get(i)) + ratio);
				}
				return new QRPolynomial(num, 0).mod(e);
			}
		};
		function QRRSBlock(totalCount, dataCount) {
			this.totalCount = totalCount;
			this.dataCount = dataCount;
		}
		QRRSBlock.RS_BLOCK_TABLE = [
			[1, 26, 19],
			[1, 26, 16],
			[1, 26, 13],
			[1, 26, 9],
			[1, 44, 34],
			[1, 44, 28],
			[1, 44, 22],
			[1, 44, 16],
			[1, 70, 55],
			[1, 70, 44],
			[2, 35, 17],
			[2, 35, 13],
			[1, 100, 80],
			[2, 50, 32],
			[2, 50, 24],
			[4, 25, 9],
			[1, 134, 108],
			[2, 67, 43],
			[2, 33, 15, 2, 34, 16],
			[2, 33, 11, 2, 34, 12],
			[2, 86, 68],
			[4, 43, 27],
			[4, 43, 19],
			[4, 43, 15],
			[2, 98, 78],
			[4, 49, 31],
			[2, 32, 14, 4, 33, 15],
			[4, 39, 13, 1, 40, 14],
			[2, 121, 97],
			[2, 60, 38, 2, 61, 39],
			[4, 40, 18, 2, 41, 19],
			[4, 40, 14, 2, 41, 15],
			[2, 146, 116],
			[3, 58, 36, 2, 59, 37],
			[4, 36, 16, 4, 37, 17],
			[4, 36, 12, 4, 37, 13],
			[2, 86, 68, 2, 87, 69],
			[4, 69, 43, 1, 70, 44],
			[6, 43, 19, 2, 44, 20],
			[6, 43, 15, 2, 44, 16],
			[4, 101, 81],
			[1, 80, 50, 4, 81, 51],
			[4, 50, 22, 4, 51, 23],
			[3, 36, 12, 8, 37, 13],
			[2, 116, 92, 2, 117, 93],
			[6, 58, 36, 2, 59, 37],
			[4, 46, 20, 6, 47, 21],
			[7, 42, 14, 4, 43, 15],
			[4, 133, 107],
			[8, 59, 37, 1, 60, 38],
			[8, 44, 20, 4, 45, 21],
			[12, 33, 11, 4, 34, 12],
			[3, 145, 115, 1, 146, 116],
			[4, 64, 40, 5, 65, 41],
			[11, 36, 16, 5, 37, 17],
			[11, 36, 12, 5, 37, 13],
			[5, 109, 87, 1, 110, 88],
			[5, 65, 41, 5, 66, 42],
			[5, 54, 24, 7, 55, 25],
			[11, 36, 12],
			[5, 122, 98, 1, 123, 99],
			[7, 73, 45, 3, 74, 46],
			[15, 43, 19, 2, 44, 20],
			[3, 45, 15, 13, 46, 16],
			[1, 135, 107, 5, 136, 108],
			[10, 74, 46, 1, 75, 47],
			[1, 50, 22, 15, 51, 23],
			[2, 42, 14, 17, 43, 15],
			[5, 150, 120, 1, 151, 121],
			[9, 69, 43, 4, 70, 44],
			[17, 50, 22, 1, 51, 23],
			[2, 42, 14, 19, 43, 15],
			[3, 141, 113, 4, 142, 114],
			[3, 70, 44, 11, 71, 45],
			[17, 47, 21, 4, 48, 22],
			[9, 39, 13, 16, 40, 14],
			[3, 135, 107, 5, 136, 108],
			[3, 67, 41, 13, 68, 42],
			[15, 54, 24, 5, 55, 25],
			[15, 43, 15, 10, 44, 16],
			[4, 144, 116, 4, 145, 117],
			[17, 68, 42],
			[17, 50, 22, 6, 51, 23],
			[19, 46, 16, 6, 47, 17],
			[2, 139, 111, 7, 140, 112],
			[17, 74, 46],
			[7, 54, 24, 16, 55, 25],
			[34, 37, 13],
			[4, 151, 121, 5, 152, 122],
			[4, 75, 47, 14, 76, 48],
			[11, 54, 24, 14, 55, 25],
			[16, 45, 15, 14, 46, 16],
			[6, 147, 117, 4, 148, 118],
			[6, 73, 45, 14, 74, 46],
			[11, 54, 24, 16, 55, 25],
			[30, 46, 16, 2, 47, 17],
			[8, 132, 106, 4, 133, 107],
			[8, 75, 47, 13, 76, 48],
			[7, 54, 24, 22, 55, 25],
			[22, 45, 15, 13, 46, 16],
			[10, 142, 114, 2, 143, 115],
			[19, 74, 46, 4, 75, 47],
			[28, 50, 22, 6, 51, 23],
			[33, 46, 16, 4, 47, 17],
			[8, 152, 122, 4, 153, 123],
			[22, 73, 45, 3, 74, 46],
			[8, 53, 23, 26, 54, 24],
			[12, 45, 15, 28, 46, 16],
			[3, 147, 117, 10, 148, 118],
			[3, 73, 45, 23, 74, 46],
			[4, 54, 24, 31, 55, 25],
			[11, 45, 15, 31, 46, 16],
			[7, 146, 116, 7, 147, 117],
			[21, 73, 45, 7, 74, 46],
			[1, 53, 23, 37, 54, 24],
			[19, 45, 15, 26, 46, 16],
			[5, 145, 115, 10, 146, 116],
			[19, 75, 47, 10, 76, 48],
			[15, 54, 24, 25, 55, 25],
			[23, 45, 15, 25, 46, 16],
			[13, 145, 115, 3, 146, 116],
			[2, 74, 46, 29, 75, 47],
			[42, 54, 24, 1, 55, 25],
			[23, 45, 15, 28, 46, 16],
			[17, 145, 115],
			[10, 74, 46, 23, 75, 47],
			[10, 54, 24, 35, 55, 25],
			[19, 45, 15, 35, 46, 16],
			[17, 145, 115, 1, 146, 116],
			[14, 74, 46, 21, 75, 47],
			[29, 54, 24, 19, 55, 25],
			[11, 45, 15, 46, 46, 16],
			[13, 145, 115, 6, 146, 116],
			[14, 74, 46, 23, 75, 47],
			[44, 54, 24, 7, 55, 25],
			[59, 46, 16, 1, 47, 17],
			[12, 151, 121, 7, 152, 122],
			[12, 75, 47, 26, 76, 48],
			[39, 54, 24, 14, 55, 25],
			[22, 45, 15, 41, 46, 16],
			[6, 151, 121, 14, 152, 122],
			[6, 75, 47, 34, 76, 48],
			[46, 54, 24, 10, 55, 25],
			[2, 45, 15, 64, 46, 16],
			[17, 152, 122, 4, 153, 123],
			[29, 74, 46, 14, 75, 47],
			[49, 54, 24, 10, 55, 25],
			[24, 45, 15, 46, 46, 16],
			[4, 152, 122, 18, 153, 123],
			[13, 74, 46, 32, 75, 47],
			[48, 54, 24, 14, 55, 25],
			[42, 45, 15, 32, 46, 16],
			[20, 147, 117, 4, 148, 118],
			[40, 75, 47, 7, 76, 48],
			[43, 54, 24, 22, 55, 25],
			[10, 45, 15, 67, 46, 16],
			[19, 148, 118, 6, 149, 119],
			[18, 75, 47, 31, 76, 48],
			[34, 54, 24, 34, 55, 25],
			[20, 45, 15, 61, 46, 16]
		];
		QRRSBlock.getRSBlocks = function(typeNumber, errorCorrectLevel) {
			var rsBlock = QRRSBlock.getRsBlockTable(typeNumber, errorCorrectLevel);
			if (rsBlock == undefined) {
				throw new Error(
					"bad rs block @ typeNumber:" +
						typeNumber +
						"/errorCorrectLevel:" +
						errorCorrectLevel
				);
			}
			var length = rsBlock.length / 3;
			var list = [];
			for (var i = 0; i < length; i++) {
				var count = rsBlock[i * 3 + 0];
				var totalCount = rsBlock[i * 3 + 1];
				var dataCount = rsBlock[i * 3 + 2];
				for (var j = 0; j < count; j++) {
					list.push(new QRRSBlock(totalCount, dataCount));
				}
			}
			return list;
		};
		QRRSBlock.getRsBlockTable = function(typeNumber, errorCorrectLevel) {
			switch (errorCorrectLevel) {
				case QRErrorCorrectLevel.L:
					return QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 0];
				case QRErrorCorrectLevel.M:
					return QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 1];
				case QRErrorCorrectLevel.Q:
					return QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 2];
				case QRErrorCorrectLevel.H:
					return QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 3];
				default:
					return undefined;
			}
		};
		function QRBitBuffer() {
			this.buffer = [];
			this.length = 0;
		}
		QRBitBuffer.prototype = {
			get: function get(index) {
				var bufIndex = Math.floor(index / 8);
				return ((this.buffer[bufIndex] >>> (7 - (index % 8))) & 1) == 1;
			},
			put: function put(num, length) {
				for (var i = 0; i < length; i++) {
					this.putBit(((num >>> (length - i - 1)) & 1) == 1);
				}
			},
			getLengthInBits: function getLengthInBits() {
				return this.length;
			},
			putBit: function putBit(bit) {
				var bufIndex = Math.floor(this.length / 8);
				if (this.buffer.length <= bufIndex) {
					this.buffer.push(0);
				}
				if (bit) {
					this.buffer[bufIndex] |= 0x80 >>> this.length % 8;
				}
				this.length++;
			}
		};
		var QRCodeLimitLength = [
			[17, 14, 11, 7],
			[32, 26, 20, 14],
			[53, 42, 32, 24],
			[78, 62, 46, 34],
			[106, 84, 60, 44],
			[134, 106, 74, 58],
			[154, 122, 86, 64],
			[192, 152, 108, 84],
			[230, 180, 130, 98],
			[271, 213, 151, 119],
			[321, 251, 177, 137],
			[367, 287, 203, 155],
			[425, 331, 241, 177],
			[458, 362, 258, 194],
			[520, 412, 292, 220],
			[586, 450, 322, 250],
			[644, 504, 364, 280],
			[718, 560, 394, 310],
			[792, 624, 442, 338],
			[858, 666, 482, 382],
			[929, 711, 509, 403],
			[1003, 779, 565, 439],
			[1091, 857, 611, 461],
			[1171, 911, 661, 511],
			[1273, 997, 715, 535],
			[1367, 1059, 751, 593],
			[1465, 1125, 805, 625],
			[1528, 1190, 868, 658],
			[1628, 1264, 908, 698],
			[1732, 1370, 982, 742],
			[1840, 1452, 1030, 790],
			[1952, 1538, 1112, 842],
			[2068, 1628, 1168, 898],
			[2188, 1722, 1228, 958],
			[2303, 1809, 1283, 983],
			[2431, 1911, 1351, 1051],
			[2563, 1989, 1423, 1093],
			[2699, 2099, 1499, 1139],
			[2809, 2213, 1579, 1219],
			[2953, 2331, 1663, 1273]
		];

		function _isSupportCanvas() {
			return typeof CanvasRenderingContext2D != "undefined";
		}

		// android 2.x doesn't support Data-URI spec
		function _getAndroid() {
			var android = false;

			return android;
		}

		var svgDrawer = (function() {
			var Drawing = function Drawing(el, htOption) {
				this._el = el;
				this._htOption = htOption;
			};

			Drawing.prototype.draw = function(oQRCode) {
				var _htOption = this._htOption;
				var _el = this._el;
				var nCount = oQRCode.getModuleCount();
				var nWidth = Math.floor(_htOption.width / nCount);
				var nHeight = Math.floor(_htOption.height / nCount);

				this.clear();

				function makeSVG(tag, attrs) {
					var el = document.createElementNS("http://www.w3.org/2000/svg", tag);
					for (var k in attrs) {
						if (attrs.hasOwnProperty(k)) el.setAttribute(k, attrs[k]);
					}
					return el;
				}

				var svg = makeSVG("svg", {
					viewBox: "0 0 " + String(nCount) + " " + String(nCount),
					width: "100%",
					height: "100%",
					fill: _htOption.colorLight
				});
				svg.setAttributeNS(
					"http://www.w3.org/2000/xmlns/",
					"xmlns:xlink",
					"http://www.w3.org/1999/xlink"
				);
				_el.appendChild(svg);

				svg.appendChild(
					makeSVG("rect", {
						fill: _htOption.colorLight,
						width: "100%",
						height: "100%"
					})
				);
				svg.appendChild(
					makeSVG("rect", {
						fill: _htOption.colorDark,
						width: "1",
						height: "1",
						id: "template"
					})
				);

				for (var row = 0; row < nCount; row++) {
					for (var col = 0; col < nCount; col++) {
						if (oQRCode.isDark(row, col)) {
							var child = makeSVG("use", {x: String(row), y: String(col)});
							child.setAttributeNS(
								"http://www.w3.org/1999/xlink",
								"href",
								"#template"
							);
							svg.appendChild(child);
						}
					}
				}
			};
			Drawing.prototype.clear = function() {
				while (this._el.hasChildNodes()) {
					this._el.removeChild(this._el.lastChild);
				}
			};
			return Drawing;
		})();

		var useSVG;
		try {
			useSVG = document.documentElement.tagName.toLowerCase() === "svg";
		} catch (e) {
			useSVG = false;
		}

		// Drawing in DOM by using Table tag
		var Drawing = useSVG
			? svgDrawer
			: !_isSupportCanvas()
			? (function() {
					var Drawing = function Drawing(el, htOption) {
						this._el = el;
						this._htOption = htOption;
					};

					/**
					 * Draw the QRCode
					 *
					 * @param {QRCode} oQRCode
					 */
					Drawing.prototype.draw = function(oQRCode) {
						var _htOption = this._htOption;
						var _el = this._el;
						var nCount = oQRCode.getModuleCount();
						var nWidth = Math.floor(_htOption.width / nCount);
						var nHeight = Math.floor(_htOption.height / nCount);
						var aHTML = ['<table style="border:0;border-collapse:collapse;">'];

						for (var row = 0; row < nCount; row++) {
							aHTML.push("<tr>");

							for (var col = 0; col < nCount; col++) {
								aHTML.push(
									'<td style="border:0;border-collapse:collapse;padding:0;margin:0;width:' +
										nWidth +
										"px;height:" +
										nHeight +
										"px;background-color:" +
										(oQRCode.isDark(row, col)
											? _htOption.colorDark
											: _htOption.colorLight) +
										';"></td>'
								);
							}

							aHTML.push("</tr>");
						}

						aHTML.push("</table>");
						_el.innerHTML = aHTML.join("");

						// Fix the margin values as real size.
						var elTable = _el.childNodes[0];
						var nLeftMarginTable = (_htOption.width - elTable.offsetWidth) / 2;
						var nTopMarginTable = (_htOption.height - elTable.offsetHeight) / 2;

						if (nLeftMarginTable > 0 && nTopMarginTable > 0) {
							elTable.style.margin = nTopMarginTable + "px " + nLeftMarginTable + "px";
						}
					};

					/**
					 * Clear the QRCode
					 */
					Drawing.prototype.clear = function() {
						this._el.innerHTML = "";
					};

					return Drawing;
			  })()
			: (function() {
					// Drawing in Canvas
					function _onMakeImage() {
						this._elImage.src = this._elCanvas.toDataURL("image/png");
						this._elImage.style.display = "block";
						this._elCanvas.style.display = "none";
					}

					// Android 2.1 bug workaround
					// http://code.google.com/p/android/issues/detail?id=5141
					if (this._android && this._android <= 2.1) {
						var factor = 1 / window.devicePixelRatio;
						var drawImage = CanvasRenderingContext2D.prototype.drawImage;
						CanvasRenderingContext2D.prototype.drawImage = function(
							image,
							sx,
							sy,
							sw,
							sh,
							dx,
							dy,
							dw,
							dh
						) {
							if ("nodeName" in image && /img/i.test(image.nodeName)) {
								for (var i = arguments.length - 1; i >= 1; i--) {
									arguments[i] = arguments[i] * factor;
								}
							} else if (typeof dw == "undefined") {
								arguments[1] *= factor;
								arguments[2] *= factor;
								arguments[3] *= factor;
								arguments[4] *= factor;
							}

							drawImage.apply(this, arguments);
						};
					}

					/**
					 * Check whether the user's browser supports Data URI or not
					 *
					 * @private
					 * @param {Function} fSuccess Occurs if it supports Data URI
					 * @param {Function} fFail Occurs if it doesn't support Data URI
					 */
					function _safeSetDataURI(fSuccess, fFail) {
						var self = this;
						self._fFail = fFail;
						self._fSuccess = fSuccess;

						// Check it just once
						if (self._bSupportDataURI === null) {
							var el = document.createElement("img");
							var fOnError = function fOnError() {
								self._bSupportDataURI = false;

								if (self._fFail) {
									self._fFail.call(self);
								}
							};
							var fOnSuccess = function fOnSuccess() {
								self._bSupportDataURI = true;

								if (self._fSuccess) {
									self._fSuccess.call(self);
								}
							};

							el.onabort = fOnError;
							el.onerror = fOnError;
							el.onload = fOnSuccess;
							el.src =
								"data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg=="; // the Image contains 1px data.
							return;
						} else if (self._bSupportDataURI === true && self._fSuccess) {
							self._fSuccess.call(self);
						} else if (self._bSupportDataURI === false && self._fFail) {
							self._fFail.call(self);
						}
					}
					/**
					 * Drawing QRCode by using canvas
					 *
					 * @constructor
					 * @param {HTMLElement} el
					 * @param {Object} htOption QRCode Options
					 */
					var Drawing = function Drawing(el, htOption) {
						this._bIsPainted = false;
						this._android = _getAndroid();

						this._htOption = htOption;
						this._elCanvas = document.createElement("canvas");
						this._elCanvas.width = htOption.width;
						this._elCanvas.height = htOption.height;
						el.appendChild(this._elCanvas);
						this._el = el;
						this._oContext = this._elCanvas.getContext("2d");
						this._bIsPainted = false;
						this._elImage = document.createElement("img");
						this._elImage.alt = "Scan me!";
						this._elImage.style.display = "none";
						this._el.appendChild(this._elImage);
						this._bSupportDataURI = null;
					};

					/**
					 * Draw the QRCode
					 *
					 * @param {QRCode} oQRCode
					 */
					Drawing.prototype.draw = function(oQRCode) {
						var _elImage = this._elImage;
						var _oContext = this._oContext;
						var _htOption = this._htOption;

						var nCount = oQRCode.getModuleCount();
						var nWidth = _htOption.width / nCount;
						var nHeight = _htOption.height / nCount;
						var nRoundedWidth = Math.round(nWidth);
						var nRoundedHeight = Math.round(nHeight);

						_elImage.style.display = "none";
						this.clear();

						for (var row = 0; row < nCount; row++) {
							for (var col = 0; col < nCount; col++) {
								var bIsDark = oQRCode.isDark(row, col);
								var nLeft = col * nWidth;
								var nTop = row * nHeight;
								_oContext.strokeStyle = bIsDark
									? _htOption.colorDark
									: _htOption.colorLight;
								_oContext.lineWidth = 1;
								_oContext.fillStyle = bIsDark
									? _htOption.colorDark
									: _htOption.colorLight;
								_oContext.fillRect(nLeft, nTop, nWidth, nHeight);

								// 안티 앨리어싱 방지 처리
								_oContext.strokeRect(
									Math.floor(nLeft) + 0.5,
									Math.floor(nTop) + 0.5,
									nRoundedWidth,
									nRoundedHeight
								);

								_oContext.strokeRect(
									Math.ceil(nLeft) - 0.5,
									Math.ceil(nTop) - 0.5,
									nRoundedWidth,
									nRoundedHeight
								);
							}
						}

						this._bIsPainted = true;
					};

					/**
					 * Make the image from Canvas if the browser supports Data URI.
					 */
					Drawing.prototype.makeImage = function() {
						if (this._bIsPainted) {
							_safeSetDataURI.call(this, _onMakeImage);
						}
					};

					/**
					 * Return whether the QRCode is painted or not
					 *
					 * @return {Boolean}
					 */
					Drawing.prototype.isPainted = function() {
						return this._bIsPainted;
					};

					/**
					 * Clear the QRCode
					 */
					Drawing.prototype.clear = function() {
						this._oContext.clearRect(
							0,
							0,
							this._elCanvas.width,
							this._elCanvas.height
						);
						this._bIsPainted = false;
					};

					/**
					 * @private
					 * @param {Number} nNumber
					 */
					Drawing.prototype.round = function(nNumber) {
						if (!nNumber) {
							return nNumber;
						}

						return Math.floor(nNumber * 1000) / 1000;
					};

					return Drawing;
			  })();

		/**
		 * Get the type by string length
		 *
		 * @private
		 * @param {String} sText
		 * @param {Number} nCorrectLevel
		 * @return {Number} type
		 */
		function _getTypeNumber(sText, nCorrectLevel) {
			var nType = 1;
			var length = _getUTF8Length(sText);

			for (var i = 0, len = QRCodeLimitLength.length; i <= len; i++) {
				var nLimit = 0;

				switch (nCorrectLevel) {
					case QRErrorCorrectLevel.L:
						nLimit = QRCodeLimitLength[i][0];
						break;
					case QRErrorCorrectLevel.M:
						nLimit = QRCodeLimitLength[i][1];
						break;
					case QRErrorCorrectLevel.Q:
						nLimit = QRCodeLimitLength[i][2];
						break;
					case QRErrorCorrectLevel.H:
						nLimit = QRCodeLimitLength[i][3];
						break;
				}

				if (length <= nLimit) {
					break;
				} else {
					nType++;
				}
			}

			if (nType > QRCodeLimitLength.length) {
				throw new Error("Too long data");
			}

			return nType;
		}

		function _getUTF8Length(sText) {
			var replacedText = encodeURI(sText)
				.toString()
				.replace(/\%[0-9a-fA-F]{2}/g, "a");
			return replacedText.length + (replacedText.length != sText ? 3 : 0);
		}

		/**
		 * @class QRCode
		 * @constructor
		 * @example
		 * new QRCode(document.getElementById("test"), "http://jindo.dev.naver.com/collie");
		 *
		 * @example
		 * var oQRCode = new QRCode("test", {
		 *    text : "http://naver.com",
		 *    width : 128,
		 *    height : 128
		 * });
		 *
		 * oQRCode.clear(); // Clear the QRCode.
		 * oQRCode.makeCode("http://map.naver.com"); // Re-create the QRCode.
		 *
		 * @param {HTMLElement|String} el target element or 'id' attribute of element.
		 * @param {Object|String} vOption
		 * @param {String} vOption.text QRCode link data
		 * @param {Number} [vOption.width=256]
		 * @param {Number} [vOption.height=256]
		 * @param {String} [vOption.colorDark="#000000"]
		 * @param {String} [vOption.colorLight="#ffffff"]
		 * @param {QRCode.CorrectLevel} [vOption.correctLevel=QRCode.CorrectLevel.H] [L|M|Q|H]
		 */
		QRCode = function QRCode(el, vOption) {
			this._htOption = {
				width: 256,
				height: 256,
				typeNumber: 4,
				colorDark: "#000000",
				colorLight: "#ffffff",
				correctLevel: QRErrorCorrectLevel.H
			};

			if (typeof vOption === "string") {
				vOption = {
					text: vOption
				};
			}

			// Overwrites options
			if (vOption) {
				for (var i in vOption) {
					this._htOption[i] = vOption[i];
				}
			}

			if (typeof el == "string") {
				el = document.getElementById(el);
			}

			if (this._htOption.useSVG) {
				Drawing = svgDrawer;
			}

			this._android = _getAndroid();
			this._el = el;
			el.innerHTML = "";
			this._oQRCode = null;
			this._oDrawing = new Drawing(this._el, this._htOption);

			if (this._htOption.text) {
				this.makeCode(this._htOption.text);
			}
		};

		/**
		 * Make the QRCode
		 *
		 * @param {String} sText link data
		 */
		QRCode.prototype.makeCode = function(sText) {
			this._oQRCode = new QRCodeModel(
				_getTypeNumber(sText, this._htOption.correctLevel),
				this._htOption.correctLevel
			);

			this._oQRCode.addData(sText);
			this._oQRCode.make();
			this._el.title = sText;
			this._oDrawing.draw(this._oQRCode);
			this.makeImage();
		};

		/**
		 * Make the Image from Canvas element
		 * - It occurs automatically
		 * - Android below 3 doesn't support Data-URI spec.
		 *
		 * @private
		 */
		QRCode.prototype.makeImage = function() {
			if (
				typeof this._oDrawing.makeImage == "function" &&
				(!this._android || this._android >= 3)
			) {
				this._oDrawing.makeImage();
			}
		};

		/**
		 * Clear the QRCode
		 */
		QRCode.prototype.clear = function() {
			this._oDrawing.clear();
		};

		/**
		 * @name QRCode.CorrectLevel
		 */
		QRCode.CorrectLevel = QRErrorCorrectLevel;
	})();

	var QRCode$1 = QRCode;

	var baoguo_hezi_o = "";
	var fuzhilianjiexian = "";
	var fuzhilianjiemian = "";
	var wenjian = "";
	var tianjia = "";
	var tianjiawenjian = "";
	var sousuowenjian = "";
	var wenjianshangchuan = "";
	var dalou = "";
	var wucan = "";
	var zengjia1 = "";
	var xinsui = "";
	var aixin = "";
	var jianshao2 = "";
	var yishoucang = "";
	var yuyinshibie = "";
	var shuruxiangce = "";
	var tupianshibie = "";
	var shuruwenjian = "";
	var shurupaizhao = "";
	var tongji4 = "";
	var pinglun1 = "";
	var gerentongxunlu = "";
	var fenxiang2 = "";
	var dianzan = "";
	var shoucang1 = "";
	var sousuo2 = "";
	var jiancexinbanben = "";
	var shezhi1 = "";
	var guanhuaimoshi = "";
	var xiugaimima = "";
	var anquantuichu = "";
	var dianhua = "";
	var shipintianchong = "";
	var xiangxiagengduo = "";
	var mubiao = "";
	var jianshao1 = "";
	var biaoqian = "";
	var duigou = "";
	var jinengbiaoqian = "";
	var jiantou_xiangyouliangci = "";
	var toupiao = "";
	var riqi = "";
	var gonggao = "";
	var tongji3 = "";
	var paihang = "";
	var mn_paiming_fill = "";
	var shaixuan = "";
	var envelope = "";
	var nvshangjia = "";
	var nan = "";
	var tongji1 = "";
	var tuceng = "";
	var tongji2 = "";
	var biaoqianlan_shouye = "";
	var shengyinjingyin = "";
	var shengyin = "";
	var shengyin1 = "";
	var tianxie = "";
	var erweima = "";
	var huanyuan = "";
	var lianjie = "";
	var changyonglogo28 = "";
	var pengyouquan = "";
	var zhixiangxia = "";
	var QQ = "";
	var quanxian = "";
	var baocun = "";
	var suoyouwenjian = "";
	var xiangxia1 = "";
	var xinzeng = "";
	var ziyuanxhdpi = "";
	var xiazai = "";
	var zhongmingming = "";
	var yunpan = "";
	var tongji = "";
	var kefu = "";
	var weibiaoti1 = "";
	var tongxunlu = "";
	var xinyongqia = "";
	var tuichu = "";
	var aixinxiantiao = "";
	var shezhi = "";
	var jianchaxinbanben = "";
	var yuyin = "";
	var shanchu = "";
	var remen = "";
	var tupian = "";
	var xiangji = "";
	var zan = "";
	var zan1 = "";
	var like = "";
	var unlike = "";
	var pinglun = "";
	var jushoucang = "";
	var jushoucanggift = "";
	var share = "";
	var shoucangfill = "";
	var fenxiang1 = "";
	var shoucang = "";
	var bofang = "";
	var daojishi = "";
	var cuohao = "";
	var duihao = "";
	var chakan = "";
	var shoujihaoma = "";
	var mima1 = "";
	var zhanghao1 = "";
	var yanzhengma1 = "";
	var kejian = "";
	var bukejian = "";
	var gengduo1 = "";
	var gengduogongneng = "";
	var gengduo2 = "";
	var gengduo3 = "";
	var fanhui1 = "";
	var fanhui2 = "";
	var sousuo = "";
	var sousuo1 = "";
	var saoyisao1 = "";
	var xiangxia = "";
	var xiangzuo = "";
	var fangxingweixuanzhong = "";
	var fangxingxuanzhongfill = "";
	var fangxingxuanzhong = "";
	var yuanxingweixuanzhong = "";
	var yuanxingxuanzhong = "";
	var yuanxingxuanzhongfill = "";
	var danxuan_xuanzhong = "";
	var danxuan_weixuanzhong = "";
	var gengduo = "";
	var fenxiang = "";
	var zhuanfa00 = "";
	var dingwei = "";
	var dingwei1 = "";
	var jianshao = "";
	var zengjia = "";
	var qingkong = "";
	var mima = "";
	var zhanghao = "";
	var yanzhengma = "";
	var icons = {
		baoguo_hezi_o: baoguo_hezi_o,
		fuzhilianjiexian: fuzhilianjiexian,
		fuzhilianjiemian: fuzhilianjiemian,
		wenjian: wenjian,
		tianjia: tianjia,
		tianjiawenjian: tianjiawenjian,
		sousuowenjian: sousuowenjian,
		wenjianshangchuan: wenjianshangchuan,
		"zuanshi-L": "",
		dalou: dalou,
		wucan: wucan,
		zengjia1: zengjia1,
		xinsui: xinsui,
		aixin: aixin,
		jianshao2: jianshao2,
		yishoucang: yishoucang,
		yuyinshibie: yuyinshibie,
		shuruxiangce: shuruxiangce,
		tupianshibie: tupianshibie,
		shuruwenjian: shuruwenjian,
		shurupaizhao: shurupaizhao,
		tongji4: tongji4,
		pinglun1: pinglun1,
		gerentongxunlu: gerentongxunlu,
		fenxiang2: fenxiang2,
		dianzan: dianzan,
		shoucang1: shoucang1,
		sousuo2: sousuo2,
		jiancexinbanben: jiancexinbanben,
		shezhi1: shezhi1,
		guanhuaimoshi: guanhuaimoshi,
		xiugaimima: xiugaimima,
		anquantuichu: anquantuichu,
		dianhua: dianhua,
		shipintianchong: shipintianchong,
		"31dianhua": "",
		xiangxiagengduo: xiangxiagengduo,
		mubiao: mubiao,
		jianshao1: jianshao1,
		biaoqian: biaoqian,
		"gantanhao-xianxingyuankuang": "",
		duigou: duigou,
		jinengbiaoqian: jinengbiaoqian,
		jiantou_xiangyouliangci: jiantou_xiangyouliangci,
		toupiao: toupiao,
		riqi: riqi,
		gonggao: gonggao,
		tongji3: tongji3,
		paihang: paihang,
		mn_paiming_fill: mn_paiming_fill,
		"line-084": "",
		"line-085": "",
		shaixuan: shaixuan,
		envelope: envelope,
		"envelope-open": "",
		"a-4_huaban1": "",
		nvshangjia: nvshangjia,
		nan: nan,
		tongji1: tongji1,
		"weibiaoti--": "",
		tuceng: tuceng,
		tongji2: tongji2,
		biaoqianlan_shouye: biaoqianlan_shouye,
		shengyinjingyin: shengyinjingyin,
		shengyin: shengyin,
		shengyin1: shengyin1,
		tianxie: tianxie,
		"file-download-fill": "",
		"file-damage-fill": "",
		"file-excel-fill": "",
		"file-copy-fill": "",
		"file-edit-fill": "",
		"file-music-fill": "",
		"file-gif-fill": "",
		"file-history-fill": "",
		"file-lock-fill": "",
		"file-info-fill": "",
		"file-pdf-fill": "",
		"file-text-fill": "",
		"file-ppt-fill": "",
		"file-upload-fill": "",
		"file-unknow-fill": "",
		"file-word-fill": "",
		"file-search-fill": "",
		"file-transfer-fill": "",
		"file-zip-fill": "",
		"folder-2-fill": "",
		"file-warning-fill": "",
		"folder-add-fill": "",
		erweima: erweima,
		"file-reduce-fill": "",
		huanyuan: huanyuan,
		lianjie: lianjie,
		changyonglogo28: changyonglogo28,
		pengyouquan: pengyouquan,
		zhixiangxia: zhixiangxia,
		QQ: QQ,
		quanxian: quanxian,
		baocun: baocun,
		suoyouwenjian: suoyouwenjian,
		xiangxia1: xiangxia1,
		"wj-gxwj": "",
		xinzeng: xinzeng,
		ziyuanxhdpi: ziyuanxhdpi,
		xiazai: xiazai,
		zhongmingming: zhongmingming,
		yunpan: yunpan,
		"file-code-fill": "",
		"file-add-fill": "",
		tongji: tongji,
		kefu: kefu,
		weibiaoti1: weibiaoti1,
		tongxunlu: tongxunlu,
		xinyongqia: xinyongqia,
		tuichu: tuichu,
		aixinxiantiao: aixinxiantiao,
		shezhi: shezhi,
		jianchaxinbanben: jianchaxinbanben,
		yuyin: yuyin,
		shanchu: shanchu,
		remen: remen,
		tupian: tupian,
		xiangji: xiangji,
		zan: zan,
		zan1: zan1,
		like: like,
		unlike: unlike,
		"like-fill": "",
		"unlike-fill": "",
		pinglun: pinglun,
		jushoucang: jushoucang,
		jushoucanggift: jushoucanggift,
		share: share,
		shoucangfill: shoucangfill,
		fenxiang1: fenxiang1,
		shoucang: shoucang,
		bofang: bofang,
		"bell-off": "",
		daojishi: daojishi,
		cuohao: cuohao,
		duihao: duihao,
		chakan: chakan,
		shoujihaoma: shoujihaoma,
		mima1: mima1,
		zhanghao1: zhanghao1,
		yanzhengma1: yanzhengma1,
		kejian: kejian,
		bukejian: bukejian,
		gengduo1: gengduo1,
		gengduogongneng: gengduogongneng,
		gengduo2: gengduo2,
		gengduo3: gengduo3,
		"a-14Bshanchu": "",
		fanhui1: fanhui1,
		fanhui2: fanhui2,
		sousuo: sousuo,
		sousuo1: sousuo1,
		saoyisao1: saoyisao1,
		xiangxia: xiangxia,
		xiangzuo: xiangzuo,
		fangxingweixuanzhong: fangxingweixuanzhong,
		fangxingxuanzhongfill: fangxingxuanzhongfill,
		fangxingxuanzhong: fangxingxuanzhong,
		yuanxingweixuanzhong: yuanxingweixuanzhong,
		yuanxingxuanzhong: yuanxingxuanzhong,
		yuanxingxuanzhongfill: yuanxingxuanzhongfill,
		danxuan_xuanzhong: danxuan_xuanzhong,
		danxuan_weixuanzhong: danxuan_weixuanzhong,
		gengduo: gengduo,
		fenxiang: fenxiang,
		zhuanfa00: zhuanfa00,
		dingwei: dingwei,
		dingwei1: dingwei1,
		jianshao: jianshao,
		zengjia: zengjia,
		qingkong: qingkong,
		mima: mima,
		zhanghao: zhanghao,
		yanzhengma: yanzhengma
	};

	var AMpcss = /*@__PURE__*/ (function(Component) {
		function AMpcss(props) {
			Component.call(this, props);
		}

		if (Component) AMpcss.__proto__ = Component;
		AMpcss.prototype = Object.create(Component && Component.prototype);
		AMpcss.prototype.constructor = AMpcss;
		AMpcss.prototype.render = function() {
			return;
		};

		return AMpcss;
	})(Component);
	AMpcss.css = {
		"@font-face": {
			fontFamily: '"iconfont_mp"',
			src:
				"url('https://at.alicdn.com/t/c/font_3560231_mff0m4knr.ttf') format('truetype')"
		}
	};

	apivm.define("a-mpcss", AMpcss);

	var AIconfont = /*@__PURE__*/ (function(Component) {
		function AIconfont(props) {
			Component.call(this, props);
		}

		if (Component) AIconfont.__proto__ = Component;
		AIconfont.prototype = Object.create(Component && Component.prototype);
		AIconfont.prototype.constructor = AIconfont;
		AIconfont.prototype.render = function() {
			return apivm.h(
				"view",
				null,
				apivm.h(
					"text",
					{
						style:
							"font-size:" +
							(this.props.size || 16) +
							"px;color:" +
							(this.props.color || "#999") +
							";\n\t\tfont-family: " +
							(api.platform == "mp" ? "iconfont_mp" : "iconfont;") +
							";\n\t\t" +
							(this.props.style || "") +
							";font-weight:400;",
						class: "" + (this.props.class || "")
					},
					icons[this.props.name + ""]
				),
				api.platform == "mp" && apivm.h("a-mpcss", null)
			);
		};

		return AIconfont;
	})(Component);
	AIconfont.css = {
		"@font-face": {
			fontFamily: '"iconfont"',
			src:
				"url('../../components/act/a-iconfont/fonts/iconfont.ttf') format('truetype')"
		}
	};
	apivm.define("a-iconfont", AIconfont);

	var YSharePoster = /*@__PURE__*/ (function(Component) {
		function YSharePoster(props) {
			Component.call(this, props);
			this.data = {
				show: false
			};
			this.compute = {
				isShow: function() {
					if (this.data.show != this.props.dataMore.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							if (T.platform() == "web") {
								new QRCode$1("qrcode", {
									width: 200,
									height: 200,
									text: this.props.dataMore.content.shareUrl
								});
							}
						}
					}
				},
				width: function() {},
				height: function() {},
				text: function() {}
			};
		}

		if (Component) YSharePoster.__proto__ = Component;
		YSharePoster.prototype = Object.create(Component && Component.prototype);
		YSharePoster.prototype.constructor = YSharePoster;
		YSharePoster.prototype.installed = function() {};
		YSharePoster.prototype.close = function() {
			this.props.dataMore.show = false;
		};
		YSharePoster.prototype.itemClick = function(_item) {
			var this$1 = this;

			if (_item.key == "save") {
				if (T.platform() == "web") {
					T.toast("请使用自带的软件截图");
				} else {
					api.screenCapture(
						{
							region: "#" + this.props.id
						},
						function(ret, err) {
							api.saveMediaToAlbum(
								{
									path: ret.savePath
								},
								function(ret, err) {
									T.toast(ret && ret.status ? "已保存到相册" : "保存失败");
									setTimeout(function() {
										this$1.fire("click", _item);
										this$1.close();
									}, 500);
								}
							);
						}
					);
				}
				return;
			}
			setTimeout(function() {
				this$1.fire("click", _item);
				this$1.close();
			}, 0);
		};
		YSharePoster.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.isShow,
					class: "sharePoster_box",
					style: "display:" + (this.props.dataMore.show ? "flex" : "none") + ";"
				},
				apivm.h(
					"view",
					{
						style:
							"flex:1;flex-shrink: 0;align-items: center;justify-content: center;"
					},
					apivm.h(
						"view",
						{id: this.props.id, class: "poster_box"},
						apivm.h(
							"view",
							null,
							this.props.dataMore.content.code == "34" &&
								apivm.h(
									"view",
									null,
									apivm.h(
										"view",
										{class: "survey_poster_top", style: "background: #FFFFFF;"},
										apivm.h(
											"text",
											{style: G.loadConfiguration(1) + "color: #000000;"},
											this.props.dataMore.content.title
										)
									),
									apivm.h(
										"view",
										{style: "padding:15px 20px;background:#FFF;"},
										apivm.h(
											"view",
											{
												class: "survey_share_bg",
												style:
													"background:url(../../img/survey_share_bg.png) no-repeat center;background-size: cover;"
											},
											apivm.h(
												"text",
												{
													style:
														G.loadConfiguration(8) +
														"margin-left:30px;color: #FFFFFF;line-height:121px;"
												},
												"问卷调查"
											)
										),
										apivm.h(
											"view",
											{style: "display:flex;flex-direction:row;"},
											apivm.h("view", {
												style:
													"width:8px;height:8px;background:#C61414;border-radius:10px;margin-top:7px;"
											}),
											apivm.h(
												"view",
												{style: "display:flex;flex-direction:column;margin-left:7px;"},
												apivm.h(
													"text",
													{
														style:
															G.loadConfiguration(0) + "color: #333333;margin-bottom:8px;"
													},
													"调查时间"
												),
												apivm.h(
													"text",
													{style: G.loadConfiguration(-2) + "color: #666666;"},
													dayjs$1(this.props.dataMore.content.beginTime).format(
														"YYYY-MM-DD HH:mm"
													),
													" 至 ",
													dayjs$1(this.props.dataMore.content.time).format(
														"YYYY-MM-DD HH:mm"
													)
												)
											)
										),
										apivm.h(
											"view",
											{style: "margin-top:40px;"},
											apivm.h("view", {
												style: "height:1px;width:100%;background:#f4f4f4;"
											}),
											apivm.h(
												"view",
												{style: "flex-direction:row;align-items: center;margin-top:15px;"},
												apivm.h("image", {
													style:
														G.loadConfigurationSize(36) +
														"border-radius:15px;margin-right:10px;",
													src: G.showImg(myjs.appUrl() + "pageImg/open/logo"),
													mode: "aspectFill"
												}),
												apivm.h(
													"view",
													{style: "width:1px;flex:1;"},
													apivm.h(
														"text",
														{
															style: G.loadConfiguration() + "color:#333;font-weight: 600;",
															class: "text_one" + (T.platform() == "app" ? "" : "2")
														},
														this.props.dataMore.content.appName || G.appName || T.appName()
													),
													apivm.h(
														"text",
														{style: G.loadConfiguration(-4) + "color:#666;margin-top:6px;"},
														"长按扫码与参与问卷调查"
													)
												),
												apivm.h(
													"view",
													{
														style:
															G.loadConfigurationSize(56) + "margin:-10px -10px -10px 5px;"
													},
													T.platform() == "web"
														? apivm.h("view", {id: "qrcode"})
														: apivm.h("image", {
																style: "width:100%;height:100%;",
																src: G.showImg(
																	myjs.tomcatAddress() +
																		"utils/qr?text=" +
																		this.props.dataMore.content.shareUrl
																),
																mode: "aspectFill"
														  })
												)
											)
										)
									)
								)
						),
						apivm.h(
							"view",
							null,
							this.props.dataMore.content.code == "" &&
								apivm.h(
									"view",
									null,
									apivm.h(
										"view",
										{
											class: "poster_top",
											style:
												"background-image:url('" +
												myjs.shareAddress(1) +
												"img/bg_poster_top.png'); background-size: 100% 100%;"
										},
										apivm.h(
											"text",
											{style: G.loadConfiguration(1) + "color: #FFF;line-height:27px;"},
											this.props.dataMore.content.title.length > 25
												? this.props.dataMore.content.title.substring(0, 25) + "..."
												: this.props.dataMore.content.title
										)
									),
									apivm.h(
										"view",
										{style: "padding:15px 20px;background:#FFF;"},
										apivm.h(
											"view",
											{style: "flex-direction:row;align-items: center;"},
											apivm.h(
												"view",
												null,
												this.props.dataMore.content.source.length > 0 &&
													apivm.h(
														"text",
														{
															style: G.loadConfiguration(-2) + "color: #333;margin-right:10px;"
														},
														this.props.dataMore.content.source.length > 8
															? this.props.dataMore.content.source.substring(0, 8) + "..."
															: this.props.dataMore.content.source
													)
											),
											apivm.h(
												"view",
												null,
												this.props.dataMore.content.time &&
													this.props.dataMore.content.time > 0 &&
													apivm.h(
														"text",
														{style: G.loadConfiguration(-2) + "color: #999;"},
														(this.props.dataMore.content.source.length > 0 ? "| " : "") +
															dayjs$1(this.props.dataMore.content.time).format("YYYY-MM-DD")
													)
											)
										),
										apivm.h(
											"view",
											null,
											this.props.dataMore.content.content.length > 0 &&
												apivm.h(
													"view",
													{style: "margin-top:15px;"},
													apivm.h(
														"text",
														{style: G.loadConfiguration(-2) + "color: #999;"},
														this.props.dataMore.content.content.length > 95
															? this.props.dataMore.content.content.substring(0, 95) + "..."
															: this.props.dataMore.content.content
													)
												)
										),

										apivm.h(
											"view",
											{style: "margin-top:40px;"},
											apivm.h("view", {
												style: "height:1px;width:100%;background:#f4f4f4;"
											}),
											apivm.h(
												"view",
												{style: "flex-direction:row;align-items: center;margin-top:15px;"},
												apivm.h("image", {
													style:
														G.loadConfigurationSize(36) +
														"border-radius:15px;margin-right:10px;",
													src: G.showImg(myjs.appUrl() + "pageImg/open/logo"),
													mode: "aspectFill"
												}),
												apivm.h(
													"view",
													{style: "width:1px;flex:1;"},
													apivm.h(
														"text",
														{
															style: G.loadConfiguration() + "color:#333;font-weight: 600;",
															class: "text_one" + (T.platform() == "app" ? "" : "2")
														},
														this.props.dataMore.content.appName || G.appName || T.appName()
													),
													apivm.h(
														"text",
														{style: G.loadConfiguration(-4) + "color:#666;margin-top:6px;"},
														"长按识别 查看详情"
													)
												),
												apivm.h(
													"view",
													{
														style:
															G.loadConfigurationSize(56) + "margin:-10px -10px -10px 5px;"
													},
													T.platform() == "web"
														? apivm.h("view", {id: "qrcode"})
														: apivm.h("image", {
																style: "width:100%;height:100%;",
																src: G.showImg(
																	myjs.tomcatAddress() +
																		"utils/qr?text=" +
																		this.props.dataMore.content.shareUrl
																),
																mode: "aspectFill"
														  })
												)
											)
										)
									)
								)
						)
					)
				),
				apivm.h(
					"scroll-view",
					{class: "sharePoster_warp", "scroll-y": true},
					this.props.dataMore.title &&
						apivm.h(
							"text",
							{
								style:
									G.loadConfiguration(4) +
									";color:#333;font-weight: 600;padding:16px 16px 0;"
							},
							this.props.dataMore.title
						),
					apivm.h(
						"scroll-view",
						{
							style:
								"" +
								(T.platform() == "web"
									? ""
									: "flex-direction:row;padding:10px 0;width:100%;"),
							"scroll-x": true,
							"scroll-y": false
						},
						T.platform() == "web"
							? apivm.h(
									"view",
									{style: "flex-direction:row;padding:10px 0;width:100%;"},
									this.props.data.map(function(item, index, list) {
										return (
											(T.isParameters(item.show) ? item.show : true) &&
											apivm.h(
												"view",
												{
													onClick: function() {
														return this$1.itemClick(item);
													},
													class: "sharePoster_btn_item"
												},
												item.type == "img"
													? apivm.h("image", {
															style: "" + G.loadConfigurationSize(34),
															class: "sharePoster_btn_icon",
															src: item.src
													  })
													: apivm.h(
															"view",
															{
																style:
																	G.loadConfigurationSize(34) +
																	"background:" +
																	(item.bg || "#F4F5F7") +
																	";",
																class: "share_btn_icon"
															},
															apivm.h("a-iconfont", {
																style: "font-weight:" + (item.weight || "400"),
																name: item.src,
																color: item.color || "#333",
																size:
																	G.appFontSize +
																	(T.isParameters(item.size) ? Number(item.size) : 8)
															})
													  ),
												apivm.h(
													"text",
													{style: G.loadConfiguration(-2) + "color:#333;margin-top:8px;"},
													item.value
												)
											)
										);
									})
							  )
							: this.props.data.map(function(item, index, list) {
									return (
										(T.isParameters(item.show) ? item.show : true) &&
										apivm.h(
											"view",
											{
												onClick: function() {
													return this$1.itemClick(item);
												},
												class: "sharePoster_btn_item"
											},
											item.type == "img"
												? apivm.h("image", {
														style: "" + G.loadConfigurationSize(34),
														class: "sharePoster_btn_icon",
														src: item.src
												  })
												: apivm.h(
														"view",
														{
															style:
																G.loadConfigurationSize(34) +
																"background:" +
																(item.bg || "#F4F5F7") +
																";",
															class: "share_btn_icon"
														},
														apivm.h("a-iconfont", {
															style: "font-weight:" + (item.weight || "400"),
															name: item.src,
															color: item.color || "#333",
															size:
																G.appFontSize +
																(T.isParameters(item.size) ? Number(item.size) : 8)
														})
												  ),
											apivm.h(
												"text",
												{style: G.loadConfiguration(-2) + "color:#333;margin-top:8px;"},
												item.value
											)
										)
									);
							  })
					)
				),
				apivm.h(
					"view",
					null,
					!this.props.dataMore.dotCancel &&
						apivm.h(
							"view",
							{
								onClick: this.close,
								style:
									"padding:18px;justify-content:center;border-top:10px solid #F8F8F8;background: #FFF; "
							},
							apivm.h(
								"text",
								{style: G.loadConfiguration() + ";color:#333;text-align: center;"},
								"取消"
							)
						)
				),
				apivm.h("view", {
					style: "background:#fff;padding-bottom:" + T.safeArea().bottom + "px;"
				})
			);
		};

		return YSharePoster;
	})(Component);
	YSharePoster.css = {
		".sharePoster_box": {
			position: "absolute",
			zIndex: "999",
			left: "0px",
			top: "0px",
			width: "100%",
			height: "100%",
			background: "rgba(0,0,0,0.4)"
		},
		".sharePoster_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px",
			height: "auto"
		},
		".sharePoster_btn_item": {
			padding: "10px 5px",
			width: "23%",
			alignItems: "center",
			justifyContent: "center",
			flexShrink: "0"
		},
		".sharePoster_btn_icon": {
			borderRadius: "50%",
			alignItems: "center",
			justifyContent: "center"
		},
		".poster_box": {
			background: "#FFFFFF",
			borderRadius: "10px",
			overflow: "hidden",
			width: "320px"
		},
		".poster_top": {padding: "20px 20px", width: "100%", minHeight: "94px"},
		".text_one": {
			textOverflow: "ellipsis",
			whiteSpace: "nowrap",
			overflow: "hidden"
		},
		".text_one2": {
			WebkitLineClamp: "1",
			WebkitBoxOrient: "vertical",
			display: "-webkit-box",
			overflow: "hidden"
		},
		".survey_share_bg": {
			width: "280px",
			height: "121px",
			borderRadius: "4px",
			marginBottom: "45px"
		},
		".survey_poster_top": {padding: "20px 20px 5px", width: "100%"}
	};
	apivm.define("y-share-poster", YSharePoster);

	var ImagePreviewer = /*@__PURE__*/ (function(Component) {
		function ImagePreviewer(props) {
			Component.call(this, props);
			this.data = {
				index: 1,
				activeIndex: 0,
				indicator: false,
				statusBarStyle: ""
			};
		}

		if (Component) ImagePreviewer.__proto__ = Component;
		ImagePreviewer.prototype = Object.create(Component && Component.prototype);
		ImagePreviewer.prototype.constructor = ImagePreviewer;
		ImagePreviewer.prototype.current = function() {
			return T.platform() == "web"
				? this.props.activeIndex
				: this.data.activeIndex;
		};
		ImagePreviewer.prototype.closePre = function() {
			this.close();
		};
		ImagePreviewer.prototype.close = function() {
			G.imagePreviewer.show = false;
			T.sendEvent("updatePage");
		};
		ImagePreviewer.prototype.installed = function() {
			// 修复顶部状态栏样式
			if (this.props.type == 1) {
				// api.setStatusBarStyle({ style: "dark" });
				this.data.indicator = true;
			} else if (this.props.type == 2) {
				// api.setStatusBarStyle({ style: "white" });
				this.data.indicator = false;
			}
			this.data.activeIndex = this.props.activeIndex;
			// 滑动到指定图片显示
			if (this.props.activeIndex) {
				this.data.index = parseInt(this.props.activeIndex) + 1;
			}
		};
		ImagePreviewer.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{class: "image-previewer-page"},
				apivm.h(
					"view",
					{style: "height:1px;flex:1;justify-content: center;"},
					apivm.h(
						"view",
						{style: "height:1px;flex:1;justify-content: center;"},
						apivm.h(
							"swiper",
							{
								onClick: function() {
									return this$1.closePre();
								},
								class: "image-previewer-swiper",
								circular: true,
								current: this.current(),
								"indicator-dots": "true",
								"indicator-color": "#737373",
								"indicator-active-color": "#ffffff"
							},
							(Array.isArray(this.props.imgs)
								? this.props.imgs
								: Object.values(this.props.imgs)
							).map(function(item$1, index$1) {
								return apivm.h(
									"swiper-item",
									{
										onClick: function() {
											return this$1.closePre();
										},
										style: "height:100%;"
									},
									apivm.h("image", {
										class: "image-previewer-img",
										src: G.showImg(item$1),
										thumbnail: "false",
										mode: "aspectFit"
									})
								);
							})
						)
					)
				)
			);
		};

		return ImagePreviewer;
	})(Component);
	ImagePreviewer.css = {
		".image-previewer-page": {height: "100%", background: "#000"},
		".image-previewer-swiper": {height: "100%"},
		".image-previewer-img": {height: "100%", width: "100%"},
		".image-previewer-header": {
			display: "flex",
			flexDirection: "row",
			alignItems: "center",
			flexWrap: "nowrap",
			padding: "0 5px"
		},
		".image-previewer-back": {
			width: "30px",
			height: "45px",
			backgroundRepeat: "no-repeat",
			backgroundPosition: "center",
			backgroundSize: "20px"
		},
		".image-previewer-close": {width: "30px", height: "45px"},
		".image-previewer-title": {
			flex: "1",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexWrap: "nowrap",
			fontSize: "18px",
			textAlign: "center",
			textOverflow: "ellipsis",
			overflow: "hidden",
			whiteSpace: "nowrap",
			color: "#fff",
			height: "45px",
			lineHeight: "45px"
		},
		".image-previewer-placeholder": {
			width: "30px",
			height: "45px",
			marginRight: "5px"
		},
		".image-previewer-right": {width: "30px", height: "45px"}
	};
	apivm.define("image-previewer", ImagePreviewer);

	var ADivider = /*@__PURE__*/ (function(Component) {
		function ADivider(props) {
			Component.call(this, props);
			this.compute = {
				boxClass: function() {
					return "a-divider " + (this.props.class || "");
				},
				lineStyle: function() {
					return this.props["line-color"]
						? "border-top-color:" + this.props["line-color"] + ";"
						: "";
				},
				textStyle: function() {
					return (
						(this.props.color ? "color:" + this.props.color + ";" : "") +
						"" +
						(this.props.style || "")
					);
				}
			};
		}

		if (Component) ADivider.__proto__ = Component;
		ADivider.prototype = Object.create(Component && Component.prototype);
		ADivider.prototype.constructor = ADivider;
		ADivider.prototype.lineClass = function(position) {
			var width =
				this.props["content-position"] == position
					? "a-divider_line-width"
					: "a-divider_line-flex";
			var style = this.props.dashed
				? "a-divider_line-dashed"
				: "a-divider_line-solid";
			return "a-divider_line " + width + " " + style;
		};
		ADivider.prototype.render = function() {
			return apivm.h(
				"view",
				{class: this.boxClass, style: this.props.style || ""},
				this.props.content
					? [
							apivm.h("view", {class: this.lineClass("left"), style: this.lineStyle}),
							apivm.h(
								"text",
								{class: "a-divider_text", style: this.textStyle},
								this.props.content
							),
							apivm.h("view", {class: this.lineClass("right"), style: this.lineStyle})
					  ]
					: apivm.h("view", {class: this.lineClass("center"), style: this.lineStyle})
			);
		};

		return ADivider;
	})(Component);
	ADivider.css = {
		".a-divider": {flexDirection: "row", alignItems: "center"},
		".a-divider_line": {borderTopWidth: "1px", borderTopColor: "#F6F6F6"},
		".a-divider_line-solid": {borderTopStyle: "solid"},
		".a-divider_line-dashed": {borderTopStyle: "dashed"},
		".a-divider_line-width": {width: "10%"},
		".a-divider_line-flex": {flex: "1"},
		".a-divider_text": {
			fontSize: "14px",
			color: "#969799",
			padding: "0 16px",
			textAlign: "center",
			maxWidth: "75%"
		}
	};
	apivm.define("a-divider", ADivider);

	var ZActionsheet = /*@__PURE__*/ (function(Component) {
		function ZActionsheet(props) {
			Component.call(this, props);
			this.data = {
				show: false
			};
			this.compute = {
				isShow: function() {
					var this$1 = this;

					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							if (this.props.dataMore.dotClose) {
								setTimeout(function() {
									this$1.props.dataMore.dotClose = false;
								}, 500);
							}
						}
					}
				}
			};
		}

		if (Component) ZActionsheet.__proto__ = Component;
		ZActionsheet.prototype = Object.create(Component && Component.prototype);
		ZActionsheet.prototype.constructor = ZActionsheet;
		ZActionsheet.prototype.installed = function() {};
		ZActionsheet.prototype.closePage = function() {
			if (this.props.dataMore.dotClose) {
				return;
			}
			this.props.dataMore.show = false;
			T.sendEvent("updatePage");
		};
		ZActionsheet.prototype.itemClick = function(_item, _index) {
			var this$1 = this;

			if (this.props.dataMore.dotClose) {
				return;
			}
			if (_item.disabled) {
				return;
			}
			if (T.isParameters(this.props.active)) {
				this.props.active.key = _item.key;
			}
			_item.buttonIndex = _index + 1;
			setTimeout(function() {
				this$1.fire("click", _item);
				this$1.closePage();
			}, 0);
		};
		ZActionsheet.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.isShow,
					class: "actionSheet_box",
					style: "display:" + (this.props.dataMore.show ? "flex" : "none") + ";"
				},
				apivm.h("view", {
					onClick: function() {
						return this$1.closePage();
					},
					style: "flex:1;min-height:30%;flex-shrink: 0;"
				}),
				apivm.h(
					"scroll-view",
					{class: "actionSheet_warp", style: "flex-shrink: 1;", "scroll-y": true},
					apivm.h(
						"view",
						null,
						this.props.dataMore.title &&
							apivm.h(
								"text",
								{
									style:
										G.loadConfiguration(-2 + 1) +
										";color:#aaa;text-align: center;padding: 15px;"
								},
								this.props.dataMore.title
							)
					),
					this.props.data.map(function(item, index) {
						return (
							(T.isParameters(item.show) ? item.show : true) && [
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.itemClick(item, index);
										},
										class: "actionSheet_item",
										style:
											"justify-content:" +
											(item.type ? "flex-start" : "center") +
											";opacity: " +
											(item.disabled ? "0.3" : "1") +
											";"
									},
									apivm.h(
										"view",
										null,
										T.isParameters(this$1.props.active) &&
											T.isParameters(this$1.props.active.direction) &&
											this$1.props.active.direction >= 0 &&
											item.key == this$1.props.active.key &&
											apivm.h("a-iconfont", {
												style:
													"margin-left:-25px;margin-right:10px;transform: rotate(" +
													(this$1.props.active.direction == 1 ? "0" : "180") +
													"deg);",
												name: "zhixiangxia",
												color: G.appTheme,
												size: G.appFontSize - 1
											})
									),
									apivm.h(
										"view",
										null,
										item.type == "img"
											? apivm.h("img", {src: "", alt: ""})
											: item.type == "icon"
											? apivm.h("a-iconfont", {
													style:
														"font-weight:" + (item.weight || "400") + ";margin-right:10px;",
													name: item.src,
													color: item.color || "#333",
													size:
														G.appFontSize +
														(T.isParameters(item.size) ? Number(item.size) : 4)
											  })
											: ""
									),
									apivm.h(
										"text",
										{
											style:
												G.loadConfiguration(-2 + 1) +
												";color:" +
												(item.key ==
												(T.isParameters(this$1.props.active) && this$1.props.active.key)
													? G.appTheme
													: "#333")
										},
										item.value
									)
								),
								index != this$1.props.data.length - 1 &&
									!this$1.props.cancel &&
									!this$1.props.dataMore.cancel &&
									apivm.h(
										"view",
										{style: "width:100%;height:1px;padding:0 16px;"},
										apivm.h("a-divider", null)
									)
							]
						);
					})
				),
				apivm.h(
					"view",
					null,
					(this.props.cancel || this.props.dataMore.cancel) &&
						apivm.h(
							"view",
							{
								onClick: function() {
									return this$1.closePage();
								},
								class: "actionSheet_item",
								style:
									"justify-content:center;border-top:10px solid #f6f6f6;background: #FFF; "
							},
							apivm.h(
								"text",
								{style: G.loadConfiguration(-2) + ";color:#333"},
								this.props.dataMore.cancel || "取消"
							)
						)
				),
				apivm.h("view", {
					style: "background:#fff;padding-bottom:" + T.safeArea().bottom + "px;"
				})
			);
		};

		return ZActionsheet;
	})(Component);
	ZActionsheet.css = {
		".actionSheet_box": {
			position: "absolute",
			zIndex: "999",
			left: "0px",
			top: "0px",
			width: "100%",
			height: "100%",
			background: "rgba(0,0,0,0.4)"
		},
		".actionSheet_warp": {
			background: "#FFF",
			borderTopLeftRadius: "10px",
			borderTopRightRadius: "10px",
			height: "auto"
		},
		".actionSheet_item": {
			width: "100%",
			minHeight: "60px",
			alignItems: "center",
			flexDirection: "row",
			padding: "0 16px"
		}
	};
	apivm.define("z-actionSheet", ZActionsheet);

	var ZSearch = /*@__PURE__*/ (function(Component) {
		function ZSearch(props) {
			Component.call(this, props);
			this.data = {
				propValue: ""
			};
			this.compute = {
				id: function() {
					return this.props.id || "input";
				}
			};
		}

		if (Component) ZSearch.__proto__ = Component;
		ZSearch.prototype = Object.create(Component && Component.prototype);
		ZSearch.prototype.constructor = ZSearch;
		ZSearch.prototype.installed = function() {
			var this$1 = this;
			if (
				T.isParameters(this.props.dataMore) ? this.props.dataMore.autofocus : false
			) {
				//是否自动获取焦点
				setTimeout(function() {
					$("#" + this$1.id).focus();
				}, 150);
			}
		};
		ZSearch.prototype.inputConfirm = function() {
			var this$1 = this;

			$("#" + this.id).blur();
			setTimeout(function() {
				this$1.fire("confirm", {});
			}, 0);
		};
		ZSearch.prototype.inputIng = function() {
			var this$1 = this;

			if (this.props.dataMore.expression) {
				//有正则表达示
				this.props.dataMore.input = this.props.dataMore.input.replace(
					new RegExp(this.props.dataMore.expression, "g"),
					""
				);
			}
			setTimeout(function() {
				this$1.fire("input", {});
			}, 0);
		};
		ZSearch.prototype.inputBlur = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.fire("blur", {});
			}, 0);
		};
		ZSearch.prototype.inputFocus = function() {
			var this$1 = this;

			$("#" + this.id).focus();
			setTimeout(function() {
				this$1.fire("focus", {});
			}, 0);
		};
		ZSearch.prototype.clean = function() {
			var this$1 = this;

			if (!this.i) {
				this.props.dataMore.input = "";
				this.i = 1;
			} else {
				this.props.dataMore.input = " ".repeat(this.i++ % 2);
				this.props.dataMore.input = " ".repeat(this.i++ % 2);
			}
			this.inputIng();
			setTimeout(function() {
				this$1.fire("clean", {});
			}, 0);
		};
		ZSearch.prototype.openLeftFilters = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.fire("leftFilters", {});
			}, 0);
		};
		ZSearch.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					class: "z_search_box " + (this.props.class || ""),
					style:
						"\n\tborder-top-left-radius: " +
						(this.props.round ? "30" : "4") +
						"px;\n\tborder-top-right-radius: " +
						(this.props.round ? "30" : "4") +
						"px;\n\tborder-bottom-left-radius: " +
						(this.props.round ? "30" : "4") +
						"px;\n\tborder-bottom-right-radius: " +
						(this.props.round ? "30" : "4") +
						"px;\n\tbackground: " +
						(this.props.bg || "#F4F5F7") +
						";\n\tjustify-content: " +
						(this.props.justify ||
							(this.props.type == "1" ? "center" : "flex-start")) +
						";\n\t" +
						(this.props.style || "")
				},
				apivm.h(
					"view",
					{style: "width:auto;height:auto;"},
					T.isParameters(this.props.dataMore) &&
						this.props.dataMore.leftFilters &&
						apivm.h(
							"view",
							{
								onClick: function() {
									return this$1.openLeftFilters();
								},
								style:
									"margin-left:-5px;margin-right:15px;flex-direction:row;align-items: center;"
							},
							apivm.h(
								"text",
								{
									style:
										G.loadConfiguration(1) +
										"font-weight: 400;color: #333;margin-right:4px;"
								},
								this.props.leftFiltersText
							),
							apivm.h("a-iconfont", {
								name: "xiangxia",
								color: "#333",
								size: G.appFontSize - 4
							}),
							apivm.h("view", {
								style: "width:1px;height:24px;background:#E5E7E8;margin-left:8px;"
							})
						)
				),
				apivm.h(
					"view",
					{style: "width:auto;height:auto;"},
					(!T.isParameters(this.props.dataMore) ||
						(!this.props.dataMore.dotIcon && !this.props.dataMore.leftFilters)) &&
						apivm.h(
							"view",
							{class: "z_search_input_icon", style: "margin-right:10px;"},
							apivm.h("a-iconfont", {
								class: "z_search_icon",
								name: "sousuo",
								color: "#999",
								size: G.appFontSize - 2
							})
						)
				),
				this.props.type == "1"
					? apivm.h(
							"text",
							{
								class: "z_search_text",
								style:
									"color:#999;line-height: " +
									(G.appFontSize + 2) +
									"px;" +
									G.loadConfiguration(-2)
							},
							this.props.placeholder
					  )
					: [
							apivm.h("input", {
								id: this.id,
								style:
									"" + G.loadConfiguration() + (this.props.dataMore.inputStyle || ""),
								"placeholder-style": "color:#999;",
								class: "z_search_input",
								type: this.props.inputType || "text",
								placeholder: this.props.placeholder,
								onInput: function(e) {
									if (typeof this$1 != "undefined") {
										this$1.props.dataMore.input = e.target.value;
									} else {
										this$1.data.this.props.dataMore.input = e.target.value;
									}
									this$1.inputIng(e);
								},
								maxlength: this.props.dataMore.maxlength,
								"confirm-type": this.props.confirmType || "search",
								"keyboard-type": this.props.keyboardType || "default",
								onConfirm: this.inputConfirm,
								onBlur: this.inputBlur,
								onFocus: this.inputFocus,
								value:
									typeof this == "undefined"
										? this.data.this.props.dataMore.input
										: this.props.dataMore.input
							}),
							this.props.dataMore.input &&
								!this.props.dataMore.dotCleanIcon &&
								apivm.h(
									"view",
									{
										onClick: this.clean,
										class: "z_search_input_icon",
										style: "padding: 5px;padding-left:0;margin-right: -10px;"
									},
									apivm.h("a-iconfont", {
										name: "qingkong",
										color: "#666",
										size: G.appFontSize
									})
								)
					  ]
			);
		};

		return ZSearch;
	})(Component);
	ZSearch.css = {
		".z_search_box": {
			width: "100%",
			height: "100%",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			padding: "2px 15px"
		},
		".z_search_text": {marginLeft: "4px"},
		".z_search_input": {
			paddingLeft: "0px",
			paddingRight: "10px",
			width: "1px",
			flex: "1",
			background: "transparent",
			borderColor: "transparent",
			color: "#333"
		},
		".z_search_input::placeholder": {color: "#999"},
		".z_search_input_icon": {
			alignItems: "center",
			justifyContent: "center",
			width: "auto",
			height: "auto"
		}
	};
	apivm.define("z-search", ZSearch);

	// 加密
	function getFileAdress(_param, _callback) {
		if (_param === void 0) {
			_param = {};
		}
		//文档地址 https://www.yozodcs.com/page/help.html#help9
		T.ajax(
			{u: "https://www.yozodcs.com/fcscloud/file/http?"},
			"onlinefile",
			function(ret, err) {
				if (ret) {
					var data = (ret.data || {}).data;
					if (data) {
						T.ajax(
							{u: "https://www.yozodcs.com/fcscloud/composite/convert?"},
							"onlinefile",
							function(ret, err) {
								if (ret) {
									var viewUrl = (ret.data || {}).viewUrl;
									if (viewUrl) {
										_callback(viewUrl, ret.data);
									} else {
										_callback(null, ret.message || "打开失败，请重试");
									}
								} else {
									_callback(null, "打开失败");
								}
							},
							"生成链接",
							"post",
							{
								values: {
									srcRelativePath: data,
									convertType:
										G.getFileInfo(data.substring(data.lastIndexOf("."))).convertType ||
										"0",
									isDccAsync: 1,
									isCopy: 0,
									noCache: 0,
									fileUrl: _param.url,
									showFooter: 0, //是否显示页脚
									isHeaderBar: 0,
									htmlTitle: "详情"
								}
							},

							{
								"content-type": "application/x-www-form-urlencoded"
							}
						);
					} else {
						_callback(null, ret.message || "打开失败，请重试");
					}
				} else {
					_callback(null, "打开失败");
				}
			},
			"在线转换",
			"post",
			{
				values: {
					fileUrl: _param.url
				}
			},

			{
				"content-type": "application/x-www-form-urlencoded"
			}
		);
	}

	var ZVideo = /*@__PURE__*/ (function(Component) {
		function ZVideo(props) {
			Component.call(this, props);
			this.data = {
				withSrc: "",
				showSrc: "",
				showPoster: "",
				showError: ""
			};
			this.compute = {
				monitor: function() {
					if (this.props.src != this.data.withSrc) {
						this.data.withSrc = this.props.src;
						this.dealWith();
					}
				}
			};
		}

		if (Component) ZVideo.__proto__ = Component;
		ZVideo.prototype = Object.create(Component && Component.prototype);
		ZVideo.prototype.constructor = ZVideo;
		ZVideo.prototype.installed = function() {};
		ZVideo.prototype.dealWith = function() {
			var this$1 = this;

			console.log(
				"withSrc:" +
					this.data.withSrc +
					"-" +
					this.data.showSrc +
					"-" +
					dayjs$1().unix()
			);
			if ((this.data.withSrc + "").indexOf("http") != 0) {
				//不是http开头 说明系统内附件
				var cachePath = T.getPrefs("attach_" + this.data.withSrc);
				console.log(cachePath);
				if (
					cachePath &&
					dayjs$1().unix() - Number(cachePath.split("-attachPath-")[0]) < 86400
				) {
					this.data.showSrc = cachePath.split("-attachPath-")[1];
					this.data.showPoster =
						myjs.tomcatAddress() + "utils2/proxyVideo?" + this.data.showSrc;
					return;
				}
				var src = myjs.appUrl() + "file/preview/" + this.data.withSrc;
				getFileAdress({url: src}, function(ret, err) {
					if (ret) {
						T.ajax(
							{u: ret, dt: "text", _this: this$1},
							"onlinefile",
							function(ret, err) {
								var matchResult = ret ? ret.match(/videoFile = "([^"]+)"/) : "";
								if (matchResult) {
									this$1.data.showError = "";
									function unicodeToChinese(str) {
										return str.replace(/\\u(\w{4})/g, function(match, code) {
											return String.fromCharCode(parseInt(code, 16));
										});
									}
									var path = unicodeToChinese(matchResult[1]).replace(/\\/g, "");
									this$1.data.showSrc = path;
									this$1.data.showPoster =
										myjs.tomcatAddress() + "utils2/proxyVideo?" + this$1.data.showSrc;
									T.setPrefs(
										"attach_" + this$1.data.withSrc,
										dayjs$1().unix() + "-attachPath-" + path
									);
								} else {
									this$1.data.showError = err;
								}
							},
							"附件详情",
							"get"
						);
					} else {
						this$1.data.showError = err;
					}
				});
			} else {
				this.data.showSrc = this.data.withSrc;
				this.data.showPoster =
					myjs.tomcatAddress() + "utils2/proxyVideo?" + this.data.showSrc;
			}
		};
		ZVideo.prototype.again = function(e) {
			if (this.data.showError) {
				this.data.showError = "";
				this.dealWith();
			}
			G.stopBubble(e);
		};
		ZVideo.prototype.render = function() {
			return apivm.h(
				"view",
				{
					a: this.monitor,
					style:
						"width:100%;height: " +
						(T.platform() == "web"
							? api.winWidth > 600
								? 600
								: api.winWidth
							: api.winWidth) *
							0.52 +
						"px;" +
						this.props.style,
					class: this.props.class
				},
				this.data.showSrc
					? apivm.h("video", {
							id: this.props.id,
							controls: true,
							style: "width:100%;height:100%;object-fit: cover;",
							src: this.data.showSrc,
							poster: this.props.poster || this.data.showPoster,
							mode: "aspectFill"
					  })
					: apivm.h(
							"view",
							{
								onClick: this.again,
								style:
									"width:100%;height:100%;align-items: center;justify-content: center;"
							},
							this.data.showError != ""
								? apivm.h(
										"view",
										{
											style:
												"flex-direction:row;align-items: center;justify-content: center;"
										},
										apivm.h("a-iconfont", {
											name: "huanyuan",
											color: "#666",
											style: "font-weight: 600;margin-right:10px;",
											size: G.appFontSize
										}),
										apivm.h(
											"text",
											{style: "color:#666;" + G.loadConfiguration()},
											"视频加载失败"
										)
								  )
								: apivm.h("image", {
										style: "width:50px;height:50px;",
										src: myjs.shareAddress(1) + "img/loading.gif",
										mode: "aspectFill",
										thumbnail: "false"
								  })
					  )
			);
		};

		return ZVideo;
	})(Component);
	apivm.define("z-video", ZVideo);

	var ZRichText = /*@__PURE__*/ (function(Component) {
		function ZRichText(props) {
			Component.call(this, props);
			this.data = {
				showText: null,
				listData: [],
				hasExpand: false, //是否有展开
				isExpand: false //是否展开了
			};
			this.compute = {
				monitor: function() {
					if (this.props.nodes != this.data.showText) {
						this.data.showText = this.props.nodes;
						this.dealWithCon();
					}
				}
			};
		}

		if (Component) ZRichText.__proto__ = Component;
		ZRichText.prototype = Object.create(Component && Component.prototype);
		ZRichText.prototype.constructor = ZRichText;
		ZRichText.prototype.installed = function() {};
		ZRichText.prototype.expandShow = function() {
			this.data.isExpand = !this.data.isExpand;
			this.dealWithCon();
		};
		ZRichText.prototype.dealWithCon = function() {
			var this$1 = this;

			var expText =
				(T.isParameters(this.data.showText) ? this.data.showText : "") + "";
			if (T.isObject(expText) || T.isArray(expText)) {
				expText = JSON.stringify(expText);
			}
			var notTagText = T.removeTag(expText);
			this.data.hasExpand =
				T.isParameters(this.props.expand) && notTagText.length > this.props.expand;
			expText =
				this.data.hasExpand && !this.data.isExpand
					? notTagText.substring(0, this.props.expand) + "..."
					: expText;

			expText = expText.replace(
				/(<style(.*?)<\/style>|<link(.*?)<\/link>|<script(.*?)<\/script>|<!--[\w\W\r\n]*?-->|^\s*|\s*$)/gi,
				""
			);
			var reLabel = function(_bel) {
				var oldText = expText;
				expText = expText.replace(
					new RegExp("<" + _bel + "[^>]*>(.*?)</" + _bel + ">", "gi"),
					"$1"
				);
				if (expText != oldText && expText.indexOf(_bel) != -1) {
					reLabel(_bel);
				}
			};
			["span"].forEach(function(item) {
				reLabel(item);
			});
			expText = expText.replace(/<\s*\/?\s*br\s*\/?\s*>/gi, "</br>");
			expText = expText.replace(/\n/gi, "</br>");
			expText = T.decodeCharacter(expText);
			expText = expText.replace(/<ins(.*?)>(.*?)<\/ins>/gi, "$2");
			if (!expText.startsWith("<") || !expText.endsWith(">")) {
				expText = "<div>" + expText + "</div>";
			}
			// console.log(expText);
			var newText = expText;
			//清空表格td中所有的标签
			var rdRegex = /<td(.*?)>(.*?)<\/td>/gi;
			var match;
			while ((match = rdRegex.exec(expText)) !== null) {
				var nText = T.removeTag(match[2]);
				newText = newText.replace(match[2], nText);
			}
			expText = newText;
			var strRegex = /<\/?\w+[^>]*>/gi;
			var nowIndexs = [],
				viewIndexs = [];
			var startIndex = [];
			expText.replace(strRegex, function(item, index) {
				//循环对应的内容和角标
				var nlabel = item.match(/<\/*([^> ]+)[^>]*>/)[1].toLocaleLowerCase();
				var styleMatch = /style\s*=\s*['"]([^'"]*)['"]/i.exec(item);
				var nowItem = {
					label: nlabel,
					index: index,
					text: item,
					style: styleMatch ? styleMatch[1] : ""
				};
				viewIndexs.push(nowItem);
				// console.log(index + "-" + nlabel + "-" + item);
				if (/^<(p|div|span).*/i.test(item)) {
					var nowItems = {
						index: nowIndexs.length,
						endIndex: 0,
						label: nlabel,
						start: nowItem,
						end: null,
						style: styleMatch ? styleMatch[1] : ""
					};
					startIndex.push(nowItems);
					nowIndexs.push(nowItems);
				} else if (/^<\/(p|div|span)>/i.test(item)) {
					if (startIndex.length) {
						nowIndexs[startIndex[startIndex.length - 1].index].end = nowItem;
						nowIndexs[startIndex[startIndex.length - 1].index].endIndex =
							nowIndexs.length - 1;
						startIndex.pop();
					}
				} else if (/^<.*?>$/.test(item)) {
					nowIndexs.push({
						index: nowIndexs.length,
						endIndex: nowIndexs.length,
						label: nlabel,
						start: nowItem,
						end: nowItem,
						style: styleMatch ? styleMatch[1] : ""
					});
				}
			});
			var showTexts = [];
			var tableData = null,
				isTable = false,
				tableItem = null;
			viewIndexs.forEach(function(item, index) {
				var minIndex = item.index + item.text.length;
				var maxIndex =
					index != viewIndexs.length - 1
						? viewIndexs[index + 1].index
						: expText.length;
				var viewText = expText.substring(minIndex, maxIndex);
				if (item.label == "table") {
					if (!isTable) {
						isTable = true;
						tableData = {
							label: "table",
							widths: [],
							data: [],
							index: minIndex,
							style: item.style
						};
					} else {
						isTable = false;
						showTexts.push(tableData);
					}
					return;
				}
				if (isTable) {
					if (item.text == "</tr>") {
						tableData.data.push(tableItem);
					} else if (item.label == "tr") {
						tableItem = {label: "tr", data: [], index: minIndex, style: item.style};
					} else if (item.text != "</td>" && item.label == "td") {
						var tdWidth = this$1.getStyle(item.style, "width") || "150px";
						if (!tableData.data.length) {
							if (tdWidth.indexOf("%") != -1) {
								//是百分比宽度
								var nW = Number(tdWidth.replace(/%/g, ""));
								tdWidth = ((nW < 30 ? 30 : nW) * G.pageWidth) / 100 + "px";
							}
							tableData.widths.push(tdWidth);
						}
						var showStyle = "";
						showStyle +=
							"border:" +
							(this$1.getStyle(item.style, "border") || "1px solid #ccc") +
							";";
						showStyle +=
							"border-left:" + this$1.getStyle(item.style, "border-left") + ";";
						showStyle +=
							"border-right:" + this$1.getStyle(item.style, "border-right") + ";";
						showStyle +=
							"border-top:" + this$1.getStyle(item.style, "border-top") + ";";
						showStyle +=
							"border-bottom:" + this$1.getStyle(item.style, "border-bottom") + ";";
						showStyle +=
							"background:" + this$1.getStyle(item.style, "background") + ";";
						showStyle += "color:" + this$1.getStyle(item.style, "color") + ";";
						tableItem.data.push({
							label: "td",
							index: minIndex,
							text: viewText,
							style: showStyle
						});
					}
					return;
				}
				if (/^(?!p|div|span).*$/i.test(item.label)) {
					if (
						item.label == "br" &&
						!item.text &&
						showTexts.length &&
						showTexts[showTexts.length - 1].label == "br"
					) {
						return;
					}
					var addItem = {label: item.label, index: item.index, style: item.style};
					minIndex = minIndex + item.text.length;
					var srcMatch = /src\s*=\s*['"]([^'"]*)['"]/i.exec(item.text);
					if (srcMatch) {
						addItem.src = srcMatch[1];
					}
					var hrefMatch = /href\s*=\s*['"]([^'"]*)['"]/i.exec(item.text);
					if (hrefMatch) {
						addItem.href = hrefMatch[1];
					}
					showTexts.push(addItem);
				}
				if (viewText.replace(/^\s*|\s*$/, "")) {
					var showText = {
						label: "text",
						index: minIndex,
						text: viewText,
						style: item.style
					};
					if (item.label == "a") {
						showText.label = "text_a";
						showText.href = showTexts.length
							? showTexts[showTexts.length - 1].href
							: "";
					}
					showTexts.push(showText);
				}
			});
			// console.log(JSON.stringify(nowIndexs));
			// console.log(JSON.stringify(viewIndexs));
			// console.log(JSON.stringify(showTexts));
			this.data.listData = showTexts;
		};
		ZRichText.prototype.openImages = function(e, _item, _index) {
			G.stopBubble(e);
			var imgs = this.data.listData.filter(function(item, index) {
				return item.label == "img";
			});
			console.log(imgs, "imgs");
			G.openImgPreviewer({
				index: G.getItemForKey(_item.index + "", imgs, "index")._i,
				imgs: imgs.map(function(obj) {
					return obj.src;
				})
			});
		};
		ZRichText.prototype.openHrefs = function(e, _item, _index) {
			G.stopBubble(e);
			if (T.platform() == "web") {
				window.open(_item.href);
				return;
			}
			T.openWin(
				"mo_details_url",
				"../mo_details_url/mo_details_url.stml",
				{url: _item.href},
				this
			);
		};
		ZRichText.prototype.getStyle = function(_text, _item) {
			var match = new RegExp(_item + ":s*([^;]+);").exec(_text);
			if (match) {
				var matchText = match[1].replace(/pt/g, "px");
				return matchText;
			}
			return "";
		};
		ZRichText.prototype.nTouchmove = function() {
			G.touchmove();
		};
		ZRichText.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{a: this.monitor, class: this.props.class},
				apivm.h(
					"view",
					null,
					(Array.isArray(this.data.listData)
						? this.data.listData
						: Object.values(this.data.listData)
					).map(function(item$1, index$1) {
						return apivm.h(
							"view",
							null,
							item$1.label == "text"
								? apivm.h(
										"view",
										null,
										apivm.h(
											"text",
											{
												style:
													this$1.props.style +
													"line-height: " +
													G.appFontSize * 1.8 +
													"px;" +
													(this$1.props.detail && T.platform() != "app"
														? "text-indent: 2em;"
														: ""),
												class: "richText"
											},
											this$1.props.detail
												? api.systemType == "android"
													? "					"
													: api.systemType == "ios"
													? "	 "
													: ""
												: "",
											item$1.text
										)
								  )
								: null,
							item$1.label == "text_a"
								? apivm.h(
										"view",
										{
											onClick: function(e) {
												return this$1.openHrefs(e, item$1, index$1);
											}
										},
										apivm.h(
											"text",
											{style: this$1.props.style + "color: blue;", class: "richText"},
											item$1.text
										)
								  )
								: item$1.label == "br"
								? apivm.h(
										"view",
										null,
										apivm.h("view", {style: "height:" + G.appFontSize + "px;"})
								  )
								: item$1.label == "img"
								? apivm.h(
										"view",
										{
											onClick: function(e) {
												return this$1.openImages(e, item$1, index$1);
											}
										},
										apivm.h(
											"view",
											{class: "richImgBox"},
											apivm.h("image", {
												class: "richImg",
												mode: "aspectFill",
												thumbnail: "false",
												src: G.showImg(item$1.src)
											})
										)
								  )
								: item$1.label == "video" || item$1.label == "source"
								? apivm.h(
										"view",
										{style: "margin:5px 0;"},
										item$1.src && apivm.h("z-video", {src: item$1.src})
								  )
								: item$1.label == "table"
								? apivm.h(
										"view",
										null,
										apivm.h(
											"scroll-view",
											{"scroll-x": true, "scroll-y": false},
											apivm.h(
												"view",
												{
													onTouchStart: this$1.nTouchmove,
													onTouchMove: this$1.nTouchmove,
													onTouchStart: this$1.nTouchmove
												},
												(item$1.data || []).map(function(nItem, nIndex) {
													return apivm.h(
														"view",
														{
															class: "richTable_item",
															style: "margin-top:" + (nIndex ? -1 : 0) + "px;"
														},
														(nItem.data || []).map(function(uItem, uIndex) {
															return apivm.h(
																"view",
																{
																	class: "richTable_item_td",
																	style:
																		uItem.style +
																		"width:" +
																		item$1.widths[uIndex] +
																		";margin-left:" +
																		(uIndex ? -1 : 0) +
																		"px;"
																},
																apivm.h(
																	"text",
																	{style: "" + G.loadConfiguration(), class: "richText"},
																	uItem.text
																)
															);
														})
													);
												})
											)
										)
								  )
								: apivm.h("view", null)
						);
					})
				),
				apivm.h(
					"view",
					null,
					this.data.hasExpand &&
						apivm.h(
							"view",
							{
								style:
									"padding: 7px 0;flex-direction:row; align-items: center;justify-content: flex-start;",
								onClick: function() {
									return this$1.expandShow();
								}
							},
							apivm.h(
								"text",
								{style: G.loadConfiguration(-2) + "color:#999"},
								this.data.isExpand ? "收起" : "展开"
							),
							apivm.h("a-iconfont", {
								name: "xiangxiagengduo",
								style:
									"margin-left:5px;transform: rotate(" +
									(this.data.isExpand ? "180" : "0") +
									"deg);",
								color: "#999",
								size: G.appFontSize - 3
							})
						)
				)
			);
		};

		return ZRichText;
	})(Component);
	ZRichText.css = {
		".richImgBox": {alignItems: "center", justifyContent: "center"},
		".richImg": {width: "100%", maxWidth: "100%", maxHeight: "100%"},
		".richTable_item": {flexFlow: "row nowrap"},
		".richTable_item_td": {
			alignItems: "center",
			justifyContent: "center",
			flexShrink: "0",
			padding: "5px"
		},
		".richText": {wordWrap: "break-word", wordBreak: "break-all"}
	};
	apivm.define("z-rich-text", ZRichText);

	var ZAlert = /*@__PURE__*/ (function(Component) {
		function ZAlert(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				search: {dotIcon: true, show: true, input: "", value: ""}
			};
			this.compute = {
				isShow: function() {
					if (this.props.dataMore.show != this.data.show) {
						this.data.show = this.props.dataMore.show;
						if (this.data.show) {
							if (this.props.dataMore.input) {
								this.data.search.input = this.props.dataMore.content;
							}
						}
					}
				}
			};
		}

		if (Component) ZAlert.__proto__ = Component;
		ZAlert.prototype = Object.create(Component && Component.prototype);
		ZAlert.prototype.constructor = ZAlert;
		ZAlert.prototype.installed = function() {};
		ZAlert.prototype.closePage = function(_type) {
			this.props.dataMore.show = false;
			if (!_type) {
				this.fire("cancel");
			}
			T.sendEvent("updatePage");
		};
		ZAlert.prototype.closeStop = function(e) {
			G.stopBubble(e);
		};
		ZAlert.prototype.inputIng = function(e) {
			if (T.isParameters(e)) {
				this.data.search.input = e.detail.value;
			}
		};
		ZAlert.prototype.itemClick = function() {
			var this$1 = this;

			setTimeout(function() {
				if (this$1.props.dataMore.input || this$1.props.dataMore.textarea) {
					this$1.props.dataMore.content = this$1.data.search.input;
				}
				this$1.data.search.input = "";
				this$1.fire("click", this$1.props.dataMore);
				this$1.fire("sure");
				this$1.closePage(1);
			}, 0);
		};
		ZAlert.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.isShow,
					class: "alert_box",
					onClick: this.closeStop,
					style: "display:" + (this.props.dataMore.show ? "flex" : "none") + ";"
				},
				this.data.show
					? apivm.h(
							"view",
							{class: "alert_warp", onClick: this.closeStop},
							apivm.h(
								"view",
								{class: "alert_content_title"},
								this.props.dataMore.title &&
									apivm.h(
										"text",
										{class: "alert_title", style: "" + G.loadConfiguration(4)},
										this.props.dataMore.title
									)
							),
							apivm.h(
								"scroll-view",
								{class: "alert_content_box", "scroll-x": false, "scroll-y": true},
								this.props.dataMore.richText
									? apivm.h("z-rich-text", {
											style: G.loadConfiguration(1) + "color:#333;",
											nodes: this.props.dataMore.content
									  })
									: this.props.dataMore.input
									? apivm.h(
											"view",
											{style: "height:36px;width:100%;"},
											apivm.h("z-search", {
												id: "alert_input",
												dataMore: this.data.search,
												placeholder: this.props.dataMore.placeholder
											})
									  )
									: this.props.dataMore.textarea
									? apivm.h("textarea", {
											id: "alert_input",
											style:
												G.loadConfiguration(1) +
												"height: " +
												(this.props.dataMore.height || "150") +
												"px;",
											class: "alert_textarea",
											placeholder: this.props.dataMore.placeholder,
											"placeholder-style": "color:#999;",
											value: this.data.search.input,
											"confirm-type": "return",
											onInput: this.inputIng
									  })
									: apivm.h(
											"text",
											{class: "alert_content", style: "" + G.loadConfiguration(1)},
											this.props.dataMore.content
									  )
							),
							apivm.h(
								"view",
								{style: "width:100%;height:1px;padding:0 15px;"},
								apivm.h("a-divider", null)
							),
							apivm.h(
								"view",
								{class: "alert_btn_box"},
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.closePage();
										},
										class: "alert_btn_item",
										style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}
									},
									apivm.h(
										"text",
										{
											style:
												G.loadConfiguration(1) +
												"color:" +
												(this.props.dataMore.cancel.color == "appTheme"
													? G.appTheme
													: this.props.dataMore.cancel.color) +
												";"
										},
										this.props.dataMore.cancel.text
									)
								),
								apivm.h(
									"view",
									{style: {display: this.props.dataMore.cancel.show ? "flex" : "none"}},
									apivm.h("view", {style: "width:1px;height:30px;background:#F6F6F6;"})
								),
								apivm.h(
									"view",
									{
										onClick: this.itemClick,
										class: "alert_btn_item",
										style: {display: this.props.dataMore.sure.show ? "flex" : "none"}
									},
									apivm.h(
										"text",
										{
											style:
												G.loadConfiguration(1) +
												"color:" +
												(this.props.dataMore.sure.color == "appTheme"
													? G.appTheme
													: this.props.dataMore.sure.color) +
												";"
										},
										this.props.dataMore.sure.text
									)
								)
							)
					  )
					: null
			);
		};

		return ZAlert;
	})(Component);
	ZAlert.css = {
		".alert_box": {
			position: "absolute",
			zIndex: "1001",
			left: "0px",
			top: "0px",
			width: "100%",
			height: "100%",
			background: "rgba(0,0,0,0.4)",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_warp": {background: "#FFF", borderRadius: "10px", width: "320px"},
		".alert_content_box": {margin: "30px 15px", maxHeight: "350px"},
		".alert_title": {
			color: "#333333",
			fontWeight: "bold",
			padding: "20px 20px 0",
			textAlign: "center"
		},
		".alert_content": {
			width: "100%",
			textAlign: "center",
			color: "#333333",
			wordWrap: "break-word"
		},
		".alert_btn_box": {flexDirection: "row", alignItems: "center"},
		".alert_btn_item": {
			flex: "1",
			padding: "10px",
			alignItems: "center",
			justifyContent: "center"
		},
		".alert_textarea": {
			borderColor: "#eee",
			borderRadius: "10px",
			padding: "8px",
			width: "100%"
		},
		".alert_textarea::placeholder": {color: "#999"}
	};
	apivm.define("z-alert", ZAlert);

	var YBasePage = /*@__PURE__*/ (function(Component) {
		function YBasePage(props) {
			Component.call(this, props);
			this.data = {
				show: false,
				showPage: false,
				initFrist: true, //首次初始化
				viewappearFrist: true, //是否首次进入页面
				initFrequency: false,
				pageParam: {},
				pageType: ""
			};
			this.compute = {
				isShow: function() {
					var this$1 = this;

					if (this.props.dataMore) {
						if (this.props.dataMore.show != this.data.show) {
							this.data.show = this.props.dataMore.show;
							if (this.data.show) {
								this.baseInit();
								setTimeout(function() {
									this$1.data.showPage = true;
								}, 80);
								console.log("base-page-param：" + JSON.stringify(this.data.pageParam));
							} else {
								this.data.showPage = false;
								if (this.data.pageParam.paramSaveKey) {
									T.removePrefs(this.data.pageParam.paramSaveKey);
								}
								this.fire("baseclose", null);
							}
						}
						//监听刷新事件
						if (this.props.dataMore.pageRefresh == 1) {
							this.props.dataMore.pageRefresh = 0;
							this.pageRefresh();
						}
					}
				}
			};
		}

		if (Component) YBasePage.__proto__ = Component;
		YBasePage.prototype = Object.create(Component && Component.prototype);
		YBasePage.prototype.constructor = YBasePage;
		YBasePage.prototype.installed = function() {
			var this$1 = this;
			if (!this.props.dataMore) {
				this.baseInit();
				console.log("base-page-param：" + JSON.stringify(this.data.pageParam)); //没有dataMore 才是新页面
				// T.addEventListener('viewappear', (ret, err)=> {
				// 	// console.log("我收到了返回事件"+JSON.stringify(T.pageParam(this)));
				// 	if(this.viewappearFrist){
				// 		this.viewappearFrist = false;
				// 		return;
				// 	}
				// 	this.pageRefresh();
				// });
				T.addEventListener("updatePage", function(ret, err) {
					this$1.update();
				});
			}
		};
		YBasePage.prototype.pageRefresh = function() {
			var this$1 = this;
			setTimeout(function() {
				this$1.fire("pageRefresh");
			}, 0);
		};
		YBasePage.prototype.baseInit = function() {
			var this$1 = this;

			//组件内 刚开始会调用多次 这里判断一下 300ms只返回一次
			if (this.data.initFrequency) {
				return;
			}
			this.data.initFrequency = true;
			setTimeout(function() {
				this$1.data.initFrequency = false;
			}, 300);
			this.data.pageParam = T.pageParam(this.props._this);
			if (this.data.pageParam.token) {
				T.setPrefs("sys_token", decodeURIComponent(this.data.pageParam.token));
				if (!T.getPrefs("sys_Mobile")) {
					getLoginInfo({header: {"u-login-areaId": ""}}, function(ret, err) {});
				}
			}
			if (this.data.pageParam.areaId) {
				T.setPrefs("sys_aresId", this.data.pageParam.areaId);
			}
			this.init();
			setTimeout(
				function() {
					this$1.fire("init", {first: this$1.data.initFrist});
					this$1.data.initFrist = false;
				},
				T.systemType() == "android" && !this.props.dataMore ? 300 : 0
			);
		};
		YBasePage.prototype.init = function() {
			this.data.pageType = this.data.pageParam.pageType || "page";
		};
		YBasePage.prototype.close = function() {
			var this$1 = this;

			setTimeout(function() {
				this$1.fire("close", null);
			}, 0);
		};
		YBasePage.prototype.penetrate = function() {};
		YBasePage.prototype.alertCallback = function(_type) {
			if (T.isNumber(_type)) {
				T.sendEvent("base_alert_callback", {buttonIndex: _type});
			} else {
				_type.detail.buttonIndex = 1;
				T.sendEvent("base_alert_callback", _type.detail);
			}
		};
		YBasePage.prototype.areaCallback = function(e) {
			T.sendEvent("base_areas_callback", e.detail);
		};
		YBasePage.prototype.actionSheetCallback = function(e) {
			T.sendEvent("base_actionSheet_callback", e.detail);
		};
		YBasePage.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					s: this.isShow,
					class: G.htmlClass + " base_page_warp",
					style:
						G.htmlStyle +
						";display:" +
						(this.props.dataMore ? (this.data.showPage ? "flex" : "none") : "flex") +
						";background:" +
						(this.props.dataMore && this.props.dataMore.type == "half"
							? "rgba(0,0,0,0.4)"
							: "#FFF") +
						";"
				},
				G.appTheme && [
					this.props.dataMore &&
						this.props.dataMore.type == "half" &&
						apivm.h("view", {
							onClick: function() {
								return this$1.close();
							},
							style:
								"height:" +
								(this.props.dataMore.boxAuto
									? "1px;flex:1"
									: (this.props.shadowH || "14%") + ";flex-shrink: 0;")
						}),
					apivm.h(
						"view",
						{
							onClick: function() {
								return this$1.penetrate();
							},
							style:
								"background:" +
								(this.props.bg || "#FFF") +
								";height:" +
								(this.props.dataMore && this.props.dataMore.boxAuto
									? "auto;max-height:90%;"
									: "1px;flex:1") +
								";border-radius: " +
								(this.props.dataMore && this.props.dataMore.type == "half"
									? "10px 10px"
									: "0 0") +
								" 0 0;"
						},
						apivm.h(
							"view",
							{style: "flex-shrink: 0;"},
							this.props.dataMore &&
								this.props.dataMore.type == "half" &&
								!this.props.closeH && [
									apivm.h(
										"view",
										{class: "base_page_header_warp", style: "height: 49px;"},
										apivm.h(
											"view",
											{
												class: "base_page_header_main",
												style: this.props.titleStyle || "padding: 0 44px;"
											},
											this.props.titleBox
												? [this.props.children.length >= 3 ? this.props.children[2] : null]
												: [
														apivm.h(
															"text",
															{
																style: G.loadConfiguration(1) + "color:#333",
																class: "base_page_header_main_text"
															},
															G.showTextSize(this.props.title, 8, 1)
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "base_page_header_left_box", style: "height:49px;"},
											this.props.back
												? [this.props.children.length >= 1 ? this.props.children[0] : null]
												: [
														apivm.h(
															"view",
															{
																onClick: function() {
																	return this$1.close();
																},
																class: "base_page_header_btn"
															},
															apivm.h(
																"text",
																{style: G.loadConfiguration(1) + "color:#666;margin:0 4px;"},
																"取消"
															)
														)
												  ]
										),
										apivm.h(
											"view",
											{class: "base_page_header_right_box", style: "height:49px;"},
											this.props.more
												? [this.props.children.length >= 2 ? this.props.children[1] : null]
												: []
										)
									),
									apivm.h(
										"view",
										{style: "width:100%;height:1px;padding:0 16px;"},
										apivm.h("a-divider", null)
									)
								]
						),
						apivm.h(
							"view",
							{
								style:
									"width:100%;height:" +
									(this.props.dataMore && this.props.dataMore.boxAuto
										? "auto"
										: "1px;flex:1") +
									";" +
									(T.platform() != "app" ? "overflow-y: scroll;" : "")
							},

							apivm.h(
								"view",
								null,
								((this.props.dataMore &&
									(this.props.dataMore.type == "full" ||
										this.data.pageParam.pageType == "home")) ||
									!this.props.dataMore) &&
									(this.props.titleBox || this.props.back || this.props.more
										? true
										: G.showHeader(this.props._this) && !this.props.closeH) &&
									apivm.h(
										"view",
										{
											style:
												"width:100%;height:auto;padding-top:" +
												G.headerTop() +
												"px;background:" +
												((this.props._this &&
												this.props._this.data &&
												this.props._this.data.headTheme
													? this.props._this.data.headTheme || ""
													: "") || G.headTheme)
										},
										apivm.h(
											"view",
											{class: "base_page_header_warp", style: "height: 44px;"},
											apivm.h(
												"view",
												{
													class: "base_page_header_main",
													style: this.props.titleStyle || "padding: 0 44px;"
												},
												this.props.titleBox
													? [this.props.children.length >= 3 ? this.props.children[2] : null]
													: [
															apivm.h(
																"text",
																{
																	style:
																		G.loadConfiguration(4) +
																		"color:" +
																		G.getHeadThemeRelatively(this.props._this),
																	class: "base_page_header_main_text"
																},
																G.showTextSize(this.props.title, 8, 1)
															)
													  ]
											),
											apivm.h(
												"view",
												{class: "base_page_header_left_box", style: "height:44px;"},
												apivm.h(
													"view",
													{style: "height: 44px;"},
													this.props.back
														? [
																this.props.children.length >= 1 ? this.props.children[0] : null
														  ]
														: [
																apivm.h(
																	"view",
																	{
																		onClick: function() {
																			return this$1.close();
																		},
																		class: "base_page_header_btn",
																		style: {
																			display: (this.props.dataMore &&
																			this.props.dataMore.type == "full"
																			? true
																			: G.showHeader(this.props._this) &&
																			  this.data.pageType == "page")
																				? "flex"
																				: "none"
																		}
																	},
																	apivm.h("a-iconfont", {
																		name: "fanhui1",
																		color: G.getHeadThemeRelatively(this.props._this),
																		size: G.appFontSize + 1
																	})
																)
														  ]
												)
											),
											apivm.h(
												"view",
												{class: "base_page_header_right_box", style: "height:44px;"},
												this.props.more
													? [this.props.children.length >= 2 ? this.props.children[1] : null]
													: []
											)
										)
									)
							),
							apivm.h(
								"view",
								{
									style:
										"width:100%;height:" +
										(this.props.dataMore && this.props.dataMore.boxAuto
											? "auto"
											: "1px;flex:1") +
										";"
								},
								this.props.children.length >= 4 ? this.props.children[3] : null
							)
						)
					),
					this.props.children.length >= 5
						? this.props.children.filter(function(item, index) {
								return index >= 4;
						  })
						: null,
					!this.props.dataMore && [
						apivm.h(
							"view",
							{
								class: "suspend_box",
								style: "display:" + (G.imagePreviewer.show ? "flex" : "none") + ";"
							},
							G.imagePreviewer.show &&
								apivm.h("image-previewer", {
									imgs: G.imagePreviewer.imgs,
									activeIndex: G.imagePreviewer.activeIndex,
									type: G.imagePreviewer.type
								})
						),
						apivm.h("mo-areas", {
							dataMore: G.areasBox,
							pageParam: G.areasBox.pageParam,
							onChange: this.areaCallback
						}),
						apivm.h("z-actionSheet", {
							dataMore: G.actionSheetBox,
							data: G.actionSheetBox.data,
							active: G.actionSheetBox.active,
							onClick: this.actionSheetCallback
						}),
						apivm.h("z-alert", {
							dataMore: G.alertBox,
							onClick: this.alertCallback,
							onCancel: function() {
								return this$1.alertCallback(2);
							}
						})
					]
				]
			);
		};

		return YBasePage;
	})(Component);
	YBasePage.css = {
		div: {flexShrink: "0", WebkitOverflowScrolling: "touch"},
		".base_page_warp": {
			width: "100%",
			height: "100%",
			position: "absolute",
			zIndex: "999",
			top: "0",
			left: "0",
			right: "0",
			bottom: "0",
			background: "rgba(0,0,0,0.4)"
		},
		".base_page_header_warp": {
			flexDirection: "row",
			width: "100%",
			alignItems: "center",
			flexShrink: "0"
		},
		".base_page_header_main": {
			width: "1px",
			height: "100%",
			flex: "1",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".base_page_header_main_text": {fontWeight: "600", flexShrink: "0"},
		".base_page_header_btn": {
			width: "auto",
			height: "100%",
			minWidth: "36px",
			padding: "0 12px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".base_page_header_btn:active": {background: "rgba(0,0,0,0.05)"},
		".base_page_header_left_box": {
			position: "absolute",
			zIndex: "999",
			left: "0",
			width: "auto",
			height: "44px",
			flexDirection: "row",
			flexShrink: "0"
		},
		".base_page_header_right_box": {
			position: "absolute",
			zIndex: "999",
			right: "0",
			width: "auto",
			height: "44px",
			flexDirection: "row-reverse",
			flexShrink: "0"
		},
		".filterNone": {filter: "none"},
		".filterGray": {filter: "grayscale(1)"},
		".avm-toast": {zIndex: "999"},
		".avm-confirm-mask": {zIndex: "999"},
		".suspend_box": {
			position: "absolute",
			zIndex: "999",
			left: "0px",
			top: "0px",
			width: "100%",
			height: "100%",
			background: "rgba(0,0,0,0.6)"
		}
	};
	apivm.define("y-base-page", YBasePage);

	var MoDetailsShare = /*@__PURE__*/ (function(Component) {
		function MoDetailsShare(props) {
			Component.call(this, props);
			this.data = {
				G: G,
				pageParam: {},
				pageType: "",
				title: "",

				share: [
					{
						show: false,
						key: "platform",
						value: "平台好友",
						type: "img",
						src: myjs.appUrl() + "pageImg/open/logo"
					},
					{
						show: false,
						key: "wxfriends",
						value: "微信好友",
						type: "icon",
						bg: "#50C614",
						color: "#FFF",
						src: "changyonglogo28",
						size: 16
					},
					{
						show: false,
						key: "wxcircle",
						value: "朋友圈",
						type: "icon",
						bg: "#50C614",
						color: "#FFF",
						src: "pengyouquan",
						size: 13
					},
					{
						show: false,
						key: "qqfriends",
						value: "QQ",
						type: "icon",
						bg: "#3AA2F3",
						color: "#FFF",
						src: "QQ",
						size: 13
					},
					{
						show: true,
						key: "qrcode",
						value: "分享海报",
						type: "icon",
						bg: "",
						color: "",
						src: "tupian",
						size: 13
					},
					{
						show: true,
						key: "link",
						value: "复制链接",
						type: "icon",
						bg: "",
						color: "",
						src: "lianjie",
						size: 13
					}
				],

				code: "",
				shareParam: "",
				shareUrl: "",

				sharePoster: {
					//分享海报弹框
					show: false,
					content: {
						title: "",
						content: "",
						source: "",
						time: "",
						shareUrl: "",
						code: "",
						beginTime: ""
					},

					data: [
						{
							show: false,
							key: "platform",
							value: "平台好友",
							type: "img",
							src: myjs.appUrl() + "pageImg/open/logo"
						},
						{
							show: false,
							key: "wxfriends",
							value: "微信好友",
							type: "icon",
							bg: "#50C614",
							color: "#FFF",
							src: "changyonglogo28",
							size: 16
						},
						{
							show: false,
							key: "wxcircle",
							value: "朋友圈",
							type: "icon",
							bg: "#50C614",
							color: "#FFF",
							src: "pengyouquan",
							size: 13
						},
						{
							show: false,
							key: "qqfriends",
							value: "QQ",
							type: "icon",
							bg: "#3AA2F3",
							color: "#FFF",
							src: "QQ",
							size: 13
						},
						{
							show: true,
							key: "save",
							value: "保存图片",
							type: "icon",
							bg: "",
							color: "",
							src: "zhixiangxia",
							size: 13
						}
					]
				}
			};
		}

		if (Component) MoDetailsShare.__proto__ = Component;
		MoDetailsShare.prototype = Object.create(Component && Component.prototype);
		MoDetailsShare.prototype.constructor = MoDetailsShare;
		MoDetailsShare.prototype.onShow = function() {
			G.onShow(this);
		};
		MoDetailsShare.prototype.installed = function() {};
		MoDetailsShare.prototype.baseInit = function(ref) {
			var detail = ref.detail;

			this.data.pageParam = T.pageParam(this);
			G.installed(this);
		};
		MoDetailsShare.prototype.init = function() {
			this.data.pageType = this.data.pageParam.pageType || "page";
			this.data.title = this.data.pageParam.title || "分享";
			this.data.shareParam = this.data.pageParam.shareParam || "";
			this.data.code = this.data.pageParam.code || "";
			this.getData();
			this.data.sharePoster.show = false;
		};
		MoDetailsShare.prototype.close = function() {
			if (this.props.dataMore) {
				this.props.dataMore.show = false;
			} else {
				T.closeWin();
			}
		};
		MoDetailsShare.prototype.getData = function() {
			var this$1 = this;

			this.data.shareUrl = "";
			if (((this.data.shareParam || {}).p || {}).id && this.data.code != "34") {
				if (
					this.data.code == "38" ||
					this.data.code == "45" ||
					(this.data.code == "39" && !G.userId)
				) {
					G.getItemForKey("qrcode", this.data.share).show = false;
					return;
				} //政协提案线索征集、联系点公众、政协书院 无海报 关闭
				G.getItemForKey("qrcode", this.data.share).show = true;
				getModuleDetails(
					{id: this.data.shareParam.p.id, code: this.data.code},
					this,
					function(ret, err) {
						if (err == "未携带code") {
							T.toast("未携带code");
							return;
						}
						var data = ret && ret.code == 200 ? ret.data || {} : {};
						this$1.data.sharePoster.content.title =
							data.title || data.infoTitle || "";
						this$1.data.sharePoster.content.content = T.removeTag(
							data.content || data.infoContent || data.opinionContent || ""
						);
						// this.sharePoster.content.beginTime=data.beginTime || ""
						this$1.data.sharePoster.content.time =
							data.publishDate || data.pubTime || data.endTime || "";
						this$1.data.sharePoster.content.source =
							data.officeName || data.infoSource || "";
						console.log(this$1.data.sharePoster.content, "this.sharePoster.content");
					}
				);
				T.ajax(
					{
						u: myjs.appUrl() + "config/openRead",
						_this: this,
						areaId: myjs.areaId(this)
					},
					"config/openRead",
					function(ret, err) {
						var data = ret ? ret.data || "" : "";
						if (data) {
							this$1.data.sharePoster.content.appName = data.systemName || "";
						}
					},
					"取app配置",
					"post",
					{
						body: JSON.stringify({codes: ["systemName"]})
					},
					{Authorization: ""}
				);
			} else if (this.data.code == "34") {
				this.data.sharePoster.content.title =
					this.data.pageParam.moudule.title || "";
				this.data.sharePoster.content.beginTime =
					this.data.pageParam.moudule.beginTime || "";
				this.data.sharePoster.content.time = this.data.pageParam.moudule.time || "";
				this.data.sharePoster.content.code = this.data.pageParam.code || "";
			}
		};
		MoDetailsShare.prototype.shareBtn = function(_item) {
			var this$1 = this;

			if (!this.data.shareUrl) {
				T.showProgress("操作中");
				if (!T.isObject(this.data.shareParam.p)) {
					this.data.shareParam.p = {};
				}
				console.log(JSON.stringify(this.data.shareParam));
				// this.shareParam.p.token = ret.data;
				this.data.shareUrl =
					myjs.shareAddress() +
					"pages/index/?" +
					JSON.stringify(this.data.shareParam)
						.replace(/\{/g, "%7B")
						.replace(/\}/g, "%7D")
						.replace(/\"/g, "%22");
				console.log(this.data.shareUrl, "this.shareUrl");
				T.ajax(
					{u: myjs.appUrl() + "longShortLink/exchange", _this: this},
					"longShortLink",
					function(ret, err) {
						T.hideProgress();
						var data = ret ? ret.data || "" : "";
						this$1.data.shareUrl = data
							? myjs.appUrl() + "viewing/" + data
							: this$1.data.shareUrl;
						console.log(this$1.data.shareUrl, "this.shareUrl");
						this$1.shareBtn(_item);
					},
					"转短链接",
					"post",
					{
						values: {
							longUrl: this.data.shareUrl
						}
					},

					{
						"content-type": "application/x-www-form-urlencoded"
					}
				);

				// var timestamp = dayjs().valueOf();
				// T.ajax({ u: M.appUrl() + 'login/api_token', _this:this }, 'login/api_token', (ret, err)=> {
				// 	if(ret&&ret.code==200){

				// 	}else{
				// 		T.hideProgress();
				// 		T.toast(ret?ret.message:T.NET_ERR);
				// 	}
				// }, "获取token", "post", {
				// 	values: {
				// 		clientId:M.clientId,
				// 		timestamp:timestamp,
				// 		sign:MD5(timestamp + M.clientId + M.clientSecret).toString().toUpperCase()
				// 	}
				// },{
				// 	"content-type":"application/x-www-form-urlencoded"
				// });
				return;
			}
			switch (_item.key) {
				case "qrcode":
					this.data.sharePoster.content.shareUrl = this.data.shareUrl || "";
					this.data.sharePoster.show = true;
					break;
				case "link":
					var copyLink = this.data.shareUrl;
					copyText(copyLink, function(ret, err) {
						T.toast(ret ? "复制成功" : "复制失败");
					});
					break;
				case "save":
					this.close();
					break;
				default:
					T.toast("分享到" + _item.value);
					break;
			}
		};
		MoDetailsShare.prototype.keyCallback = function(ref) {
			var detail = ref.detail;

			this.shareBtn(detail);
		};
		MoDetailsShare.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"y-base-page",
				{
					_this: this,
					closeH: true,
					dataMore: this.props.dataMore,
					pageParam: this.props.pageParam,
					onInit: this.baseInit,
					onClose: this.close,
					pageRefresh: ""
				},
				apivm.h("view", {style: "width:auto;height:100%;"}),
				apivm.h("view", {style: "width:auto;height:100%;"}),
				apivm.h("view", null),
				apivm.h(
					"scroll-view",
					{
						"scroll-y": true,
						"scroll-x": false,
						style:
							"overflow-x: hidden;width:100%;height:" +
							(this.props.dataMore && this.props.dataMore.boxAuto
								? "auto"
								: "1px;flex:1") +
							";"
					},
					apivm.h(
						"text",
						{
							style:
								this.data.G.loadConfiguration(1) +
								"color:#333;font-weight: 600;padding:20px 16px;"
						},
						"分享到"
					),
					apivm.h(
						"view",
						{class: "share_btn_box"},
						this.data.share.map(function(item, index, list) {
							return (
								(T.isParameters(item.show) ? item.show : true) &&
								apivm.h(
									"view",
									{
										onClick: function() {
											return this$1.shareBtn(item);
										},
										class: "share_btn_item"
									},
									item.type == "img"
										? apivm.h("image", {
												style: "" + this$1.data.G.loadConfigurationSize(34),
												class: "share_btn_icon",
												src: item.src
										  })
										: apivm.h(
												"view",
												{
													style:
														this$1.data.G.loadConfigurationSize(34) +
														"background:" +
														(item.bg || "#F4F5F7") +
														";",
													class: "share_btn_icon"
												},
												apivm.h("a-iconfont", {
													style: "font-weight:" + (item.weight || "400"),
													name: item.src,
													color: item.color || "#333",
													size:
														this$1.data.G.appFontSize +
														(T.isParameters(item.size) ? Number(item.size) : 8)
												})
										  ),
									apivm.h(
										"text",
										{
											style:
												this$1.data.G.loadConfiguration(-2) + "color:#333;margin-top:8px;"
										},
										item.value
									)
								)
							);
						})
					),
					apivm.h(
						"view",
						null,
						!this.props.dataMore.dotCancel &&
							apivm.h(
								"view",
								{
									onClick: this.close,
									style:
										"padding:18px;justify-content:center;border-top:10px solid #F8F8F8;background: #FFF; "
								},
								apivm.h(
									"text",
									{
										style:
											this.data.G.loadConfiguration() + ";color:#333;text-align: center;"
									},
									"取消"
								)
							)
					),
					apivm.h("view", {
						style: "background:#fff;padding-bottom:" + T.safeArea().bottom + "px;"
					})
				),

				apivm.h("y-share-poster", {
					id: "shareposter",
					dataMore: this.data.sharePoster,
					data: this.data.sharePoster.data,
					onClick: this.keyCallback
				})
			);
		};

		return MoDetailsShare;
	})(Component);
	MoDetailsShare.css = {
		".share_btn_box": {padding: "10px 0", flexDirection: "row", flexWrap: "wrap"},
		".share_btn_item": {
			padding: "10px 5px",
			width: "25%",
			alignItems: "center",
			justifyContent: "center"
		},
		".share_btn_icon": {
			borderRadius: "50%",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("mo-details-share", MoDetailsShare);

	var ZButton = /*@__PURE__*/ (function(Component) {
		function ZButton(props) {
			Component.call(this, props);
		}

		if (Component) ZButton.__proto__ = Component;
		ZButton.prototype = Object.create(Component && Component.prototype);
		ZButton.prototype.constructor = ZButton;
		ZButton.prototype.zButtonClick = function(e, _props) {
			var this$1 = this;

			if (!this.props.disabled) {
				setTimeout(function() {
					this$1.fire("click", {});
				}, 0);
			}
			if (!this.props.bubble) {
				G.stopBubble(e);
			}
		};
		ZButton.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				{
					class: "z-button",
					style:
						"\n\t\tborder-top-left-radius: " +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;\n\t\tborder-top-right-radius: " +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;\n\t\tborder-bottom-right-radius: " +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;\n\t\tborder-bottom-left-radius: " +
						(this.props.round ? "999" : this.props.roundSize || "4") +
						"px;\n\t\tborder-color:" +
						this.props.color +
						";\n\t\tbackground:" +
						(this.props.plain ? "#FFF" : this.props.color) +
						";\n\t\topacity:" +
						(this.props.disabled ? "0.5" : "1") +
						";\n\t\t" +
						this.props.style,
					onClick: function(e) {
						return this$1.zButtonClick(e, this$1.props);
					}
				},
				apivm.h(
					"text",
					{
						style:
							"\n\t\tcolor:" +
							(this.props.plain ? this.props.color : "#FFF") +
							";\n\t\t" +
							G.loadConfiguration((this.props.size || 16) - 16) +
							"\n\t"
					},
					this.props.text
				)
			);
		};

		return ZButton;
	})(Component);
	ZButton.css = {
		".z-button": {
			padding: "8.3px 12px",
			textAlign: "center",
			borderWidth: "1px",
			borderStyle: "solid",
			boxSizing: "border-box",
			display: "flex",
			alignItems: "center",
			justifyContent: "center"
		}
	};
	apivm.define("z-button", ZButton);

	var ZRadioInput = /*@__PURE__*/ (function(Component) {
		function ZRadioInput(props) {
			Component.call(this, props);
			this.data = {};
		}

		if (Component) ZRadioInput.__proto__ = Component;
		ZRadioInput.prototype = Object.create(Component && Component.prototype);
		ZRadioInput.prototype.constructor = ZRadioInput;
		ZRadioInput.prototype.apiready = function() {};
		ZRadioInput.prototype.isRadioTrue = function(i, arr) {
			return arr.indexOf(i) != -1 ? "danxuan_xuanzhong" : "danxuan_weixuanzhong";
		};
		ZRadioInput.prototype.changeSelect = function(index, i, studyTopicFormName) {
			if (this.props.isUpdata) {
				return false;
			}
			if (studyTopicFormName == "单选") {
				this.props.data[index].check = [i];
				// console.log(this.props.data[index].check,'this.props.data[index].check')
			} else {
				this.props.data[index].check.indexOf(i) == -1
					? this.props.data[index].check.push(i)
					: (this.props.data[index].check = this.props.data[index].check.filter(
							function(item) {
								return item != i;
							}
					  ));
			}
		};
		ZRadioInput.prototype.inputIng = function(e, index) {
			this.props.data[index].answer = e.detail.value;
			console.log(this.props.data[index].answer, "this.props.data[index].answer");
		};
		ZRadioInput.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"view",
				null,
				(Array.isArray(this.props.data)
					? this.props.data
					: Object.values(this.props.data)
				).map(function(item$1, index$1) {
					return apivm.h(
						"view",
						null,
						item$1.studyTopicFormName == "单选"
							? apivm.h(
									"view",
									null,
									apivm.h(
										"view",
										{class: this$1.props.isH5 == "true" ? "qu_box" : ""},
										apivm.h(
											"view",
											{
												style:
													"display: flex;flex-direction:row;flex-wrap:wrap;margin-bottom:10px;line-height:30px;"
											},
											item$1.isNeed == "1"
												? apivm.h(
														"span",
														{
															style:
																"position: absolute;color: #F61C1C;left:-8px;" +
																G.loadConfiguration(1)
														},
														"*"
												  )
												: [],
											apivm.h(
												"text",
												{style: G.loadConfiguration(1) + "font-weight:600;"},
												index$1 +
													1 +
													"." +
													item$1.title +
													"[" +
													item$1.studyTopicFormName +
													"]"
											)
										),
										(Array.isArray(item$1.options)
											? item$1.options
											: Object.values(item$1.options)
										).map(function(op, i) {
											return apivm.h(
												"view",
												{
													style:
														"display:flex;align-items:center;flex-direction:row;margin-bottom:15px;",
													onClick: function() {
														return this$1.changeSelect(index$1, i, item$1.studyTopicFormName);
													}
												},
												apivm.h("a-iconfont", {
													name: this$1.isRadioTrue(i, item$1.check),
													color: item$1.check.indexOf(i) != -1 ? this$1.props.color : "",
													size: G.appFontSize - 2
												}),
												apivm.h(
													"text",
													{
														class: "option_text",
														style:
															item$1.check.indexOf(i) != -1
																? "color:" + this$1.props.color + ";"
																: "color: #000000;"
													},
													op.optionTitle
												)
											);
										})
									)
							  )
							: item$1.studyTopicFormName == "多选"
							? apivm.h(
									"view",
									null,
									apivm.h(
										"view",
										{class: this$1.props.isH5 == "true" ? "qu_box" : ""},
										apivm.h(
											"view",
											{
												style:
													"display: flex;flex-direction:row;flex-wrap:wrap;margin-bottom:10px;line-height:30px;"
											},
											item$1.isNeed == "1"
												? apivm.h(
														"span",
														{
															style:
																"position: absolute;color: #F61C1C;left:-8px;" +
																G.loadConfiguration(1)
														},
														"*"
												  )
												: [],
											apivm.h(
												"text",
												{style: G.loadConfiguration(1) + "font-weight:600;"},
												index$1 +
													1 +
													"." +
													item$1.title +
													"[" +
													item$1.studyTopicFormName +
													"]"
											)
										),
										(Array.isArray(item$1.options)
											? item$1.options
											: Object.values(item$1.options)
										).map(function(op, i) {
											return apivm.h(
												"view",
												{
													style:
														"display:flex;align-items:center;flex-direction:row;margin-bottom:15px;",
													onClick: function() {
														return this$1.changeSelect(index$1, i, item$1.studyTopicFormName);
													}
												},
												apivm.h("a-iconfont", {
													name: this$1.isRadioTrue(i, item$1.check),
													color: item$1.check.indexOf(i) != -1 ? this$1.props.color : "",
													size: G.appFontSize - 2
												}),
												apivm.h(
													"text",
													{
														class: "option_text",
														style:
															item$1.check.indexOf(i) != -1
																? "color:" + this$1.props.color + ";"
																: "color: #000000;"
													},
													op.optionTitle
												)
											);
										})
									)
							  )
							: item$1.studyTopicFormName == "问答"
							? apivm.h(
									"view",
									null,
									apivm.h(
										"view",
										{class: this$1.props.isH5 == "true" ? "qu_box" : ""},
										item$1.isNeed == "1"
											? apivm.h(
													"span",
													{
														style:
															"position: absolute;color: #F61C1C;left:" +
															(this$1.props.isH5 == "true" ? "13px" : "-8px") +
															";" +
															G.loadConfiguration(1)
													},
													"*"
											  )
											: [],
										apivm.h(
											"text",
											{
												style:
													G.loadConfiguration(1) +
													"font-weight:600;margin-bottom: 10px;line-height:30px;"
											},
											index$1 + 1 + "." + item$1.title
										),
										apivm.h(
											"view",
											{style: "min-height:126px;"},
											apivm.h("textarea", {
												class: "input",
												onInput: function(e) {
													if (typeof item$1 != "undefined") {
														item$1.answer = e.target.value;
													} else {
														this$1.data.item$1.answer = e.target.value;
													}
													(function() {
														return this$1.inputIng(e, index$1);
													})();
												},
												placeholder: "请输入",
												"auto-height": true,
												maxlength: "800",
												disabled: this$1.props.isUpdata ? true : false,
												value:
													typeof item$1 == "undefined"
														? this$1.data.item$1.answer
														: item$1.answer
											})
										)
									)
							  )
							: []
					);
				})
			);
		};

		return ZRadioInput;
	})(Component);
	ZRadioInput.css = {
		".qu_box": {
			background: "#FFFFFF",
			borderRadius: "4px",
			padding: "15px 20px 0",
			marginBottom: "10px",
			position: "relative",
			top: "0"
		},
		".option_text": {marginLeft: "10px", fontSize: "17px", lineHeight: "30px"},
		".input": {
			width: "100%",
			marginBottom: "15px",
			minHeight: "116px",
			borderRadius: "4px",
			border: "1px solid lightgrey",
			padding: "10px"
		},
		"input[type=text]::placeholder": {color: "#CCCCCC"},
		".page": {height: "100%"}
	};
	apivm.define("z-radio-input", ZRadioInput);

	var ASkeleton = /*@__PURE__*/ (function(Component) {
		function ASkeleton(props) {
			Component.call(this, props);
			this.data = {};
			this.compute = {
				rows: function() {
					var row = this.props.row || 0;
					return Array.from({length: row}).fill("");
				},
				length: function() {},
				avatarClass: function() {
					return (
						"a-skeleton_avatar " +
						(this.props["avatar-shape"] == "square" ? "" : "a-skeleton_round")
					);
				},
				avatarStyle: function() {
					var size = this.props["avatar-size"];
					return size ? "width:" + size + ";height:" + size + ";" : "";
				},
				titleStyle: function() {
					var titleWidth = this.props["title-width"];
					return titleWidth ? "width:" + titleWidth + ";" : "";
				}
			};
		}

		if (Component) ASkeleton.__proto__ = Component;
		ASkeleton.prototype = Object.create(Component && Component.prototype);
		ASkeleton.prototype.constructor = ASkeleton;
		ASkeleton.prototype.beforeRender = function() {
			if (!("loading" in this.props)) {
				this.props.loading = true;
			}
		};
		ASkeleton.prototype.getChildNode = function() {
			return this.props.children.length > 0 ? this.props.children[0] : null;
		};
		ASkeleton.prototype.getRowStyle = function(index) {
			return "width:" + this.getRowWidth(index) + ";";
		};
		ASkeleton.prototype.getRowWidth = function(index) {
			var rowWidth = this.props["row-width"] || "100%";

			if (rowWidth === "100%" && index === this.props.row - 1) {
				return "60%";
			}

			if (Array.isArray(rowWidth)) {
				return rowWidth[index];
			}

			return rowWidth;
		};
		ASkeleton.prototype.render = function() {
			var this$1 = this;
			return (
				(this.props.loading &&
					apivm.h(
						"view",
						{class: "a-skeleton"},
						this.props.avatar &&
							apivm.h("view", {class: this.avatarClass, style: this.avatarStyle}),
						apivm.h(
							"view",
							{style: "flex:1"},
							this.props.title &&
								apivm.h(
									"text",
									{class: "a-skeleton_title", style: this.titleStyle},
									this.props.title
								),
							this.props.row &&
								apivm.h(
									"view",
									null,
									(Array.isArray(this.rows) ? this.rows : Object.values(this.rows)).map(
										function(item$1, index$1) {
											return apivm.h("view", {
												class: "a-skeleton_row",
												style: this$1.getRowStyle(index$1)
											});
										}
									)
								)
						)
					)) ||
				this.getChildNode()
			);
		};

		return ASkeleton;
	})(Component);
	ASkeleton.css = {
		".a-skeleton": {width: "100%", flexDirection: "row", padding: "10px 16px"},
		".a-skeleton_avatar": {
			flexShrink: "0",
			width: "32px",
			height: "32px",
			marginRight: "16px",
			backgroundColor: "#f2f3f5"
		},
		".a-skeleton_round": {borderRadius: "999px"},
		".a-skeleton_title": {
			width: "40%",
			height: "16px",
			backgroundColor: "#f2f3f5"
		},
		".a-skeleton_row": {
			marginTop: "12px",
			width: "40%",
			height: "16px",
			backgroundColor: "#f2f3f5"
		}
	};
	apivm.define("a-skeleton", ASkeleton);

	var YScrollView = /*@__PURE__*/ (function(Component) {
		function YScrollView(props) {
			Component.call(this, props);
			this.data = {
				refreshTriggered: false, //设置当前下拉刷新状态，true 表示下拉刷新已经被触发，false 表示下拉刷新未被触发
				upperThreshold: this.props.upperThreshold || 50, //距顶部/左边多远时，触发 scrolltoupper 事件
				lowerThreshold: this.props.lowerThreshold || 50, //距底部/右边多远时，触发 scrolltolower 事件

				scrollVID: "" //滚动位置
			};
			this.compute = {
				monitor: function() {
					if (this.data.scrollVID != (this.props["scroll-into-view"] || "")) {
						this.data.scrollVID = this.props["scroll-into-view"] || "";
						if (this.data.scrollVID) {
							this.scrollTo(this.data.scrollVID);
						}
					}
				}
			};
		}

		if (Component) YScrollView.__proto__ = Component;
		YScrollView.prototype = Object.create(Component && Component.prototype);
		YScrollView.prototype.constructor = YScrollView;
		YScrollView.prototype.install = function() {};
		YScrollView.prototype.installed = function() {};
		YScrollView.prototype.onscrolltoupper = function(e) {};
		YScrollView.prototype.onscrolltolower = function(e) {
			this.fire("up", {});
		};
		YScrollView.prototype.onscroll = function(ref) {
			var detail = ref.detail;

			this.fire("scroll", detail);
		};
		YScrollView.prototype.onrefresherrefresh = function(e) {
			var this$1 = this;

			this.fire("lower", {});
			this.data.refreshTriggered = true;
			setTimeout(function() {
				this$1.data.refreshTriggered = false;
			}, 150);
		};
		YScrollView.prototype.scrollTo = function(nowView) {
			try {
				if (T.platform() != "mp") {
					var _animated = T.isParameters(this.props["scroll-with-animation"])
						? this.props["scroll-with-animation"]
						: true;
					document
						.getElementById(this.props.id)
						.scrollTo(
							T.platform() == "app"
								? {view: nowView, animated: _animated}
								: {
										top: this.getOffestValue(
											document.getElementById(nowView),
											this.props.id
										).top,
										behavior: _animated ? "smooth" : "instant"
								  }
						);
				}
			} catch (e) {}
		};
		YScrollView.prototype.getOffestValue = function(elem, parentId) {
			var Far = null;
			var topValue = elem && elem.offsetTop;
			var leftValue = elem && elem.offsetLeft;
			var offsetFar = elem && elem.offsetParent;
			while (offsetFar) {
				topValue += offsetFar.offsetTop;
				leftValue += offsetFar.offsetLeft;
				Far = offsetFar;
				offsetFar = offsetFar.offsetParent;
				if (offsetFar.id == parentId) {
					break;
				}
			}
			return {top: topValue, left: leftValue, Far: Far};
		};
		YScrollView.prototype.render = function() {
			return apivm.h(
				"view",
				{a: this.monitor, style: "flex:1;height:1px;" + (this.props.style || "")},
				apivm.h(
					"scroll-view",
					{
						id: "" + this.props.id,
						style: (this.props.style || "") + "background-color:transparent;",
						class: "" + (this.props.class || ""),
						"scroll-x": T.isParameters(this.props["scroll-x"])
							? this.props["scroll-x"]
							: false,
						"scroll-y": T.isParameters(this.props["scroll-y"])
							? this.props["scroll-y"]
							: true,
						bounces: T.isParameters(this.props["bounces"])
							? this.props["bounces"]
							: true,
						"scroll-into-view":
							"" + (T.platform() == "mp" ? this.props["scroll-into-view"] || "" : ""),
						"scroll-with-animation":
							T.platform() == "mp"
								? T.isParameters(this.props["scroll-with-animation"])
									? this.props["scroll-with-animation"]
									: true
								: false,
						"refresher-enabled":
							T.platform() != "web" &&
							(T.isParameters(this.props["refresher-enabled"])
								? this.props["refresher-enabled"]
								: false),
						"refresher-threshold": T.isParameters(this.props["refresher-threshold"])
							? this.props["refresher-threshold"]
							: 65,
						"refresher-background": T.isParameters(this.props["refresher-background"])
							? this.props["refresher-background"]
							: "#FFF",
						"refresher-triggered": this.data.refreshTriggered,
						"upper-threshold": this.upperThreshold,
						"lower-threshold": this.lowerThreshold,
						onScrolltoupper: this.onscrolltoupper,
						onScrolltolower: this.onscrolltolower,
						onRefresherrefresh: this.onrefresherrefresh,
						onScroll: this.onscroll
					},
					apivm.h(
						"view",
						{style: this.props.firsts || ""},
						this.props.children || null
					),
					apivm.h(
						"view",
						null,
						T.isParameters(this.props.data)
							? [
									apivm.h(
										"view",
										{
											class: "y_scroll_box",
											style: {display: this.props.data.skeleton ? "flex" : "none"}
										},
										(Array.isArray([1, 2, 3]) ? [1, 2, 3] : Object.values([1, 2, 3])).map(
											function(item$1, index$1) {
												return apivm.h("a-skeleton", {title: true, row: "3"});
											}
										)
									),
									apivm.h(
										"view",
										{
											class: "y_scroll_box",
											style: {
												display:
													!this.props.data.skeleton &&
													!this.props.data.notList &&
													this.props.data.listLength == 0
														? "flex"
														: "none"
											}
										},
										apivm.h(
											"text",
											{style: G.loadConfiguration(-4) + "color: #CCCCCC;padding:16px;"},
											this.props.data.text || "暂无数据"
										)
									),
									apivm.h(
										"view",
										{
											class: "y_scroll_box",
											onClick: this.onscrolltolower,
											style: {
												display:
													!this.props.data.skeleton &&
													!this.props.data.notList &&
													this.props.data.listLength != 0
														? "flex"
														: "none"
											}
										},
										apivm.h(
											"text",
											{style: G.loadConfiguration(-4) + "color: #CCCCCC;padding:15px;"},
											this.props.data.text
										)
									),
									apivm.h(
										"view",
										{style: {display: !this.props.data.dotFooter ? "flex" : "none"}},
										apivm.h("view", {
											style: "padding-bottom:" + T.safeArea().bottom + "px;"
										})
									)
							  ]
							: null
					)
				)
			);
		};

		return YScrollView;
	})(Component);
	YScrollView.css = {
		".y_scroll_box": {
			alignItems: "center",
			justifyContent: "center",
			width: "100%"
		}
	};
	apivm.define("y-scroll-view", YScrollView);

	var MoSurveyDetails = /*@__PURE__*/ (function(Component) {
		function MoSurveyDetails(props) {
			Component.call(this, props);
			this.data = {
				G: G,
				pageParam: {},
				pageType: "",
				title: "",
				pageNot: {
					skeleton: false,
					notList: true,
					listLength: 0,
					type: "0",
					text: "",
					dotFooter: true
				},
				id: "",
				name: "",
				info: "",
				beginTime: "1687881600000",
				endTime: "1688054400000",
				isShare: null,
				examineId: "",
				tipsTitle: "",
				subText: "",

				pageNo: 1,
				pageSize: 15,
				refreshPageSize: 0,
				pageNot: {
					skeleton: true,
					listLength: 0,
					type: "0",
					text: "",
					dotFooter: true
				},
				listData: [],

				sharePage: {
					//分享弹框
					show: false,
					type: "half", //full全屏打开  half半屏打开
					boxAuto: true,
					pageParam: null
				},

				isUpdata: false,
				examStatus: 0,
				code: "34",
				btnStatus: "1", //1可点击 2不可点击
				paperStatus: "进行中"
			};
		}

		if (Component) MoSurveyDetails.__proto__ = Component;
		MoSurveyDetails.prototype = Object.create(Component && Component.prototype);
		MoSurveyDetails.prototype.constructor = MoSurveyDetails;
		MoSurveyDetails.prototype.onShow = function() {
			G.onShow(this);
		};
		MoSurveyDetails.prototype.installed = function() {};
		MoSurveyDetails.prototype.baseInit = function() {
			this.data.pageParam = T.pageParam(this);
			G.installed(this);
		};
		MoSurveyDetails.prototype.baseclose = function() {};
		MoSurveyDetails.prototype.init = function() {
			this.data.pageType = this.data.pageParam.pageType || "page";
			this.data.id = this.data.pageParam.id || ""; //业务id
			this.data.examineId = this.data.pageParam.examineId || "";
			this.data.paperStatus = this.data.pageParam.paperStatus || "进行中";
			// this.code = this.pageParam.code || "34";//业务code
			this.getData();
		};
		MoSurveyDetails.prototype.close = function() {
			if (this.props.dataMore) {
				this.props.dataMore.show = false;
			} else {
				T.closeWin();
			}
		};
		MoSurveyDetails.prototype.submit = function() {
			var this$1 = this;

			if (this.data.btnStatus == "2") {
				T.toast("请勿重复提交！");
				return;
			}
			this.data.btnStatus = "2"; //点击之后先关上
			var swich = true;
			var paperAnswer = {
				form: {
					id: this.data.examineId
					// terminalName:"APP",
					// paperId:this.id
				},
				answers: []
			};

			var urlDeta = "";
			if (this.data.isUpdata == false && this.data.examStatus == 3) {
				paperAnswer.form.paperId = this.data.id;
				paperAnswer.form.terminalName = "APP";
				urlDeta = "studyexamine/submitquestion";
			} else {
				urlDeta = "studyexamine/submit";
			}
			console.log(this.data.listData, "this.listData");
			try {
				this.data.listData.forEach(function(_litem, _lIndex, _lArr) {
					if (_litem.isNeed == 1 && _litem.check.length == 0) {
						// console.log(_litem,'_litem')
						if (!_litem.answer || _litem.answe == "") {
							T.toast("必填项不能不为空！");
							this$1.data.btnStatus = "1";
							swich = false;
							throw new Error("必填项不能为空！");
						}
					}
					var item = {};
					var answersArr = [];
					item.paperTopicId = _litem.id;
					if (_litem.studyTopicFormName != "问答") {
						_litem.options.forEach(function(j, k) {
							if (_litem.check.indexOf(k) != -1) {
								answersArr.push(j.optionCode);
							}
						});
						item.answer = answersArr.join(",");
					} else {
						item.answer = _litem.answer;
					}
					paperAnswer.answers.push(item);
				});
			} catch (error) {
				// console.log(error)
			}
			// console.log(swich,'swich')
			// return
			if (swich) {
				T.ajax(
					{u: myjs.appUrl() + urlDeta, _this: this},
					urlDeta,
					function(ret, err) {
						T.hideProgress();
						if (ret.code == 200) {
							T.toast("提交成功！");
							setTimeout(function() {
								T.closeWin();
							}, 500);
						} else {
							this$1.data.btnStatus = "1";
						}
					},
					"我的答卷",
					"post",
					{
						body: JSON.stringify(paperAnswer)
					}
				);
			} else {
				this.data.btnStatus = "1";
				T.toast("必填项不能不为空！");
				return;
			}
		};
		MoSurveyDetails.prototype.getData = function(_type) {
			var this$1 = this;

			if (_type == -1) {
				this.data.pageNot.skeleton = true;
				this.data.listData = [];
				_type = 0;
			}
			if (!_type) {
				this.data.pageNo = 1;
			}
			if (!this.data.id || this.data.id == "") {
				return;
			}
			if (this.data.paperStatus == "已结束") {
				this.data.isUpdata = true;
				this.myStudypapertopic(_type);
			} else {
				var param = {
					form: {
						paperId: this.data.id,
						terminalName: "APP"
						// paperId:"628504263072550912"
					}
				};

				T.ajax(
					{u: myjs.appUrl() + "studyexamine/add", _this: this},
					"studyexamine/add",
					function(ret, err) {
						T.hideProgress();
						if (ret.code == 500) {
							T.toast(ret.message);
							this$1.data.pageNot.skeleton = false;
							this$1.data.tipsTitle = ret.message;
						} else {
							var data = ret ? ret.data || [] : [];
							this$1.data.isShare = data.paper.isShare || this$1.data.isShare;
							this$1.data.beginTime = data.paper.beginTime || this$1.data.beginTime;
							this$1.data.endTime = data.paper.endTime || this$1.data.endTime;
							this$1.data.examineId = data.id;
							this$1.data.examStatus = data.examStatus || 0;
							this$1.data.isUpdata = data.examStatus == "3" ? true : false;
							this$1.data.subText = this$1.data.examStatus == 3 ? "重新提交" : "提交";
							this$1.myStudypapertopic(_type);
						}
					},
					"立即考试",
					"post",
					{
						body: JSON.stringify(param)
					}
				);
			}
		};
		MoSurveyDetails.prototype.myStudypapertopic = function(_type) {
			var this$1 = this;

			//区分 结束状态已参与 和 未参与状态区分
			if (this.data.pageParam.examStatus == 3) {
				var postParam = {
					// examineId: "1673961489473540098" //这里注释
					examineId: this.data.examineId,
					needStudyInfo: true
				};

				T.ajax(
					{u: myjs.appUrl() + "studypapertopic/mytopics", _this: this},
					"studypapertopic/mytopics",
					function(ret, err) {
						T.hideProgress();
						this$1.data.pageNot.skeleton = false;
						var code = ret ? ret.code : "";
						var data = ret ? ret.data || [] : [];
						this$1.data.pageNot.type = ret ? (code == 200 ? 0 : 1) : 1; //类型 列表中只有有网和无网的情况
						this$1.data.pageNot.text =
							ret && code != 200 ? ret.message || ret.data : ""; //只有接口报的异常才改文字
						if (!_type) {
							this$1.data.pageNo = 1;
						}
						if (data) {
							var nowList = [];
							this$1.data.name = data.title || this$1.data.name;
							this$1.data.info = data.info || this$1.data.info;
							data.answerVos.forEach(function(_eItem, _eIndex, _eArr) {
								//item index 原数组对象
								var item = {};
								item.paperStatus = _eItem.paperStatus;
								item.id = _eItem.id;
								item.paperId = _eItem.paperId;
								item.isAnswer = _eItem.isAnswer || 0;
								item.title = _eItem.title;
								item.topicId = _eItem.topicId;
								item.isNeed = _eItem.isNeed || 0;
								item.studyTopicFormName = _eItem.studyTopicFormName;
								item.answer = _eItem.answer;
								item.options = _eItem.options;
								item.isUpdata = this$1.data.isUpdata;
								item.check = [];
								if (
									_eItem.studyTopicFormName != "问答" &&
									_eItem.answer &&
									_eItem.answer != ""
								) {
									var arrL = _eItem.answer.split(",");
									arrL.forEach(function(i) {
										_eItem.options.forEach(function(v, n) {
											if (i == v.optionCode) {
												item.check.push(n);
											}
										});
									});
								}
								item.id = _eItem.id;
								nowList.push(item);
							});
							if (!_type) {
								this$1.data.listData = nowList;
							} else {
								this$1.data.listData = this$1.data.listData.concat(nowList);
							}
							this$1.data.pageNo++;
							this$1.data.pageNot.text = !this$1.data.listData.length
								? ""
								: data.length >= postParam.pageSize
								? T.LOAD_MORE
								: T.LOAD_ALL; //当前返回的数量 等于 请求的数量 说明可能还有	少于说明没有了
							(this$1.data.refreshPageSize =
								Math.ceil(this$1.data.listData.length / this$1.data.pageSize) *
								this$1.data.pageSize),
								(this$1.data.pageNo =
									Math.ceil(this$1.data.listData.length / this$1.data.pageSize) + 1);
						} else if (_type == 1) {
							//加载更多的时候 底部显示文字
							this$1.data.pageNot.text = ret
								? code == 200
									? T.LOAD_ALL
									: ret.message
								: T.NET_ERR;
						} else {
							this$1.data.listData = [];
						}
						this$1.data.pageNot.listLength = this$1.data.listData.length;
					},
					"答卷",
					"post",
					{
						body: JSON.stringify(postParam)
					}
				);
			} else {
				var postParam = {
					detailId: this.data.id
				};

				T.ajax(
					{u: myjs.appUrl() + "studypaper/exampaper", _this: this},
					"studypaper/exampaper",
					function(ret, err) {
						T.hideProgress();
						this$1.data.pageNot.skeleton = false;
						var code = ret ? ret.code : "";
						var data = ret ? ret.data || [] : [];
						this$1.data.pageNot.type = ret ? (code == 200 ? 0 : 1) : 1; //类型 列表中只有有网和无网的情况
						this$1.data.pageNot.text =
							ret && code != 200 ? ret.message || ret.data : ""; //只有接口报的异常才改文字
						if (!_type) {
							this$1.data.pageNo = 1;
						}
						if (data) {
							var nowList = [];
							this$1.data.name = data.name || this$1.data.name;
							this$1.data.info = data.info || this$1.data.info;
							data.topics.forEach(function(_eItem, _eIndex, _eArr) {
								//item index 原数组对象
								var item = {};
								item.paperStatus = _eItem.paperStatus;
								item.id = _eItem.id;
								item.paperId = _eItem.paperId;
								item.isAnswer = _eItem.isAnswer || 0;
								item.title = _eItem.title;
								item.topicId = _eItem.topicId;
								item.isNeed = _eItem.isNeed || 0;
								item.studyTopicFormName = _eItem.studyTopicFormName;
								item.answer = _eItem.answer;
								item.options = _eItem.options;
								item.isUpdata = this$1.data.isUpdata;
								item.check = [];
								if (
									_eItem.studyTopicFormName != "问答" &&
									_eItem.answer &&
									_eItem.answer != ""
								) {
									var arrL = _eItem.answer.split(",");
									arrL.forEach(function(i) {
										_eItem.options.forEach(function(v, n) {
											if (i == v.optionCode) {
												item.check.push(n);
											}
										});
									});
								}
								item.id = _eItem.id;
								nowList.push(item);
							});
							if (!_type) {
								this$1.data.listData = nowList;
							} else {
								this$1.data.listData = this$1.data.listData.concat(nowList);
							}
							this$1.data.pageNo++;
							this$1.data.pageNot.text = !this$1.data.listData.length
								? ""
								: data.length >= postParam.pageSize
								? T.LOAD_MORE
								: T.LOAD_ALL; //当前返回的数量 等于 请求的数量 说明可能还有	少于说明没有了
							(this$1.data.refreshPageSize =
								Math.ceil(this$1.data.listData.length / this$1.data.pageSize) *
								this$1.data.pageSize),
								(this$1.data.pageNo =
									Math.ceil(this$1.data.listData.length / this$1.data.pageSize) + 1);
						} else if (_type == 1) {
							//加载更多的时候 底部显示文字
							this$1.data.pageNot.text = ret
								? code == 200
									? T.LOAD_ALL
									: ret.message
								: T.NET_ERR;
						} else {
							this$1.data.listData = [];
						}
						this$1.data.pageNot.listLength = this$1.data.listData.length;
					},
					"答卷",
					"post",
					{
						body: JSON.stringify(postParam)
					}
				);
			}
		};
		MoSurveyDetails.prototype.loadMore = function() {
			if (
				(this.data.pageNot.text == T.LOAD_MORE ||
					this.data.pageNot.text == T.NET_ERR) &&
				this.data.pageNo != 1
			) {
				this.data.pageNot.text = T.LOAD_ING;
				this.getData(this.data.listData.length ? 1 : 0); //列表没数据时 算下拉 有数据上拉
			}
		};
		MoSurveyDetails.prototype.changeUpdata = function() {
			this.data.isUpdata = !this.data.isUpdata;
		};
		MoSurveyDetails.prototype.openDetails = function(_item) {
			openModuleDetails(_item, this);
		};
		MoSurveyDetails.prototype.shareBtn = function() {
			this.data.sharePage.pageParam = {
				shareParam: {
					n: "mo_survey_details_h5",
					u: "../mo_survey_details_h5/mo_survey_details_h5.stml",
					p: {id: this.data.id}
				},
				moudule: {
					title: this.data.name,
					beginTime: this.data.beginTime,
					time: this.data.endTime
				}
			};
			console.log(this.data.sharePage.pageParam, "this.sharePage.pageParam");
			this.data.sharePage.pageParam.shareParam.p.areaId = myjs.areaId(this);
			this.data.sharePage.pageParam.code = this.data.code;
			this.data.sharePage.show = true;
		};
		MoSurveyDetails.prototype.openStatic = function() {
			var pageParam = {
				id: this.data.id
			};

			T.openWin(
				"mo_survey_result_statistic",
				"../mo_survey_result_statistic/mo_survey_result_statistic.stml",
				pageParam,
				this
			);
		};
		MoSurveyDetails.prototype.render = function() {
			var this$1 = this;
			return apivm.h(
				"y-base-page",
				{
					_this: this,
					title: this.data.title,
					closeH: true,
					dataMore: this.props.dataMore,
					more: true,
					pageParam: this.props.pageParam,
					onInit: this.baseInit,
					onClose: this.close,
					onBaseclose: this.baseclose,
					onPageRefresh: function() {
						return this$1.getData(false);
					}
				},
				apivm.h("view", {style: "width:auto;height:100%;"}),
				apivm.h(
					"view",
					{style: "width:auto;height:100%;"},
					this.data.isShare == "1" &&
						apivm.h(
							"view",
							{
								class: "header_btn",
								onClick: function() {
									return this$1.shareBtn();
								}
							},
							apivm.h("a-iconfont", {
								name: "share",
								color: this.data.G.getHeadThemeRelatively(this),
								size: this.data.G.appFontSize + 3
							})
						)
				),
				apivm.h("view", null),
				apivm.h(
					"view",
					{
						style:
							"width:100%;height:" +
							(this.props.dataMore && this.props.dataMore.boxAuto
								? "auto"
								: "1px;flex:1") +
							";"
					},
					apivm.h(
						"y-scroll-view",
						{
							style: "width:100%;height:100%;flex:1;",
							"refresher-enabled": true,
							data: this.data.pageNot,
							onLower: function() {
								return this$1.getData(0);
							},
							onUp: function() {
								return this$1.loadMore();
							}
						},
						apivm.h(
							"view",
							null,
							apivm.h(
								"view",
								null,
								apivm.h(
									"text",
									{class: "label_title", style: "" + this.data.G.loadConfiguration(4)},
									this.data.name
								)
							),
							apivm.h(
								"view",
								null,
								apivm.h(
									"text",
									{class: "label_des", style: "" + this.data.G.loadConfiguration(-2)},
									this.data.info
								)
							),
							apivm.h(
								"view",
								{class: "answer_area"},
								apivm.h(
									"view",
									{style: "padding:0 16px"},
									apivm.h("z-radio-input", {
										data: this.data.listData,
										isUpdata: this.data.isUpdata,
										color: this.data.G.appTheme,
										isH5: "false"
									})
								),
								this.data.isUpdata == false &&
									this.data.paperStatus != "已结束" &&
									apivm.h(
										"view",
										{style: "padding: 0 24px;"},
										apivm.h("z-button", {
											onClick: function() {
												return this$1.submit();
											},
											text: "" + this.data.subText,
											size: this.data.G.appFontSize,
											fontstyle: this.data.G.loadConfiguration(2) + "font-weight: 400;",
											style:
												"box-shadow: 0px 2px 10px 1px rgba(198,20,20,0.12);height:40px;border-radius:18px;",
											color: this.data.G.appTheme
										})
									)
							)
						)
					),
					apivm.h(
						"view",
						{style: "flex-direction:row;"},
						this.data.isUpdata &&
							this.data.paperStatus != "已结束" &&
							apivm.h(
								"view",
								{
									class: "amend_box",
									onClick: this.changeUpdata,
									style: "border-right: 0.5px solid lightgrey;"
								},
								apivm.h("image", {
									style: "max-width:20px;background:" + this.data.G.appTheme,
									src: "../../img/amend_transparent.png"
								}),
								apivm.h(
									"text",
									{
										style:
											this.data.G.loadConfiguration(0) +
											"color:" +
											this.data.G.appTheme +
											";"
									},
									"修改"
								)
							),
						this.data.pageParam.ifShowCountButton &&
							apivm.h(
								"view",
								{
									class: "amend_box",
									onClick: function() {
										return this$1.openStatic();
									}
								},
								apivm.h("a-iconfont", {
									name: "toupiao",
									color: "#000000",
									size: this.data.G.appFontSize + 3
								}),
								apivm.h(
									"text",
									{style: this.data.G.loadConfiguration(0) + "color:#000000;"},
									"查看统计"
								)
							)
					)
				),

				apivm.h("mo-details-share", {
					dataMore: this.data.sharePage,
					pageParam: this.data.sharePage.pageParam,
					type: "survey"
				})
			);
		};

		return MoSurveyDetails;
	})(Component);
	MoSurveyDetails.css = {
		".header_btn": {
			width: "auto",
			height: "100%",
			minWidth: "36px",
			padding: "0 12px",
			alignItems: "center",
			justifyContent: "center",
			flexDirection: "row",
			flexShrink: "0"
		},
		".label_title": {textAlign: "center", marginTop: "20px", fontWeight: "600"},
		".label_des": {
			padding: "0 16px",
			margin: "30px 0",
			color: "#333333",
			textIndent: "1cm"
		},
		".radio_select": {
			width: "14px",
			height: "14px",
			borderRadius: "50%",
			background: "url(../../img/check.png)",
			backgroundSize: "cover",
			marginRight: "10px"
		},
		".radio": {
			width: "14px",
			height: "14px",
			borderRadius: "50%",
			border: "1px solid #999999",
			backgroundSize: "cover",
			marginRight: "10px"
		},
		".amend_box": {
			flex: "1",
			height: "32px",
			boxShadow: "0 -2px 10px 1px #18407614",
			display: "flex",
			justifyContent: "center",
			alignItems: "center",
			flexDirection: "row",
			borderTop: "2px solid #18407614"
		}
	};
	apivm.define("mo-survey-details", MoSurveyDetails);
	apivm.render(apivm.h("mo-survey-details", null), "body");
})();
