#!/bin/bash

replaceArray=(
    'return getPrefs("sys_sign") || "zx";'
    'myjs.iszx = true;'
    'https://productpc.cszysoft.com:8081/lzt/'
)

replaceInFiles() {
    local path="$1"
    local -n searchArray="$2"
    local -n replaceArray="$3"

    echo "start task length: $(find "$path" -type f -name "*.js" | wc -l)"
    find "$path" -type f -name "*.js" -print0 | while IFS= read -r -d '' file; do
        echo "task ing: $file"
        content=$(<"$file")
        for ((i=0; i<${#searchArray[@]}; i++)); do
            content=$(sed -E "s@${searchArray[$i]}@${replaceArray[$i]}@g" <<< "$content")
        done
        printf '%s' "$content" > "$file"
    done
}
searchArray=(
    'return getPrefs\("sys_sign"\) \|\| "(.*?)"\;'
    'myjs.iszx = (.*?);'
    'https://productpc.cszysoft.com:(.*?)/lzt/'
)
startTime=$(date +%s)
replaceInFiles "pages" searchArray replaceArray
replaceInFiles "html/script" searchArray replaceArray
endTime=$(date +%s)
timeTaken=$((endTime - startTime))
echo ""
echo "start task ok! Press any key to exit, time consuming: ${timeTaken}s"
#read -rsn1