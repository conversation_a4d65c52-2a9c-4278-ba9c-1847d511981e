var avm = function() {
	function asap(t) {
		setTimeout(t, 0)
	}
	function noop() {}
	var LAST_ERROR = null
	  , IS_ERROR = {};
	function getThen(t) {
		try {
			return t.then
		} catch (t) {
			LAST_ERROR = t;
			return IS_ERROR
		}
	}
	function tryCallOne(t, e) {
		try {
			return t(e)
		} catch (t) {
			LAST_ERROR = t;
			return IS_ERROR
		}
	}
	function tryCallTwo(t, e, n) {
		try {
			t(e, n)
		} catch (t) {
			LAST_ERROR = t;
			return IS_ERROR
		}
	}
	function getStackTraceStack() {
		return global.avm ? global.avm._stackTraceStack : []
	}
	function setStackTraceStack(t) {
		global.avm && (global.avm._stackTraceStack = t)
	}
	function Promise(t) {
		var e = [Error().stack].concat(getStackTraceStack());
		if ("object" != typeof this)
			throw new TypeError("Promises must be constructed via new");
		if ("function" != typeof t)
			throw new TypeError("not a function");
		Object.defineProperties(this, {
			_deferredState: {
				value: 0,
				writable: !0
			},
			_state: {
				value: 0,
				writable: !0
			},
			_value: {
				value: null,
				writable: !0
			},
			_stackTraceStack: {
				value: e,
				writable: !1
			},
			_deferreds: {
				value: null,
				writable: !0
			}
		});
		t !== noop && doResolve(t, this)
	}
	Promise._onHandle = null;
	Promise._onReject = null;
	Promise._noop = noop;
	Promise.prototype.then = function(t, e) {
		if (this.constructor !== Promise)
			return safeThen(this, t, e);
		var n = new Promise(noop);
		handle(this, new Handler(t,e,n));
		return n
	}
	;
	function safeThen(i, r, o) {
		return new i.constructor(function(t, e) {
			var n = new Promise(noop);
			n.then(t, e);
			handle(i, new Handler(r,o,n))
		}
		)
	}
	function handle(t, e) {
		for (; 3 === t._state; )
			t = t._value;
		Promise._onHandle && Promise._onHandle(t);
		if (0 !== t._state)
			handleResolved(t, e);
		else {
			if (0 === t._deferredState) {
				t._deferredState = 1;
				t._deferreds = e;
				return
			}
			if (1 === t._deferredState) {
				t._deferredState = 2;
				t._deferreds = [t._deferreds, e];
				return
			}
			t._deferreds.push(e)
		}
	}
	function handleResolved(i, r) {
		asap(function() {
			var t = 1 === i._state ? r.onFulfilled : r.onRejected;
			if (null !== t) {
				var e = getStackTraceStack();
				setStackTraceStack(i._stackTraceStack);
				var n = tryCallOne(t, i._value);
				setStackTraceStack(e);
				n === IS_ERROR ? reject(r.promise, LAST_ERROR) : resolve(r.promise, n)
			} else
				(1 === i._state ? resolve : reject)(r.promise, i._value)
		})
	}
	function resolve(t, e) {
		if (e === t)
			return reject(t, new TypeError("A promise cannot be resolved with itself."));
		if (e && ("object" == typeof e || "function" == typeof e)) {
			var n = getThen(e);
			if (n === IS_ERROR)
				return reject(t, LAST_ERROR);
			if (n === t.then && e instanceof Promise) {
				t._state = 3;
				t._value = e;
				finale(t);
				return
			}
			if ("function" == typeof n) {
				doResolve(n.bind(e), t);
				return
			}
		}
		t._state = 1;
		t._value = e;
		finale(t)
	}
	function reject(t, e) {
		t._state = 2;
		t._value = e;
		Promise._onReject && Promise._onReject(t, e);
		finale(t)
	}
	function finale(t) {
		if (1 === t._deferredState) {
			handle(t, t._deferreds);
			t._deferreds = null
		}
		if (2 === t._deferredState) {
			for (var e = 0; e < t._deferreds.length; e++)
				handle(t, t._deferreds[e]);
			t._deferreds = null
		}
	}
	function Handler(t, e, n) {
		this.onFulfilled = "function" == typeof t ? t : null;
		this.onRejected = "function" == typeof e ? e : null;
		this.promise = n
	}
	function doResolve(t, e) {
		var n = !1
		  , i = tryCallTwo(t, function(t) {
			if (!n) {
				n = !0;
				resolve(e, t)
			}
		}, function(t) {
			if (!n) {
				n = !0;
				reject(e, t)
			}
		});
		if (!n && i === IS_ERROR) {
			n = !0;
			reject(e, LAST_ERROR)
		}
	}
	function valuePromise(t) {
		var e = new Promise(Promise._noop);
		e._state = 1;
		e._value = t;
		return e
	}
	Promise.resolve = function(t) {
		if (t instanceof Promise)
			return t;
		if ("object" == typeof t || "function" == typeof t)
			try {
				var e = t.then;
				if ("function" == typeof e)
					return new Promise(e.bind(t))
			} catch (n) {
				return new Promise(function(t, e) {
					return e(n)
				}
				)
			}
		return valuePromise(t)
	}
	;
	Promise.all = function(t) {
		var s = Array.prototype.slice.call(t);
		return new Promise(function(i, r) {
			if (0 === s.length)
				return i([]);
			var o = s.length;
			function a(e, t) {
				if (t && ("object" == typeof t || "function" == typeof t)) {
					if (t instanceof Promise && t.then === Promise.prototype.then) {
						for (; 3 === t._state; )
							t = t._value;
						if (1 === t._state)
							return a(e, t._value);
						2 === t._state && r(t._value);
						t.then(function(t) {
							return a(e, t)
						}, r);
						return
					}
					var n = t.then;
					if ("function" == typeof n) {
						new Promise(n.bind(t)).then(function(t) {
							return a(e, t)
						}, r);
						return
					}
				}
				s[e] = t;
				0 == --o && i(s)
			}
			for (var t = 0; t < s.length; t++)
				a(t, s[t])
		}
		)
	}
	;
	Promise.reject = function(n) {
		return new Promise(function(t, e) {
			return e(n)
		}
		)
	}
	;
	Promise.race = function(t) {
		return new Promise(function(e, n) {
			return t.forEach(function(t) {
				return Promise.resolve(t).then(e, n)
			})
		}
		)
	}
	;
	Promise.prototype.catch = function(t) {
		return this.then(null, t)
	}
	;
	Promise.prototype[Symbol.toStringTag] = "Promise";
	var jsConsole = function() {
		var t = global.console.print ? bindNativeConsole(global.console) : global.console;
		t.debug || (t.debug = function() {
			t.log.apply(t, arguments)
		}
		);
		return t
	};
	function format(t) {
		if ("string" == typeof t)
			return t;
		for (var e = Array(arguments.length), n = 0; n < arguments.length; n++)
			e[n] = arguments[n];
		return e.join(" ")
	}
	function getStackLineDes() {
		var t = Error("trace").stack;
		if (!t)
			return "";
		var e = t.split("\n");
		if (e.length < 4)
			return "";
		var n = e[3].split("(");
		return n.length < 2 ? "" : " at " + n[1].split(")")[0]
	}
	var warn = function() {
		var t;
		(t = jsConsole()).warn.apply(t, arguments)
	};
	function bindNativeConsole(n) {
		function t() {
			var e = o[r];
			i[e] = function() {
				var t = format.apply(void 0, arguments);
				t += getStackLineDes();
				n.print(e, t)
			}
		}
		for (var i = {}, r = 0, o = ["debug", "info", "log", "warn", "error"]; r < o.length; r++)
			t();
		return i
	}
	try {
		"undefined" == typeof global ? window.global = window : global.window = global.self = global;
		"undefined" == typeof window && (global.window = global)
	} catch (t) {}
	global.Promise = Promise;
	global.console && global.console.print && (global.console = bindNativeConsole(global.console));
	global.$_getTemplateData = function(templateJs, item, index) {
		var str = "(" + templateJs + ")";
		return eval(str)
	}
	;
	String.prototype.trim || (String.prototype.trim = function() {
		return this.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, "")
	}
	);
	"function" != typeof Object.assign && Object.defineProperty(Object, "assign", {
		value: function(t, e) {
			if (null == t)
				throw new TypeError("Cannot convert undefined or null to object");
			for (var n = Object(t), i = 1; i < arguments.length; i++) {
				var r = arguments[i];
				if (null != r)
					for (var o in r)
						Object.prototype.hasOwnProperty.call(r, o) && (n[o] = r[o])
			}
			return n
		},
		writable: !0,
		configurable: !0,
		enumerable: !1
	});
	Object.values || (Object.values = function(t) {
		if (t !== Object(t))
			throw new TypeError("Object.values called on a non-object");
		var e, n = [];
		for (e in t)
			Object.prototype.hasOwnProperty.call(t, e) && n.push(t[e]);
		return n
	}
	);
	Array.prototype.remove || (Array.prototype.remove = function(t) {
		var e = this.indexOf(t);
		0 <= e && this.splice(e, 1);
		return this.length
	}
	);
	function _defineProperties(t, e) {
		for (var n = 0; n < e.length; n++) {
			var i = e[n];
			i.enumerable = i.enumerable || !1;
			i.configurable = !0;
			"value"in i && (i.writable = !0);
			Object.defineProperty(t, i.key, i)
		}
	}
	function _createClass(t, e, n) {
		e && _defineProperties(t.prototype, e);
		n && _defineProperties(t, n);
		return t
	}
	function _defineProperty(t, e, n) {
		e in t ? Object.defineProperty(t, e, {
			value: n,
			enumerable: !0,
			configurable: !0,
			writable: !0
		}) : t[e] = n;
		return t
	}
	function _inheritsLoose(t, e) {
		t.prototype = Object.create(e.prototype);
		(t.prototype.constructor = t).__proto__ = e
	}
	function _assertThisInitialized(t) {
		if (void 0 === t)
			throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
		return t
	}
	var nativePlatform = global.app
	  , inMiniapp = "undefined" != typeof wx
	  , inApp = void 0 !== nativePlatform
	  , inBrowser = "undefined" != typeof window && !inApp
	  , inIOS = "ios" === nativePlatform
	  , callListener = function(t, e, n) {
		return "function" == typeof t ? t.call(e, n) : t.handleEvent(n)
	};
	function dispatchEvent(e) {
		var n = this
		  , t = (e = e || {}).type;
		if (!inBrowser) {
			if (e.target) {
				Object.assign(n, e.target)
			} else
				Object.assign(n, e);
			e.target = n
		}
		n.listeners || (n.listeners = {});
		var i = n.listeners[t];
		if (i) {
			e.currentTarget = n;
			return 1 == i.length ? callListener(i[0], n, e) : i.forEach(function(t) {
				callListener(t, n, e)
			})
		}
	}
	function addEventListener(t, e) {
		this.listeners || (this.listeners = {});
		this.listeners[t] || (this.listeners[t] = []);
		~this.listeners[t].indexOf(e) || this.listeners[t].push(e);
		this.ownerDocument.$addEvent(this.ref, t)
	}
	function removeEventListener(t, e) {
		if (this.listeners && this.listeners[t]) {
			var n = this.listeners[t]
			  , i = n.indexOf(e);
			-1 != i && n.splice(i, 1);
			this.ownerDocument.$removeEvent(this.ref, t)
		}
	}
	var nodeId = 0;
	function uniqueId() {
		return "o" + ++nodeId
	}
	var _jsId = 0;
	function jsId() {
		return "i" + ++_jsId
	}
	var _nobjId = 0;
	function nativeId() {
		return "n" + ++_nobjId
	}
	function insertIndex(t, e, n) {
		n < 0 && (n = 0);
		var i = e[n - 1]
		  , r = e[n];
		e.splice(n, 0, t);
		i && (i.nextSibling = t);
		t.previousSibling = i;
		(t.nextSibling = r) && (r.previousSibling = t);
		return n
	}
	function moveIndex(t, e, n) {
		var i = e.indexOf(t);
		if (i < 0)
			return -1;
		var r = e[i - 1]
		  , o = e[1 + i];
		r && (r.nextSibling = o);
		o && (o.previousSibling = r);
		e.splice(i, 1);
		var a = n;
		i <= n && (a = n - 1);
		var s = e[a - 1]
		  , c = e[a];
		e.splice(a, 0, t);
		s && (s.nextSibling = t);
		t.previousSibling = s;
		(t.nextSibling = c) && (c.previousSibling = t);
		return i === a ? -1 : n
	}
	function removeIndex(t, e, n) {
		var i = e.indexOf(t);
		if (!(i < 0)) {
			if (n) {
				var r = e[i - 1]
				  , o = e[1 + i];
				r && (r.nextSibling = o);
				o && (o.previousSibling = r)
			}
			e.splice(i, 1)
		}
	}
	function linkParent(e, t) {
		if ((e.parentNode = t).ownerDocument) {
			e.ownerDocument = t.ownerDocument;
			e.depth = t.depth + 1
		}
		e.childNodes && e.childNodes.forEach(function(t) {
			linkParent(t, e)
		})
	}
	function isTextElement(t) {
		return t && "#text" == t.nodeName
	}
	var Element = function() {
		function n(t, e) {
			this.nodeType = 1;
			this.nodeId = uniqueId();
			this.tagName = (t || "").toLowerCase();
			this.nodeName = this.tagName;
			this.ownerDocument = e || null;
			this.attributes = {};
			this.style = {};
			this.classStyle = {};
			this.childNodes = [];
			this.parentNode = null;
			this.nextSibling = null;
			this.previousSibling = null;
			this.firstChild = null;
			this.ref = this.nodeId;
			this.merged = !1;
			this.destroyed = !1;
			this._checked = !1;
			this._disabled = !1;
			this._value = ""
		}
		n.$create = function(t, e) {
			return new n(t,e)
		}
		;
		var t = n.prototype;
		t.appendChild = function(t) {
			if (t.parentNode) {
				t.parentNode.removeChild(t);
				this.appendChild(t)
			} else {
				linkParent(t, this);
				insertIndex(t, this.childNodes, this.childNodes.length);
				this.connectedCallback && this.connectedCallback();
				isTextElement(t) ? this.setAttribute("text", t.nodeValue) : this.ownerDocument.$addElement(t.ref, this.ref, t, -1);
				this.firstChild = this.childNodes[0]
			}
		}
		;
		t.insertBefore = function(t, e) {
			if (t.parentNode) {
				t.parentNode.removeChild(t);
				this.insertBefore(t, e)
			} else {
				linkParent(t, this);
				var n = insertIndex(t, this.childNodes, this.childNodes.indexOf(e));
				isTextElement(t) ? this.setAttribute("text", t.nodeValue) : this.ownerDocument.$addElement(t.ref, this.ref, t, n);
				this.firstChild = this.childNodes[0]
			}
		}
		;
		t.insertAfter = function(t, e) {
			if (!(t.parentNode && t.parentNode !== this || t === e || t.previousSibling && t.previousSibling === e)) {
				if (t.parentNode) {
					var n = moveIndex(t, this.childNodes, 1 + this.childNodes.indexOf(e));
					this.ownerDocument.$moveElement(t.ref, this.ref, n)
				} else {
					linkParent(t, this);
					var i = insertIndex(t, this.childNodes, 1 + this.childNodes.indexOf(e));
					isTextElement(t) ? this.setAttribute("text", t.nodeValue) : this.ownerDocument.$addElement(this.ref, t.ref, t, i)
				}
				this.firstChild = this.childNodes[0]
			}
		}
		;
		t.removeChild = function(t) {
			if (t.parentNode) {
				removeIndex(t, this.childNodes, !0);
				this.disconnectedCallback && this.disconnectedCallback();
				isTextElement(t) ? this.setAttribute("text", "") : this.ownerDocument.$removeElement(t.ref, t)
			}
			t.parentNode = null;
			this.firstChild = this.childNodes[0]
		}
		;
		t.replaceChild = function(t, e) {
			this.insertBefore(t, e);
			this.removeChild(e)
		}
		;
		t.setAttribute = function(t, e, n) {
			if (this.attributes[t] !== e || !1 === n) {
				this.attributes[t] = e;
				n || this.setter(t, e)
			}
		}
		;
		t.getAttribute = function(t) {
			return this.attributes[t]
		}
		;
		t.removeAttribute = function(t) {
			if (this.attributes[t]) {
				delete this.attributes[t];
				this.ownerDocument.$removeAttrs(this.ref, t)
			}
		}
		;
		t.setAttributeNS = function(t, e, n, i) {
			this.setAttribute(e, n, i)
		}
		;
		t.removeAttributeNS = function(t, e) {
			this.removeAttribute(e)
		}
		;
		t.getComputedStyle = function() {}
		;
		t.getBoundingClientRect = function() {
			return this.callMethod("getBoundingClientRect") || {}
		}
		;
		t.setStyle = function(t, e, n) {
			if (this.style[t] !== e || !1 === n) {
				this.style[t] = e;
				if (!n) {
					var i = {};
					i[t] = e;
					this.ownerDocument.$setStyles(this.ref, i)
				}
			}
		}
		;
		t.setStyles = function(t) {
			t && "object" == typeof t ? Object.assign(this.style, t) : this.style = t;
			this.ownerDocument.$setStyles(this.ref, t)
		}
		;
		t.setClassStyle = function(t) {
			for (var e in this.classStyle)
				this.classStyle[e] = "";
			Object.assign(this.classStyle, t);
			this.ownerDocument.$setStyles(this.ref, this.toStyle())
		}
		;
		t.toStyle = function() {
			return "string" == typeof this.style ? this.style : Object.assign({}, this.classStyle, this.style)
		}
		;
		t.toJSON = function() {
			var t = {
				id: this.ref,
				type: this.tagName,
				attributes: this.attributes ? this.attributes : {}
			};
			t.attributes.style = this.toStyle();
			if (this.listeners) {
				var e = Object.keys(this.listeners);
				e.length && (t.events = e)
			}
			this.childNodes.length && (t.children = this.childNodes.map(function(t) {
				return t.toJSON()
			}));
			return t
		}
		;
		t.destroy = function() {
			this.ownerDocument.$unregisterNode(this);
			this.parentNode = null;
			this.childNodes.forEach(function(t) {
				t.destroy()
			});
			this.destroyed = !0
		}
		;
		t.isDestroyed = function() {
			return this.destroyed
		}
		;
		t.callMethod = function(t, e, n) {
			return this.ownerDocument.$callMethod(this, t, e, n)
		}
		;
		t.getter = function(t) {
			if (t)
				return this.ownerDocument.$getAttrs(this, t)
		}
		;
		t.setter = function(t, e) {
			if (t) {
				var n = {};
				n[t] = e;
				this.ownerDocument.$setAttrs(this.ref, n)
			}
		}
		;
		t.callNative = function(t, e, n) {
			return this.callMethod(t, e, n)
		}
		;
		_createClass(n, [{
			key: "checked",
			get: function() {
				this._checked = this.getter("checked");
				return this._checked
			},
			set: function(t) {
				this._checked = t;
				this.setter("checked", t)
			}
		}, {
			key: "disabled",
			get: function() {
				this._disabled = this.getter("disabled");
				return this._disabled
			},
			set: function(t) {
				this._disabled = t;
				this.setter("disabled", t)
			}
		}, {
			key: "value",
			get: function() {
				var t = this.getter("value");
				null != t && (this._value = t);
				return this._value
			},
			set: function(t) {
				if (this._value != t) {
					this._value = t;
					this.setter("value", t)
				}
			}
		}, {
			key: "lastChild",
			get: function() {
				return this.childNodes[this.childNodes.length - 1]
			}
		}, {
			key: "scrollTop",
			get: function() {
				return this.getter("scrollTop")
			}
		}, {
			key: "scrollLeft",
			get: function() {
				return this.getter("scrollLeft")
			}
		}, {
			key: "offsetLeft",
			get: function() {
				return this.getter("offsetLeft")
			}
		}, {
			key: "offsetTop",
			get: function() {
				return this.getter("offsetTop")
			}
		}, {
			key: "offsetWidth",
			get: function() {
				return this.getter("offsetWidth")
			}
		}, {
			key: "offsetHeight",
			get: function() {
				return this.getter("offsetHeight")
			}
		}]);
		return n
	}();
	Element.prototype.removeEventListener = removeEventListener;
	Element.prototype.addEventListener = addEventListener;
	Element.prototype.dispatchEvent = dispatchEvent;
	Element.prototype[Symbol.toStringTag] = "Element";
	var TextNode = function(i) {
		_inheritsLoose(r, i);
		function r(t, e) {
			var n;
			(n = i.call(this, "#text", e) || this).nodeId = jsId();
			n.nodeType = 3;
			n.dataValue = t || "";
			return n
		}
		var t = r.prototype;
		t.splitText = function(t) {
			var e = this.dataValue ? this.dataValue.length : 0
			  , n = this.substringData(0, t)
			  , i = this.substringData(t, e - t);
			this.dataValue = n;
			return new r(i)
		}
		;
		t.substringData = function(t, e) {
			return this.dataValue ? this.dataValue.substr(t, e) : ""
		}
		;
		t.appendData = function(t) {
			var e = t;
			e && (this.dataValue ? this.dataValue += e : this.dataValue = e)
		}
		;
		t.insertData = function() {}
		;
		t.deleteData = function() {}
		;
		t.replaceData = function() {}
		;
		t.appendChild = function() {}
		;
		t.removeChild = function() {}
		;
		t.setAttribute = function() {}
		;
		t.removeAttribute = function() {}
		;
		t.addEventListener = function() {}
		;
		t.removeEventListener = function() {}
		;
		_createClass(r, [{
			key: "length",
			get: function() {
				return this.dataValue ? this.dataValue.length : 0
			}
		}, {
			key: "nodeValue",
			get: function() {
				return this.dataValue
			},
			set: function(t) {
				this.dataValue = t;
				this.parentNode && this.parentNode.setAttribute("text", t)
			}
		}]);
		return r
	}(Element);
	TextNode.prototype.toString = function() {
		return this.dataValue
	}
	;
	var slice = Array.prototype.slice;
	function domWalk(t, e) {
		"length"in t || (t = [t]);
		t = slice.call(t);
		for (; t.length; ) {
			var n = t.shift()
			  , i = e(n);
			if (i)
				return i;
			n.childNodes && n.childNodes.length && (t = slice.call(n.childNodes).concat(t))
		}
	}
	function arrayWalk(t, e) {
		"length"in t || (t = [t]);
		t = slice.call(t);
		for (; t.length; ) {
			var n = e(t.shift());
			if (n)
				return n
		}
	}
	var QuerySelectors = function() {
		function t(t) {
			this.ownerDocument = t;
			this.nodeMap = t._nodeMap
		}
		var e = t.prototype;
		e.byId = function(t) {
			var e = t;
			if (!e)
				return null;
			var n = arrayWalk(this.ownerDocument.childNodes, function(t) {
				if (t.attributes.id === e)
					return t
			});
			return n ? assignNativeBridge(this.ownerDocument, n) : null
		}
		;
		e.byTagName = function(t) {
			var e = t
			  , n = [];
			if (!e)
				return n;
			e = e.toLowerCase();
			for (var i in this.nodeMap)
				this.nodeMap[i].type === e && n.push(this.nodeMap[i]);
			return n
		}
		;
		e.byClassName = function(t) {
			var e = t
			  , n = [];
			if (!e)
				return n;
			e = e.toLowerCase();
			for (var i in this.nodeMap) {
				var r = this.nodeMap[i].attributes.class;
				r && ~r.indexOf(e) && n.push(this.nodeMap[i])
			}
			return n
		}
		;
		e.selector = function(t) {
			if (!t)
				return null;
			var e = this.selectorAll(t);
			if (e && Array.isArray(e) && 0 < e.length) {
				return assignNativeBridge(this.ownerDocument, e[0])
			}
			return e
		}
		;
		e.selectorAll = function(t) {
			if (!t)
				return null;
			for (var e = t.split(" "), n = this, i = 0, r = e.length; i < r; i++)
				n = handleQuery(e[i], n);
			return n
		}
		;
		return t
	}();
	function handleQuery(t, e) {
		var n = null;
		/^#([\w-]+)$/.test(t) ? n = e.byId(t.substring(1)) : /^[\w-]+$/.test(t) ? n = e.byTagName(t) : /^\.([\w-]+)$/.test(t) && (n = e.byClassName(t.substring(1)));
		return n
	}
	function assignNativeBridge(doc, ele) {
		if (ele && !ele.merged) {
			var o = doc.$client.get(ele.ref, "bridge-object");
			o && ("string" != typeof o ? assignAPIs(ele, o) : Object.assign(ele, eval(o)));
			ele.merged = !0
		}
		return ele
	}
	function assignAPIs(t, i) {
		if (i && i.methods) {
			function e() {
				var n = i.methods[r].name;
				t[n] = function(t, e) {
					this.callMethod(n, t, e)
				}
			}
			for (var r = 0; r < i.methods.length; r++)
				e()
		}
		if (i && i.attrs) {
			function n() {
				var e = i.attrs[o].name
				  , n = i.attrs[o].writable;
				Object.defineProperty(t, e, {
					get: function() {
						return this.getter(e)
					},
					set: function(t) {
						n && this.setter(e, t)
					}
				})
			}
			for (var o = 0; o < i.attrs.length; o++)
				n()
		}
	}
	var NativePipe = function() {
		function t(t) {
			this.$nb = t || global.native_hooks
		}
		var e = t.prototype;
		e.create = function(t, e, n) {
			this.$nb.create(t, e, n)
		}
		;
		e.get = function(t, e) {
			return this.$nb.get(t, e)
		}
		;
		e.set = function(t, e) {
			this.$nb.set(t, e)
		}
		;
		e.call = function(t, e, n, i) {
			return this.$nb.call(t, e, n, i)
		}
		;
		e.listen = function(t, e, n) {
			this.$nb.listen(t, e, n)
		}
		;
		e.destroy = function(t) {
			this.$nb.destroy(t)
		}
		;
		e.contentLoaded = function(t) {
			this.$nb.contentLoaded(t)
		}
		;
		e.load = function(t) {
			return this.$nb.load(t)
		}
		;
		e.loadAndExecute = function(t, e, n) {
			return this.$nb.loadAndExecute(t, e, n)
		}
		;
		return t
	}()
	  , $NativePipe = new NativePipe
	  , HTMLElement = function(n) {
		_inheritsLoose(t, n);
		function t(t, e) {
			return n.call(this, t, e) || this
		}
		var e = t.prototype;
		e.connectedCallback = function() {}
		;
		e.disconnectedCallback = function() {}
		;
		return t
	}(Element)
	  , HTMLHeadElement = function(e) {
		_inheritsLoose(t, e);
		function t(t) {
			return e.call(this, "head", t) || this
		}
		t.prototype.appendChild = function() {}
		;
		return t
	}(HTMLElement)
	  , HTMLBodyElement = function(e) {
		_inheritsLoose(t, e);
		function t(t) {
			return e.call(this, "body", t) || this
		}
		return t
	}(HTMLElement)
	  , HTML_KEY = "LMTHrenni".split("").reverse().join("")
	  , IS_NON_DIMENSIONAL = /acit|ex(?:s|g|n|p|$)|rph|ows|mnc|ntw|ine[ch]|zoo|^ord/i;
	function extend(t, e) {
		for (var n in e)
			t[n] = e[n];
		return t
	}
	function applyRef(t, e) {
		t && ("function" == typeof t ? t(e) : t.current = e)
	}
	function isEmptyObject(e) {
		if (!e)
			return 1;
		else
			return function() {
				for (var t in e)
					return;
				return 1
			}()
	}
	var defer = Promise.resolve().then.bind(Promise.resolve());
	function nProps(e) {
		if (!e || Array.isArray(e))
			return {};
		var n = {};
		Object.keys(e).forEach(function(t) {
			n[t] = e[t].value
		});
		return n
	}
	function getUse(s, t, e, n) {
		var c = [];
		t.forEach(function(t, e) {
			if ("string" == typeof t)
				c[e] = getTargetByPath(s, t);
			else {
				var n = Object.keys(t)[0]
				  , i = t[n];
				if ("string" == typeof i)
					c[e] = getTargetByPath(s, i);
				else {
					var r = i[0];
					if ("string" == typeof r) {
						var o = getTargetByPath(s, r);
						c[e] = i[1] ? i[1](o) : o
					} else {
						var a = [];
						r.forEach(function(t) {
							a.push(getTargetByPath(s, t))
						});
						c[e] = i[1].apply(null, a)
					}
				}
				c[n] = c[e]
			}
		});
		e && (e[n] = c);
		return c
	}
	function getTargetByPath(t, e) {
		for (var n = e.replace(/]/g, "").replace(/\[/g, ".").split("."), i = t, r = 0, o = n.length; r < o; r++)
			i = i[n[r]];
		return i
	}
	function removeItem(t, e) {
		for (var n = 0, i = e.length; n < i; n++)
			if (e[n] === t) {
				e.splice(n, 1);
				break
			}
	}
	function Fragment(t) {
		return t.children
	}
	var options$1 = {
		scopedStyle: !1,
		mapping: {},
		staticStyleMapping: {},
		styleCache: []
	}
	  , mapping = options$1.mapping;
	function isSameNodeType(t, e, n) {
		if ("string" == typeof e || "number" == typeof e)
			return void 0 !== t.splitText;
		if ("string" != typeof e.nodeName)
			return n || t._componentConstructor === e.nodeName;
		var i = mapping[e.nodeName];
		return i ? n || t._componentConstructor === i : !t._componentConstructor && isNamedNode(t, e.nodeName)
	}
	function isNamedNode(t, e) {
		return t.e === e || t.nodeName.toLowerCase() == e.toLowerCase()
	}
	function getNodeProps(t) {
		var e = extend({}, t.attributes);
		e.children = t.children;
		var n = t.nodeName.defaultProps;
		if (void 0 !== n)
			for (var i in n)
				void 0 === e[i] && (e[i] = n[i]);
		return e
	}
	function createNode(t, e) {
		var n = e ? Document$1.createElementNS("http://www.w3.org/2000/svg", t) : Document$1.createElement(t);
		n.e = t;
		return n
	}
	function createTextNode(t) {
		return Document$1.createTextNode(t)
	}
	function parseCSSText(t) {
		var e = t.replace(/\/\*(.|\s)*?\*\//g, " ").replace(/\s+/g, " ")
		  , n = {}
		  , i = e.match(/ ?(.*?) ?{([^}]*)}/) || [r, o, e]
		  , r = i[0]
		  , o = i[1]
		  , a = i[2].split(";").map(function(t) {
			return function(t) {
				if (!t)
					return !1;
				var e = (t = t.split(":"))[0].trim()
				  , n = t.slice(1).join(":").trim();
				return !(!e || !n) && [e, n]
			}(t)
		});
		for (var s in a) {
			var c = a[s]
			  , u = c[0]
			  , f = c[1];
			u && f && (n[u.replace(/\W+\w/g, function(t) {
				return t.slice(-1).toUpperCase()
			})] = f)
		}
		return isEmptyObject(n) ? t : n
	}
	function removeNode(t) {
		var e = t.parentNode;
		e && e.removeChild(t)
	}
	function setAttributes(e, t, n) {
		t.forEach(function(t) {
			setAccessor(e, t.name, t.oldVal, t.newVal, n)
		})
	}
	function setAccessor(t, e, n, i, r) {
		"className" === e && (e = "class");
		if ("key" !== e)
			if ("ref" === e) {
				applyRef(n, null);
				applyRef(i, t)
			} else if ("class" !== e || r)
				if ("style" === e) {
					if (inBrowser) {
						setStyleInBrowser(t, e, n, i);
						return
					}
					var o = n
					  , a = i;
					"string" == typeof n && (o = parseCSSText(n));
					"string" == typeof i && (a = parseCSSText(i));
					var s = {}
					  , c = !1;
					if (o) {
						for (var u in o)
							"object" != typeof a || u in a || (c = !(s[u] = ""));
						for (var f in a)
							if (a[f] !== o[f]) {
								s[f] = a[f];
								c = !0
							}
						c && t.setStyles(s)
					} else
						t.setStyles(a)
				} else if ("o" == e[0] && "n" == e[1]) {
					var h = e !== (e = e.replace(/Capture$/, ""));
					e = e.toLowerCase().substring(2);
					if (i) {
						if (!n) {
							t.addEventListener(e, eventProxy, h);
							if ("tap" == e) {
								t.addEventListener("touchstart", touchStart, h);
								t.addEventListener("touchend", touchEnd, h)
							}
						}
					} else {
						t.removeEventListener(e, eventProxy, h);
						if ("tap" == e) {
							t.removeEventListener("touchstart", touchStart, h);
							t.removeEventListener("touchend", touchEnd, h)
						}
					}
					(t._listeners || (t._listeners = {}))[e] = i
				} else if ("dangerouslySetInnerHTML" === e)
					i && (t[HTML_KEY] = i.__html || "");
				else if ("list" !== e && "type" !== e && !r && e in t && "tagName" !== e && "form" !== e && "size" !== e && "download" !== e && "href" !== e)
					setProperty(t, e, i = null == i ? "" : i);
				else {
					var l = r && e !== (e = e.replace(/^xlink:?/, ""));
					if (null == i || !1 === i) {
						i = null == i ? "" : i;
						l ? t.setAttributeNS("http://www.w3.org/1999/xlink", e.toLowerCase(), i) : t.setAttribute(e, i)
					} else if ("function" != typeof i)
						if (l)
							t.setAttributeNS("http://www.w3.org/1999/xlink", e.toLowerCase(), i);
						else {
							t.setAttribute(e, i);
							t.avmDataset || (t.avmDataset = {});
							e.indexOf("data-") || (e = e.substr(5));
							t.avmDataset[e] = i
						}
					else
						setProperty(t, e, i)
				}
			else {
				t.className = i || "";
				t.setAttribute("class", t.className)
			}
	}
	function setStyleInBrowser(t, e, n, i) {
		i && "string" != typeof i && "string" != typeof n || (t.style.cssText = i || "");
		if (i && "object" == typeof i) {
			if ("string" != typeof n)
				for (var r in n)
					r in i || (t.style[r] = "");
			for (var r in i)
				t.style[r] = "number" == typeof i[r] && !1 === IS_NON_DIMENSIONAL.test(r) ? i[r] + "px" : i[r]
		}
	}
	function setProperty(t, e, n) {
		try {
			t[e] = n
		} catch (t) {}
	}
	function eventProxy(e) {
		var lisFun = this._listeners[e.type]
		  , comp = lisFun._comp || Document$1.$rootvm;
		inBrowser && !inMiniapp && parseEvent(e);
		if ("function" == typeof lisFun) {
			e = options$1.event && options$1.event(e) || e;
			if (inBrowser && !inMiniapp) {
				if (e.target && e.target.avmDataset) {
					Object.defineProperty(e.target, "dataset", {
						writable: !0
					});
					e.target.dataset = e.target.avmDataset
				}
				if (e.currentTarget && e.currentTarget.avmDataset) {
					Object.defineProperty(e.currentTarget, "dataset", {
						writable: !0
					});
					e.currentTarget.dataset = e.currentTarget.avmDataset
				}
			}
			return lisFun(e)
		}
		if ("string" == typeof lisFun) {
			var Fn = parseFun(lisFun);
			if (comp[Fn.name])
				return comp[Fn.name].apply(comp, Fn.params);
			var fun = eval(Fn.name);
			return fun.apply(comp, Fn.params)
		}
	}
	function parseEvent(t) {
		if ("input" == t.type) {
			Object.defineProperty(t, "detail", {
				writable: !0
			});
			t.detail = {
				value: t.currentTarget.value
			}
		}
		if (!inMiniapp)
			if ("scroll" == t.type) {
				t.detail = {
					scrollLeft: t.currentTarget.scrollLeft,
					scrollTop: t.currentTarget.scrollTop,
					scrollWidth: t.currentTarget.scrollWidth,
					scrollHeight: t.currentTarget.scrollHeight,
					deltaX: isNaN(t.currentTarget._avmOldScrollLeft) ? 0 : t.currentTarget._avmOldScrollLeft - t.currentTarget.scrollLeft,
					deltaY: isNaN(t.currentTarget._avmOldScrollTop) ? 0 : t.currentTarget._avmOldScrollTop - t.currentTarget.scrollTop
				};
				t.currentTarget._avmOldScrollLeft = t.currentTarget.scrollLeft;
				t.currentTarget._avmOldScrollTop = t.currentTarget.scrollTop
			} else
				"change" == t.type && (t.detail = {
					value: t.currentTarget.checked
				})
	}
	function touchStart(t) {
		this.___touchX = t.touches[0].pageX;
		this.___touchY = t.touches[0].pageY;
		this.___scrollTop = Document$1.body.scrollTop
	}
	function touchEnd(t) {
		Math.abs(t.changedTouches[0].pageX - this.___touchX) < 30 && Math.abs(t.changedTouches[0].pageY - this.___touchY) < 30 && Math.abs(Document$1.body.scrollTop - this.___scrollTop) < 30 && this.dispatchEvent(new CustomEvent("tap",{
			detail: t
		}))
	}
	function parseFun(t) {
		var e = {
			name: "",
			params: []
		};
		if (~(t = t.trim()).indexOf("(") && ~t.indexOf(")")) {
			var n = t.split("(");
			e.name = n[0].trim();
			if ((n = n[1].split(")"))[0]) {
				n = n[0].split(",");
				for (var i = 0; i < n.length; ++i) {
					var r = n[i].trim();
					"'" == r[0] && "'" == r[0 | r.length - 1] && (r = r.substr(1, r.length - 2));
					e.params.push(r)
				}
			}
		} else
			e.name = t;
		return e
	}
	var items = [];
	function enqueueRender(t) {
		1 == items.push(t) && (options$1.debounceRendering || defer)(rerender)
	}
	function rerender() {
		for (var t; t = items.pop(); )
			renderComponent(t)
	}
	var obaa = function u(t, e, n) {
		function i(t, e, n) {
			t.$observer || (t.$observer = this);
			var i = t.$observer
			  , r = [];
			if (u.isArray(t)) {
				if (0 === t.length) {
					t.$observeProps = {};
					t.$observeProps.$observerPath = "#"
				}
				i.mock(t)
			}
			for (var o in t)
				if (t.hasOwnProperty(o))
					if (n) {
						if (u.isArray(e) && u.isInArray(e, o)) {
							r.push(o);
							i.watch(t, o)
						} else if (u.isString(e) && o == e) {
							r.push(o);
							i.watch(t, o)
						}
					} else {
						r.push(o);
						i.watch(t, o)
					}
			i.target = t;
			i.propertyChangedHandler || (i.propertyChangedHandler = []);
			i.propertyChangedHandler.push({
				all: !n,
				propChanged: n || e,
				eventPropArr: r
			})
		}
		i.prototype = {
			onPropertyChanged: function(t, e, n, i, r) {
				if (e !== n && this.propertyChangedHandler)
					for (var o = u._getRootName(t, r), a = 0, s = this.propertyChangedHandler.length; a < s; a++) {
						var c = this.propertyChangedHandler[a];
						!c.all && !u.isInArray(c.eventPropArr, o) && o.indexOf("Array-") || c.propChanged.call(this.target, t, e, n, r)
					}
				t.indexOf("Array-") && "object" == typeof e && this.watch(i, t, i.$observeProps.$observerPath)
			},
			mock: function(e) {
				var r = this;
				u.methods.forEach(function(i) {
					Object.defineProperty(e, i, {
						enumerable: !1,
						writable: !0,
						configurable: !0
					});
					e[i] = function() {
						var t = Array.prototype.slice.call(this, 0)
						  , e = Array.prototype[i].apply(this, Array.prototype.slice.call(arguments));
						if (RegExp("\\b" + i + "\\b").test(u.triggerStr)) {
							for (var n in this)
								this.hasOwnProperty(n) && !u.isFunction(this[n]) && r.watch(this, n, this.$observeProps.$observerPath);
							r.onPropertyChanged("Array-" + i, this, t, this, this.$observeProps.$observerPath)
						}
						return e
					}
					;
					var t = "pure" + i.substring(0, 1).toUpperCase() + i.substring(1);
					Object.defineProperty(e, t, {
						enumerable: !1,
						writable: !0,
						configurable: !0
					});
					e[t] = function() {
						return Array.prototype[i].apply(this, Array.prototype.slice.call(arguments))
					}
				})
			},
			watch: function(n, i, t) {
				Object.defineProperty(n, "$observeProps", {
					enumerable: !1,
					writable: !0,
					configurable: !0
				});
				n.$observeProps && Object.defineProperty(n.$observeProps, "$observerPath", {
					enumerable: !1,
					writable: !0,
					configurable: !0
				});
				if ("$observeProps" != i && "$observer" != i && !u.isFunction(n[i])) {
					n.$observeProps || (n.$observeProps = {});
					n.$observeProps.$observerPath = void 0 !== t ? t : "#";
					var r = this
					  , e = n.$observeProps[i] = n[i];
					Object.defineProperty(n, i, {
						get: function() {
							return this.$observeProps[i]
						},
						set: function(t) {
							var e = this.$observeProps[i];
							r.onPropertyChanged(i, this.$observeProps[i] = t, e, this, n.$observeProps.$observerPath)
						}
					});
					if ("object" == typeof e) {
						if (u.isArray(e)) {
							this.mock(e);
							if (0 === e.length) {
								e.$observeProps || (e.$observeProps = {});
								e.$observeProps.$observerPath = void 0 !== t ? t : "#"
							}
						}
						for (var o in e)
							e.hasOwnProperty(o) && this.watch(e, o, n.$observeProps.$observerPath + "-" + i)
					}
				}
			}
		};
		return new i(t,e,n)
	};
	obaa.methods = ["concat", "copyWithin", "entries", "every", "fill", "filter", "find", "findIndex", "forEach", "includes", "indexOf", "join", "keys", "lastIndexOf", "map", "pop", "push", "reduce", "reduceRight", "reverse", "shift", "slice", "some", "sort", "splice", "toLocaleString", "toString", "unshift", "values", "size"];
	obaa.triggerStr = "concat,copyWithin,fill,pop,push,reverse,shift,sort,splice,unshift,size";
	obaa.isArray = function(t) {
		return "[object Array]" === Object.prototype.toString.call(t)
	}
	;
	obaa.isString = function(t) {
		return "string" == typeof t
	}
	;
	obaa.isInArray = function(t, e) {
		for (var n = t.length; -1 < --n; )
			if (e === t[n])
				return !0;
		return !1
	}
	;
	obaa.isFunction = function(t) {
		return "[object Function]" == Object.prototype.toString.call(t)
	}
	;
	obaa._getRootName = function(t, e) {
		return "#" === e ? t : e.split("-")[1]
	}
	;
	obaa.add = function(t, e) {
		t.$observer.watch(t, e)
	}
	;
	obaa.set = function(t, e, n, i) {
		i || (t[e] = n);
		t.$observer.watch(t, e);
		i && (t[e] = n)
	}
	;
	Array.prototype.size = function(t) {
		this.length = t
	}
	;
	var callbacks = []
	  , nextTickCallback = [];
	function fireTick() {
		callbacks.forEach(function(t) {
			t.fn.call(t.scope)
		});
		nextTickCallback.forEach(function(t) {
			t.fn.call(t.scope)
		});
		nextTickCallback.length = 0
	}
	function proxyData(t) {
		var e = null;
		obaa(t.data, function() {
			if (!t._willUpdate)
				if (t.constructor.nextUpdate) {
					clearTimeout(e);
					e = setTimeout(function() {
						t.update();
						fireTick()
					}, 0)
				} else {
					t.update();
					fireTick()
				}
		})
	}
	var Component = function() {
		function t(t, e) {
			this.props = Object.assign(nProps(this.constructor.props), this.constructor.defaultProps, t);
			this.data = this.constructor.data || this.data || {};
			this._preCss = null;
			this.store = e;
			this.computed = {}
		}
		var e = t.prototype;
		e.update = function(t) {
			if (!this._willUpdate) {
				this.beforeUpdate();
				this.beforeRender();
				this._willUpdate = !0;
				t && (this._renderCallbacks = this._renderCallbacks || []).push(t);
				renderComponent(this, 2);
				options$1.componentChange && options$1.componentChange(this, this.base);
				this._willUpdate = !1;
				this.afterUpdate()
			}
		}
		;
		e.updateSelf = function() {
			if (!this._willUpdateSelf) {
				this._willUpdateSelf = !0;
				renderComponent(this, 2, null, null, !0);
				this._willUpdateSelf = !1
			}
		}
		;
		e.fire = function(e, n) {
			var i = this;
			Object.keys(this.props).every(function(t) {
				if ("on" + e.toLowerCase() != t.toLowerCase())
					return !0;
				i.props[t]({
					detail: n
				});
				return !1
			})
		}
		;
		e.render = function() {}
		;
		e.install = function() {}
		;
		e.installed = function() {}
		;
		e.uninstall = function() {}
		;
		e.beforeUpdate = function() {}
		;
		e.afterUpdate = function() {}
		;
		e.beforeRender = function() {}
		;
		return t
	}();
	_defineProperty(Component, "observe", !0);
	_defineProperty(Component, "nextUpdate", !1);
	_defineProperty(Component, "_isDefinite", !0);
	var storeHelpers = ["use", "useSelf"];
	function define(t, n, o) {
		if (n._isDefinite)
			customElementsDefine(t, n);
		else {
			var e = function(r) {
				_inheritsLoose(t, r);
				function t() {
					for (var t, e = arguments.length, n = Array(e), i = 0; i < e; i++)
						n[i] = arguments[i];
					_defineProperty(_assertThisInitialized(t = r.call.apply(r, [this].concat(n)) || this), "compute", o && o.compute);
					return t
				}
				var e = t.prototype;
				e.render = function() {
					return n.call(this, this)
				}
				;
				e.installed = function() {
					this._isInstalled = !0
				}
				;
				return t
			}(Component);
			if (o) {
				function i(t) {
					"function" == typeof o[t] && (e.prototype[t] = function() {
						return o[t].apply(this, arguments)
					}
					)
				}
				for (var r in o)
					i(r);
				storeHelpers.forEach(function(t) {
					o[t] && "function" !== o[t] && (e.prototype[t] = function() {
						return o[t]
					}
					)
				})
			}
			customElementsDefine(t, e)
		}
	}
	function customElementsDefine(t, e) {
		(options$1.mapping[t] = e).use ? e.updatePath = getPath(e.use) : e.data && (e.updatePath = getUpdatePath(e.data))
	}
	function getPath(t, e, n) {
		if (Array.isArray(t)) {
			var i = {};
			t.forEach(function(t) {
				if ("string" == typeof t)
					i[t] = !0;
				else {
					var e = t[Object.keys(t)[0]];
					"string" == typeof e ? i[e] = !0 : "string" == typeof e[0] ? i[e[0]] = !0 : e[0].forEach(function(t) {
						return i[t] = !0
					})
				}
			});
			e && (e[n] = i);
			return i
		}
		return getUpdatePath(t)
	}
	function getUpdatePath(t) {
		var e = {};
		dataToPath(t, e);
		return e
	}
	function dataToPath(n, i) {
		Object.keys(n).forEach(function(t) {
			i[t] = !0;
			var e = Object.prototype.toString.call(n[t]);
			"[object Object]" === e ? _objToPath(n[t], t, i) : "[object Array]" === e && _arrayToPath(n[t], t, i)
		})
	}
	function _objToPath(n, i, r) {
		Object.keys(n).forEach(function(t) {
			r[i + "." + t] = !0;
			delete r[i];
			var e = Object.prototype.toString.call(n[t]);
			"[object Object]" === e ? _objToPath(n[t], i + "." + t, r) : "[object Array]" === e && _arrayToPath(n[t], i + "." + t, r)
		})
	}
	function _arrayToPath(t, i, r) {
		t.forEach(function(t, e) {
			r[i + "[" + e + "]"] = !0;
			delete r[i];
			var n = Object.prototype.toString.call(t);
			"[object Object]" === n ? _objToPath(t, i + "[" + e + "]", r) : "[object Array]" === n && _arrayToPath(t, i + "[" + e + "]", r)
		})
	}
	var components = {};
	function collectComponent(t) {
		unlinkComponent(t)
	}
	function createComponent(t, e, n, i, r) {
		var o, a = components[t.name];
		if (t.prototype && t.prototype.render) {
			o = new t(e,n);
			Component.call(o, e, n)
		} else {
			(o = new Component(e,n)).constructor = t;
			o.render = doRender
		}
		linkComponent(o, r);
		i && (o.scopedCssAttr = i.css);
		if (o.store) {
			if (o.use) {
				var s = "function" == typeof o.use ? o.use() : o.use;
				if (options.isMultiStore) {
					var c = {}
					  , u = {};
					for (var f in s) {
						c[f] = {};
						u[f] = {};
						getPath(s[f], c, f);
						getUse(o.store[f].data, s[f], u, f);
						o.store[f].instances.push(o)
					}
					o.using = u;
					o._updatePath = c
				} else {
					o._updatePath = getPath(s);
					o.using = getUse(o.store.data, s);
					o.store.instances.push(o)
				}
			}
			if (o.useSelf) {
				var h = "function" == typeof o.useSelf ? o.useSelf() : o.useSelf;
				if (options.isMultiStore) {
					var l = {}
					  , d = {};
					for (var v in h) {
						getPath(h[v], l, v);
						getUse(o.store[v].data, h[v], d, v);
						o.store[v].updateSelfInstances.push(o)
					}
					o.usingSelf = d;
					o._updateSelfPath = l
				} else {
					o._updateSelfPath = getPath(h);
					o.usingSelf = getUse(o.store.data, h);
					o.store.updateSelfInstances.push(o)
				}
			}
			if (o.compute)
				for (var p in o.compute)
					o.computed[p] = o.compute[p].call(options.isMultiStore ? o.store : o.store.data)
		}
		if (o.compute)
			for (var p in o.compute)
				bindComputed(o, p, o.compute[p]);
		if (a)
			for (var m = a.length; m--; )
				if (a[m].constructor === t) {
					o.nextBase = a[m].nextBase;
					a.splice(m, 1);
					break
				}
		return o
	}
	function linkComponent(t, e) {
		if (e) {
			(t._host = e)._child || (e._child = []);
			e._child.push(t)
		}
	}
	function unlinkComponent(t) {
		if (t) {
			t._host = null;
			t._child = null
		}
	}
	function doRender(t, e, n) {
		return this.constructor(t, n)
	}
	var _none_ = function() {}
	  , definition = {
		enumerable: !0,
		configurable: !0,
		get: _none_,
		set: _none_
	};
	function bindComputed(t, e, n) {
		if ("function" == typeof n) {
			definition.get = toGetter(n);
			definition.set = _none_
		} else {
			definition.get = n.get ? toGetter(n.get) : _none_;
			definition.set = n.set || _none_
		}
		Object.defineProperty(t, e, definition)
	}
	function toGetter(t) {
		return function() {
			return t.call(this, this)
		}
	}
	var styleId = 0;
	function getCtorName(t) {
		for (var e = 0, n = options$1.styleCache.length; e < n; e++) {
			var i = options$1.styleCache[e];
			if (i.ctor === t)
				return i.attrName
		}
		var r = "s" + styleId;
		options$1.styleCache.push({
			ctor: t,
			attrName: r
		});
		styleId++;
		return r
	}
	function copyJsonScoper(t, e) {
		var n = {};
		for (var i in t)
			n[i + e] = t[i];
		return n
	}
	function scoper(t, r) {
		r = "[" + r.toLowerCase() + "]";
		if ("object" == typeof t)
			return copyJsonScoper(t, r);
		else
			return (t = t.replace(/\/\*[^*]*\*+([^/][^*]*\*+)*\//g, "")).replace(/([^\r\n,{}:]+)(:[^\r\n,{}]+)?(,(?=[^{}]*{)|s*{)/g, function(t, e, n, i) {
				void 0 === n && (n = "");
				return e.match(/^\s*(@media|\d+%?|@-webkit-keyframes|@keyframes|to|from|@font-face)/) ? e + n + i : e.replace(/(\s*)$/, "") + r + n + i
			})
	}
	function addStyle(t, e) {
		Document$1.$createStyle(t, e)
	}
	function addStyleWithoutId(t) {
		Document$1.$createStyle(t)
	}
	function addScopedAttrStatic(t, e) {
		options$1.scopedStyle && scopeVdom(e, t)
	}
	function addStyleLinkToHead(t) {
		if (!options$1.staticStyleMapping[t]) {
			Document$1.$createLink(t);
			options$1.staticStyleMapping[t] = !0
		}
	}
	function addStyleToHead(t, e) {
		if (options$1.scopedStyle) {
			if (!options$1.staticStyleMapping[e]) {
				addStyle(scoper(t, e), e);
				options$1.staticStyleMapping[e] = !0
			}
		} else if (!options$1.staticStyleMapping[e]) {
			addStyleWithoutId(t);
			options$1.staticStyleMapping[e] = !0
		}
	}
	function scopeVdom(e, t) {
		if ("object" == typeof t) {
			t.attributes = t.attributes || {};
			t.attributes[e] = "";
			t.css = t.css || {};
			t.css[e] = "";
			t.children.forEach(function(t) {
				return scopeVdom(e, t)
			})
		}
	}
	function scopeHost(t, e) {
		if ("object" == typeof t && e) {
			t.attributes = t.attributes || {};
			for (var n in e)
				t.attributes[n] = ""
		}
	}
	function buildComponentFromVNode(t, e, n, i, r, o) {
		for (var a = t && t._component, s = a, c = t, u = a && t._componentConstructor === e.nodeName, f = u, h = getNodeProps(e); a && !f && (a = a._parentComponent); )
			f = a.constructor === e.nodeName;
		if (a && f && (!i || a._component)) {
			r || setComponentProps(a, h, 3, n, i, a);
			t = a.base
		} else {
			if (s && !u) {
				unmountComponent(s);
				t = c = null
			}
			a = createComponent(e.nodeName, h, n, e, o);
			for (var l in a) {
				var d = a[l];
				if ("function" == typeof d) {
					if ("constructor" == l)
						continue;
					a[l] = d.bind(a)
				}
			}
			if (t && !a.nextBase) {
				a.nextBase = t;
				c = null
			}
			setComponentProps(a, h, 1, n, i, a);
			t = a.base;
			if (c && t !== c) {
				c._component = null;
				recollectNodeTree(c, !1)
			}
		}
		return t
	}
	function setComponentProps(t, e, n, i, r, o) {
		if (!t._disable) {
			t._disable = !0;
			(t.__ref = e.ref) && delete e.ref;
			(t.__key = e.key) && delete e.key;
			if (!t.base || r) {
				t.beforeInstall && t.beforeInstall();
				t.install && t.install();
				t.constructor.observe && proxyData(t)
			} else
				t.receiveProps && t.receiveProps(e, t.data, t.props);
			if (i && i !== t.context) {
				t.prevContext || (t.prevContext = t.context);
				t.context = i
			}
			t.prevProps || (t.prevProps = t.props);
			t.props = e;
			t._disable = !1;
			0 !== n && (1 !== n && !1 === options$1.syncComponentUpdates && t.base ? enqueueRender(t) : renderComponent(t, 1, r, !1, !1, o));
			applyRef(t.__ref, t)
		}
	}
	function renderComponent(t, e, n, i, r, o) {
		if (!t._disable) {
			var a, s, c, u = t.props, f = t.data, h = t.context, l = t.prevProps || u, d = t.prevState || f, v = t.prevContext || h, p = t.base, m = t.nextBase, g = p || m, b = t._component, w = !1;
			if (p) {
				t.props = l;
				t.data = d;
				t.context = v;
				if (t.store || 2 == e || shallowComparison(l, u)) {
					w = !1;
					t.beforeUpdate && t.beforeUpdate(u, f, h)
				} else
					w = !0;
				t.props = u;
				t.data = f;
				t.context = h
			}
			t.prevProps = t.prevState = t.prevContext = t.nextBase = null;
			if (!w) {
				t.beforeRender && t.beforeRender();
				a = t.render(u, f, h);
				flushComponentPre(t, a);
				scopeHost(a, t.scopedCssAttr);
				t.getChildContext && (h = extend(extend({}, h), t.getChildContext()));
				var y, k, x = options$1.mapping[a && a.nodeName];
				if (x) {
					var _ = getNodeProps(a);
					if ((s = b) && s.constructor === x && _.key == s.__key)
						setComponentProps(s, _, 1, h, !1, s);
					else {
						y = s;
						t._component = s = createComponent(x, _, h, null, o);
						s.nextBase = s.nextBase || m;
						s._parentComponent = t;
						setComponentProps(s, _, 0, h, !1, s);
						renderComponent(s, 1, n, !0, !1, o)
					}
					k = s.base
				} else {
					c = g;
					(y = b) && (c = t._component = null);
					if (g || 1 === e) {
						c && (c._component = null);
						k = diff(c, a, h, n || !p, g && g.parentNode, !0, r, o || t)
					}
				}
				if (g && k !== g && s !== b) {
					var T = g.parentNode;
					if (T && k !== T) {
						T.replaceChild(k, g);
						if (!y) {
							g._component = null;
							recollectNodeTree(g, !1)
						}
					}
				}
				y && unmountComponent(y);
				if ((t.base = k) && !i) {
					for (var E = t, O = t; O = O._parentComponent; )
						(E = O).base = k;
					k._component = E;
					k._componentConstructor = E.constructor
				}
			}
			if (!p || n)
				unshiftMount(t);
			else if (!w) {
				t.afterUpdate && t.afterUpdate(l, d, v);
				t.updated && t.updated(l, d, v);
				options$1.afterUpdate && options$1.afterUpdate(t)
			}
			if (null != t._renderCallbacks)
				for (; t._renderCallbacks.length; )
					t._renderCallbacks.pop().call(t);
			diffLevel || i || flushMounts()
		}
	}
	function flushComponentPre(t, e) {
		if (t.constructor.csslink || t.csslink) {
			var n = t.constructor.csslink ? t.constructor.csslink : "function" == typeof t.csslink ? t.csslink() : t.csslink;
			if (Array.isArray(n))
				for (var i = 0; i < n.length; ++i)
					addStyleLinkToHead(n[i])
		}
		if (t.constructor.css || t.css) {
			var r = "scope_" + getCtorName(t.constructor);
			addScopedAttrStatic(e, r);
			addStyleToHead(t.constructor.css ? t.constructor.css : "function" == typeof t.css ? t.css() : t.css, r)
		}
	}
	function shallowComparison(t, e) {
		var n;
		for (n in t)
			if (null == e[n] && null != t[n])
				return 1;
		if (0 < t.children.length || 0 < e.children.length)
			return 1;
		for (n in e)
			if ("children" != n) {
				var i = typeof e[n];
				if ("function" == i || "object" == i)
					return 1;
				if (e[n] != t[n])
					return 1
			}
	}
	function unmountComponent(t) {
		options$1.beforeUnmount && options$1.beforeUnmount(t);
		var e = t.base;
		t._disable = !0;
		t.uninstall && t.uninstall();
		if (t.store)
			if (options.isMultiStore)
				for (var n in t.store) {
					var i = t.store[n];
					i.instances && removeItem(t, i.instances);
					i.updateSelfInstances && removeItem(t, i.updateSelfInstances)
				}
			else {
				t.store.instances && removeItem(t, t.store.instances);
				t.store.updateSelfInstances && removeItem(t, t.store.updateSelfInstances)
			}
		t.base = null;
		var r = t._component;
		if (r)
			unmountComponent(r);
		else if (e) {
			null != e.__inattr_ && applyRef(e.__inattr_.ref, null);
			removeNode(t.nextBase = e);
			collectComponent(t);
			spliceMount(t);
			removeChildren(e)
		}
		applyRef(t.__ref, null)
	}
	function VNode(t, e, n) {
		this.nodeName = t;
		this.children = e;
		this.attributes = n
	}
	var diffLevel = 0
	  , isSvgMode = !1
	  , hydrating = !1
	  , mounts = []
	  , mountMaps = {};
	function spliceMount(t) {
		delete mountMaps[t.constructor.name]
	}
	function unshiftMount(t) {
		mounts.unshift(t);
		mountMaps[t.constructor.name] = t
	}
	function flushMounts() {
		for (var t; t = mounts.pop(); ) {
			options$1.afterMount && options$1.afterMount(t);
			t.installed && t.installed()
		}
	}
	function diff(t, e, n, i, r, o, a, s) {
		if (!diffLevel++) {
			isSvgMode = null != r && void 0 !== r.ownerSVGElement;
			hydrating = null != t && !("__inattr_"in t)
		}
		var c;
		Array.isArray(e) && (e = new VNode(inBrowser ? "div" : "view",e));
		c = idiff(t, e, n, i, o, a, s);
		r && c.parentNode !== r && r.appendChild(c);
		if (!--diffLevel) {
			hydrating = !1;
			o || flushMounts()
		}
		return c
	}
	function idiff(t, e, n, i, r, o, a) {
		var s = t
		  , c = isSvgMode;
		null != e && "boolean" != typeof e || (e = "");
		var u = e.nodeName;
		if (options$1.mapping[u]) {
			e.nodeName = options$1.mapping[u];
			return buildComponentFromVNode(t, e, n, i, o, a)
		}
		if ("function" == typeof u)
			return buildComponentFromVNode(t, e, n, i, o, a);
		if ("string" == typeof e || "number" == typeof e) {
			if (t && void 0 !== t.splitText && t.parentNode && (!t._component || r))
				t.nodeValue != e && (t.nodeValue = e);
			else {
				s = createTextNode(e);
				if (t) {
					t.parentNode && t.parentNode.replaceChild(s, t);
					recollectNodeTree(t, !0)
				}
			}
			try {
				s.__inattr_ = !0
			} catch (t) {}
			return s
		}
		isSvgMode = "svg" === u || "foreignObject" !== u && isSvgMode;
		u += "";
		if (!t || !isNamedNode(t, u)) {
			s = createNode(u, isSvgMode);
			if (t) {
				for (; t.firstChild; )
					s.appendChild(t.firstChild);
				t.parentNode && t.parentNode.replaceChild(s, t);
				recollectNodeTree(t, !0)
			}
		}
		var f = s.firstChild
		  , h = s.__inattr_
		  , l = e.children;
		if (null == h) {
			h = s.__inattr_ = {};
			for (var d = s.attributes, v = d.length; v--; ) {
				var p = d[v].name;
				"type" !== p && (h[p] = d[v].value)
			}
		}
		!hydrating && l && 1 === l.length && "string" == typeof l[0] && null != f && void 0 !== f.splitText && null == f.nextSibling ? f.nodeValue != l[0] && (f.nodeValue = l[0]) : (l && l.length || null != f) && innerDiffNode(s, l, n, i, hydrating || null != h.dangerouslySetInnerHTML, o, a);
		a && (s.avmComp = a);
		diffAttributes(s, e.attributes, h);
		isSvgMode = c;
		inBrowser && !inMiniapp && initWebComponents(s, e.attributes, a);
		return s
	}
	var WEB_COM_FIXED_LIST = ["refresh", "scroll-view", "swiper", "picker", "frame", "rich-text", "image", "switch", "input", "checkbox", "checkbox-group", "radio", "radio-group", "slider", "textarea"];
	function initWebComponents(t, e, n) {
		WEB_COM_FIXED_LIST.includes(t.e) && global.api._initWebCmpt && global.api._initWebCmpt(t.e, t, e, n)
	}
	function innerDiffNode(t, e, n, i, r, o, a) {
		var s, c, u, f, h, l = t.childNodes, d = [], v = {}, p = 0, m = 0, g = l.length, b = 0, w = e ? e.length : 0;
		if (0 !== g)
			for (var y = 0; y < g; y++) {
				var k = l[y]
				  , x = k.__inattr_;
				if (null != (_ = w && x ? k._component ? k._component.__key : x.key : null)) {
					p++;
					v[_] = k
				} else
					(x || (void 0 !== k.splitText ? !r || k.nodeValue.trim() : r)) && (d[b++] = k)
			}
		if (0 !== w)
			for (y = 0; y < w; y++) {
				var _;
				if ((h = null) != (_ = (f = e[y]).key)) {
					if (p && void 0 !== v[_]) {
						h = v[_];
						v[_] = void 0;
						p--
					}
				} else if (!h && m < b)
					for (s = m; s < b; s++)
						if (void 0 !== d[s] && isSameNodeType(c = d[s], f, r)) {
							h = c;
							d[s] = void 0;
							s === b - 1 && b--;
							s === m && m++;
							break
						}
				h = idiff(h, f, n, i, o, !1, a);
				u = l[y];
				h && h !== t && h !== u && (null == u ? t.appendChild(h) : h === u.nextSibling ? removeNode(u) : t.insertBefore(h, u))
			}
		if (p)
			for (var y in v)
				void 0 !== v[y] && recollectNodeTree(v[y], !1);
		for (; m <= b; )
			void 0 !== (h = d[b--]) && recollectNodeTree(h, !1)
	}
	function recollectNodeTree(t, e) {
		var n = t._component;
		if (n)
			unmountComponent(n);
		else {
			null != t.__inattr_ && applyRef(t.__inattr_.ref, null);
			!1 !== e && null != t.__inattr_ || removeNode(t);
			removeChildren(t)
		}
	}
	function removeChildren(t) {
		t = t.lastChild;
		for (; t; ) {
			var e = t.previousSibling;
			recollectNodeTree(t, !0);
			t = e
		}
	}
	function diffAttributes(t, e, n) {
		var i, r = [];
		for (i in n)
			if ((!e || null == e[i]) && null != n[i]) {
				var o = n[i]
				  , a = n[i] = void 0;
				r.push({
					name: i,
					oldVal: o,
					newVal: a
				})
			}
		for (i in e)
			if (!("children" === i || i === HTML_KEY || i in n && e[i] === ("value" === i || "checked" === i ? t[i] : n[i]))) {
				o = n[i],
				a = n[i] = e[i];
				r.push({
					name: i,
					oldVal: o,
					newVal: a
				})
			}
		0 < r.length && setAttributes(t, 1 < r.length ? adjustmentSort(r) : r, isSvgMode)
	}
	function adjustmentSort(t) {
		t.sort(ATTRS_SORT_COMPARE);
		for (var e = null, n = 0; n < t.length; n++) {
			var i = t[n];
			if ("style" === i.name) {
				t.splice(n, 1);
				e = i;
				break
			}
		}
		e && t.push(e);
		return t
	}
	var ATTRS_SORT_COMPARE = function(t, e) {
		var n = t.name
		  , i = e.name;
		return n < i ? 1 : i < n ? -1 : 0
	};
	function render(t, e, n, i, r) {
		"string" == typeof e && (e = Document$1.querySelector(e));
		if (n)
			if (n.data)
				obsStore(n);
			else {
				options$1.isMultiStore = !0;
				for (var o in n)
					n[o].data && obsStore(n[o], o)
			}
		if (i)
			for (; e.firstChild; )
				e.removeChild(e.firstChild);
		var a = diff(r = r && ("string" == typeof r ? Document$1.querySelector(r) : r), t, n, !1, e, !1, null);
		e === Document$1.body && (Document$1.$rootvm = a._component);
		return a
	}
	function obsStore(o, t) {
		o.instances = [];
		o.updateSelfInstances = [];
		extendStoreUpate(o, t);
		obaa(o.data, function(t, e, n, i) {
			var r = {};
			r[fixPath(i + "-" + t)] = !0;
			o.update(r)
		})
	}
	function merge(t, e, n) {
		obsStore(n);
		return diff(e = "string" == typeof e ? Document$1.querySelector(e) : e, t, n)
	}
	function extendStoreUpate(r, o) {
		r.update = function(e) {
			var n = this;
			if (!(Object.keys(e).length <= 0)) {
				var i = matchGlobalData(this.globalData, e);
				this.instances.forEach(function(t) {
					compute(t, o);
					if (o) {
						if (i || n.updateAll || t._updatePath && t._updatePath[o] && needUpdate(e, t._updatePath[o])) {
							t.use && getUse(r.data, ("function" == typeof t.use ? t.use() : t.use)[o], t.using, o);
							t.update()
						}
					} else if (i || n.updateAll || t._updatePath && needUpdate(e, t._updatePath)) {
						t.use && (t.using = getUse(r.data, "function" == typeof t.use ? t.use() : t.use));
						t.update()
					}
				});
				this.updateSelfInstances.forEach(function(t) {
					compute(t, o);
					if (o) {
						if (t._updateSelfPath && t._updateSelfPath[o] && needUpdate(e, t._updateSelfPath[o])) {
							t.useSelf && getUse(r.data, ("function" == typeof t.useSelf ? t.useSelf() : t.useSelf)[o], t.usingSelf, o);
							t.updateSelf()
						}
					} else if (t._updateSelfPath && needUpdate(e, t._updateSelfPath)) {
						t.usingSelf = getUse(r.data, "function" == typeof t.useSelf ? t.useSelf() : t.useSelf);
						t.updateSelf()
					}
				});
				this.onChange && this.onChange(e)
			}
		}
	}
	function matchGlobalData(t, e) {
		if (!t)
			return !1;
		for (var n in e) {
			if (~t.indexOf(n))
				return !0;
			for (var i = 0, r = t.length; i < r; i++)
				if (includePath(n, t[i]))
					return !0
		}
		return !1
	}
	function compute(t, e) {
		if (t.compute)
			for (var n in t.compute)
				t.computed[n] = t.compute[n].call(e ? t.store : t.store.data)
	}
	function needUpdate(t, e) {
		for (var n in t) {
			if (e[n])
				return 1;
			for (var i in e)
				if (includePath(n, i))
					return 1
		}
	}
	function includePath(t, e) {
		if (!t.indexOf(e)) {
			var n = t.substr(e.length, 1);
			if ("[" == n || "." == n)
				return 1
		}
	}
	function fixPath(t) {
		var n = "";
		t.replace("#-", "").split("-").forEach(function(t, e) {
			e ? isNaN(+t) ? n += "." + t : n += "[" + t + "]" : n += t
		});
		return n
	}
	function $_createCell(t, e, n) {
		return this.$bindCell_ ? render(this.$bindCell_(t, e, n)) : null
	}
	function $_updateCell(t, e, n, i) {
		return this.$bindCell_ ? render(this.$bindCell_(e, n, i), null, null, null, t) : null
	}
	var RecyclerElement = function(n) {
		_inheritsLoose(i, n);
		function i(t, e) {
			return n.call(this, t, e) || this
		}
		i.$create = function(t, e) {
			return new i(t,e)
		}
		;
		return i
	}(Element);
	RecyclerElement.prototype.$_createCell = $_createCell;
	RecyclerElement.prototype.$_updateCell = $_updateCell;
	RecyclerElement.prototype[Symbol.toStringTag] = "RecyclerElement";
	var EventTarget = function() {
		function t() {
			this.ref = nativeId();
			this.$events = {};
			this.ownerDocument = Document$1
		}
		var e = t.prototype;
		e.on = function(t, e) {
			if (!this.$events[t]) {
				this.$events[t] = e;
				this.ownerDocument.$addEvent(this.ref, t)
			}
			return this
		}
		;
		e.off = function(t) {
			if (this.$events[t]) {
				delete this.$events[t];
				this.ownerDocument.$removeEvent(this.ref, t)
			}
		}
		;
		e.dispatchEvent = function(t) {
			var e = this.$events[t.type];
			if (e)
				return e.call(t.target = this, t);
			else
				return null
		}
		;
		e.save = function() {
			this.ownerDocument.$addObject(this)
		}
		;
		e.dispose = function() {
			this.ownerDocument.destroy(this.ref)
		}
		;
		return t
	}()
	  , NativeObject = function(n) {
		_inheritsLoose(t, n);
		function t(t) {
			var e;
			(e = n.call(this) || this).type = t;
			e.$pipe = $NativePipe;
			return e
		}
		var e = t.prototype;
		e.create = function(t, e, n) {
			this.$pipe.create(t, e, n)
		}
		;
		e.get = function(t, e) {
			return this.$pipe.get(t, e)
		}
		;
		e.set = function(t, e) {
			this.$pipe.set(t, e)
		}
		;
		e.call = function(t, e, n, i) {
			return this.$pipe.call(t, e, n, i)
		}
		;
		e.listen = function(t, e, n) {
			this.$pipe.listen(t, e, n)
		}
		;
		e.callNative = function(t, e, n) {
			return this.call(this.ref, t, e, n)
		}
		;
		e.getNative = function(t) {
			return this.get(this.ref, t)
		}
		;
		e.setNative = function(t) {
			return this.set(this.ref, t)
		}
		;
		return t
	}(EventTarget)
	  , OPCODES = {
		arc: 1,
		arcTo: 2,
		beginPath: 3,
		bezierCurveTo: 4,
		clearRect: 5,
		closePath: 6,
		fill: 7,
		fillRect: 8,
		fillStyle: 9,
		fillText: 10,
		lineCap: 11,
		lineJoin: 12,
		lineTo: 13,
		lineWidth: 14,
		moveTo: 15,
		quadraticCurveTo: 16,
		rect: 17,
		restore: 18,
		rotate: 19,
		save: 20,
		scale: 21,
		setTransform: 22,
		stroke: 23,
		strokeRect: 24,
		strokeStyle: 25,
		strokeText: 26,
		textAlign: 27,
		textBaseline: 28,
		transform: 29,
		translate: 30,
		font: 31,
		drawImage: 32
	}
	  , Graphics = function(n) {
		_inheritsLoose(t, n);
		function t(t) {
			var e;
			(e = n.call(this) || this)._operations = [];
			e._doubles = [];
			e._booleans = [];
			e._strings = [];
			e._ints = [];
			e.create(e.ref, "$graphics", t);
			return e
		}
		var e = t.prototype;
		e.init = function(t) {
			this.callNative("init", t)
		}
		;
		e.getImageData = function(t, e, n, i) {
			var r = this.callNative("getImageData", {
				x: t,
				y: e,
				width: n,
				height: i
			});
			return r instanceof Uint8ClampedArray ? r : new Uint8ClampedArray(r)
		}
		;
		e.putImageData = function(t, e, n, i, r, o, a) {
			this.callNative("putImageData", {
				data: t.data,
				width: t.width,
				height: t.height,
				x: e = e || 0,
				y: n = n || 0,
				dirtyX: i = i || 0,
				dirtyY: r = r || 0,
				dirtyWidth: o = o || t.width,
				dirtyHeight: a = a || t.height
			})
		}
		;
		e.addOperation = function(t, e) {
			if (inIOS)
				this._operations.push({
					name: t,
					arg: e
				});
			else {
				var n = OPCODES[t];
				if (!n)
					throw Error("Invalid operation");
				this._operations.push(n)
			}
		}
		;
		e.addBoolean = function() {
			Array.prototype.push.apply(this._booleans, arguments)
		}
		;
		e.addDouble = function() {
			Array.prototype.push.apply(this._doubles, arguments)
		}
		;
		e.addInt = function() {
			Array.prototype.push.apply(this._ints, arguments)
		}
		;
		e.addString = function() {
			Array.prototype.push.apply(this._strings, arguments)
		}
		;
		e.flush = function() {
			if (0 < this._operations.length) {
				this.callNative("flush", inIOS ? {
					actionlist: this._operations
				} : {
					actionlist: [this._operations, this._doubles, this._booleans, this._strings, this._ints]
				});
				this._operations = [];
				this._doubles = [];
				this._booleans = [];
				this._strings = [];
				this._ints = []
			}
		}
		;
		return t
	}(NativeObject)
	  , ImageData = function(t, e, n) {
		if (arguments.length < 2)
			throw new TypeError("Not enough arguments to ImageData");
		var i, r, o;
		if (t instanceof Uint8ClampedArray) {
			i = checkArray(t);
			r = checkSize(e);
			o = 2 < arguments.length ? checkSize(n) : i.byteLength / 4 / r;
			if (i.byteLength !== r * o * 4)
				throw Error("wrong array size")
		} else {
			r = checkSize(t);
			o = checkSize(e);
			i = new Uint8ClampedArray(r * o * 4)
		}
		Object.defineProperties(this, {
			data: {
				value: i
			},
			width: {
				value: r
			},
			height: {
				value: o
			}
		})
	};
	Object.defineProperty(ImageData.prototype, "data", {
		value: null
	});
	Object.defineProperty(ImageData.prototype, "width", {
		value: 0
	});
	Object.defineProperty(ImageData.prototype, "height", {
		value: 0
	});
	ImageData.prototype[Symbol.toStringTag] = "ImageData";
	function checkArray(t) {
		if (t.byteLength % 4 != 0)
			throw Error("Illegal array length");
		return t
	}
	function checkSize(t) {
		var e = Math.floor(t);
		if (e <= 0 || !isFinite(e))
			throw Error("Illegal size for ImageData");
		return e
	}
	var SUPPORTED_ENCODINGS = ["ascii", "utf-8"];
	function encodeCheck(t, e) {
		if ("string" != typeof t)
			throw Error("Invalid text, must be a string");
		if (!SUPPORTED_ENCODINGS.includes(e))
			throw Error("Unsupported encoding: " + e)
	}
	function decodeCheck(t, e) {
		if (!(t instanceof ArrayBuffer))
			throw Error("Invalid buffer type");
		if (!SUPPORTED_ENCODINGS.includes(e))
			throw Error("Unsupported encoding: " + e)
	}
	var TextCodec = function(e) {
		_inheritsLoose(o, e);
		function o() {
			var t;
			(t = e.call(this) || this).save();
			t.create(t.ref, "$textcodec", {});
			return t
		}
		var t = o.prototype;
		t.decode = function(t, e) {
			this.call(this.ref, "decode", {
				data: t,
				encoding: e
			})
		}
		;
		t.decodeSync = function(t, e) {
			return this.call(this.ref, "decode", {
				data: t,
				encoding: e
			})
		}
		;
		t.encode = function(t, e) {
			this.call(this.ref, "encode", {
				text: t,
				encoding: e
			})
		}
		;
		t.encodeSync = function(t, e) {
			return this.call(this.ref, "encodeSync", {
				text: t,
				encoding: e
			})
		}
		;
		o.getInstance = function() {
			this._instance || Object.defineProperty(this, "_instance", {
				enumerable: !1,
				writable: !1,
				value: new o
			});
			return this._instance
		}
		;
		o.decode = function(t, r) {
			return new Promise(function(n, i) {
				ArrayBuffer.isView(t) && (t = t.buffer);
				decodeCheck(t, r = r || "utf-8");
				(new o).on("result", function(t) {
					var e = t.target;
					n(t.string);
					e.dispose()
				}).on("error", function(t) {
					var e = t.target;
					i(Error("Could not decode " + r));
					e.dispose()
				}).decode(t, r)
			}
			)
		}
		;
		o.decodeSync = function(t, e) {
			ArrayBuffer.isView(t) && (t = t.buffer);
			decodeCheck(t, e = e || "utf-8");
			return o.getInstance().decodeSync(t, e)
		}
		;
		o.encode = function(t, r) {
			return new Promise(function(n, i) {
				encodeCheck(t, r = r || "utf-8");
				(new o).on("result", function(t) {
					var e = t.target;
					n(t.data);
					e.dispose()
				}).on("error", function(t) {
					var e = t.target;
					i(Error("Could not encode " + r));
					e.dispose()
				}).encode(t, r)
			}
			)
		}
		;
		o.encodeSync = function(t, e) {
			encodeCheck(t, e = e || "utf-8");
			return o.getInstance().encodeSync(t, e)
		}
		;
		return o
	}(NativeObject);
	TextCodec._instance = null;
	var bytesSym = Symbol("bytes");
	function getBytes(t) {
		return t[bytesSym]
	}
	function setBytes(t, e) {
		return t[bytesSym] = e
	}
	function createNativeCallback(t, e, n) {
		return function() {
			try {
				t.apply(e || global, n || arguments)
			} catch (t) {}
		}
	}
	var Blob$1 = function() {
		function o(t, e) {
			void 0 === t && (t = []);
			void 0 === e && (e = {});
			if (Object.getPrototypeOf(t) !== Array.prototype)
				throw new TypeError("Argument 1 of Blob.constructor can't be converted to a sequence.");
			if (Object.getPrototypeOf(e) !== Object.prototype)
				throw new TypeError("Argument 2 of Blob.constructor can't be converted to a dictionary.");
			setBytes(this, join(t));
			Object.defineProperty(this, "type", {
				value: "type"in e ? e.type + "" : ""
			})
		}
		var t = o.prototype;
		t.slice = function(t, e, n) {
			var i = getBytes(this);
			i = i.slice(t || 0, e || i.byteLength);
			var r = new o([],{
				type: n = n || this.type
			});
			setBytes(r, i);
			return r
		}
		;
		t.arrayBuffer = function() {
			return Promise.resolve(getBytes(this).slice(0))
		}
		;
		t.text = function() {
			return Promise.resolve(TextCodec.decode(getBytes(this)))
		}
		;
		_createClass(o, [{
			key: "size",
			get: function() {
				return getBytes(this).byteLength
			}
		}]);
		return o
	}();
	Object.defineProperty(Blob$1.prototype, "type", {
		value: ""
	});
	Blob$1.prototype[Symbol.toStringTag] = "Blob";
	function join(t) {
		for (var e = 0, n = [], i = 0; i < t.length; i++) {
			n[i] = partToChunk(t[i]);
			e += n[i].byteLength
		}
		var r = new Uint8Array(e)
		  , o = 0;
		for (i = 0; i < n.length; i++) {
			r.set(n[i], o);
			o += n[i].byteLength
		}
		return r.buffer
	}
	function partToChunk(t) {
		return t instanceof ArrayBuffer ? new Uint8Array(t) : ArrayBuffer.isView(t) ? new Uint8Array(t.buffer) : t instanceof Blob$1 ? new Uint8Array(getBytes(t)) : new Uint8Array(TextCodec.encodeSync(t + ""))
	}
	function _create(e) {
		var t = getOptions(arguments);
		if (e instanceof Blob$1)
			return load(t, function(t) {
				return t.loadEncodedImage(getBytes(e))
			});
		if (e instanceof ImageData)
			return load(t, function(t) {
				return t.loadImageData(e.data, e.width, e.height)
			});
		if (e instanceof ImageBitmap)
			return e.source.isDestroyed() ? Promise.reject(new TypeError("Can not create ImageBitmap from another closed ImageBitmap")) : load(t, function(t) {
				return t.loadImageBitmap(e)
			});
		if (e instanceof CanvasElement)
			return e.isDestroyed() ? Promise.reject(new TypeError("Can not create ImageBitmap from a destroyd Canvas")) : load(t, function(t) {
				return t.loadCanvas(e)
			});
		throw new TypeError("Argument 1 of createImageBitmap could not be converted to any of: Blob, ImageData, ImageBitmap, Canvas.")
	}
	Object.assign(global, {
		createImageBitmap: _create
	});
	var ImageBitmap = function() {
		t.createImageBitmap = function(t) {
			return _create(t)
		}
		;
		function t(t, e) {
			if (!(t instanceof _ImageBitmap))
				throw new TypeError("ImageBitmap Illegal constructor");
			this.ref = t.ref;
			this.source = t;
			Object.defineProperty(this, "width", {
				value: e.width
			});
			Object.defineProperty(this, "height", {
				value: e.height
			})
		}
		t.prototype.close = function() {
			this.source.isDestroyed() || this.source.destroy()
		}
		;
		return t
	}();
	Object.defineProperty(ImageBitmap.prototype, "width", {
		value: 0
	});
	Object.defineProperty(ImageBitmap.prototype, "height", {
		value: 0
	});
	ImageBitmap.prototype[Symbol.toStringTag] = "ImageBitmap";
	var _ImageBitmap = function(n) {
		_inheritsLoose(t, n);
		function t(t) {
			var e;
			(e = n.call(this) || this).create(e.ref, "$imagebitmap", t);
			return e
		}
		var e = t.prototype;
		e.loadEncodedImage = function(n) {
			var i = this;
			return new Promise(function(t, e) {
				return i.callNative("loadEncodedImage", {
					image: n,
					onSuccess: t,
					onError: e
				})
			}
			)
		}
		;
		e.loadImageData = function(n, i, r) {
			var o = this;
			return new Promise(function(t, e) {
				return o.callNative("loadImageData", {
					image: n,
					width: i,
					height: r,
					onSuccess: t,
					onError: e
				})
			}
			)
		}
		;
		e.loadImageBitmap = function(n) {
			var i = this;
			return new Promise(function(t, e) {
				return i.callNative("loadImageBitmap", {
					image: n.ref,
					onSuccess: t,
					onError: e
				})
			}
			)
		}
		;
		e.loadCanvas = function(n) {
			var i = this;
			return new Promise(function(t, e) {
				n._context && n._context._graphics.flush();
				i.callNative("loadCanvas", {
					image: n.ref,
					onSuccess: t,
					onError: e
				})
			}
			)
		}
		;
		return t
	}(NativeObject);
	function getOptions(t) {
		if (!~[1, 2, 5, 6].indexOf(t.length))
			throw new TypeError(t.length + " is not a valid argument count for any overload of createImageBitmap.");
		var e = {};
		if (2 === t.length && null != t[1]) {
			if (!(t[1]instanceof Object))
				throw new TypeError("Argument 2 of createImageBitmap is not an object.");
			Object.assign(e, t[1])
		} else if (5 <= t.length) {
			if (6 === t.length && null != t[5] && !(t[5]instanceof Object))
				throw new TypeError("Argument 6 of createImageBitmap is not an object.");
			e.rect = {
				sx: t[1],
				sy: t[2],
				sw: t[3],
				sh: t[4]
			};
			Object.assign(e, t[5] || {})
		}
		return e
	}
	function load(t, e) {
		var n = new _ImageBitmap(t);
		return e(n).then(function(t) {
			var e = new ImageBitmap(n,t);
			Object.defineProperty(e, "wrapper", {
				value: e
			});
			return e
		}).catch(function(t) {
			n.dispose();
			throw Error(t)
		})
	}
	var CanvasContext = function() {
		function t(t) {
			this.ref = t.ref;
			this._graphics = t;
			this._state = createState();
			this._savedStates = [];
			this.canvas = {
				width: 0,
				height: 0,
				style: {}
			};
			for (var e in properties)
				defineProperty(this, e)
		}
		var e = t.prototype;
		e._init = function(t, e) {
			this.canvas.width = t;
			this.canvas.height = e;
			this._graphics.init({
				width: t,
				height: e
			})
		}
		;
		e.measureText = function(t) {
			return {
				width: 5 * t.length + 5
			}
		}
		;
		e.getImageData = function(t, e, n, i) {
			this._graphics.flush();
			var r = this._graphics.getImageData(t, e, n, i);
			return new ImageData(r,n,i)
		}
		;
		e.putImageData = function(t, e, n, i, r, o, a) {
			this._graphics.flush();
			this._graphics.putImageData(t, e, n, i, r, o, a)
		}
		;
		e.createImageData = function(t, e) {
			if (t instanceof ImageData) {
				var n = t;
				t = n.width;
				e = n.height
			}
			return new ImageData(t,e)
		}
		;
		e.flush = function() {
			this._graphics.flush()
		}
		;
		e.draw = function() {
			this._graphics.flush()
		}
		;
		e.destroy = function() {
			this._graphics.dispose()
		}
		;
		return t
	}();
	CanvasContext.getContext = function(t) {
		if (!t._graphics) {
			var e = new Graphics({
				parent: t.ref
			});
			t._graphics = e
		}
		if (!t._context) {
			t._context = new CanvasContext(t._graphics);
			t._context._init(t.width, t.height)
		}
		return t._context
	}
	;
	CanvasContext.prototype[Symbol.toStringTag] = "CanvasRenderingContext2D";
	defineMethod("save", function() {
		this._savedStates.push(Object.assign({}, this._state))
	});
	defineMethod("restore", function() {
		this._state = this._savedStates.pop() || this._state
	});
	defineMethod("beginPath");
	defineMethod("closePath");
	defineMethod("lineTo", function(t, e) {
		this._graphics.addDouble(t, e)
	});
	defineMethod("moveTo", function(t, e) {
		this._graphics.addDouble(t, e)
	});
	defineMethod("bezierCurveTo", function(t, e, n, i, r, o) {
		this._graphics.addDouble(t, e, n, i, r, o)
	});
	defineMethod("quadraticCurveTo", function(t, e, n, i) {
		this._graphics.addDouble(t, e, n, i)
	});
	defineMethod("rect", function(t, e, n, i) {
		this._graphics.addDouble(t, e, n, i)
	});
	defineMethod("arc", function(t, e, n, i, r, o) {
		this._graphics.addDouble(t, e, n, i, r);
		this._graphics.addBoolean(!!o)
	});
	defineMethod("arcTo", function(t, e, n, i, r) {
		this._graphics.addDouble(t, e, n, i, r)
	});
	defineMethod("scale", function(t, e) {
		this._graphics.addDouble(t, e)
	});
	defineMethod("rotate", function(t) {
		this._graphics.addDouble(t)
	});
	defineMethod("translate", function(t, e) {
		this._graphics.addDouble(t, e)
	});
	defineMethod("transform", function(t, e, n, i, r, o) {
		this._graphics.addDouble(t, e, n, i, r, o)
	});
	defineMethod("setTransform", function(t, e, n, i, r, o) {
		this._graphics.addDouble(t, e, n, i, r, o)
	});
	defineMethod("clearRect", function(t, e, n, i) {
		this._graphics.addDouble(t, e, n, i)
	});
	defineMethod("fillRect", function(t, e, n, i) {
		this._graphics.addDouble(t, e, n, i)
	});
	defineMethod("strokeRect", function(t, e, n, i) {
		this._graphics.addDouble(t, e, n, i)
	});
	defineMethod("fillText", function(t, e, n) {
		this._graphics.addString(t);
		this._graphics.addBoolean(!1, !1, !1);
		this._graphics.addDouble(e, n)
	});
	defineMethod("strokeText", function(t, e, n) {
		this._graphics.addString(t);
		this._graphics.addBoolean(!1, !1, !1);
		this._graphics.addDouble(e, n)
	});
	defineMethod("fill");
	defineMethod("stroke");
	defineMethod("drawImage", function(t, e, n, i, r, o, a, s, c) {
		if (!(t.ref || t instanceof ImageBitmap))
			throw new TypeError("First argument of CanvasContext.drawImage must be of type ImageBitmap or ImageElement.");
		this._graphics.addString(t.ref);
		if (9 === arguments.length)
			this._graphics.addDouble(e, n, i, r, o, a, s, c);
		else if (5 === arguments.length)
			this._graphics.addDouble(0, 0, t.width, t.height, e, n, i, r);
		else {
			if (3 !== arguments.length)
				throw new TypeError(arguments.length + " is not a valid argument count for any overload of Canvas.drawImage.");
			this._graphics.addDouble(0, 0, t.width, t.height, e, n, t.width, t.height)
		}
	});
	var properties = {
		lineWidth: {
			init: 1,
			encode: function(t) {
				if (!isNaN(t) && 0 < t)
					return t;
				throw Error("Invalid value " + t)
			},
			decode: passThrough,
			addOperations: function(t) {
				this._graphics.addDouble(t)
			}
		},
		lineCap: {
			init: "butt",
			values: toObject(["butt", "round", "square"]),
			encode: checkValue,
			decode: passThrough,
			addOperations: function(t) {
				this._graphics.addString(t)
			}
		},
		lineJoin: {
			init: "miter",
			values: toObject(["bevel", "miter", "round"]),
			encode: checkValue,
			decode: passThrough,
			addOperations: function(t) {
				this._graphics.addString(t)
			}
		},
		fillStyle: {
			init: "#000",
			encode: passThrough,
			decode: passThrough,
			addOperations: function(t) {
				this._graphics.addString(t)
			}
		},
		strokeStyle: {
			init: "#000",
			encode: passThrough,
			decode: passThrough,
			addOperations: function(t) {
				this._graphics.addString(t)
			}
		},
		textAlign: {
			init: "start",
			values: toObject(["start", "end", "left", "right", "center"]),
			encode: checkValue,
			decode: passThrough,
			addOperations: function(t) {
				this._graphics.addString(t)
			}
		},
		textBaseline: {
			init: "alphabetic",
			values: toObject(["top", "hanging", "middle", "alphabetic", "ideographic", "bottom"]),
			encode: checkValue,
			decode: passThrough,
			addOperations: function(t) {
				this._graphics.addString(t)
			}
		},
		font: {
			init: "normal 12px normal sans-serif",
			encode: passThrough,
			decode: passThrough,
			addOperations: function(t) {
				this._graphics.addString(t)
			}
		}
	};
	function passThrough(t) {
		return t
	}
	function checkValue(t) {
		if (t in this.values)
			return t;
		throw Error("Invalid value " + t)
	}
	function toObject(t) {
		var e = {};
		t.forEach(function(t) {
			e[t] = !0
		});
		return e
	}
	function createState() {
		var t = {};
		for (var e in properties)
			t[e] = properties[e].init;
		return t
	}
	function defineMethod(e, t) {
		CanvasContext.prototype[e] = inIOS ? function(t) {
			if (0 < arguments.length) {
				t.ref && (t = t.ref)
			}
			this._graphics.addOperation(e, Array.prototype.slice.call(arguments))
		}
		: function() {
			this._graphics.addOperation(e);
			t && t.apply(this, arguments)
		}
	}
	function defineProperty(t, n) {
		var i = properties[n];
		Object.defineProperty(t, n, {
			get: function() {
				return i.decode(t._state[n])
			},
			set: function(e) {
				try {
					t._state[n] = i.encode(e);
					if (inIOS) {
						t._graphics.addOperation(n, [t._state[n]]);
						return
					}
					t._graphics.addOperation(n);
					i.addOperations.call(t, t._state[n])
				} catch (t) {
					warn("Unsupported value for " + n + ": " + e)
				}
			}
		})
	}
	var MIME_TYPES = {
		"image/png": "image/png",
		"image/jpeg": "image/jpeg",
		"image/webp": "image/webp"
	}
	  , TYPE_QUALITY = {
		"image/png": 1,
		"image/jpeg": .92,
		"image/webp": .8
	}
	  , CanvasElement = function(i) {
		_inheritsLoose(n, i);
		function n(t, e) {
			var n;
			(n = i.call(this, t, e) || this)._canvasw = 300;
			n._canvash = 150;
			return n
		}
		n.$create = function(t, e) {
			return new n(t,e)
		}
		;
		var t = n.prototype;
		t.getContext = function(t, e) {
			void 0 === e && (e = {});
			return "2d" === t || "webgl" === t || "webgl2" === t ? CanvasContext.getContext(this, e) : null
		}
		;
		t.flush = function() {
			this._context && this._context.flush()
		}
		;
		t.createImage = function() {}
		;
		t.createImageData = function(t, e) {
			if (t instanceof ImageData) {
				var n = t;
				t = n.width;
				e = n.height
			}
			return this._context.createImageData(t, e)
		}
		;
		t.toDataURL = function(t, e) {
			this._context && this._context.flush();
			var n = MIME_TYPES["string" == typeof t ? t : ""] || MIME_TYPES["image/png"];
			return this.callNative("toDataURL", {
				mimeType: n,
				quality: "number" == typeof e && !isNaN(e) && 0 <= e && e <= 1 ? e : TYPE_QUALITY[n]
			})
		}
		;
		t.toBlob = function(i, t, e) {
			if (0 === arguments.length)
				throw new TypeError("Canvas.toBlob requires at least 1 argument, but only 0 were passed");
			if (!(i instanceof Function))
				throw new TypeError("Argument 1 of Canvas.toBlob is not a function");
			this._context && this._context.flush();
			var n = "number" == typeof e && !isNaN(e) && 0 <= e && e <= 1
			  , r = MIME_TYPES["string" == typeof t ? t : ""] || MIME_TYPES["image/png"];
			this.callNative("toBlob", {
				onSuccess: createNativeCallback(function(t, e) {
					if (t) {
						var n = new Blob$1([],{
							type: e
						});
						setBytes(n, t);
						i(n)
					} else
						i(null)
				}),
				mimeType: r,
				quality: n ? e : TYPE_QUALITY[r]
			})
		}
		;
		t.destroy = function() {
			i.prototype.destroy.call(this);
			this._context && this._context.destroy()
		}
		;
		_createClass(n, [{
			key: "width",
			get: function() {
				var t = this.getter("canvasw");
				t && (this._canvasw = t);
				return this._canvasw
			},
			set: function(t) {
				if (this._canvasw != t) {
					this._canvasw = t;
					this.setter("canvasw", t)
				}
			}
		}, {
			key: "height",
			get: function() {
				var t = this.getter("canvash");
				t && (this._canvash = t);
				return this._canvash
			},
			set: function(t) {
				if (this._canvash != t) {
					this._canvash = t;
					this.setter("canvash", t)
				}
			}
		}]);
		return n
	}(Element);
	CanvasElement.prototype[Symbol.toStringTag] = "CanvasElement";
	var ImageElement = function(n) {
		_inheritsLoose(i, n);
		function i(t, e) {
			return n.call(this, t, e) || this
		}
		i.$create = function(t, e) {
			return new i(t,e)
		}
		;
		_createClass(i, [{
			key: "width",
			get: function() {
				return this.getter("width")
			},
			set: function(t) {
				this.setter("width", t)
			}
		}, {
			key: "height",
			get: function() {
				return this.getter("height")
			},
			set: function(t) {
				this.setter("height", t)
			}
		}]);
		return i
	}(Element);
	ImageElement.prototype[Symbol.toStringTag] = "ImageElement";
	var SUPPORT_TAGS = {
		view: {
			tag: "div"
		},
		text: {
			tag: "span"
		},
		image: {
			tag: "img"
		},
		"list-view": {
			tag: "ul"
		},
		cell: {
			tag: "li"
		},
		"list-header": {
			tag: "div"
		},
		"list-footer": {
			tag: "div"
		},
		refresh: {
			tag: "div"
		},
		frame: {
			tag: "iframe"
		},
		"safe-area": {
			tag: "div"
		},
		"scroll-view": {
			tag: "div",
			mini: "scroll-view"
		},
		swiper: {
			tag: "div",
			mini: "swiper"
		},
		"swiper-item": {
			tag: "div",
			mini: "swiper-item"
		},
		switch: {
			tag: "input",
			type: "checkbox",
			mini: "switch"
		},
		slider: {
			tag: "input",
			type: "range",
			mini: "slider"
		},
		checkbox: {
			tag: "input",
			type: "checkbox",
			mini: "wx-checkbox"
		},
		"checkbox-group": {
			tag: "div",
			mini: "wx-checkbox-group"
		},
		radio: {
			tag: "input",
			type: "radio",
			mini: "wx-radio"
		},
		"radio-group": {
			tag: "div",
			mini: "wx-radio-group"
		}
	}
	  , RECYCLERS_CELLS = {
		cell: !0,
		"swiper-item": !0
	};
	function isCell(t) {
		return RECYCLERS_CELLS[t]
	}
	function tagMaps(t) {
		var e = SUPPORT_TAGS[t];
		return inMiniapp && e && e.mini ? {
			tag: e.mini
		} : e || {
			tag: t
		}
	}
	var WebDocument = function() {
		function t() {}
		t.newDefault = function() {
			return new t
		}
		;
		var e = t.prototype;
		e.createElementNS = function(t, e, n) {
			return this.createElement(e, n)
		}
		;
		e.createElement = function(t) {
			var e = tagMaps(t)
			  , n = document.createElement(e.tag);
			e.type && (n.type = e.type);
			return n
		}
		;
		e.createTextNode = function(t) {
			return document.createTextNode(t)
		}
		;
		e.getElementById = function(t) {
			return document.getElementById(t)
		}
		;
		e.getElementsByTagName = function(t) {
			t = tagMaps(t);
			return document.getElementsByTagName(t)
		}
		;
		e.getElementsByClassName = function(t) {
			return document.getElementsByClassName(t)
		}
		;
		e.querySelector = function(t) {
			return document.querySelector(t)
		}
		;
		e.querySelectorAll = function(t) {
			return document.querySelectorAll(t)
		}
		;
		e.dispatchEvent = function() {}
		;
		e.$createStyle = function(t, e) {
			if (e) {
				e = e.toLowerCase();
				var n = this.getElementById(e);
				n && n.parentNode === this.head && this.head.removeChild(n)
			}
			var i = this.createElement("style");
			this.head.appendChild(i);
			i.setAttribute("type", "text/css");
			e && i.setAttribute("id", e);
			"object" == typeof t && (t = convertToCss(t));
			i.textContent = t
		}
		;
		e.$createLink = function(t) {
			var e = this.createElement("link");
			e.setAttribute("type", "text/css");
			e.setAttribute("rel", "stylesheet");
			e.href = t;
			this.head.appendChild(e)
		}
		;
		e.$addElement = function() {}
		;
		e.$removeElement = function() {}
		;
		e.$moveElement = function() {}
		;
		e.$setStyles = function() {}
		;
		e.$setAttrs = function() {}
		;
		e.$removeAttrs = function() {}
		;
		e.$addEvent = function() {}
		;
		e.$removeEvent = function() {}
		;
		e.$contentLoaded = function() {}
		;
		e.$addKeyframe = function() {}
		;
		e.$addKeyframeMap = function() {}
		;
		e.$scrollTo = function() {}
		;
		e.$getComputedStyle = function() {}
		;
		_createClass(t, [{
			key: "body",
			get: function() {
				return document.body
			}
		}, {
			key: "head",
			get: function() {
				return document.head
			}
		}, {
			key: "location",
			get: function() {
				return document.location
			},
			set: function(t) {
				document.location = t
			}
		}]);
		return t
	}()
	  , JS_TO_CSS = {};
	function convertToCss(t) {
		var e = "";
		for (var n in t) {
			var i = t[n];
			if (i) {
				e += n + " {";
				for (var r in i) {
					e += JS_TO_CSS[r] || (JS_TO_CSS[r] = r.replace(/([A-Z])/g, "-$1").toLowerCase());
					e += ": ";
					e += i[r];
					e += ";"
				}
				e += "}"
			}
		}
		return e || void 0
	}
	var EXTEND_MAP = {
		image: ImageElement,
		img: ImageElement,
		"list-view": RecyclerElement,
		"grid-view": RecyclerElement,
		swiper: RecyclerElement,
		canvas: CanvasElement
	}
	  , Document = function() {
		function t() {
			this.nodeType = 9;
			this.keyframes = {};
			this._selectors = new QuerySelectors(this);
			this._readyState = 0;
			this._head = null;
			this._body = null;
			this._location = null;
			this._nodeMap = {};
			this.childNodes = [];
			this.$rootvm = null;
			this.$client = $NativePipe;
			this.webDocument = new WebDocument
		}
		t.newDefault = function() {
			return new t
		}
		;
		var e = t.prototype;
		e.createElementNS = function(t, e, n) {
			return inBrowser ? this.webDocument.createElementNS(t, e) : this.createElement(e, n)
		}
		;
		e.createElement = function(t, e) {
			var n;
			if (inBrowser)
				return this.webDocument.createElement(t);
			n = (EXTEND_MAP[t] || Element).$create(t, this);
			this.$client.create(n.ref, t, e || {});
			return n
		}
		;
		e.createTextNode = function(t) {
			return inBrowser ? this.webDocument.createTextNode(t) : new TextNode(t,this)
		}
		;
		e.getElementById = function(t) {
			return inBrowser ? this.webDocument.getElementById(t) : this._selectors.byId(t)
		}
		;
		e.getElementsByTagName = function(t) {
			return inBrowser ? this.webDocument.getElementsByTagName(t) : this._selectors.byTagName(t)
		}
		;
		e.getElementsByClassName = function(t) {
			return inBrowser ? this.webDocument.getElementsByClassName(t) : this._selectors.byClassName(t)
		}
		;
		e.querySelector = function(t) {
			return "body" === t ? inBrowser ? this.webDocument.body : this.body : inBrowser ? this.webDocument.querySelector(t) : this._selectors.selector(t)
		}
		;
		e.querySelectorAll = function(t) {
			return "body" === t ? inBrowser ? [this.webDocument.body] : [this.body] : inBrowser ? this.webDocument.querySelectorAll(t) : this._selectors.selectorAll(t)
		}
		;
		e.createEvent = function() {}
		;
		e.dispatchEvent = function(t, e, n) {
			if (arguments.length < 1)
				throw Error("Not enough arguments to dispatchEvent");
			(n = n || {}).type || (n.type = e);
			if (t) {
				var i = this.$getObject(t);
				return i ? i.dispatchEvent(n) : null
			}
			if (this.$rootvm) {
				var r = this.$rootvm
				  , o = r[e];
				if (o)
					return o.call(r, n) || !0
			}
		}
		;
		e.destroy = function(t) {
			t && this.$removeObject(t);
			this.$client.destroy(t = t || -1)
		}
		;
		e.$createStyle = function(t, e) {
			if (!inBrowser) {
				if (e) {
					e = e.toLowerCase();
					var n = this.getElementById(e)
					  , i = this.head;
					n && n.parentNode === i && i.removeChild(n)
				}
				var r = new Element("style",this);
				this.head.appendChild(r);
				e && r.setAttribute("id", e, !0);
				var o = {
					textContent: r.textContent = t
				};
				e && (o.scopeId = e);
				this.$client.create(r.ref, "style", o);
				return r
			}
			this.webDocument.$createStyle(t, e)
		}
		;
		e.$createLink = function(t) {
			if (!inBrowser) {
				var e = new Element("style",this);
				this.head.appendChild(e);
				var n = {
					href: e.href = t
				};
				this.$client.create(e.ref, "style", n);
				return e
			}
			this.webDocument.$createLink(t)
		}
		;
		e.$addElement = function(t, e, n, i) {
			this.$registerNode(n);
			var r = {
				parent: e,
				index: i
			};
			isCell(n.nodeName) && (r.elements = n.toJSON());
			this.$client.set(t, r);
			if (e === this.body.ref && 3 != this._readyState) {
				this._readyState = 3;
				this.$contentLoaded(n)
			}
		}
		;
		e.$moveElement = function(t, e, n) {
			this.$client.set(t, {
				zIndex: n
			})
		}
		;
		e.$removeElement = function(t, e) {
			this.$unregisterNode(e);
			this.$client.set(t, {
				parent: "$remove"
			})
		}
		;
		e.$setStyles = function(t, e) {
			this.$client.set(t, {
				style: e || {}
			})
		}
		;
		e.$setAttrs = function(t, e) {
			this.$client.set(t, e || {})
		}
		;
		e.$getAttrs = function(t, e) {
			var n;
			if (n = "string" == typeof t ? this.getElementById(t) : t)
				return this.$client.get(n.ref, e)
		}
		;
		e.$removeAttrs = function() {}
		;
		e.$addEvent = function(t, e) {
			this.$client.listen(t, e, !0)
		}
		;
		e.$removeEvent = function(t, e) {
			this.$client.listen(t, e, !1)
		}
		;
		e.$contentLoaded = function() {
			this.$client.contentLoaded({})
		}
		;
		e.$callMethod = function(t, e, n, i) {
			if (!t)
				return null;
			var r;
			return (r = "string" == typeof t ? this.getElementById(t) : t) ? this.$client.call(r.ref, e, n, i) : void 0
		}
		;
		e.$registerNode = function(t) {
			this.$addObject(t);
			this.childNodes.push(t)
		}
		;
		e.$unregisterNode = function(t) {
			var e = this;
			domWalk(t, function(t) {
				e.$removeObject(t);
				e.childNodes.remove(t)
			})
		}
		;
		e.$addObject = function(t) {
			this._nodeMap[t.ref] = t
		}
		;
		e.$getObject = function(t) {
			return this._nodeMap[t]
		}
		;
		e.$removeObject = function(t) {
			delete this._nodeMap[t.ref ? t.ref : t]
		}
		;
		e.$_createCell = function(t, e, n, i) {
			if (arguments.length < 1)
				throw Error("Not enough arguments to $_createCell");
			if (t) {
				var r = this.$getObject(t);
				if (r && r.$_createCell) {
					var o = r.$_createCell(e, n, i);
					if (o) {
						this.$registerNode(o);
						return o.ref
					}
				}
			}
			throw Error("Not found jsId to $_createCell or cur element is unsupport $_createCell.")
		}
		;
		e.$_updateCell = function(t, e, n, i, r) {
			if (arguments.length < 2)
				throw Error("Not enough arguments to $_updateCell.");
			if (t && e) {
				var o = this.$getObject(t)
				  , a = this.$getObject(e);
				if (o && o.$_updateCell && a) {
					var s = o.$_updateCell(a, n, i, r);
					return s ? s.ref : null
				}
			}
			throw Error("Not found jsId to $_updateCell or cur element is unsupport $_updateCell.")
		}
		;
		e.$_callMethod = function(t, e, n) {
			var i = this.$getObject(t);
			return i && i[e] && i[e](n)
		}
		;
		e.$addKeyframe = function(t, e) {
			var n = {
				amin: e = e || {}
			};
			this.$client.set(t = t || -1, n)
		}
		;
		e.$addKeyframeMap = function(t, e) {
			var n = {
				amin: e = e || {}
			};
			this.$client.set(t = t || -1, n)
		}
		;
		e.$scrollTo = function() {}
		;
		e.$getComputedStyle = function() {}
		;
		_createClass(t, [{
			key: "head",
			get: function() {
				if (inBrowser)
					return this.webDocument.head;
				if (!this._head) {
					this._head = new HTMLHeadElement(this);
					this.$registerNode(this._head)
				}
				return this._head
			}
		}, {
			key: "body",
			get: function() {
				if (inBrowser)
					return this.webDocument.body;
				if (!this._body) {
					this._body = new HTMLBodyElement(this);
					this.$registerNode(this._body);
					this.$client.create(this._body.ref, "body", {})
				}
				return this._body
			}
		}, {
			key: "location",
			get: function() {
				if (inBrowser)
					return this.webDocument.location;
				this._location || (this._location = {
					href: ""
				});
				return this._location
			},
			set: function(t) {
				inBrowser ? this.webDocument.location = t : this._location = t
			}
		}]);
		return t
	}()
	  , Document$1 = Document.newDefault()
	  , Apivm = function() {
		function t() {
			this._started = !1
		}
		var e = t.prototype;
		e.$_dispatchGlobalEvent = function(t, e) {
			var n = global[t];
			if (n) {
				return n.call(Document$1.$rootvm, e) || !0
			}
			return !1
		}
		;
		e.$_notify = function(t, e, n) {
			if (!t) {
				var i = this.$_dispatchGlobalEvent(e, n);
				if (i)
					return i
			}
			return Document$1.dispatchEvent(t, e, n)
		}
		;
		e.$_createCell = function(t, e, n, i) {
			return t ? Document$1.$_createCell(t, e, n, i) : null
		}
		;
		e.$_updateCell = function(t, e, n, i, r) {
			return t ? Document$1.$_updateCell(t, e, n, i, r) : null
		}
		;
		e.$_method = function(t, e, n) {
			if (!t || !e)
				return null;
			else
				return Document$1.$_callMethod(t, e, 3 <= arguments.length ? n : null)
		}
		;
		_createClass(t, [{
			key: "version",
			get: function() {
				return "3.0.0"
			}
		}, {
			key: "started",
			get: function() {
				return this._started
			}
		}]);
		return t
	}()
	  , vm = new Apivm;
	vm.$_start = function() {
		this._started = !0;
		return this.$_notify("", "apiready", "")
	}
	;
	var stack = [];
	function h(t, e) {
		var n, i, r, o, a = [];
		for (o = arguments.length; 2 < o--; )
			stack.push(arguments[o]);
		if (e && null != e.children) {
			stack.length || stack.push(e.children);
			delete e.children
		}
		for (; stack.length; )
			if ((i = stack.pop()) && void 0 !== i.pop)
				for (o = i.length; o--; )
					stack.push(i[o]);
			else {
				"boolean" == typeof i && (i = null);
				(r = "function" != typeof t) && (null == i ? i = "" : "number" == typeof i ? i += "" : "string" != typeof i && (r = !1));
				r && n ? a[a.length - 1] += i : 0 === a.length ? a = [i] : a.push(i);
				n = r
			}
		var s = new VNode(t,a,e);
		s.key = null == e ? void 0 : e.key;
		void 0 !== options$1.vnode && options$1.vnode(s);
		return s
	}
	function tag(e) {
		return function(t) {
			define(e, t)
		}
	}
	var hasOwn = {}.hasOwnProperty;
	function classNames() {
		for (var t = [], e = 0; e < arguments.length; e++) {
			var n = arguments[e];
			if (n) {
				var i = typeof n;
				if ("string" == i || "number" == i)
					t.push(n);
				else if (Array.isArray(n) && n.length) {
					var r = classNames.apply(null, n);
					r && t.push(r)
				} else if ("object" == i)
					for (var o in n)
						hasOwn.call(n, o) && n[o] && t.push(o)
			}
		}
		return t.join(" ")
	}
	function extractClass() {
		var t = Array.prototype.slice.call(arguments, 0)
		  , e = t[0]
		  , n = t.slice(1);
		if (e)
			if (e.class) {
				n.unshift(e.class);
				delete e.class
			} else if (e.className) {
				n.unshift(e.className);
				delete e.className
			}
		if (0 < n.length)
			return {
				class: classNames.apply(null, n)
			}
	}
	function getHost(t) {
		return t._host
	}
	function getRoot(t) {
		if (t) {
			var e = t._host;
			if (e)
				for (; e._host; )
					e = e._host;
			return e
		}
	}
	function extendDocument(t) {
		void 0 === t.document && (t.document = Document$1)
	}
	var Timer = function(n) {
		_inheritsLoose(t, n);
		function t(t) {
			var e;
			(e = n.call(this) || this).ref = t.ref;
			e.save();
			e.create(e.ref, "$timer", t);
			return e
		}
		var e = t.prototype;
		e.cancel = function() {
			this.call(this.ref, "cancel", {})
		}
		;
		e.start = function() {
			this.call(this.ref, "start", {})
		}
		;
		return t
	}(NativeObject);
	function extendWindowTimer(a) {
		if ("function" != typeof a.setTimeout) {
			var t = 0
			  , s = {};
			a.setTimeout = function(t, e) {
				if (arguments.length < 1)
					throw new TypeError("Not enough arguments to setTimeout");
				if ("function" != typeof t)
					throw new TypeError("Illegal argument to setTimeout: not a function");
				var n = Array.prototype.slice.call(arguments, 2);
				return i(t, adjustDelay(e), !1, n)
			}
			;
			a.setInterval = function(t, e) {
				if (arguments.length < 1)
					throw new TypeError("Not enough arguments to setInterval");
				if ("function" != typeof t)
					throw new TypeError("Illegal argument to setInterval: not a function");
				var n = Array.prototype.slice.call(arguments, 2);
				return i(t, adjustDelay(e), !0, n)
			}
			;
			a.clearTimeout = a.clearInterval = function(t) {
				var e = s[t];
				if (e) {
					e.cancel();
					e.dispose();
					delete s[t]
				}
			}
		}
		function i(e, n, i, r) {
			var o = "t" + t++;
			!function() {
				var t = new Timer({
					delay: n,
					repeat: i,
					ref: o
				});
				t.on("run", function() {
					e.apply(a, r);
					if (!i) {
						t.dispose();
						delete s[o]
					}
				});
				(s[o] = t).start()
			}();
			return o
		}
	}
	function adjustDelay(t) {
		return "number" == typeof t && isFinite(t) ? Math.max(0, Math.round(t)) : 0
	}
	global.api$__vm && global;
	var $document = Document$1
	  , $ = $document.querySelector.bind($document)
	  , vm$1 = Object.assign(vm, {
		h: h,
		_H: h,
		createElement: h,
		Component: Component,
		render: render,
		rerender: rerender,
		options: options$1,
		define: define,
		classNames: classNames,
		extractClass: extractClass,
		getHost: getHost,
		getRoot: getRoot,
		tag: tag,
		merge: merge,
		$document: $document,
		$: $,
		Fragment: Fragment,
		f: Fragment
	});
	Object.assign(global, {
		h: h,
		_H: h,
		createElement: h,
		Component: Component,
		render: render,
		define: define,
		classNames: classNames,
		extractClass: extractClass,
		getHost: getHost,
		getRoot: getRoot,
		tag: tag,
		merge: merge,
		options: options$1,
		$document: $document,
		$: $,
		Fragment: Fragment,
		f: Fragment
	});
	global.avm = global.apivm = global.api$__vm = global.apiweb$__vm = vm$1;
	extendDocument(global);
	extendWindowTimer(global);
	function t() {
		return r = {},
		n.m = i = [function(t, e, n) {
			t.exports = n(1)
		}
		, function(t, e, n) {
			function i(t) {
				var e = new a(t)
				  , n = o(a.prototype.request, e);
				return r.extend(n, a.prototype, e),
				r.extend(n, e),
				n
			}
			var r = n(2)
			  , o = n(3)
			  , a = n(4)
			  , s = n(22)
			  , c = i(n(10));
			c.Axios = a,
			c.create = function(t) {
				return i(s(c.defaults, t))
			}
			,
			c.Cancel = n(23),
			c.CancelToken = n(24),
			c.isCancel = n(9),
			c.all = function(t) {
				return Promise.all(t)
			}
			,
			c.spread = n(25),
			t.exports = c,
			t.exports.default = c
		}
		, function(t, e, n) {
			function o(t) {
				return "[object Array]" === u.call(t)
			}
			function i(t) {
				return void 0 === t
			}
			function r(t) {
				return null !== t && "object" == typeof t
			}
			function a(t) {
				return "[object Function]" === u.call(t)
			}
			function s(t, e) {
				if (null != t)
					if ("object" != typeof t && (t = [t]),
					o(t))
						for (var n = 0, i = t.length; n < i; n++)
							e.call(null, t[n], n, t);
					else
						for (var r in t)
							Object.prototype.hasOwnProperty.call(t, r) && e.call(null, t[r], r, t)
			}
			var c = n(3)
			  , u = Object.prototype.toString;
			t.exports = {
				isArray: o,
				isArrayBuffer: function(t) {
					return "[object ArrayBuffer]" === u.call(t)
				},
				isBuffer: function(t) {
					return null !== t && !i(t) && null !== t.constructor && !i(t.constructor) && "function" == typeof t.constructor.isBuffer && t.constructor.isBuffer(t)
				},
				isFormData: function(t) {
					return "undefined" != typeof FormData && t instanceof FormData
				},
				isArrayBufferView: function(t) {
					return "undefined" != typeof ArrayBuffer && ArrayBuffer.isView ? ArrayBuffer.isView(t) : t && t.buffer && t.buffer instanceof ArrayBuffer
				},
				isString: function(t) {
					return "string" == typeof t
				},
				isNumber: function(t) {
					return "number" == typeof t
				},
				isObject: r,
				isUndefined: i,
				isDate: function(t) {
					return "[object Date]" === u.call(t)
				},
				isFile: function(t) {
					return "[object File]" === u.call(t)
				},
				isBlob: function(t) {
					return "[object Blob]" === u.call(t)
				},
				isFunction: a,
				isStream: function(t) {
					return r(t) && a(t.pipe)
				},
				isURLSearchParams: function(t) {
					return "undefined" != typeof URLSearchParams && t instanceof URLSearchParams
				},
				isStandardBrowserEnv: function() {
					return ("undefined" == typeof navigator || "ReactNative" !== navigator.product && "NativeScript" !== navigator.product && "NS" !== navigator.product) && "undefined" != typeof window && "undefined" != typeof document
				},
				forEach: s,
				merge: function n() {
					function t(t, e) {
						"object" == typeof i[e] && "object" == typeof t ? i[e] = n(i[e], t) : i[e] = t
					}
					for (var i = {}, e = 0, r = arguments.length; e < r; e++)
						s(arguments[e], t);
					return i
				},
				deepMerge: function n() {
					function t(t, e) {
						"object" == typeof i[e] && "object" == typeof t ? i[e] = n(i[e], t) : i[e] = "object" == typeof t ? n({}, t) : t
					}
					for (var i = {}, e = 0, r = arguments.length; e < r; e++)
						s(arguments[e], t);
					return i
				},
				extend: function(n, t, i) {
					return s(t, function(t, e) {
						n[e] = i && "function" == typeof t ? c(t, i) : t
					}),
					n
				},
				trim: function(t) {
					return t.replace(/^\s*/, "").replace(/\s*$/, "")
				}
			}
		}
		, function(t) {
			t.exports = function(n, i) {
				return function() {
					for (var t = Array(arguments.length), e = 0; e < t.length; e++)
						t[e] = arguments[e];
					return n.apply(i, t)
				}
			}
		}
		, function(t, e, n) {
			function r(t) {
				this.defaults = t,
				this.interceptors = {
					request: new a,
					response: new a
				}
			}
			var o = n(2)
			  , i = n(5)
			  , a = n(6)
			  , s = n(7)
			  , c = n(22);
			r.prototype.request = function(t, e) {
				"string" == typeof t ? (t = e || {}).url = t : t = t || {},
				(t = c(this.defaults, t)).method ? t.method = t.method.toLowerCase() : this.defaults.method ? t.method = this.defaults.method.toLowerCase() : t.method = "get";
				var n = [s, void 0]
				  , i = Promise.resolve(t);
				for (this.interceptors.request.forEach(function(t) {
					n.unshift(t.fulfilled, t.rejected)
				}),
				this.interceptors.response.forEach(function(t) {
					n.push(t.fulfilled, t.rejected)
				}); n.length; )
					i = i.then(n.shift(), n.shift());
				return i
			}
			,
			r.prototype.getUri = function(t) {
				return t = c(this.defaults, t),
				i(t.url, t.params, t.paramsSerializer).replace(/^\?/, "")
			}
			,
			o.forEach(["delete", "get", "head", "options"], function(n) {
				r.prototype[n] = function(t, e) {
					return this.request(o.merge(e || {}, {
						method: n,
						url: t
					}))
				}
			}),
			o.forEach(["post", "put", "patch"], function(i) {
				r.prototype[i] = function(t, e, n) {
					return this.request(o.merge(n || {}, {
						method: i,
						url: t,
						data: e
					}))
				}
			}),
			t.exports = r
		}
		, function(t, e, n) {
			function a(t) {
				return encodeURIComponent(t).replace(/%40/gi, "@").replace(/%3A/gi, ":").replace(/%24/g, "$").replace(/%2C/gi, ",").replace(/%20/g, "+").replace(/%5B/gi, "[").replace(/%5D/gi, "]")
			}
			var s = n(2);
			t.exports = function(t, e, n) {
				if (!e)
					return t;
				var i;
				if (n)
					i = n(e);
				else if (s.isURLSearchParams(e))
					i = "" + e;
				else {
					var r = [];
					s.forEach(e, function(t, e) {
						null != t && (s.isArray(t) ? e += "[]" : t = [t],
						s.forEach(t, function(t) {
							s.isDate(t) ? t = t.toISOString() : s.isObject(t) && (t = JSON.stringify(t)),
							r.push(a(e) + "=" + a(t))
						}))
					}),
					i = r.join("&")
				}
				if (i) {
					var o = t.indexOf("#");
					-1 != o && (t = t.slice(0, o)),
					t += (!~t.indexOf("?") ? "?" : "&") + i
				}
				return t
			}
		}
		, function(t, e, n) {
			function i() {
				this.handlers = []
			}
			var r = n(2);
			i.prototype.use = function(t, e) {
				return this.handlers.push({
					fulfilled: t,
					rejected: e
				}),
				this.handlers.length - 1
			}
			,
			i.prototype.eject = function(t) {
				this.handlers[t] && (this.handlers[t] = null)
			}
			,
			i.prototype.forEach = function(e) {
				r.forEach(this.handlers, function(t) {
					null !== t && e(t)
				})
			}
			,
			t.exports = i
		}
		, function(t, e, n) {
			function i(t) {
				t.cancelToken && t.cancelToken.throwIfRequested()
			}
			var r = n(2)
			  , o = n(8)
			  , a = n(9)
			  , s = n(10);
			t.exports = function(e) {
				i(e),
				e.headers = e.headers || {},
				e.data = o(e.data, e.headers, e.transformRequest),
				e.headers = r.merge(e.headers.common || {}, e.headers[e.method] || {}, e.headers),
				r.forEach(["delete", "get", "head", "post", "put", "patch", "common"], function(t) {
					delete e.headers[t]
				});
				return (e.adapter || s.adapter)(e).then(function(t) {
					return i(e),
					t.data = o(t.data, t.headers, e.transformResponse),
					t
				}, function(t) {
					return a(t) || (i(e),
					t && t.response && (t.response.data = o(t.response.data, t.response.headers, e.transformResponse))),
					Promise.reject(t)
				})
			}
		}
		, function(t, e, n) {
			var i = n(2);
			t.exports = function(e, n, t) {
				return i.forEach(t, function(t) {
					e = t(e, n)
				}),
				e
			}
		}
		, function(t) {
			t.exports = function(t) {
				return !(!t || !t.__CANCEL__)
			}
		}
		, function(t, e, n) {
			function i(t, e) {
				!r.isUndefined(t) && r.isUndefined(t["Content-Type"]) && (t["Content-Type"] = e)
			}
			var r = n(2)
			  , o = n(11)
			  , a = {
				"Content-Type": "application/x-www-form-urlencoded"
			}
			  , s = {
				adapter: (("undefined" != typeof XMLHttpRequest || "undefined" != typeof process && "[object process]" === Object.prototype.toString.call(process)) && (c = n(12)),
				c),
				transformRequest: [function(t, e) {
					return o(e, "Accept"),
					o(e, "Content-Type"),
					r.isFormData(t) || r.isArrayBuffer(t) || r.isBuffer(t) || r.isStream(t) || r.isFile(t) || r.isBlob(t) ? t : r.isArrayBufferView(t) ? t.buffer : r.isURLSearchParams(t) ? (i(e, "application/x-www-form-urlencoded;charset=utf-8"),
					"" + t) : r.isObject(t) ? (i(e, "application/json;charset=utf-8"),
					JSON.stringify(t)) : t
				}
				],
				transformResponse: [function(t) {
					if ("string" == typeof t)
						try {
							t = JSON.parse(t)
						} catch (t) {}
					return t
				}
				],
				timeout: 0,
				xsrfCookieName: "XSRF-TOKEN",
				xsrfHeaderName: "X-XSRF-TOKEN",
				maxContentLength: -1,
				validateStatus: function(t) {
					return 200 <= t && t < 300
				},
				headers: {
					common: {
						Accept: "application/json, text/plain, */*"
					}
				}
			};
			var c;
			r.forEach(["delete", "get", "head"], function(t) {
				s.headers[t] = {}
			}),
			r.forEach(["post", "put", "patch"], function(t) {
				s.headers[t] = r.merge(a)
			}),
			t.exports = s
		}
		, function(t, e, n) {
			var r = n(2);
			t.exports = function(n, i) {
				r.forEach(n, function(t, e) {
					e !== i && e.toUpperCase() == i.toUpperCase() && (n[i] = t,
					delete n[e])
				})
			}
		}
		, function(t, e, u) {
			var f = u(2)
			  , h = u(13)
			  , l = u(5)
			  , d = u(16)
			  , v = u(19)
			  , p = u(20)
			  , m = u(14);
			t.exports = function(c) {
				return new Promise(function(e, n) {
					var i = c.data
					  , r = c.headers;
					f.isFormData(i) && delete r["Content-Type"];
					var o = new XMLHttpRequest;
					if (c.auth) {
						r.Authorization = "Basic " + btoa((c.auth.username || "") + ":" + (c.auth.password || ""))
					}
					var t = d(c.baseURL, c.url);
					if (o.open(c.method.toUpperCase(), l(t, c.params, c.paramsSerializer), !0),
					o.timeout = c.timeout,
					o.onreadystatechange = function() {
						if (o && 4 === o.readyState && (0 !== o.status || o.responseURL && !o.responseURL.indexOf("file:"))) {
							var t = "getAllResponseHeaders"in o ? v(o.getAllResponseHeaders()) : null;
							h(e, n, {
								data: c.responseType && "text" !== c.responseType ? o.response : o.responseText,
								status: o.status,
								statusText: o.statusText,
								headers: t,
								config: c,
								request: o
							}),
							o = null
						}
					}
					,
					o.onabort = function() {
						o && (n(m("Request aborted", c, "ECONNABORTED", o)),
						o = null)
					}
					,
					o.onerror = function() {
						n(m("Network Error", c, null, o)),
						o = null
					}
					,
					o.ontimeout = function() {
						var t = "timeout of " + c.timeout + "ms exceeded";
						c.timeoutErrorMessage && (t = c.timeoutErrorMessage),
						n(m(t, c, "ECONNABORTED", o)),
						o = null
					}
					,
					f.isStandardBrowserEnv()) {
						var a = u(21)
						  , s = (c.withCredentials || p(t)) && c.xsrfCookieName ? a.read(c.xsrfCookieName) : void 0;
						s && (r[c.xsrfHeaderName] = s)
					}
					if ("setRequestHeader"in o && f.forEach(r, function(t, e) {
						void 0 === i && "content-type" == e.toLowerCase() ? delete r[e] : o.setRequestHeader(e, t)
					}),
					f.isUndefined(c.withCredentials) || (o.withCredentials = !!c.withCredentials),
					c.responseType)
						try {
							o.responseType = c.responseType
						} catch (e) {
							if ("json" !== c.responseType)
								throw e
						}
					"function" == typeof c.onDownloadProgress && o.addEventListener("progress", c.onDownloadProgress),
					"function" == typeof c.onUploadProgress && o.upload && o.upload.addEventListener("progress", c.onUploadProgress),
					c.cancelToken && c.cancelToken.promise.then(function(t) {
						o && (o.abort(),
						n(t),
						o = null)
					}),
					void 0 === i && (i = null),
					o.send(i)
				}
				)
			}
		}
		, function(t, e, n) {
			var r = n(14);
			t.exports = function(t, e, n) {
				var i = n.config.validateStatus;
				!i || i(n.status) ? t(n) : e(r("Request failed with status code " + n.status, n.config, null, n.request, n))
			}
		}
		, function(t, e, n) {
			var o = n(15);
			t.exports = function(t, e, n, i, r) {
				return o(Error(t), e, n, i, r)
			}
		}
		, function(t) {
			t.exports = function(t, e, n, i, r) {
				return t.config = e,
				n && (t.code = n),
				t.request = i,
				t.response = r,
				t.isAxiosError = !0,
				t.toJSON = function() {
					return {
						message: this.message,
						name: this.name,
						description: this.description,
						number: this.number,
						fileName: this.fileName,
						lineNumber: this.lineNumber,
						columnNumber: this.columnNumber,
						stack: this.stack,
						config: this.config,
						code: this.code
					}
				}
				,
				t
			}
		}
		, function(t, e, n) {
			var i = n(17)
			  , r = n(18);
			t.exports = function(t, e) {
				return t && !i(e) ? r(t, e) : e
			}
		}
		, function(t) {
			t.exports = function(t) {
				return /^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(t)
			}
		}
		, function(t) {
			t.exports = function(t, e) {
				return e ? t.replace(/\/+$/, "") + "/" + e.replace(/^\/+/, "") : t
			}
		}
		, function(t, e, n) {
			var o = n(2)
			  , a = ["age", "authorization", "content-length", "content-type", "etag", "expires", "from", "host", "if-modified-since", "if-unmodified-since", "last-modified", "location", "max-forwards", "proxy-authorization", "referer", "retry-after", "user-agent"];
			t.exports = function(t) {
				var e, n, i, r = {};
				return t && o.forEach(t.split("\n"), function(t) {
					if (i = t.indexOf(":"),
					e = o.trim(t.substr(0, i)).toLowerCase(),
					n = o.trim(t.substr(1 + i)),
					e) {
						if (r[e] && ~a.indexOf(e))
							return;
						r[e] = "set-cookie" == e ? (r[e] ? r[e] : []).concat([n]) : r[e] ? r[e] + ", " + n : n
					}
				}),
				r
			}
		}
		, function(t, e, n) {
			var i = n(2);
			t.exports = i.isStandardBrowserEnv() ? (a = /(msie|trident)/i.test(navigator.userAgent),
			s = document.createElement("a"),
			o = r(window.location.href),
			function(t) {
				var e = i.isString(t) ? r(t) : t;
				return e.protocol === o.protocol && e.host === o.host
			}
			) : function() {
				return !0
			}
			;
			function r(t) {
				var e = t;
				return a && (s.setAttribute("href", e),
				e = s.href),
				s.setAttribute("href", e),
				{
					href: s.href,
					protocol: s.protocol ? s.protocol.replace(/:$/, "") : "",
					host: s.host,
					search: s.search ? s.search.replace(/^\?/, "") : "",
					hash: s.hash ? s.hash.replace(/^#/, "") : "",
					hostname: s.hostname,
					port: s.port,
					pathname: "/" == s.pathname[0] ? s.pathname : "/" + s.pathname
				}
			}
			var o, a, s
		}
		, function(t, e, n) {
			var s = n(2);
			t.exports = s.isStandardBrowserEnv() ? {
				write: function(t, e, n, i, r, o) {
					var a = [];
					a.push(t + "=" + encodeURIComponent(e)),
					s.isNumber(n) && a.push("expires=" + new Date(n).toGMTString()),
					s.isString(i) && a.push("path=" + i),
					s.isString(r) && a.push("domain=" + r),
					!0 === o && a.push("secure"),
					document.cookie = a.join("; ")
				},
				read: function(t) {
					var e = document.cookie.match(RegExp("(^|;\\s*)(" + t + ")=([^;]*)"));
					return e ? decodeURIComponent(e[3]) : null
				},
				remove: function(t) {
					this.write(t, "", Date.now() - 864e5)
				}
			} : {
				write: function() {},
				read: function() {
					return null
				},
				remove: function() {}
			}
		}
		, function(t, e, n) {
			var c = n(2);
			t.exports = function(e, n) {
				n = n || {};
				var i = {}
				  , t = ["url", "method", "params", "data"]
				  , r = ["headers", "auth", "proxy"]
				  , o = ["baseURL", "url", "transformRequest", "transformResponse", "paramsSerializer", "timeout", "withCredentials", "adapter", "responseType", "xsrfCookieName", "xsrfHeaderName", "onUploadProgress", "onDownloadProgress", "maxContentLength", "validateStatus", "maxRedirects", "httpAgent", "httpsAgent", "cancelToken", "socketPath"];
				c.forEach(t, function(t) {
					void 0 !== n[t] && (i[t] = n[t])
				}),
				c.forEach(r, function(t) {
					c.isObject(n[t]) ? i[t] = c.deepMerge(e[t], n[t]) : void 0 !== n[t] ? i[t] = n[t] : c.isObject(e[t]) ? i[t] = c.deepMerge(e[t]) : void 0 !== e[t] && (i[t] = e[t])
				}),
				c.forEach(o, function(t) {
					void 0 !== n[t] ? i[t] = n[t] : void 0 !== e[t] && (i[t] = e[t])
				});
				var a = t.concat(r).concat(o)
				  , s = Object.keys(n).filter(function(t) {
					return !~a.indexOf(t)
				});
				return c.forEach(s, function(t) {
					void 0 !== n[t] ? i[t] = n[t] : void 0 !== e[t] && (i[t] = e[t])
				}),
				i
			}
		}
		, function(t) {
			function e(t) {
				this.message = t
			}
			e.prototype.toString = function() {
				return "Cancel" + (this.message ? ": " + this.message : "")
			}
			,
			e.prototype.__CANCEL__ = !0,
			t.exports = e
		}
		, function(t, e, n) {
			function i(t) {
				if ("function" != typeof t)
					throw new TypeError("executor must be a function.");
				var e;
				this.promise = new Promise(function(t) {
					e = t
				}
				);
				var n = this;
				t(function(t) {
					n.reason || (n.reason = new r(t),
					e(n.reason))
				})
			}
			var r = n(23);
			i.prototype.throwIfRequested = function() {
				if (this.reason)
					throw this.reason
			}
			,
			i.source = function() {
				var e;
				return {
					token: new i(function(t) {
						e = t
					}
					),
					cancel: e
				}
			}
			,
			t.exports = i
		}
		, function(t) {
			t.exports = function(e) {
				return function(t) {
					return e.apply(null, t)
				}
			}
		}
		],
		n.c = r,
		n.p = "",
		n(0);
		function n(t) {
			if (r[t])
				return r[t].exports;
			var e = r[t] = {
				exports: {},
				id: t,
				loaded: !1
			};
			return i[t].call(e.exports, e, e.exports, n),
			e.loaded = !0,
			e.exports
		}
		var i, r
	}
	var axios = t()
	  , _shared = {
		pullStartY: null,
		pullMoveY: null,
		handlers: [],
		styleEl: null,
		events: null,
		dist: 0,
		state: "pending",
		timeout: null,
		distResisted: 0,
		supportsPassive: !1,
		supportsPointerEvents: "undefined" != typeof window && !!window.PointerEvent
	};
	try {
		window.addEventListener("test", null, {
			get passive() {
				_shared.supportsPassive = !0
			}
		})
	} catch (t) {}
	function setupDOM(t) {
		if (!t.ptrElement || !t.mainElement.parentNode.contains(t.ptrElement)) {
			var e = document.createElement("div");
			t.mainElement !== document.body ? t.mainElement.parentNode.insertBefore(e, t.mainElement) : document.body.insertBefore(e, document.body.firstChild);
			e.classList.add(t.classPrefix + "ptr");
			e.innerHTML = t.getMarkup().replace(/__PREFIX__/g, t.classPrefix);
			t.ptrElement = e;
			"function" == typeof t.onInit && t.onInit(t);
			if (!_shared.styleEl) {
				_shared.styleEl = document.createElement("style");
				_shared.styleEl.setAttribute("id", "pull-to-refresh-js-style");
				document.head.appendChild(_shared.styleEl)
			}
			_shared.styleEl.textContent = t.getStyles().replace(/__PREFIX__/g, t.classPrefix).replace(/\s+/g, " ")
		}
		return t
	}
	function onReset(t) {
		if (!t.ptrElement)
			return;
		t.ptrElement.classList.remove(t.classPrefix + "refresh");
		t.ptrElement.style[t.cssProp] = "0px";
		setTimeout(function() {
			if (t.ptrElement && t.ptrElement.parentNode) {
				t.ptrElement.parentNode.removeChild(t.ptrElement);
				t.ptrElement = null
			}
			_shared.state = "pending"
		}, t.refreshTimeout)
	}
	function update(t) {
		var e = t.ptrElement.querySelector("." + t.classPrefix + "icon")
		  , n = t.ptrElement.querySelector("." + t.classPrefix + "text");
		e && ("refreshing" === _shared.state ? e.innerHTML = t.iconRefreshing : e.innerHTML = t.iconArrow);
		t.onStateChange(_shared.state);
		if (n) {
			"releasing" === _shared.state && (n.innerHTML = t.instructionsReleaseToRefresh);
			"pulling" !== _shared.state && "pending" !== _shared.state || (n.innerHTML = t.instructionsPullToRefresh);
			"refreshing" === _shared.state && (n.innerHTML = t.instructionsRefreshing)
		}
	}
	var _ptr = {
		setupDOM: setupDOM,
		onReset: onReset,
		update: update
	}, _timeout, screenY = function(t) {
		return _shared.pointerEventsEnabled && _shared.supportsPointerEvents ? t.screenY : t.touches[0].screenY
	}, _setupEvents = function() {
		var n;
		function t(e) {
			var t = _shared.handlers.filter(function(t) {
				return t.contains(e.target)
			})[0];
			_shared.enable = !!t;
			if (t && "pending" === _shared.state) {
				n = _ptr.setupDOM(t);
				t.shouldPullToRefresh() && (_shared.pullStartY = screenY(e));
				clearTimeout(_shared.timeout);
				_ptr.update(t)
			}
		}
		function e(t) {
			if (n && n.ptrElement && _shared.enable) {
				_shared.pullStartY ? _shared.pullMoveY = screenY(t) : n.shouldPullToRefresh() && (_shared.pullStartY = screenY(t));
				if ("refreshing" !== _shared.state) {
					if ("pending" === _shared.state) {
						n.ptrElement.classList.add(n.classPrefix + "pull");
						_shared.state = "pulling";
						_ptr.update(n)
					}
					_shared.pullStartY && _shared.pullMoveY && (_shared.dist = _shared.pullMoveY - _shared.pullStartY);
					_shared.distExtra = _shared.dist - n.distIgnore;
					if (0 < _shared.distExtra) {
						t.cancelable && t.preventDefault();
						n.ptrElement.style[n.cssProp] = _shared.distResisted + "px";
						_shared.distResisted = n.resistanceFunction(_shared.distExtra / n.distThreshold) * Math.min(n.distMax, _shared.distExtra);
						if ("pulling" === _shared.state && n.distThreshold < _shared.distResisted) {
							n.ptrElement.classList.add(n.classPrefix + "release");
							_shared.state = "releasing";
							_ptr.update(n)
						}
						if ("releasing" === _shared.state && _shared.distResisted < n.distThreshold) {
							n.ptrElement.classList.remove(n.classPrefix + "release");
							_shared.state = "pulling";
							_ptr.update(n)
						}
					}
				} else
					t.cancelable && n.shouldPullToRefresh() && _shared.pullStartY < _shared.pullMoveY && t.preventDefault()
			}
		}
		function i() {
			if (n && n.ptrElement && _shared.enable) {
				clearTimeout(_timeout);
				_timeout = setTimeout(function() {
					n && n.ptrElement && "pending" === _shared.state && _ptr.onReset(n)
				}, 500);
				if ("releasing" === _shared.state && n.distThreshold < _shared.distResisted) {
					_shared.state = "refreshing";
					n.ptrElement.style[n.cssProp] = n.distReload + "px";
					n.ptrElement.classList.add(n.classPrefix + "refresh");
					_shared.timeout = setTimeout(function() {
						var t = n.onRefresh(function() {
							return _ptr.onReset(n)
						});
						t && "function" == typeof t.then && t.then(function() {
							return _ptr.onReset(n)
						});
						t || n.onRefresh.length || _ptr.onReset(n)
					}, n.refreshTimeout)
				} else {
					if ("refreshing" === _shared.state)
						return;
					n.ptrElement.style[n.cssProp] = "0px";
					_shared.state = "pending"
				}
				_ptr.update(n);
				n.ptrElement.classList.remove(n.classPrefix + "release");
				n.ptrElement.classList.remove(n.classPrefix + "pull");
				_shared.pullStartY = _shared.pullMoveY = null;
				_shared.dist = _shared.distResisted = 0
			}
		}
		function r() {
			n && n.mainElement.classList.toggle(n.classPrefix + "top", n.shouldPullToRefresh())
		}
		var o = _shared.supportsPassive ? {
			passive: _shared.passive || !1
		} : void 0;
		if (_shared.pointerEventsEnabled && _shared.supportsPointerEvents) {
			window.addEventListener("pointerup", i);
			window.addEventListener("pointerdown", t);
			window.addEventListener("pointermove", e, o)
		} else {
			window.addEventListener("touchend", i);
			window.addEventListener("touchstart", t);
			window.addEventListener("touchmove", e, o)
		}
		window.addEventListener("scroll", r);
		return {
			onTouchEnd: i,
			onTouchStart: t,
			onTouchMove: e,
			onScroll: r,
			destroy: function() {
				if (_shared.pointerEventsEnabled && _shared.supportsPointerEvents) {
					window.removeEventListener("pointerdown", t);
					window.removeEventListener("pointerup", i);
					window.removeEventListener("pointermove", e, o)
				} else {
					window.removeEventListener("touchstart", t);
					window.removeEventListener("touchend", i);
					window.removeEventListener("touchmove", e, o)
				}
				window.removeEventListener("scroll", r)
			}
		}
	}, _ptrMarkup = '\n<div class="__PREFIX__box">\n  <div class="__PREFIX__content">\n	<div class="__PREFIX__icon"></div>\n	<div class="__PREFIX__text"></div>\n  </div>\n</div>\n', _ptrStyles = "\n.__PREFIX__ptr {\n  box-shadow: inset 0 -3px 5px rgba(0, 0, 0, 0.12);\n  pointer-events: none;\n  font-size: 0.85em;\n  font-weight: bold;\n  top: 0;\n  height: 0;\n  transition: height 0.3s, min-height 0.3s;\n  text-align: center;\n  width: 100%;\n  overflow: hidden;\n  display: flex;\n  align-content: stretch;\n}\n\n.__PREFIX__box {\n  padding: 10px;\n  flex-basis: 100%;\n}\n\n.__PREFIX__pull {\n  transition: none;\n}\n\n.__PREFIX__text {\n  margin-top: .33em;\n  color: rgba(0, 0, 0, 0.3);\n}\n\n.__PREFIX__icon {\n  color: rgba(0, 0, 0, 0.3);\n  transition: transform .3s;\n}\n\n/*\nWhen at the top of the page, disable vertical overscroll so passive touch\nlisteners can take over.\n*/\n.__PREFIX__top {\n  touch-action: pan-x pan-down pinch-zoom;\n}\n\n.__PREFIX__release .__PREFIX__icon {\n  transform: rotate(180deg);\n}\n", _defaults = {
		distThreshold: 60,
		distMax: 80,
		distReload: 60,
		distIgnore: 0,
		mainElement: "body",
		triggerElement: "body",
		ptrElement: ".ptr",
		classPrefix: "ptr--",
		cssProp: "min-height",
		iconArrow: "&#8675;",
		iconRefreshing: "&hellip;",
		instructionsPullToRefresh: "Pull down to refresh",
		instructionsReleaseToRefresh: "Release to refresh",
		instructionsRefreshing: "Refreshing",
		refreshTimeout: 500,
		getMarkup: function() {
			return _ptrMarkup
		},
		getStyles: function() {
			return _ptrStyles
		},
		onInit: function() {},
		onStateChange: function() {},
		onRefresh: function() {
			return location.reload()
		},
		resistanceFunction: function(t) {
			return Math.min(1, t / 2.5)
		},
		shouldPullToRefresh: function() {
			return !this.mainElement.scrollTop
		}
	}, _methods = ["mainElement", "ptrElement", "triggerElement"], _setupHandler = function(e) {
		var n = {};
		Object.keys(_defaults).forEach(function(t) {
			n[t] = e[t] || _defaults[t]
		});
		n.refreshTimeout = "number" == typeof e.refreshTimeout ? e.refreshTimeout : _defaults.refreshTimeout;
		_methods.forEach(function(t) {
			"string" == typeof n[t] && (n[t] = document.querySelector(n[t]))
		});
		_shared.events || (_shared.events = _setupEvents());
		n.contains = function(t) {
			return n.triggerElement.contains(t)
		}
		;
		n.destroy = function() {
			clearTimeout(_shared.timeout);
			var t = _shared.handlers.indexOf(n);
			_shared.handlers.splice(t, 1)
		}
		;
		n.reset = function() {
			_ptr.onReset(n)
		}
		;
		return n
	}, index = {
		setPassiveMode: function(t) {
			_shared.passive = t
		},
		setPointerEventsMode: function(t) {
			_shared.pointerEventsEnabled = t
		},
		destroyAll: function() {
			if (_shared.events) {
				_shared.events.destroy();
				_shared.events = null
			}
			_shared.handlers.forEach(function(t) {
				t.destroy()
			})
		},
		init: function(t) {
			void 0 === t && (t = {});
			var e = _setupHandler(t);
			_shared.handlers.push(e);
			return e
		},
		_: {
			setupHandler: _setupHandler,
			setupEvents: _setupEvents,
			setupDOM: _ptr.setupDOM,
			onReset: _ptr.onReset,
			update: _ptr.update
		}
	}, utils = {
		uuid: uuid,
		urlQuery: urlQuery,
		toUrlEncoded: toUrlEncoded
	};
	function uuid(t) {
		var n = (new Date).getTime();
		window.performance && "function" == typeof window.performance.now && (n += performance.now());
		var e = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx";
		t && (e = "xxxxxxxxxyxxxxxxxxyxxxxxxxxyxxxxxxxx".substr(0, t));
		return e.replace(/[xy]/g, function(t) {
			var e = (n + 16 * Math.random()) % 16 | 0;
			n = Math.floor(n / 16);
			return ("x" == t ? e : 3 & e | 8).toString(16)
		})
	}
	function urlQuery(t) {
		for (var e = location.href.substr(1 + location.href.indexOf("?")), n = (e = decodeURI(e)).split("&"), i = 0; i < n.length; i++) {
			var r = n[i].split("=");
			if (r[0] == t)
				return r[1]
		}
		return null
	}
	function toUrlEncoded(t) {
		if (t) {
			var e = "";
			for (var n in t)
				e += (0 < e.length ? "&" : "") + encodeURI(n) + "=" + encodeURIComponent(t[n]);
			return e
		}
	}
	function Swipe(n, a) {
		function e() {}
		var o = function(t) {
			setTimeout(t || e, 0)
		}
		  , s = {
			addEventListener: !!window.addEventListener,
			touch: "ontouchstart"in window || window.DocumentTouch && document instanceof DocumentTouch,
			transitions: function(t) {
				var e = ["transitionProperty", "WebkitTransition", "MozTransition", "OTransition", "msTransition"];
				for (var n in e)
					if (void 0 !== t.style[e[n]])
						return !0;
				return !1
			}(document.createElement("swipe"))
		};
		if (n) {
			var c, u, f, i, h = n;
			n = h.parentNode;
			var l = parseInt((a = a || {}).startSlide, 10) || 0
			  , d = a.speed || 300;
			a.continuous = void 0 === a.continuous || a.continuous;
			var t, r, v = a.auto || 0, p = {}, m = {}, g = {
				handleEvent: function(t) {
					switch (t.type) {
					case "touchstart":
						this.start(t);
						break;
					case "touchmove":
						this.move(t);
						break;
					case "touchend":
					case "touchcancel":
						this.end(t);
						break;
					case "webkitTransitionEnd":
					case "msTransitionEnd":
					case "oTransitionEnd":
					case "otransitionend":
					case "transitionend":
						o(this.transitionEnd(t));
						break;
					case "resize":
						o(b)
					}
					a.stopPropagation && t.stopPropagation()
				},
				start: function(t) {
					var e = t.touches[0];
					p = {
						x: e.pageX,
						y: e.pageY,
						time: +new Date
					};
					r = void 0;
					h.addEventListener("touchmove", this, !(m = {}));
					h.addEventListener("touchend", this, !1);
					h.addEventListener("touchcancel", this, !1)
				},
				move: function(t) {
					if (!(1 < t.touches.length || t.scale && 1 !== t.scale)) {
						a.disableScroll && t.preventDefault();
						var e = t.touches[0];
						m = {
							x: e.pageX - p.x,
							y: e.pageY - p.y
						};
						void 0 === r && (r = !!(r || Math.abs(m.x) < Math.abs(m.y)));
						if (!r) {
							t.preventDefault();
							E();
							if (a.continuous) {
								_(y(l - 1), m.x + u[y(l - 1)], 0);
								_(l, m.x + u[l], 0);
								_(y(l + 1), m.x + u[y(l + 1)], 0)
							} else {
								m.x = m.x / (!l && 0 < m.x || l == c.length - 1 && m.x < 0 ? Math.abs(m.x) / f + 1 : 1);
								_(l - 1, m.x + u[l - 1], 0);
								_(l, m.x + u[l], 0);
								_(l + 1, m.x + u[l + 1], 0)
							}
						}
					}
				},
				end: function() {
					var t = !l && 0 < m.x || l == c.length - 1 && m.x < 0;
					a.continuous && (t = !1);
					if (!r)
						if ((+(new Date - p.time) < 250 && 20 < Math.abs(m.x) || f / 2 < Math.abs(m.x)) && !t) {
							if (m.x < 0) {
								if (a.continuous) {
									x(y(l - 1), -f, 0);
									x(y(l + 2), f, 0)
								} else
									x(l - 1, -f, 0);
								x(l, u[l] - f, d);
								x(y(l + 1), u[y(l + 1)] - f, d);
								l = y(l + 1)
							} else {
								if (a.continuous) {
									x(y(l + 1), f, 0);
									x(y(l - 2), -f, 0)
								} else
									x(l + 1, f, 0);
								x(l, u[l] + f, d);
								x(y(l - 1), u[y(l - 1)] + f, d);
								l = y(l - 1)
							}
							a.callback && a.callback(l, c[l])
						} else if (a.continuous) {
							x(y(l - 1), -f, d);
							x(l, 0, d);
							x(y(l + 1), f, d)
						} else {
							x(l - 1, -f, d);
							x(l, 0, d);
							x(l + 1, f, d)
						}
					h.removeEventListener("touchmove", g, !1);
					h.removeEventListener("touchend", g, !1);
					h.removeEventListener("touchcancel", g, !1);
					v = a.auto
				},
				transitionEnd: function(t) {
					if (parseInt(t.target.getAttribute("data-index"), 10) == l) {
						v && T();
						a.transitionEnd && a.transitionEnd.call(t, l, c[l])
					}
				}
			};
			b();
			v && T();
			if (s.addEventListener) {
				s.touch && h.addEventListener("touchstart", g, !1);
				if (s.transitions) {
					h.addEventListener("webkitTransitionEnd", g, !1);
					h.addEventListener("msTransitionEnd", g, !1);
					h.addEventListener("oTransitionEnd", g, !1);
					h.addEventListener("otransitionend", g, !1);
					h.addEventListener("transitionend", g, !1)
				}
				window.addEventListener("resize", g, !1)
			} else
				window.onresize = function() {
					b()
				}
				;
			return {
				setup: function() {
					b()
				},
				slide: function(t, e) {
					E();
					k(t, e)
				},
				prev: function() {
					E();
					(a.continuous || l) && k(l - 1)
				},
				next: function() {
					E();
					w()
				},
				stop: function() {
					E()
				},
				getPos: function() {
					return l
				},
				getNumSlides: function() {
					return i
				},
				kill: function() {
					E();
					h.style.width = "";
					h.style.left = "";
					for (var t = c.length; t--; ) {
						var e = c[t];
						e.style.width = "";
						e.style.left = "";
						s.transitions && _(t, 0, 0)
					}
					if (s.addEventListener) {
						h.removeEventListener("touchstart", g, !1);
						h.removeEventListener("webkitTransitionEnd", g, !1);
						h.removeEventListener("msTransitionEnd", g, !1);
						h.removeEventListener("oTransitionEnd", g, !1);
						h.removeEventListener("otransitionend", g, !1);
						h.removeEventListener("transitionend", g, !1);
						window.removeEventListener("resize", g, !1)
					} else
						window.onresize = null
				}
			}
		}
		function b() {
			(i = (c = h.children).length) < 2 && (a.continuous = !1);
			if (s.transitions && a.continuous && c.length < 3) {
				h.appendChild(c[0].cloneNode(!0));
				h.appendChild(h.children[1].cloneNode(!0));
				c = h.children
			}
			u = Array(c.length);
			f = n.getBoundingClientRect().width || n.offsetWidth;
			n.style.overflowX = "hidden";
			h.style.width = c.length * f + "px";
			h.style.whiteSpace = "nowrap";
			h.style.display = "block";
			for (var t = c.length; t--; ) {
				var e = c[t];
				e.style.width = f + "px";
				e.style.display = "inline-block";
				e.setAttribute("data-index", t);
				if (s.transitions) {
					e.style.left = t * -f + "px";
					x(t, t < l ? -f : l < t ? f : 0, 0)
				}
			}
			if (a.continuous && s.transitions) {
				x(y(l - 1), -f, 0);
				x(y(l + 1), f, 0)
			}
			s.transitions || (h.style.left = l * -f + "px");
			n.style.visibility = "visible"
		}
		function w() {
			(a.continuous || l < c.length - 1) && k(l + 1)
		}
		function y(t) {
			return (c.length + t % c.length) % c.length
		}
		function k(t, e) {
			if (l != t) {
				if (s.transitions) {
					var n = Math.abs(l - t) / (l - t);
					if (a.continuous) {
						var i = n;
						(n = -u[y(t)] / f) !== i && (t = -n * c.length + t)
					}
					for (var r = Math.abs(l - t) - 1; r--; )
						x(y((l < t ? t : l) - r - 1), f * n, 0);
					t = y(t);
					x(l, f * n, e || d);
					x(t, 0, e || d);
					a.continuous && x(y(t - n), -f * n, 0)
				} else {
					t = y(t);
					!function(e, n, i) {
						if (i)
							var r = +new Date
							  , o = setInterval(function() {
								var t = new Date - r;
								if (i < t) {
									h.style.left = n + "px";
									v && T();
									a.transitionEnd && a.transitionEnd.call(event, l, c[l]);
									clearInterval(o)
								} else
									h.style.left = Math.floor(t / i * 100) / 100 * (n - e) + e + "px"
							}, 4);
						else
							h.style.left = n + "px"
					}(l * -f, t * -f, e || d)
				}
				l = t;
				o(a.callback && a.callback(l, c[l]))
			}
		}
		function x(t, e, n) {
			_(t, e, n);
			u[t] = e
		}
		function _(t, e, n) {
			var i = c[t]
			  , r = i && i.style;
			if (r) {
				r.webkitTransitionDuration = r.MozTransitionDuration = r.msTransitionDuration = r.OTransitionDuration = r.transitionDuration = n + "ms";
				r.webkitTransform = "translate(" + e + "px,0)translateZ(0)";
				r.msTransform = r.MozTransform = r.OTransform = "translateX(" + e + "px)"
			}
		}
		function T() {
			t = setTimeout(w, v)
		}
		function E() {
			v = 0;
			clearTimeout(t)
		}
	}
	function PickerFactory() {
		return r = {},
		n.m = i = [function(t, e, n) {
			e.__esModule = !0;
			var i = (r = n(1)) && r.__esModule ? r : {
				default: r
			};
			var r;
			i.default.version = "1.1.2",
			e.default = i.default,
			t.exports = e.default
		}
		, function(t, e, n) {
			function i(t) {
				return t && t.__esModule ? t : {
					default: t
				}
			}
			e.__esModule = !0;
			var r = function(t, e, n) {
				return e && h(t.prototype, e),
				n && h(t, n),
				t
			}
			  , o = i(n(2))
			  , a = i(n(3))
			  , s = n(4)
			  , c = n(5)
			  , u = i(n(6))
			  , f = i(n(14));
			function h(t, e) {
				for (var n = 0; n < e.length; n++) {
					var i = e[n];
					i.enumerable = i.enumerable || !1,
					i.configurable = !0,
					"value"in i && (i.writable = !0),
					Object.defineProperty(t, i.key, i)
				}
			}
			n(15);
			var l = (function(t, e) {
				if ("function" != typeof e && null !== e)
					throw new TypeError("Super expression must either be null or a function, not " + typeof e);
				t.prototype = Object.create(e && e.prototype, {
					constructor: {
						value: t,
						enumerable: !1,
						writable: !0,
						configurable: !0
					}
				}),
				e && (Object.setPrototypeOf ? Object.setPrototypeOf(t, e) : t.__proto__ = e)
			}(d, a.default),
			r(d, [{
				key: "_init",
				value: function() {
					if (this.selectedIndex = [],
					this.selectedVal = [],
					this.options.selectedIndex)
						this.selectedIndex = this.options.selectedIndex;
					else
						for (var t = 0; t < this.data.length; t++)
							this.selectedIndex[t] = 0;
					this._bindEvent()
				}
			}, {
				key: "_bindEvent",
				value: function() {
					var r = this;
					(0,
					c.addEvent)(this.pickerEl, "touchmove", function(t) {
						t.preventDefault()
					}),
					(0,
					c.addEvent)(this.confirmEl, "click", function() {
						r.hide();
						for (var t = !1, e = 0; e < r.data.length; e++) {
							var n = r.wheels[e].getSelectedIndex();
							r.selectedIndex[e] = n;
							var i = null;
							r.data[e].length && (i = r.data[e][n].value),
							r.selectedVal[e] !== i && (t = !0),
							r.selectedVal[e] = i
						}
						r.trigger("picker.select", r.selectedVal, r.selectedIndex),
						t && r.trigger("picker.valuechange", r.selectedVal, r.selectedIndex)
					}),
					(0,
					c.addEvent)(this.cancelEl, "click", function() {
						r.hide(),
						r.trigger("picker.cancel")
					})
				}
			}, {
				key: "_createWheel",
				value: function(t, e) {
					var n = this;
					return this.wheels[e] = new o.default(t[e],{
						wheel: !0,
						selectedIndex: this.selectedIndex[e]
					}),
					n.wheels[i = e].on("scrollEnd", function() {
						var t = n.wheels[i].getSelectedIndex();
						n.selectedIndex[e] !== t && (n.selectedIndex[e] = t,
						n.trigger("picker.change", i, t))
					}),
					this.wheels[e];
					var i
				}
			}, {
				key: "show",
				value: function(n) {
					var i = this;
					this.pickerEl.style.display = "block";
					var r = this.options.showCls;
					window.setTimeout(function() {
						if ((0,
						c.addClass)(i.maskEl, r),
						(0,
						c.addClass)(i.panelEl, r),
						i.wheels)
							for (var t = 0; t < i.data.length; t++)
								i.wheels[t].enable(),
								i.wheels[t].wheelTo(i.selectedIndex[t]);
						else {
							i.wheels = [];
							for (var e = 0; e < i.data.length; e++)
								i._createWheel(i.wheelEl, e)
						}
						n && n()
					}, 0)
				}
			}, {
				key: "hide",
				value: function() {
					var e = this
					  , t = this.options.showCls;
					(0,
					c.removeClass)(this.maskEl, t),
					(0,
					c.removeClass)(this.panelEl, t),
					window.setTimeout(function() {
						e.pickerEl.style.display = "none";
						for (var t = 0; t < e.data.length; t++)
							e.wheels[t].disable()
					}, 500)
				}
			}, {
				key: "refillColumn",
				value: function(t, e) {
					var n = this.scrollEl[t]
					  , i = this.wheels[t];
					if (n && i) {
						var r = this.data[t];
						n.innerHTML = (0,
						f.default)(this.data[t] = e);
						var o = i.getSelectedIndex()
						  , a = 0;
						if (r.length)
							for (var s = r[o].value, c = 0; c < e.length; c++)
								if (e[c].value === s) {
									a = c;
									break
								}
						return this.selectedIndex[t] = a,
						i.refresh(),
						i.wheelTo(a),
						a
					}
				}
			}, {
				key: "refill",
				value: function(t) {
					var n = this
					  , i = [];
					return t.length && t.forEach(function(t, e) {
						i[e] = n.refillColumn(e, t)
					}),
					i
				}
			}, {
				key: "scrollColumn",
				value: function(t, e) {
					this.wheels[t].wheelTo(e)
				}
			}]),
			d);
			function d(t) {
				!function(t) {
					if (!(t instanceof d))
						throw new TypeError("Cannot call a class as a function")
				}(this);
				var e = function(t, e) {
					if (!t)
						throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
					return !e || "object" != typeof e && "function" != typeof e ? t : e
				}(this, (d.__proto__ || Object.getPrototypeOf(d)).call(this));
				return e.options = {
					data: [],
					title: "",
					selectedIndex: null,
					showCls: "show"
				},
				(0,
				s.extend)(e.options, t),
				e.data = e.options.data,
				e.pickerEl = (0,
				c.createDom)((0,
				u.default)({
					data: e.data,
					title: e.options.title
				})),
				document.body.appendChild(e.pickerEl),
				e.maskEl = e.pickerEl.getElementsByClassName("mask-hook")[0],
				e.wheelEl = e.pickerEl.getElementsByClassName("wheel-hook"),
				e.panelEl = e.pickerEl.getElementsByClassName("panel-hook")[0],
				e.confirmEl = e.pickerEl.getElementsByClassName("confirm-hook")[0],
				e.cancelEl = e.pickerEl.getElementsByClassName("cancel-hook")[0],
				e.scrollEl = e.pickerEl.getElementsByClassName("wheel-scroll-hook"),
				e._init(),
				e
			}
			e.default = l,
			t.exports = e.default
		}
		, function(t) {
			t.exports = (r = {},
			n.m = i = [function(t, e, n) {
				var i = n(1);
				i.BScroll.Version = "0.1.14",
				t.exports = i.BScroll
			}
			, function(t, e, n) {
				Object.defineProperty(e, "__esModule", {
					value: !0
				}),
				e.BScroll = void 0;
				var i = function(t, e, n) {
					return e && r(t.prototype, e),
					n && r(t, n),
					t
				}
				  , d = n(2);
				function r(t, e) {
					for (var n = 0; n < e.length; n++) {
						var i = e[n];
						i.enumerable = i.enumerable || !1,
						i.configurable = !0,
						"value"in i && (i.writable = !0),
						Object.defineProperty(t, i.key, i)
					}
				}
				e.BScroll = (function(t, e) {
					if ("function" != typeof e && null !== e)
						throw new TypeError("Super expression must either be null or a function, not " + typeof e);
					t.prototype = Object.create(e && e.prototype, {
						constructor: {
							value: t,
							enumerable: !1,
							writable: !0,
							configurable: !0
						}
					}),
					e && (Object.setPrototypeOf ? Object.setPrototypeOf(t, e) : t.__proto__ = e)
				}(o, d.EventEmitter),
				i(o, [{
					key: "_init",
					value: function() {
						this.x = 0,
						this.y = 0,
						this.directionX = 0,
						this.directionY = 0,
						this._addEvents()
					}
				}, {
					key: "_initSnap",
					value: function() {
						var l = this;
						if (this.currentPage = {},
						this.options.snapLoop) {
							var t = this.scroller.children;
							0 < t.length && ((0,
							d.prepend)(t[t.length - 1].cloneNode(!0), this.scroller),
							this.scroller.appendChild(t[1].cloneNode(!0)))
						}
						"string" == typeof this.options.snap && (this.options.snap = this.scroller.querySelectorAll(this.options.snap)),
						this.on("refresh", function() {
							if (l.pages = [],
							l.wrapperWidth && l.wrapperHeight && l.scrollerWidth && l.scrollerHeight) {
								var t, e, n, i, r, o, a, s = l.options.snapStepX || l.wrapperWidth, c = l.options.snapStepY || l.wrapperHeight, u = 0, f = 0, h = 0;
								if (!0 === l.options.snap)
									for (e = Math.round(s / 2),
									n = Math.round(c / 2); -l.scrollerWidth < u; ) {
										for (l.pages[f] = [],
										t = i = 0; -l.scrollerHeight < t; )
											l.pages[f][i] = {
												x: Math.max(u, l.maxScrollX),
												y: Math.max(t, l.maxScrollY),
												width: s,
												height: c,
												cx: u - e,
												cy: t - n
											},
											t -= c,
											i++;
										u -= s,
										f++
									}
								else
									for (i = (o = l.options.snap).length,
									r = -1; f < i; f++)
										a = (0,
										d.getRect)(o[f]),
										(0 === f || a.left <= (0,
										d.getRect)(o[f - 1]).left) && (h = 0,
										r++),
										l.pages[h] || (l.pages[h] = []),
										l.pages[h][r] = {
											x: u = Math.max(-a.left, l.maxScrollX),
											y: t = Math.max(-a.top, l.maxScrollY),
											width: a.width,
											height: a.height,
											cx: e = u - Math.round(a.width / 2),
											cy: n = t - Math.round(a.height / 2)
										},
										l.maxScrollX < u && h++;
								l.goToPage(l.currentPage.pageX || (l.options.snapLoop ? 1 : 0), l.currentPage.pageY || 0, 0),
								l.options.snapThreshold % 1 == 0 ? (l.snapThresholdX = l.options.snapThreshold,
								l.snapThresholdY = l.options.snapThreshold) : (l.snapThresholdX = Math.round(l.pages[l.currentPage.pageX][l.currentPage.pageY].width * l.options.snapThreshold),
								l.snapThresholdY = Math.round(l.pages[l.currentPage.pageX][l.currentPage.pageY].height * l.options.snapThreshold))
							}
						}),
						this.on("scrollEnd", function() {
							l.options.snapLoop && (0 === l.currentPage.pageX && l.goToPage(l.pages.length - 2, l.currentPage.pageY, 0),
							l.currentPage.pageX === l.pages.length - 1 && l.goToPage(1, l.currentPage.pageY, 0))
						}),
						this.on("flick", function() {
							l.goToPage(l.currentPage.pageX + l.directionX, l.currentPage.pageY + l.directionY, l.options.snapSpeed || Math.max(Math.max(Math.min(Math.abs(l.x - l.startX), 1e3), Math.min(Math.abs(l.y - l.startY), 1e3)), 300))
						})
					}
				}, {
					key: "_nearestSnap",
					value: function(t, e) {
						if (!this.pages.length)
							return {
								x: 0,
								y: 0,
								pageX: 0,
								pageY: 0
							};
						var n = 0;
						if (Math.abs(t - this.absStartX) < this.snapThresholdX && Math.abs(e - this.absStartY) < this.snapThresholdY)
							return this.currentPage;
						0 < t ? t = 0 : t < this.maxScrollX && (t = this.maxScrollX),
						0 < e ? e = 0 : e < this.maxScrollY && (e = this.maxScrollY);
						for (var i = this.pages.length; n < i; n++)
							if (this.pages[n][0].cx <= t) {
								t = this.pages[n][0].x;
								break
							}
						i = this.pages[n].length;
						for (var r = 0; r < i; r++)
							if (this.pages[0][r].cy <= e) {
								e = this.pages[0][r].y;
								break
							}
						return n === this.currentPage.pageX && ((n += this.directionX) < 0 ? n = 0 : this.pages.length <= n && (n = this.pages.length - 1),
						t = this.pages[n][0].x),
						r === this.currentPage.pageY && ((r += this.directionY) < 0 ? r = 0 : this.pages[0].length <= r && (r = this.pages[0].length - 1),
						e = this.pages[0][r].y),
						{
							x: t,
							y: e,
							pageX: n,
							pageY: r
						}
					}
				}, {
					key: "_addEvents",
					value: function() {
						this._handleEvents(d.addEvent)
					}
				}, {
					key: "_removeEvents",
					value: function() {
						this._handleEvents(d.removeEvent)
					}
				}, {
					key: "_handleEvents",
					value: function(t) {
						var e = this.options.bindToWrapper ? this.wrapper : window;
						t(window, "orientationchange", this),
						t(window, "resize", this),
						this.options.click && t(this.wrapper, "click", this),
						this.options.disableMouse || (t(this.wrapper, "mousedown", this),
						t(e, "mousemove", this),
						t(e, "mousecancel", this),
						t(e, "mouseup", this)),
						d.hasTouch && !this.options.disableTouch && (t(this.wrapper, "touchstart", this),
						t(e, "touchmove", this),
						t(e, "touchcancel", this),
						t(e, "touchend", this)),
						t(this.scroller, d.style.transitionEnd, this)
					}
				}, {
					key: "_start",
					value: function(t) {
						var e = d.eventType[t.type];
						if ((1 === e || 0 === t.button) && this.enabled && (!this.initiated || this.initiated === e)) {
							if (this.initiated = e,
							!this.options.preventDefault || d.isBadAndroid || (0,
							d.preventDefaultException)(t.target, this.options.preventDefaultException) || t.preventDefault(),
							this.moved = !1,
							this.distX = 0,
							this.distY = 0,
							this.directionX = 0,
							this.directionY = 0,
							this.directionLocked = 0,
							this._transitionTime(),
							this.startTime = +new Date,
							this.options.wheel && (this.target = t.target),
							this.options.useTransition && this.isInTransition) {
								this.isInTransition = !1;
								var n = this.getComputedPosition();
								this._translate(n.x, n.y),
								this.options.wheel ? this.target = this.items[Math.round(-n.y / this.itemHeight)] : this.trigger("scrollEnd")
							}
							var i = t.touches ? t.touches[0] : t;
							this.startX = this.x,
							this.startY = this.y,
							this.absStartX = this.x,
							this.absStartY = this.y,
							this.pointX = i.pageX,
							this.pointY = i.pageY,
							this.trigger("beforeScrollStart")
						}
					}
				}, {
					key: "_move",
					value: function(t) {
						if (this.enabled && d.eventType[t.type] === this.initiated) {
							this.options.preventDefault && t.preventDefault();
							var e = t.touches ? t.touches[0] : t
							  , n = e.pageX - this.pointX
							  , i = e.pageY - this.pointY;
							this.pointX = e.pageX,
							this.pointY = e.pageY,
							this.distX += n,
							this.distY += i;
							var r = Math.abs(this.distX)
							  , o = Math.abs(this.distY)
							  , a = +new Date;
							if (!(this.options.momentumLimitTime < a - this.endTime && o < this.options.momentumLimitDistance && r < this.options.momentumLimitDistance)) {
								if (this.directionLocked || this.options.freeScroll || (o + this.options.directionLockThreshold < r ? this.directionLocked = "h" : r + this.options.directionLockThreshold <= o ? this.directionLocked = "v" : this.directionLocked = "n"),
								"h" === this.directionLocked) {
									if ("vertical" === this.options.eventPassthrough)
										t.preventDefault();
									else if ("horizontal" === this.options.eventPassthrough)
										return void (this.initiated = !1);
									i = 0
								} else if ("v" === this.directionLocked) {
									if ("horizontal" === this.options.eventPassthrough)
										t.preventDefault();
									else if ("vertical" === this.options.eventPassthrough)
										return void (this.initiated = !1);
									n = 0
								}
								var s = this.x + (n = this.hasHorizontalScroll ? n : 0)
								  , c = this.y + (i = this.hasVerticalScroll ? i : 0);
								(0 < s || s < this.maxScrollX) && (s = this.options.bounce ? this.x + n / 3 : 0 < s ? 0 : this.maxScrollX),
								(0 < c || c < this.maxScrollY) && (c = this.options.bounce ? this.y + i / 3 : 0 < c ? 0 : this.maxScrollY),
								this.directionX = 0 < n ? -1 : n < 0 ? 1 : 0,
								this.directionY = 0 < i ? -1 : i < 0 ? 1 : 0,
								this.moved || (this.moved = !0,
								this.trigger("scrollStart")),
								this._translate(s, c),
								this.options.momentumLimitTime < a - this.startTime && (this.startTime = a,
								this.startX = this.x,
								this.startY = this.y,
								1 === this.options.probeType && this.trigger("scroll", {
									x: this.x,
									y: this.y
								})),
								1 < this.options.probeType && this.trigger("scroll", {
									x: this.x,
									y: this.y
								});
								var u = document.documentElement.scrollLeft || window.pageXOffset || document.body.scrollLeft
								  , f = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop
								  , h = this.pointX - u
								  , l = this.pointY - f;
								(h > document.documentElement.clientWidth - this.options.momentumLimitDistance || h < this.options.momentumLimitDistance || l < this.options.momentumLimitDistance || l > document.documentElement.clientHeight - this.options.momentumLimitDistance) && this._end(t)
							}
						}
					}
				}, {
					key: "_end",
					value: function(t) {
						if (this.enabled && d.eventType[t.type] === this.initiated && (this.initiated = !1,
						this.options.preventDefault && !(0,
						d.preventDefaultException)(t.target, this.options.preventDefaultException) && t.preventDefault(),
						this.trigger("touchend", {
							x: this.x,
							y: this.y
						}),
						!this.resetPosition(this.options.bounceTime, d.ease.bounce))) {
							this.isInTransition = !1;
							var e = Math.round(this.x)
							  , n = Math.round(this.y);
							if (!this.moved) {
								if (this.options.wheel) {
									if (this.target && "wheel-scroll" === this.target.className) {
										var i = Math.abs(Math.round(n / this.itemHeight))
										  , r = Math.round((this.pointY + (0,
										d.offset)(this.target).top - this.itemHeight / 2) / this.itemHeight);
										this.target = this.items[i + r]
									}
									this.scrollToElement(this.target, this.options.adjustTime, !0, !0, d.ease.swipe)
								} else
									this.options.tap && (0,
									d.tap)(t, this.options.tap),
									this.options.click && (0,
									d.click)(t);
								return void this.trigger("scrollCancel")
							}
							this.scrollTo(e, n),
							this.endTime = +new Date;
							var o = this.endTime - this.startTime
							  , a = Math.abs(e - this.startX)
							  , s = Math.abs(n - this.startY);
							if (this._events.flick && o < this.options.momentumLimitTime && a < this.options.momentumLimitDistance && s < this.options.momentumLimitDistance)
								return void this.trigger("flick");
							var c = 0;
							if (this.options.momentum && o < this.options.momentumLimitTime && (this.options.momentumLimitDistance < s || this.options.momentumLimitDistance < a)) {
								var u = this.hasHorizontalScroll ? (0,
								d.momentum)(this.x, this.startX, o, this.maxScrollX, this.options.bounce ? this.wrapperWidth : 0, this.options) : {
									destination: e,
									duration: 0
								}
								  , f = this.hasVerticalScroll ? (0,
								d.momentum)(this.y, this.startY, o, this.maxScrollY, this.options.bounce ? this.wrapperHeight : 0, this.options) : {
									destination: n,
									duration: 0
								};
								e = u.destination,
								n = f.destination,
								c = Math.max(u.duration, f.duration),
								this.isInTransition = 1
							} else
								this.options.wheel && (n = Math.round(n / this.itemHeight) * this.itemHeight,
								c = this.options.adjustTime);
							var h = d.ease.swipe;
							if (this.options.snap) {
								var l = this._nearestSnap(e, n);
								this.currentPage = l,
								c = this.options.snapSpeed || Math.max(Math.max(Math.min(Math.abs(e - l.x), 1e3), Math.min(Math.abs(n - l.y), 1e3)), 300),
								e = l.x,
								n = l.y,
								this.directionX = 0,
								this.directionY = 0,
								h = d.ease.bounce
							}
							if (e !== this.x || n !== this.y)
								return (0 < e || e < this.maxScrollX || 0 < n || n < this.maxScrollY) && (h = d.ease.swipeBounce),
								void this.scrollTo(e, n, c, h);
							this.options.wheel && (this.selectedIndex = 0 | Math.abs(this.y / this.itemHeight)),
							this.trigger("scrollEnd")
						}
					}
				}, {
					key: "_resize",
					value: function() {
						var t = this;
						this.enabled && (clearTimeout(this.resizeTimeout),
						this.resizeTimeout = setTimeout(function() {
							t.refresh()
						}, this.options.resizePolling))
					}
				}, {
					key: "_startProbe",
					value: function() {
						(0,
						d.cancelAnimationFrame)(this.probeTimer),
						this.probeTimer = (0,
						d.requestAnimationFrame)(function t() {
							var e = n.getComputedPosition();
							n.trigger("scroll", e),
							n.isInTransition && (n.probeTimer = (0,
							d.requestAnimationFrame)(t))
						});
						var n = this
					}
				}, {
					key: "_transitionTime",
					value: function(t) {
						var e = this
						  , n = arguments.length <= 0 || void 0 === t ? 0 : t;
						if (this.scrollerStyle[d.style.transitionDuration] = n + "ms",
						this.options.wheel && !d.isBadAndroid)
							for (var i = 0; i < this.items.length; i++)
								this.items[i].style[d.style.transitionDuration] = n + "ms";
						!n && d.isBadAndroid && (this.scrollerStyle[d.style.transitionDuration] = "0.001s",
						(0,
						d.requestAnimationFrame)(function() {
							"0.0001ms" === e.scrollerStyle[d.style.transitionDuration] && (e.scrollerStyle[d.style.transitionDuration] = "0s")
						}))
					}
				}, {
					key: "_transitionTimingFunction",
					value: function(t) {
						if (this.scrollerStyle[d.style.transitionTimingFunction] = t,
						this.options.wheel && !d.isBadAndroid)
							for (var e = 0; e < this.items.length; e++)
								this.items[e].style[d.style.transitionTimingFunction] = t
					}
				}, {
					key: "_transitionEnd",
					value: function(t) {
						t.target === this.scroller && this.isInTransition && (this._transitionTime(),
						this.resetPosition(this.options.bounceTime, d.ease.bounce) || (this.isInTransition = !1,
						this.trigger("scrollEnd")))
					}
				}, {
					key: "_translate",
					value: function(t, e) {
						if (this.options.useTransform ? this.scrollerStyle[d.style.transform] = "translate(" + t + "px," + e + "px)" + this.translateZ : (t = Math.round(t),
						e = Math.round(e),
						this.scrollerStyle.left = t + "px",
						this.scrollerStyle.top = e + "px"),
						this.options.wheel && !d.isBadAndroid)
							for (var n = 0; n < this.items.length; n++)
								this.items[n].style[d.style.transform] = "rotateX(" + this.options.rotate * (e / this.itemHeight + n) + "deg)";
						this.x = t,
						this.y = e
					}
				}, {
					key: "enable",
					value: function() {
						this.enabled = !0
					}
				}, {
					key: "disable",
					value: function() {
						this.enabled = !1
					}
				}, {
					key: "refresh",
					value: function() {
						this.wrapperWidth = parseInt(this.wrapper.style.width) || this.wrapper.clientWidth,
						this.wrapperHeight = parseInt(this.wrapper.style.height) || this.wrapper.clientHeight,
						this.scrollerWidth = parseInt(this.scroller.style.width) || this.scroller.clientWidth,
						this.scrollerHeight = parseInt(this.scroller.style.height) || this.scroller.clientHeight,
						this.options.wheel ? (this.items = this.scroller.children,
						this.options.itemHeight = this.itemHeight = this.items.length ? this.items[0].clientHeight : 0,
						void 0 === this.selectedIndex && (this.selectedIndex = this.options.selectedIndex),
						this.options.startY = -this.selectedIndex * this.itemHeight,
						this.maxScrollX = 0,
						this.maxScrollY = -this.itemHeight * (this.items.length - 1)) : (this.maxScrollX = this.wrapperWidth - this.scrollerWidth,
						this.maxScrollY = this.wrapperHeight - this.scrollerHeight),
						this.hasHorizontalScroll = this.options.scrollX && this.maxScrollX < 0,
						this.hasVerticalScroll = this.options.scrollY && this.maxScrollY < 0,
						this.hasHorizontalScroll || (this.maxScrollX = 0,
						this.scrollerWidth = this.wrapperWidth),
						this.hasVerticalScroll || (this.maxScrollY = 0,
						this.scrollerHeight = this.wrapperHeight),
						this.endTime = 0,
						this.directionX = 0,
						this.directionY = 0,
						this.wrapperOffset = (0,
						d.offset)(this.wrapper),
						this.trigger("refresh"),
						this.resetPosition()
					}
				}, {
					key: "resetPosition",
					value: function(t, e) {
						var n = this.x;
						!this.hasHorizontalScroll || 0 < n ? n = 0 : n < this.maxScrollX && (n = this.maxScrollX);
						var i = this.y;
						return !this.hasVerticalScroll || 0 < i ? i = 0 : i < this.maxScrollY && (i = this.maxScrollY),
						(n !== this.x || i !== this.y) && (this.scrollTo(n, i, arguments.length <= 0 || void 0 === t ? 0 : t, arguments.length <= 1 || void 0 === e ? d.ease.bounce : e),
						!0)
					}
				}, {
					key: "wheelTo",
					value: function(t) {
						this.options.wheel && (this.y = -t * this.itemHeight,
						this.scrollTo(0, this.y))
					}
				}, {
					key: "scrollBy",
					value: function(t, e, n, i) {
						this.scrollTo(t = this.x + t, e = this.y + e, arguments.length <= 2 || void 0 === n ? 0 : n, arguments.length <= 3 || void 0 === i ? d.ease.bounce : i)
					}
				}, {
					key: "scrollTo",
					value: function(t, e, n, i) {
						var r = arguments.length <= 3 || void 0 === i ? d.ease.bounce : i;
						this.isInTransition = this.options.useTransition && 0 < n && (t !== this.x || e !== this.y),
						n && !this.options.useTransition || (this._transitionTimingFunction(r.style),
						this._transitionTime(n),
						this._translate(t, e),
						n && 3 === this.options.probeType && this._startProbe(),
						this.options.wheel && (0 < e ? this.selectedIndex = 0 : e < this.maxScrollY ? this.selectedIndex = this.items.length - 1 : this.selectedIndex = 0 | Math.abs(e / this.itemHeight)))
					}
				}, {
					key: "getSelectedIndex",
					value: function() {
						return this.options.wheel && this.selectedIndex
					}
				}, {
					key: "getCurrentPage",
					value: function() {
						return this.options.snap && this.currentPage
					}
				}, {
					key: "scrollToElement",
					value: function(t, e, n, i, r) {
						if (t && (t = t.nodeType ? t : this.scroller.querySelector(t),
						!this.options.wheel || "wheel-item" === t.className)) {
							var o = (0,
							d.offset)(t);
							o.left -= this.wrapperOffset.left,
							o.top -= this.wrapperOffset.top,
							!0 === n && (n = Math.round(t.offsetWidth / 2 - this.wrapper.offsetWidth / 2)),
							!0 === i && (i = Math.round(t.offsetHeight / 2 - this.wrapper.offsetHeight / 2)),
							o.left -= n || 0,
							o.top -= i || 0,
							o.left = 0 < o.left ? 0 : o.left < this.maxScrollX ? this.maxScrollX : o.left,
							o.top = 0 < o.top ? 0 : o.top < this.maxScrollY ? this.maxScrollY : o.top,
							this.options.wheel && (o.top = Math.round(o.top / this.itemHeight) * this.itemHeight),
							this.scrollTo(o.left, o.top, e = null == e || "auto" === e ? Math.max(Math.abs(this.x - o.left), Math.abs(this.y - o.top)) : e, r)
						}
					}
				}, {
					key: "getComputedPosition",
					value: function() {
						var t, e, n = window.getComputedStyle(this.scroller, null);
						return e = this.options.useTransform ? (t = +((n = n[d.style.transform].split(")")[0].split(", "))[12] || n[4]),
						+(n[13] || n[5])) : (t = +n.left.replace(/[^-\d.]/g, ""),
						+n.top.replace(/[^-\d.]/g, "")),
						{
							x: t,
							y: e
						}
					}
				}, {
					key: "goToPage",
					value: function(t, e, n, i) {
						var r = arguments.length <= 3 || void 0 === i ? d.ease.bounce : i;
						this.pages.length <= t ? t = this.pages.length - 1 : t < 0 && (t = 0),
						this.pages[t].length <= e ? e = this.pages[t].length - 1 : e < 0 && (e = 0);
						var o = this.pages[t][e].x
						  , a = this.pages[t][e].y;
						n = void 0 === n ? this.options.snapSpeed || Math.max(Math.max(Math.min(Math.abs(o - this.x), 1e3), Math.min(Math.abs(a - this.y), 1e3)), 300) : n,
						this.currentPage = {
							x: o,
							y: a,
							pageX: t,
							pageY: e
						},
						this.scrollTo(o, a, n, r)
					}
				}, {
					key: "next",
					value: function(t, e) {
						var n = this.currentPage.pageX
						  , i = this.currentPage.pageY;
						++n >= this.pages.length && this.hasVerticalScroll && (n = 0,
						i++),
						this.goToPage(n, i, t, e)
					}
				}, {
					key: "prev",
					value: function(t, e) {
						var n = this.currentPage.pageX
						  , i = this.currentPage.pageY;
						--n < 0 && this.hasVerticalScroll && (n = 0,
						i--),
						this.goToPage(n, i, t, e)
					}
				}, {
					key: "destroy",
					value: function() {
						this._removeEvents(),
						this.trigger("destroy")
					}
				}, {
					key: "handleEvent",
					value: function(t) {
						switch (t.type) {
						case "touchstart":
						case "mousedown":
							this._start(t);
							break;
						case "touchmove":
						case "mousemove":
							this._move(t);
							break;
						case "touchend":
						case "mouseup":
						case "touchcancel":
						case "mousecancel":
							this._end(t);
							break;
						case "orientationchange":
						case "resize":
							this._resize();
							break;
						case "transitionend":
						case "webkitTransitionEnd":
						case "oTransitionEnd":
						case "MSTransitionEnd":
							this._transitionEnd(t);
							break;
						case "click":
							!this.enabled || t._constructed || /(SELECT|INPUT|TEXTAREA)/i.test(t.target.tagName) || (t.preventDefault(),
							t.stopPropagation())
						}
					}
				}]),
				o);
				function o(t, e) {
					!function(t) {
						if (!(t instanceof o))
							throw new TypeError("Cannot call a class as a function")
					}(this);
					var n = function(t, e) {
						if (!t)
							throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
						return !e || "object" != typeof e && "function" != typeof e ? t : e
					}(this, Object.getPrototypeOf(o).call(this));
					return n.wrapper = "string" == typeof t ? document.querySelector(t) : t,
					n.scroller = n.wrapper.children[0],
					n.scrollerStyle = n.scroller.style,
					n.options = {
						startX: 0,
						startY: 0,
						scrollY: !0,
						directionLockThreshold: 5,
						momentum: !0,
						bounce: !0,
						selectedIndex: 0,
						rotate: 25,
						wheel: !1,
						snap: !1,
						snapLoop: !1,
						snapThreshold: .1,
						swipeTime: 2500,
						bounceTime: 700,
						adjustTime: 400,
						swipeBounceTime: 1200,
						deceleration: .001,
						momentumLimitTime: 300,
						momentumLimitDistance: 15,
						resizePolling: 60,
						preventDefault: !0,
						preventDefaultException: {
							tagName: /^(INPUT|TEXTAREA|BUTTON|SELECT)$/
						},
						HWCompositing: !0,
						useTransition: !0,
						useTransform: !0
					},
					(0,
					d.extend)(n.options, e),
					n.translateZ = n.options.HWCompositing && d.hasPerspective ? " translateZ(0)" : "",
					n.options.useTransition = n.options.useTransition && d.hasTransition,
					n.options.useTransform = n.options.useTransform && d.hasTransform,
					n.options.eventPassthrough = !0 === n.options.eventPassthrough ? "vertical" : n.options.eventPassthrough,
					n.options.preventDefault = !n.options.eventPassthrough && n.options.preventDefault,
					n.options.scrollX = "horizontal" !== n.options.eventPassthrough && n.options.scrollX,
					n.options.scrollY = "vertical" !== n.options.eventPassthrough && n.options.scrollY,
					n.options.freeScroll = n.options.freeScroll && !n.options.eventPassthrough,
					n.options.directionLockThreshold = n.options.eventPassthrough ? 0 : n.options.directionLockThreshold,
					!0 === n.options.tap && (n.options.tap = "tap"),
					n._init(),
					n.options.snap && n._initSnap(),
					n.refresh(),
					n.options.snap || n.scrollTo(n.options.startX, n.options.startY),
					n.enable(),
					n
				}
			}
			, function(t, e, n) {
				Object.defineProperty(e, "__esModule", {
					value: !0
				});
				var i = n(3);
				Object.keys(i).forEach(function(t) {
					"default" !== t && "__esModule" !== t && Object.defineProperty(e, t, {
						enumerable: !0,
						get: function() {
							return i[t]
						}
					})
				});
				var r = n(4);
				Object.keys(r).forEach(function(t) {
					"default" !== t && "__esModule" !== t && Object.defineProperty(e, t, {
						enumerable: !0,
						get: function() {
							return r[t]
						}
					})
				});
				var o = n(5);
				Object.keys(o).forEach(function(t) {
					"default" !== t && "__esModule" !== t && Object.defineProperty(e, t, {
						enumerable: !0,
						get: function() {
							return o[t]
						}
					})
				});
				var a = n(6);
				Object.keys(a).forEach(function(t) {
					"default" !== t && "__esModule" !== t && Object.defineProperty(e, t, {
						enumerable: !0,
						get: function() {
							return a[t]
						}
					})
				});
				var s = n(7);
				Object.keys(s).forEach(function(t) {
					"default" !== t && "__esModule" !== t && Object.defineProperty(e, t, {
						enumerable: !0,
						get: function() {
							return s[t]
						}
					})
				});
				var c = n(8);
				Object.keys(c).forEach(function(t) {
					"default" !== t && "__esModule" !== t && Object.defineProperty(e, t, {
						enumerable: !0,
						get: function() {
							return c[t]
						}
					})
				})
			}
			, function(t, e) {
				function n(t) {
					return !1 !== o && ("standard" === o ? t : o + t[0].toUpperCase() + t.substr(1))
				}
				function i(t, e) {
					e.parentNode.insertBefore(t, e)
				}
				Object.defineProperty(e, "__esModule", {
					value: !0
				}),
				e.addEvent = function(t, e, n, i) {
					t.addEventListener(e, n, {
						passive: !1,
						capture: !!i
					})
				}
				,
				e.removeEvent = function(t, e, n, i) {
					t.removeEventListener(e, n, !!i)
				}
				,
				e.offset = function(t) {
					for (var e = 0, n = 0; t; )
						e -= t.offsetLeft,
						n -= t.offsetTop,
						t = t.offsetParent;
					return {
						left: e,
						top: n
					}
				}
				,
				e.getRect = function(t) {
					if (t instanceof window.SVGElement) {
						var e = t.getBoundingClientRect();
						return {
							top: e.top,
							left: e.left,
							width: e.width,
							height: e.height
						}
					}
					return {
						top: t.offsetTop,
						left: t.offsetLeft,
						width: t.offsetWidth,
						height: t.offsetHeight
					}
				}
				,
				e.preventDefaultException = function(t, e) {
					for (var n in e)
						if (e[n].test(t[n]))
							return !0;
					return !1
				}
				,
				e.tap = function(t, e) {
					var n = document.createEvent("Event");
					n.initEvent(e, !0, !0),
					n.pageX = t.pageX,
					n.pageY = t.pageY,
					t.target.dispatchEvent(n)
				}
				,
				e.click = function(t) {
					var e = t.target;
					if (!/(SELECT|INPUT|TEXTAREA)/i.test(e.tagName)) {
						var n = document.createEvent(window.MouseEvent ? "MouseEvents" : "Event");
						n.initEvent("click", !0, !0),
						n._constructed = !0,
						e.dispatchEvent(n)
					}
				}
				,
				e.prepend = function(t, e) {
					e.firstChild ? i(t, e.firstChild) : e.appendChild(t)
				}
				,
				e.before = i;
				var r = document.createElement("div").style
				  , o = function() {
					var t = {
						webkit: "webkitTransform",
						Moz: "MozTransform",
						O: "OTransform",
						ms: "msTransform",
						standard: "transform"
					};
					for (var e in t)
						if (void 0 !== r[t[e]])
							return e;
					return !1
				}()
				  , a = n("transform")
				  , s = (e.hasPerspective = n("perspective")in r,
				e.hasTouch = "ontouchstart"in window,
				e.hasTransform = !1 !== a,
				e.hasTransition = n("transition")in r,
				e.style = {
					transform: a,
					transitionTimingFunction: n("transitionTimingFunction"),
					transitionDuration: n("transitionDuration"),
					transitionDelay: n("transitionDelay"),
					transformOrigin: n("transformOrigin"),
					transitionEnd: n("transitionEnd")
				},
				1);
				e.eventType = {
					touchstart: s,
					touchmove: s,
					touchend: s,
					mousedown: 2,
					mousemove: 2,
					mouseup: 2
				}
			}
			, function(t, e) {
				Object.defineProperty(e, "__esModule", {
					value: !0
				});
				e.isBadAndroid = /Android /.test(window.navigator.appVersion) && !/Chrome\/\d/.test(window.navigator.appVersion)
			}
			, function(t, e) {
				Object.defineProperty(e, "__esModule", {
					value: !0
				});
				e.ease = {
					swipe: {
						style: "cubic-bezier(0.23, 1, 0.32, 1)",
						fn: function(t) {
							return 1 + --t * t * t * t * t
						}
					},
					swipeBounce: {
						style: "cubic-bezier(0.25, 0.46, 0.45, 0.94)",
						fn: function(t) {
							return t * (2 - t)
						}
					},
					bounce: {
						style: "cubic-bezier(0.165, 0.84, 0.44, 1)",
						fn: function(t) {
							return 1 - --t * t * t * t
						}
					}
				}
			}
			, function(t, e) {
				Object.defineProperty(e, "__esModule", {
					value: !0
				});
				function s(t, e) {
					if (Array.isArray(t))
						return t;
					if (Symbol.iterator in Object(t))
						return function(t, e) {
							var n, i = [], r = !0, o = !1;
							try {
								for (var a, s = t[Symbol.iterator](); !(r = (a = s.next()).done) && (i.push(a.value),
								!e || i.length !== e); r = !0)
									;
							} catch (t) {
								o = !0,
								n = t
							} finally {
								try {
									!r && s.return && s.return()
								} finally {
									if (o)
										throw n
								}
							}
							return i
						}(t, e);
					throw new TypeError("Invalid attempt to destructure non-iterable instance")
				}
				function i(t, e) {
					for (var n = 0; n < e.length; n++) {
						var i = e[n];
						i.enumerable = i.enumerable || !1,
						i.configurable = !0,
						"value"in i && (i.writable = !0),
						Object.defineProperty(t, i.key, i)
					}
				}
				e.EventEmitter = (function(t, e, n) {
					return e && i(t.prototype, e),
					n && i(t, n),
					t
				}(n, [{
					key: "on",
					value: function(t, e, n) {
						var i = arguments.length <= 2 || void 0 === n ? this : n;
						this._events[t] || (this._events[t] = []),
						this._events[t].push([e, i])
					}
				}, {
					key: "once",
					value: function(e, n, t) {
						var i = arguments.length <= 2 || void 0 === t ? this : t
						  , r = !1;
						this.on(e, function t() {
							this.off(e, t),
							r || (r = !0,
							n.apply(i, arguments))
						})
					}
				}, {
					key: "off",
					value: function(t, e) {
						var n = this._events[t];
						if (n)
							for (var i = n.length; i--; )
								n[i][0] === e && (n[i][0] = void 0)
					}
				}, {
					key: "trigger",
					value: function(t) {
						var e = this._events[t];
						if (e)
							for (var n = e.length, i = [].concat(function(t) {
								if (Array.isArray(t)) {
									for (var e = 0, n = Array(t.length); e < t.length; e++)
										n[e] = t[e];
									return n
								}
								return Array.from(t)
							}(e)), r = 0; r < n; r++) {
								var o = s(i[r], 2)
								  , a = o[0];
								a && a.apply(o[1], [].slice.call(arguments, 1))
							}
					}
				}]),
				n);
				function n() {
					(function(t) {
						if (!(t instanceof n))
							throw new TypeError("Cannot call a class as a function")
					}
					)(this),
					this._events = {}
				}
			}
			, function(t, e) {
				Object.defineProperty(e, "__esModule", {
					value: !0
				}),
				e.momentum = function(t, e, n, i, r, o) {
					var a = t - e
					  , s = Math.abs(a) / n
					  , c = o.itemHeight
					  , u = o.swipeBounceTime
					  , f = o.bounceTime
					  , h = o.swipeTime
					  , l = o.wheel ? 4 : 15
					  , d = t + s / o.deceleration * (a < 0 ? -1 : 1);
					return o.wheel && c && (d = Math.round(d / c) * c),
					d < i ? (d = r ? i - r / l * s : i,
					h = u - f) : 0 < d && (d = r ? r / l * s : 0,
					h = u - f),
					{
						destination: Math.round(d),
						duration: h
					}
				}
			}
			, function(t, e) {
				Object.defineProperty(e, "__esModule", {
					value: !0
				}),
				e.extend = function(t, e) {
					for (var n in e)
						t[n] = e[n]
				}
				;
				e.requestAnimationFrame = window.requestAnimationFrame || window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || window.oRequestAnimationFrame || function(t) {
					return window.setTimeout(t, (t.interval || 100 / 60) / 2)
				}
				,
				e.cancelAnimationFrame = window.cancelAnimationFrame || window.webkitCancelAnimationFrame || window.mozCancelAnimationFrame || window.oCancelAnimationFrame || function(t) {
					window.clearTimeout(t)
				}
			}
			],
			n.c = r,
			n.p = "/assets/",
			n(0));
			function n(t) {
				if (r[t])
					return r[t].exports;
				var e = r[t] = {
					exports: {},
					id: t,
					loaded: !1
				};
				return i[t].call(e.exports, e, e.exports, n),
				e.loaded = !0,
				e.exports
			}
			var i, r
		}
		, function(t, e) {
			e.__esModule = !0;
			function s(t, e) {
				if (Array.isArray(t))
					return t;
				if (Symbol.iterator in Object(t))
					return function(t, e) {
						var n, i = [], r = !0, o = !1;
						try {
							for (var a, s = t[Symbol.iterator](); !(r = (a = s.next()).done) && (i.push(a.value),
							!e || i.length !== e); r = !0)
								;
						} catch (t) {
							o = !0,
							n = t
						} finally {
							try {
								!r && s.return && s.return()
							} finally {
								if (o)
									throw n
							}
						}
						return i
					}(t, e);
				throw new TypeError("Invalid attempt to destructure non-iterable instance")
			}
			var n = (function(t, e, n) {
				return e && r(t.prototype, e),
				n && r(t, n),
				t
			}(i, [{
				key: "on",
				value: function(t, e, n) {
					var i = 2 < arguments.length && void 0 !== n ? n : this;
					this._events[t] || (this._events[t] = []),
					this._events[t].push([e, i])
				}
			}, {
				key: "once",
				value: function(e, n, t) {
					var i = 2 < arguments.length && void 0 !== t ? t : this
					  , r = !1;
					this.on(e, function t() {
						this.off(e, t),
						r || (r = !0,
						n.apply(i, arguments))
					})
				}
			}, {
				key: "off",
				value: function(t, e) {
					var n = this._events[t];
					if (n)
						for (var i = n.length; i--; )
							n[i][0] === e && (n[i][0] = void 0)
				}
			}, {
				key: "trigger",
				value: function(t) {
					var e = this._events[t];
					if (e)
						for (var n = e.length, i = [].concat(function(t) {
							if (Array.isArray(t)) {
								for (var e = 0, n = Array(t.length); e < t.length; e++)
									n[e] = t[e];
								return n
							}
							return Array.from(t)
						}(e)), r = 0; r < n; r++) {
							var o = s(i[r], 2)
							  , a = o[0];
							a && a.apply(o[1], [].slice.call(arguments, 1))
						}
				}
			}]),
			i);
			function i() {
				(function(t) {
					if (!(t instanceof i))
						throw new TypeError("Cannot call a class as a function")
				}
				)(this),
				this._events = {}
			}
			function r(t, e) {
				for (var n = 0; n < e.length; n++) {
					var i = e[n];
					i.enumerable = i.enumerable || !1,
					i.configurable = !0,
					"value"in i && (i.writable = !0),
					Object.defineProperty(t, i.key, i)
				}
			}
			e.default = n,
			t.exports = e.default
		}
		, function(t, e) {
			e.__esModule = !0,
			e.extend = function(t, e) {
				for (var n in e)
					t[n] = e[n]
			}
		}
		, function(t, e) {
			function i(t, e) {
				return RegExp("(^|\\s)" + e + "(\\s|$)").test(t.className)
			}
			e.__esModule = !0,
			e.createDom = function(t) {
				var e = document.createElement("div");
				return e.innerHTML = t,
				e.childNodes[0]
			}
			,
			e.addEvent = function(t, e, n, i) {
				t.addEventListener(e, n, !!i)
			}
			,
			e.removeEvent = function(t, e, n, i) {
				t.removeEventListener(e, n, !!i)
			}
			,
			e.hasClass = i,
			e.addClass = function(t, e) {
				if (!i(t, e)) {
					var n = t.className.split(" ");
					n.push(e),
					t.className = n.join(" ")
				}
			}
			,
			e.removeClass = function(t, e) {
				if (i(t, e)) {
					var n = RegExp("(^|\\s)" + e + "(\\s|$)", "g");
					t.className = t.className.replace(n, " ")
				}
			}
		}
		, function(t, e, n) {
			var i = n(7);
			t.exports = (i.default || i).template(function(t, e, o, n, i) {
				function r(t, e) {
					var n, i, r = "";
					return r += '\n				<li class="wheel-item" data-val="',
					n = (i = o.value) ? i.call(t, {
						hash: {},
						data: e
					}) : typeof (i = t && t.value) === u ? i.call(t, {
						hash: {},
						data: e
					}) : i,
					r += f(n) + '">',
					n = (i = o.text) ? i.call(t, {
						hash: {},
						data: e
					}) : typeof (i = t && t.text) === u ? i.call(t, {
						hash: {},
						data: e
					}) : i,
					r + f(n) + "</li>\n			  "
				}
				this.compilerInfo = [4, ">= 1.0.0"],
				o = this.merge(o, t.helpers),
				i = i || {};
				var a, s, c = "", u = "function", f = this.escapeExpression, h = this;
				return c += '<div class="picker">\n  <div class="picker-mask mask-hook"></div>\n  <div class="picker-panel panel-hook">\n	<div class="picker-choose choose-hook">\n	  <span class="cancel cancel-hook">取消</span>\n	  <span class="confirm confirm-hook">确定</span>\n	  <h1 class="picker-title">',
				a = (s = o.title) ? s.call(e, {
					hash: {},
					data: i
				}) : typeof (s = e && e.title) === u ? s.call(e, {
					hash: {},
					data: i
				}) : s,
				c += f(a) + '</h1>\n	</div>\n	<div class="picker-content">\n	  <div class="mask-top border-1px"></div>\n	  <div class="mask-bottom border-1px"></div>\n	  <div class="wheel-wrapper wheel-wrapper-hook">\n		',
				!(a = o.each.call(e, e && e.data, {
					hash: {},
					inverse: h.noop,
					fn: h.program(1, function(t, e) {
						var n, i = "";
						return i += '\n		  <div class="wheel wheel-hook">\n			<ul class="wheel-scroll wheel-scroll-hook">\n			  ',
						!(n = o.each.call(t, t, {
							hash: {},
							inverse: h.noop,
							fn: h.program(2, r, e),
							data: e
						})) && 0 !== n || (i += n),
						i + "\n			</ul>\n		  </div>\n		"
					}, i),
					data: i
				})) && 0 !== a || (c += a),
				c + '\n	  </div>\n	</div>\n	<div class="picker-footer footer-hook"></div>\n  </div>\n</div>'
			})
		}
		, function(t, e, n) {
			t.exports = n(8)
		}
		, function(t, e, n) {
			function i() {
				var e = new r.HandlebarsEnvironment;
				return s.extend(e, r),
				e.SafeString = o,
				e.Exception = a,
				e.Utils = s,
				e.VM = c,
				e.template = function(t) {
					return c.template(t, e)
				}
				,
				e
			}
			var r = n(9)
			  , o = n(11).default
			  , a = n(12).default
			  , s = n(10)
			  , c = n(13)
			  , u = i();
			u.create = i,
			e.default = u
		}
		, function(t, e, n) {
			function i(t, e) {
				this.helpers = t || {},
				this.partials = e || {},
				(r = this).registerHelper("helperMissing", function(t) {
					if (2 !== arguments.length)
						throw new a("Missing helper: '" + t + "'")
				}),
				r.registerHelper("blockHelperMissing", function(t, e) {
					var n = e.inverse || function() {}
					  , i = e.fn;
					return f(t) && (t = t.call(this)),
					!0 === t ? i(this) : !1 === t || null == t ? n(this) : u(t) ? 0 < t.length ? r.helpers.each(t, e) : n(this) : i(t)
				}),
				r.registerHelper("each", function(t, e) {
					var n, i = e.fn, r = e.inverse, o = 0, a = "";
					if (f(t) && (t = t.call(this)),
					e.data && (n = l(e.data)),
					t && "object" == typeof t)
						if (u(t))
							for (var s = t.length; o < s; o++)
								n && (n.index = o,
								n.first = 0 === o,
								n.last = o === t.length - 1),
								a += i(t[o], {
									data: n
								});
						else
							for (var c in t)
								t.hasOwnProperty(c) && (n && (n.key = c,
								n.index = o,
								n.first = 0 === o),
								a += i(t[c], {
									data: n
								}),
								o++);
					return 0 === o && (a = r(this)),
					a
				}),
				r.registerHelper("if", function(t, e) {
					return f(t) && (t = t.call(this)),
					!e.hash.includeZero && !t || o.isEmpty(t) ? e.inverse(this) : e.fn(this)
				}),
				r.registerHelper("unless", function(t, e) {
					return r.helpers.if.call(this, t, {
						fn: e.inverse,
						inverse: e.fn,
						hash: e.hash
					})
				}),
				r.registerHelper("with", function(t, e) {
					if (f(t) && (t = t.call(this)),
					!o.isEmpty(t))
						return e.fn(t)
				}),
				r.registerHelper("log", function(t, e) {
					r.log(e.data && null != e.data.level ? parseInt(e.data.level, 10) : 1, t)
				});
				var r
			}
			function r(t, e) {
				h.log(t, e)
			}
			var o = n(10)
			  , a = n(12).default;
			e.VERSION = "1.3.0";
			e.COMPILER_REVISION = 4;
			e.REVISION_CHANGES = {
				1: "<= 1.0.rc.2",
				2: "== 1.0.0-rc.3",
				3: "== 1.0.0-rc.4",
				4: ">= 1.0.0"
			};
			var u = o.isArray
			  , f = o.isFunction
			  , s = o.toString
			  , c = "[object Object]";
			(e.HandlebarsEnvironment = i).prototype = {
				constructor: i,
				logger: h,
				log: r,
				registerHelper: function(t, e, n) {
					if (s.call(t) === c) {
						if (n || e)
							throw new a("Arg not supported with multiple helpers");
						o.extend(this.helpers, t)
					} else
						n && (e.not = n),
						this.helpers[t] = e
				},
				registerPartial: function(t, e) {
					s.call(t) === c ? o.extend(this.partials, t) : this.partials[t] = e
				}
			};
			var h = {
				methodMap: {
					0: "debug",
					1: "info",
					2: "warn",
					3: "error"
				},
				DEBUG: 0,
				INFO: 1,
				WARN: 2,
				ERROR: 3,
				level: 3,
				log: function(t) {
					if (h.level <= t)
						;
				}
			};
			e.logger = h,
			e.log = r;
			var l = function(t) {
				var e = {};
				return o.extend(e, t),
				e
			};
			e.createFrame = l
		}
		, function(t, e, n) {
			function i(t) {
				return o[t] || "&amp;"
			}
			var r = n(11).default
			  , o = {
				"&": "&amp;",
				"<": "&lt;",
				">": "&gt;",
				'"': "&quot;",
				"'": "&#x27;",
				"`": "&#x60;"
			}
			  , a = /[&<>"'`]/g
			  , s = /[&<>"'`]/;
			e.extend = function(t, e) {
				for (var n in e)
					Object.prototype.hasOwnProperty.call(e, n) && (t[n] = e[n])
			}
			;
			var c, u = Object.prototype.toString;
			e.toString = u;
			(c = function(t) {
				return "function" == typeof t
			}
			)(/x/) && (c = function(t) {
				return "function" == typeof t && "[object Function]" === u.call(t)
			}
			);
			e.isFunction = c;
			var f = Array.isArray || function(t) {
				return !(!t || "object" != typeof t) && "[object Array]" === u.call(t)
			}
			;
			e.isArray = f,
			e.escapeExpression = function(t) {
				return t instanceof r ? "" + t : t || 0 === t ? s.test(t = "" + t) ? t.replace(a, i) : t : ""
			}
			,
			e.isEmpty = function(t) {
				return !t && 0 !== t || !(!f(t) || 0 !== t.length)
			}
		}
		, function(t, e) {
			function n(t) {
				this.string = t
			}
			n.prototype.toString = function() {
				return "" + this.string
			}
			,
			e.default = n
		}
		, function(t, e) {
			function n(t, e) {
				var n;
				e && e.firstLine && (t += " - " + (n = e.firstLine) + ":" + e.firstColumn);
				for (var i = Error.prototype.constructor.call(this, t), r = 0; r < 7; r++)
					this[o[r]] = i[o[r]];
				n && (this.lineNumber = n,
				this.column = e.firstColumn)
			}
			var o = ["description", "fileName", "lineNumber", "message", "name", "number", "stack"];
			n.prototype = Error(),
			e.default = n
		}
		, function(t, e, n) {
			function r(t, n, i) {
				function e(t, e) {
					return n(t, (e = e || {}).data || i)
				}
				return e.program = t,
				e.depth = 0,
				e
			}
			var i = n(10)
			  , u = n(12).default
			  , o = n(9).COMPILER_REVISION
			  , a = n(9).REVISION_CHANGES;
			e.checkRevision = function(t) {
				var e = t && t[0] || 1;
				if (e !== o) {
					if (e < o) {
						throw new u("Template was precompiled with an older version of Handlebars than the current runtime. Please update your precompiler to a newer version (" + a[o] + ") or downgrade your runtime to an older version (" + a[e] + ").")
					}
					throw new u("Template was precompiled with a newer version of Handlebars than the current runtime. Please update your runtime to a newer version (" + t[1] + ").")
				}
			}
			,
			e.template = function(a, c) {
				if (!c)
					throw new u("No environment passed to template");
				var s = {
					escapeExpression: i.escapeExpression,
					invokePartial: function(t, e, n, i, r, o) {
						var a = c.VM.invokePartial.apply(this, arguments);
						if (null != a)
							return a;
						if (c.compile) {
							var s = {
								helpers: i,
								partials: r,
								data: o
							};
							return r[e] = c.compile(t, {
								data: void 0 !== o
							}, c),
							r[e](n, s)
						}
						throw new u("The partial " + e + " could not be compiled when running in runtime-only mode")
					},
					programs: [],
					program: function(t, e, n) {
						var i = this.programs[t];
						return n ? r(t, e, n) : i || (this.programs[t] = r(t, e))
					},
					merge: function(t, e) {
						var n = t || e;
						return t && e && t !== e && (i.extend(n = {}, e),
						i.extend(n, t)),
						n
					},
					programWithDepth: c.VM.programWithDepth,
					noop: c.VM.noop,
					compilerInfo: null
				};
				return function(t, e) {
					var n, i, r = (e = e || {}).partial ? e : c;
					e.partial || (n = e.helpers,
					i = e.partials);
					var o = a.call(s, r, t, n, i, e.data);
					return e.partial || c.VM.checkRevision(s.compilerInfo),
					o
				}
			}
			,
			e.programWithDepth = function(t, n, i) {
				function e(t, e) {
					return n.apply(this, [t, (e = e || {}).data || i].concat(r))
				}
				var r = Array.prototype.slice.call(arguments, 3);
				return e.program = t,
				e.depth = r.length,
				e
			}
			,
			e.program = r,
			e.invokePartial = function(t, e, n, i, r, o) {
				var a = {
					partial: !0,
					helpers: i,
					partials: r,
					data: o
				};
				if (void 0 === t)
					throw new u("The partial " + e + " could not be found");
				if (t instanceof Function)
					return t(n, a)
			}
			,
			e.noop = function() {
				return ""
			}
		}
		, function(t, e, n) {
			var i = n(7);
			t.exports = (i.default || i).template(function(t, e, o, n, i) {
				this.compilerInfo = [4, ">= 1.0.0"],
				o = this.merge(o, t.helpers);
				var r, a = "function", s = this.escapeExpression;
				return (r = o.each.call(e, e, {
					hash: {},
					inverse: this.noop,
					fn: this.program(1, function(t, e) {
						var n, i, r = "";
						return r += '\n  <li class="wheel-item" data-val="',
						n = (i = o.value) ? i.call(t, {
							hash: {},
							data: e
						}) : typeof (i = t && t.value) == a ? i.call(t, {
							hash: {},
							data: e
						}) : i,
						r += s(n) + '">',
						n = (i = o.text) ? i.call(t, {
							hash: {},
							data: e
						}) : typeof (i = t && t.text) == a ? i.call(t, {
							hash: {},
							data: e
						}) : i,
						r + s(n) + "</li>\n"
					}, i = i || {}),
					data: i
				})) || 0 === r ? r : ""
			})
		}
		, function(t, e, n) {
			var i = n(16);
			"string" == typeof i && (i = [[t.id, i, ""]]);
			n(18)(i, {});
			i.locals && (t.exports = i.locals)
		}
		, function(t, e, n) {
			(t.exports = n(17)()).push([t.id, '.picker div{display:block;flex-direction:initial;}.picker{display:none;position:fixed;top:0;z-index:100;width:100%;height:100%;overflow:hidden;text-align:center;font-family:PingFang SC,STHeitiSC-Light,Helvetica-Light,arial,sans-serif;font-size:14px;-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;user-select:none}.picker .picker-mask{position:absolute;z-index:500;width:100%;height:100%;transition:all .5s;-webkit-transition:all .5s;background:transparent;opacity:0}.picker .picker-mask.show{background:rgba(0,0,0,.6);opacity:1}.picker .picker-panel{position:absolute;z-index:600;bottom:0;width:100%;height:243px;background:#fff;transform:translateY(243px);-webkit-transform:translateY(243px);transition:all .5s;-webkit-transition:all .5s}.picker .picker-panel.show{transform:translateY(0);-webkit-transform:translateY(0)}.picker .picker-panel .picker-choose{position:relative;height:50px;color:#878787;font-size:14px}.picker .picker-panel .picker-choose .picker-title{line-height:50px;font-size:19px;text-align:center;color:#333}.picker .picker-panel .picker-choose .cancel,.picker .picker-panel .picker-choose .confirm{position:absolute;padding:10px;top:6px}.picker .picker-panel .picker-choose .confirm{right:0;color:#fa8919}.picker .picker-panel .picker-choose .cancel{left:0}.picker .picker-panel .picker-content{position:relative}.picker .picker-panel .picker-content .mask-bottom,.picker .picker-panel .picker-content .mask-top{position:absolute;z-index:10;width:100%;height:68px;pointer-events:none;transform:translateZ(0);-webkit-transform:translateZ(0)}.picker .picker-panel .picker-content .mask-top{top:0;background:-webkit-gradient(linear,left bottom,left top,from(hsla(0,0%,100%,.4)),to(hsla(0,0%,100%,.8)));background:-o-linear-gradient(bottom,hsla(0,0%,100%,.4),hsla(0,0%,100%,.8))}.picker .picker-panel .picker-content .mask-top:after,.picker .picker-panel .picker-content .mask-top:before{display:block;position:absolute;border-top:1px solid #ccc;left:0;width:100%;content:" "}.picker .picker-panel .picker-content .mask-top:before{display:none;top:0}.picker .picker-panel .picker-content .mask-top:after{display:block;bottom:0}.picker .picker-panel .picker-content .mask-bottom{bottom:0;background:-webkit-gradient(linear,left top,left bottom,from(hsla(0,0%,100%,.4)),to(hsla(0,0%,100%,.8)));background:-o-linear-gradient(top,hsla(0,0%,100%,.4),hsla(0,0%,100%,.8))}.picker .picker-panel .picker-content .mask-bottom:after,.picker .picker-panel .picker-content .mask-bottom:before{display:block;position:absolute;border-top:1px solid #ccc;left:0;width:100%;content:" "}.picker .picker-panel .picker-content .mask-bottom:before{display:block;top:0}.picker .picker-panel .picker-content .mask-bottom:after{display:none;bottom:0}.picker .picker-panel .wheel-wrapper{display:-ms-flexbox;display:-webkit-box;display:flex;padding:0 10px}.picker .picker-panel .wheel-wrapper .wheel{-ms-flex:1 1 1e-9px;-webkit-box-flex:1;flex:1;flex-basis:1e-9px;width:1%;height:173px;overflow:hidden;font-size:21px}.picker .picker-panel .wheel-wrapper .wheel .wheel-scroll{margin-top:68px;line-height:36px}.picker .picker-panel .wheel-wrapper .wheel .wheel-scroll .wheel-item{height:36px;overflow:hidden;white-space:nowrap;color:#333}.picker .picker-footer{height:20px}@media (-webkit-min-device-pixel-ratio:1.5),(min-device-pixel-ratio:1.5){.border-1px:after,.border-1px:before{-webkit-transform:scaleY(.7);-webkit-transform-origin:0 0;transform:scaleY(.7)}.border-1px:after{-webkit-transform-origin:left bottom}}@media (-webkit-min-device-pixel-ratio:2),(min-device-pixel-ratio:2){.border-1px:after,.border-1px:before{-webkit-transform:scaleY(.5);transform:scaleY(.5)}}', ""])
		}
		, function(t) {
			t.exports = function() {
				var a = [];
				return a.toString = function() {
					for (var t = [], e = 0; e < this.length; e++) {
						var n = this[e];
						n[2] ? t.push("@media " + n[2] + "{" + n[1] + "}") : t.push(n[1])
					}
					return t.join("")
				}
				,
				a.i = function(t, e) {
					"string" == typeof t && (t = [[null, t, ""]]);
					for (var n = {}, i = 0; i < this.length; i++) {
						var r = this[i][0];
						"number" == typeof r && (n[r] = !0)
					}
					for (i = 0; i < t.length; i++) {
						var o = t[i];
						"number" == typeof o[0] && n[o[0]] || (e && !o[2] ? o[2] = e : e && (o[2] = "(" + o[2] + ") and (" + e + ")"),
						a.push(o))
					}
				}
				,
				a
			}
		}
		, function(t) {
			function s(t, e) {
				for (var n = 0; n < t.length; n++) {
					var i = t[n]
					  , r = v[i.id];
					if (r) {
						r.refs++;
						for (var o = 0; o < r.parts.length; o++)
							r.parts[o](i.parts[o]);
						for (; o < i.parts.length; o++)
							r.parts.push(l(i.parts[o], e))
					} else {
						var a = [];
						for (o = 0; o < i.parts.length; o++)
							a.push(l(i.parts[o], e));
						v[i.id] = {
							id: i.id,
							refs: 1,
							parts: a
						}
					}
				}
			}
			function c(t) {
				for (var e = [], n = {}, i = 0; i < t.length; i++) {
					var r = t[i]
					  , o = r[0]
					  , a = {
						css: r[1],
						media: r[2],
						sourceMap: r[3]
					};
					n[o] ? n[o].parts.push(a) : e.push(n[o] = {
						id: o,
						parts: [a]
					})
				}
				return e
			}
			function u(t, e) {
				var n = r()
				  , i = o[o.length - 1];
				if ("top" === t.insertAt)
					i ? i.nextSibling ? n.insertBefore(e, i.nextSibling) : n.appendChild(e) : n.insertBefore(e, n.firstChild),
					o.push(e);
				else {
					if ("bottom" !== t.insertAt)
						throw Error("Invalid value for parameter 'insertAt'. Must be 'top' or 'bottom'.");
					n.appendChild(e)
				}
			}
			function f(t) {
				t.parentNode.removeChild(t);
				var e = o.indexOf(t);
				0 <= e && o.splice(e, 1)
			}
			function h(t) {
				var e = document.createElement("style");
				return e.type = "text/css",
				u(t, e),
				e
			}
			function l(e, t) {
				var n, i, r;
				if (t.singleton) {
					var o = m++;
					n = p = p || h(t),
					i = d.bind(null, n, o, !1),
					r = d.bind(null, n, o, !0)
				} else
					r = e.sourceMap && "function" == typeof URL && "function" == typeof URL.createObjectURL && "function" == typeof URL.revokeObjectURL && "function" == typeof Blob && "function" == typeof btoa ? (n = (a = t,
					(s = document.createElement("link")).rel = "stylesheet",
					u(a, s),
					s),
					i = function(t, e) {
						var n = e.css
						  , i = e.sourceMap;
						i && (n += "\n/*# sourceMappingURL=data:application/json;base64," + btoa(unescape(encodeURIComponent(JSON.stringify(i)))) + " */");
						var r = new Blob([n],{
							type: "text/css"
						})
						  , o = t.href;
						t.href = URL.createObjectURL(r),
						o && URL.revokeObjectURL(o)
					}
					.bind(null, n),
					function() {
						f(n),
						n.href && URL.revokeObjectURL(n.href)
					}
					) : (n = h(t),
					i = function(t, e) {
						var n = e.css
						  , i = e.media;
						if (i && t.setAttribute("media", i),
						t.styleSheet)
							t.styleSheet.cssText = n;
						else {
							for (; t.firstChild; )
								t.removeChild(t.firstChild);
							t.appendChild(document.createTextNode(n))
						}
					}
					.bind(null, n),
					function() {
						f(n)
					}
					);
				var a, s;
				return i(e),
				function(t) {
					if (t) {
						if (t.css === e.css && t.media === e.media && t.sourceMap === e.sourceMap)
							return;
						i(e = t)
					} else
						r()
				}
			}
			function d(t, e, n, i) {
				var r = n ? "" : i.css;
				if (t.styleSheet)
					t.styleSheet.cssText = g(e, r);
				else {
					var o = document.createTextNode(r)
					  , a = t.childNodes;
					a[e] && t.removeChild(a[e]),
					a.length ? t.insertBefore(o, a[e]) : t.appendChild(o)
				}
			}
			function e(t) {
				var e;
				return function() {
					return void 0 === e && (e = t.apply(this, arguments)),
					e
				}
			}
			var v = {}
			  , n = e(function() {
				return /msie [6-9]\b/.test(window.navigator.userAgent.toLowerCase())
			})
			  , r = e(function() {
				return document.head || document.getElementsByTagName("head")[0]
			})
			  , p = null
			  , m = 0
			  , o = [];
			t.exports = function(t, o) {
				void 0 === (o = o || {}).singleton && (o.singleton = n()),
				void 0 === o.insertAt && (o.insertAt = "bottom");
				var a = c(t);
				return s(a, o),
				function(t) {
					for (var e = [], n = 0; n < a.length; n++) {
						(i = v[a[n].id]).refs--,
						e.push(i)
					}
					if (t)
						s(c(t), o);
					for (n = 0; n < e.length; n++) {
						var i;
						if (0 === (i = e[n]).refs) {
							for (var r = 0; r < i.parts.length; r++)
								i.parts[r]();
							delete v[i.id]
						}
					}
				}
			}
			;
			var g = (i = [],
			function(t, e) {
				return i[t] = e,
				i.filter(Boolean).join("\n")
			}
			);
			var i
		}
		],
		n.c = r,
		n.p = "",
		n(0);
		function n(t) {
			if (r[t])
				return r[t].exports;
			var e = r[t] = {
				exports: {},
				id: t,
				loaded: !1
			};
			return i[t].call(e.exports, e, e.exports, n),
			e.loaded = !0,
			e.exports
		}
		var i, r
	}
	var Picker = PickerFactory();
	!function(t) {
		var e, n, i, r, o, a, s, c = '<svg><symbol id="icon-loading" viewBox="0 0 1024 1024"><path d="M563.2 819.2a102.4 102.4 0 1 1 0 204.8 102.4 102.4 0 0 1 0-204.8z m-320.4608-153.6a128 128 0 1 1 0 256 128 128 0 0 1 0-256z m592.7936 25.6a102.4 102.4 0 1 1 0 204.8 102.4 102.4 0 0 1 0-204.8zM947.2 477.1328a76.8 76.8 0 1 1 0 153.6 76.8 76.8 0 0 1 0-153.6zM128 307.2a128 128 0 1 1 0 256 128 128 0 0 1 0-256z m782.6432-40.6016a51.2 51.2 0 1 1 0 102.4 51.2 51.2 0 0 1 0-102.4zM409.6 0a153.6 153.6 0 1 1 0 307.2 153.6 153.6 0 0 1 0-307.2z m384 153.6a25.6 25.6 0 1 1 0 51.2 25.6 25.6 0 0 1 0-51.2z"  ></path></symbol></svg>';
		if ((e = document.getElementsByTagName("script"))[e.length - 1].getAttribute("data-injectcss") && !t.__iconfont__svg__cssinject__) {
			t.__iconfont__svg__cssinject__ = !0;
			try {
				document.write("<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>")
			} catch (t) {}
		}
		function u() {
			a || (a = !0,
			r())
		}
		n = function() {
			var t, e, n, i, r = document.createElement("div");
			r.innerHTML = c,
			c = null,
			(t = r.getElementsByTagName("svg")[0]) && (t.setAttribute("aria-hidden", "true"),
			t.style.position = "absolute",
			t.style.width = 0,
			t.style.height = 0,
			t.style.overflow = "hidden",
			e = t,
			(n = document.body).firstChild ? (i = n.firstChild).parentNode.insertBefore(e, i) : n.appendChild(e))
		}
		,
		document.addEventListener ? ~["complete", "loaded", "interactive"].indexOf(document.readyState) ? setTimeout(n, 0) : (i = function() {
			document.removeEventListener("DOMContentLoaded", i, !1),
			n()
		}
		,
		document.addEventListener("DOMContentLoaded", i, !1)) : document.attachEvent && (r = n,
		o = t.document,
		a = !1,
		(s = function() {
			try {
				o.documentElement.doScroll("left")
			} catch (t) {
				return void setTimeout(s, 50)
			}
			u()
		}
		)(),
		o.onreadystatechange = function() {
			"complete" == o.readyState && (o.onreadystatechange = null,
			u())
		}
		)
	}(window);
	!function() {
		var t = document.createElement("style");
		document.head.appendChild(t);
		t.setAttribute("type", "text/css");
		t.textContent = '.avm-hide-scrollbar::-webkit-scrollbar{display: none;}.avm-mask-modal{position: fixed;top: 0;left: 0;right: 0;bottom: 0;z-index: 999;}.avm-progress-wrap{width: 140px;min-height: 140px;position: fixed;top: 50%;left: 50%;display: flex;flex-direction: column;justify-content: center;box-sizing: border-box;padding: 10px;margin-top: -70px;margin-left: -70px;text-align: center;border-radius: 10px;background-color: rgba(0,0,0,.5);z-index: 9999;}.avm-iconfont-svg{height: 50px;width: 100%;margin-bottom: 10px;fill: white;overflow: hidden;animation: progress-turn 2s linear infinite;-webkit-animation: progress-turn 2s linear infinite;}.avm-progress-title{margin: 0;line-height: 2;color: white;font-size: 18px;text-align: center;}.avm-progress-text{margin: 0;color: white;line-height: 1.2;font-size: 14px;text-align: center;}@keyframes progress-turn{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}@-webkit-keyframes progress-turn{0%{-webkit-transform:rotate(0deg);}100%{-webkit-transform:rotate(360deg);}}.avm-actionsheet-mask{position: fixed;top: 0;left: 0;right: 0;bottom: 0;z-index: 999;background-color: rgba(0,0,0,.5)}.avm-actionsheet-wrap{position: absolute;bottom: 0;width: 100%;background-color: white;border-radius: 15px 15px 0 0;overflow: hidden;transition: transform 0.3s;-webkit-transition: -webkit-transform 0.3s;transform: translate3d(0,100%,0);-webkit-transform: translate3d(0,100%,0);}.avm-actionsheet-item{text-align: center;line-height: 50px;}.avm-actionsheet-cancel{text-align: center;line-height: 50px;border-top: 5px solid #eee;color: #646566;}.avm-actionsheet-item:active, .avm-actionsheet-cancel:active{background-color: #f2f3f5;}.avm-toast{position: fixed;bottom: 20vh;left: 50%;margin-right: -50%;transform: translateX(-50%);-webkit-transform: translateX(-50%);max-width: 66vw;min-width: 100px;text-align: center;line-height: 1.5;padding: 10px;border-radius: 5px;background-color: rgba(0,0,0,.5);font-size: 16px;color: white;transition: all 0.2s;}.avm-confirm-mask{position: fixed;top: 0;left: 0;right: 0;bottom: 0;background-color: rgba(0,0,0,.5);display: flex;display: -webkit-flex;align-items: center;align-items: center;-webkit-align-items: center;-justify-content: center;-webkit-justify-content: center;}.avm-confirm{width: 70%;box-sizing: border-box;background-color: white;border-radius: 10px;padding-top: 20px;transition: transform 0.2s;-webkit-transition: transform 0.2s;transform: scale3d(0, 0, 0);-webkit-transform: scale3d(0, 0, 0);}.avm-confirm-title{margin: 0;text-align: center;font-size: 18px;font-weight: 400;padding: 0 15px 20px;}.avm-confirm-msg{margin: 0;line-height: 1.5;padding: 0 15px 10px;color: #444;}.avm-confirm-btn-wrap{display: flex;display: -webkit-flex;flex-direction: row;-webkit-flex-direction: row;border-top: 1px solid #ddd;margin-top: 20px;}.avm-confirm-btn{flex: 1;-webkit-flex: 1;text-align: center;padding: 1em 0;}.avm-confirm-btn:nth-child(n+2){border-left: 1px solid #ddd}.avm-switch{width: 52px;height: 32px;display: inline-block;border: 1px solid #DFDFDF;outline: none;border-radius: 16px;box-sizing: border-box;background-color: #DFDFDF;transition: background-color 0.1s, border 0.1s;-webkit-appearance: none;appearance: none;position: relative;vertical-align: middle; background-color: rgb(4, 190, 2);}.avm-switch:checked{border: 1px solid transparent;}.avm-switch:disabled{opacity: 0.5;}.avm-switch::before{content: " ";position: absolute;top: 0;left: 0;width: 50px;height: 30px;border-radius: 15px;background-color: #FDFDFD;transition: -webkit-transform 0.3s;transition: transform 0.3s;transition: transform 0.3s, -webkit-transform 0.3s;}.avm-switch:checked::before{\t-webkit-transform: scale(0);\ttransform: scale(0);}.avm-switch::after{\t content: " ";	position: absolute;	top: 0;	left: 0;	width: 30px;	height: 30px;	border-radius: 15px;	background-color: #FFFFFF;	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);	transition: -webkit-transform 0.3s;	transition: transform 0.3s;	transition: transform 0.3s, -webkit-transform 0.3s;}.avm-switch:checked::after{\t-webkit-transform: translateX(20px);\ttransform: translateX(20px);}.avm-checkbox{	margin-right: 5px;	-webkit-appearance: none;	appearance: none;	outline: 0;	text-indent: 0;	border: 1px solid #D1D1D1;	background-color: #FFFFFF;	border-radius: 3px;	width: 22px;	height: 22px;	position: relative;}.avm-checkbox__custom-icon{border:none;background-size:100%;}.avm-checkbox.avm-checkbox__default-icon:checked::before{content: "\\2713";font-size: 22px;position: absolute;top: 50%;left: 50%;transform: translate(-50%, -48%) scale(0.73);-webkit-transform: translate(-50%, -48%) scale(0.73);}.avm-checkbox:disabled{opacity: 0.5;}.avm-radio{-webkit-appearance: none;appearance: none;margin-right: 5px;outline: 0;border: 1px solid #D1D1D1;background-color: #ffffff;border-radius: 50%;width: 22px;height: 22px;position: relative;background-color: #09BB07;border-color: #09BB07;}.avm-radio:not(:checked){\tbackground-color: #ffffff !important;\tborder-color: #D1D1D1 !important;}.avm-radio:checked::before{content: "\\2713";color: #ffffff;font-size: 18px;position: absolute;top: 50%;left: 50%;transform: translate(-50%, -48%) scale(0.73);-webkit-transform: translate(-50%, -48%) scale(0.73);}.avm-radio:disabled{opacity: 0.5;}rich-text div,rich-text label,rich-text ul,rich-text li{display: initial;}[scroll-x=true]>*{white-space: initial;vertical-align: top;}img[mode=aspectFit]{\tobject-fit: contain;}img[mode=aspectFill]{\tobject-fit: cover;}img[mode=widthFix]{\theight: auto;}.avm-slider{	height: 2px;	background-image: linear-gradient(to right,#1aad19 0%, #e9e9e9 0%);	position: relative;\t margin: 16px 18px; \t border: none;	-webkit-appearance: none;	appearance: none;}.avm-slider::before{	content: "";	display: inline-block;	transform: translate(-50%, -50%);\t position: relative;\t top: 50%;	border-radius: 50%;	z-index: 2;	box-shadow: 0 0 4px rgb(0 0 0 / 20%);}.avm-slider::after{\tcontent: "";	width: 100%;	height: 100%;	background-color: transparent;	display: block;	position: absolute;	left: 0;}.avm-slider:disabled{opacity: 0.5;}.avm-slider::-webkit-slider-thumb {	-webkit-appearance: none;}.avm-textarea{\tdisplay: block;\twidth: 100%;\theight: auto;\tresize: none;\toutline: none;\tborder-color: #aaa;\tborder-radius: 5px;\tpadding:5px;\tbox-sizing: border-box;\tmin-height: 100px;}.avm-textarea::-webkit-scrollbar {  display: none;}'
	}();
	!function(t) {
		var a = -1
		  , s = ["background", "shadow", "color", "fontSize"]
		  , c = {
			b: "navigationBar",
			navigationBar: "b",
			t: "title",
			title: "t",
			n: "name",
			name: "n",
			u: "url",
			url: "u",
			p: "pageParam",
			pageParam: "p"
		}
		  , u = t.parent
		  , n = t.parent;
		window.$route = v();
		var f = {
			platform: "web",
			pageParam: function() {
				if ($route && $route.pageParam) {
					if ("sessionStorage" != $route.pageParam)
						return $route.pageParam;
					var t = sessionStorage.getItem($route.routeKey);
					return JSON.parse(t).pageParam
				}
				return {}
			}(),
			hasPermission: function(t) {
				return t.list.map(function(t) {
					return {
						name: t,
						granted: !0
					}
				})
			},
			ajax: function(t, e) {
				var n = {
					url: t.url,
					method: t.method || "get",
					timeout: t.timeout && 1e3 * t.timeout,
					responseType: t.dataType,
					responseEncoding: t.charset,
					headers: t.headers,
					withCredentials: !0
				};
				n.headers = Object.assign({
					"Cache-Control": "no-cache"
				}, n.headers);
				if (!t.data || !t.data.files) {
					t.tag && (n.cancelToken = new CancelToken(function(t) {
						self.cancel = t
					}
					));
					t.data && ("get" == n.method.toLowerCase() ? n.params = t.data.body || t.data.values : n.data = t.data.body || utils.toUrlEncoded(t.data.values));
					axios(n).then(function(t) {
						e(t.data)
					})
				} else {
					n.report = t.report;
					n.data = t.data;
					!function(n, e) {
						var i = new FormData;
						n.data.values && Object.keys(n.data.values).forEach(function(t) {
							i.append(t, n.data.values[t])
						});
						Object.keys(n.data.files).forEach(function(e) {
							filePath = n.data.files[e];
							filePath instanceof Array ? filePath.forEach(function(t) {
								i.append(e, d[t])
							}) : i.append(e, d[filePath])
						});
						if (n.report) {
							n.onUploadProgress = function(t) {
								e({
									status: 0,
									progress: parseInt(t.loaded / t.total * 100)
								})
							}
							;
							delete n.report
						}
						n.data = i;
						axios(n).then(function(t) {
							e(t.data)
						})
					}(n, e)
				}
			},
			openTabLayout: function(t) {
				if (t.hideNavigationBar)
					return e(t);
				t.navigationBar || (t.navigationBar = {});
				e(t)
			},
			setTabBarItemAttr: function() {},
			setTabBarAttr: function(t) {
				if (u === window) {
					if (!isNaN(+t.index)) {
						var e = _avm_root_config.tabBar
						  , n = t.index;
						if (a != n) {
							var i = u.document.getElementsByClassName("avm-win-tabbar__item");
							i[n].classList.add("avm-win-tabbar__item-active");
							i[n].getElementsByClassName("avm-win-tabbar__text")[0].style.color = e.selectedColor;
							var r = u.document.querySelector(".avm-win-tabbar__page__" + n);
							if (!r) {
								f._insertIframe(e.frames[n], u.document.querySelector(".avm-win-tabbar__page"), "avm-win-tabbar__page__" + n)
							}
							if (-1 !== a) {
								if (r) {
									r.style.display = "block";
									r.contentWindow.$document.$rootvm.onHide && r.contentWindow.$document.$rootvm.onHide()
								}
								var o = u.document.querySelector(".avm-win-tabbar__page__" + a);
								o.style.display = "none";
								o.contentWindow.$document.$rootvm.onHide && o.contentWindow.$document.$rootvm.onHide();
								i[a].getElementsByClassName("avm-win-tabbar__text")[0].style.color = e.color;
								i[a].classList.remove("avm-win-tabbar__item-active")
							}
							a = n
						}
					}
				} else
					u.api.setTabBarAttr(t)
			},
			setTabLayoutAttr: function(t) {
				if (t.title)
					if (u !== window && "root" == u.api._getRoute().name)
						u.api.setTabLayoutAttr({
							title: t.title
						});
					else {
						var e = document.getElementsByClassName("avm-navigation-bar")[0];
						e && (e.getElementsByClassName("avm-navigation-bar__title")[0].innerText = t.title)
					}
				if ("boolean" == typeof t.hideTabBar) {
					var n = u.document.getElementsByClassName("avm-win-tabbar__footer")[0];
					n && (t.hideTabBar && !n.classList.contains("avm-hide") ? n.classList.add("avm-hide") : !t.hideTabBar && n.classList.contains("avm-hide") && n.classList.remove("avm-hide"))
				}
			},
			setNavBarAttr: function(t) {
				if ("root" == u.api._getRoute().name)
					var e = u.document.getElementsByClassName("avm-navigation-bar")[0];
				else
					e = document.getElementsByClassName("avm-navigation-bar")[0];
				if (e) {
					t.background && (e.style["background-color"] = t.background);
					t.shadow && (e.style["border-bottom-color"] = t.shadow);
					t.color && (e.style.color = t.color)
				}
			},
			openWin: e,
			openFrame: function(t) {
				t = f._insertIframe(t, document.body, "avm-iframe-frame")
			},
			closeFrame: function(t) {
				var e = t && t.name || $route.name;
				n.document.body.removeChild(n.document.querySelector("iframe[name=" + e + "]"))
			},
			closeWin: function() {
				u.history.back()
			},
			closeToWin: function(e) {
				if (!e.delta) {
					var t = u.history.state.$routerArr
					  , n = t.findIndex(function(t) {
						return e.name == t
					});
					if (n == 0 && t.length == 1) {
						return;
					}
					-1 == n && (n = t.findIndex(function(t) {
						return "root" == t
					}));
					-1 != n ? u.history.go(-(t.length - n - 1)) : f.openWin({
						name: "root",
						url: avm_rootPath
					})
				} else
					u.history.go(-e.delta)
			},
			getPrefs: function(t, e) {
				var n = localStorage.getItem(t.key);
				if (t.sync)
					return n;
				e({
					value: n
				})
			},
			setPrefs: function(t) {
				"string" != typeof t.value && (t.value = JSON.stringify(t.value));
				localStorage.setItem(t.key, t.value)
			},
			removePrefs: function(t) {
				localStorage.removeItem(t.key)
			},
			setGlobalData: function(t) {
				var e = JSON.stringify({
					v: t.value
				});
				sessionStorage.setItem(t.key, e)
			},
			getGlobalData: function(t) {
				var e = sessionStorage.getItem(t.key);
				return e && JSON.parse(e).v
			},
			sendEvent: function(t) {
				o[t.name] && o[t.name](t.extra)
			},
			addEventListener: function(t, e) {
				o[t.name] = function(t) {
					e({
						value: t
					})
				}
			},
			confirm: h,
			toast: function(t) {
				var isIframeFirst = false;
				try{
					window.parent._avmState;
					isIframeFirst = false;
				}catch(e){
					isIframeFirst = true;
				}
				if (window.location.href.indexOf('.stml') == -1||window==window.top||isIframeFirst) {
					var e = document.querySelector(".avm-toast");
					if (e)
						e.innerText = t.msg;
					else {
						(e = document.createElement("div")).classList.add("avm-toast");
						e.innerText = t.msg;
						document.body.appendChild(e)
					}
					e.style.marginBottom = "-50PX";
					e.style.opacity = .3;
					setTimeout(function() {
						e.style.marginBottom = 0;
						e.style.opacity = 1
					}, 100);
					clearTimeout(i);
					clearTimeout(r);
					i = setTimeout(function() {
						e.style.opacity = .3;
						e.style.marginBottom = "50px";
						r = setTimeout(function() {
							e.parentNode.removeChild(e)
						}, 200)
					}, t.duration || 2e3)
				} else
					u.api.toast(t)
			},
			showProgress: function(t) {
				void 0 === (t = t || {}).modal && (t.modal = !0);
				void 0 === t.title && (t.title = "加载中");
				void 0 === t.text && (t.text = "请稍候...");
				document.body.insertAdjacentHTML("beforeend", '<div class="avm-progress-wrap" style="backgrouund">' + (t.modal ? '<div class="avm-mask-modal"></div>' : "") + '<svg class="avm-iconfont-svg" aria-hidden="true"><use xlink:href="#icon-loading"></use></svg>' + (t.title ? '<p class="avm-progress-title">' + t.title + "</p>" : "") + (t.text ? '<p class="avm-progress-text">' + t.text + "</p>" : "") + "</div>")
			},
			hideProgress: function() {
				document.querySelector(".avm-progress-wrap") && document.querySelector(".avm-progress-wrap").parentNode.removeChild(document.querySelector(".avm-progress-wrap"))
			},
			getCacheSize: function() {
				return ""
			},
			clearCache: function(t, e) {
				"function" == typeof t && (e = t);
				"function" == typeof e && e()
			},
			alert: function(t, e) {
				t.buttons = t.buttons || ["确定"];
				h(t, e)
			},
			actionSheet: function(t, e) {
				var n = "avm-actionsheet" + Date.now()
				  , i = '<div id="' + n + '" class="avm-actionsheet-mask"><div class="avm-actionsheet-wrap">' + t.buttons.reduce(function(t, e) {
					return t + '<div class="avm-actionsheet-item">' + e + "</div>"
				}, "") + '<div class="avm-actionsheet-cancel">取消</div>';
				document.body.insertAdjacentHTML("beforeend", i);
				var r = document.querySelector("#" + n);
				setTimeout(function() {
					r.querySelector(".avm-actionsheet-wrap").style.transform = "translate3d(0,0,0)";
					r.querySelector(".avm-actionsheet-wrap").style.webkitTransform = "translate3d(0,0,0)"
				}, 0);
				r.addEventListener("click", function(t) {
					t.target.className.includes("avm-actionsheet-mask") && s()
				}, !0);
				r.querySelector(".avm-actionsheet-cancel").addEventListener("click", s);
				for (var o = r.querySelectorAll(".avm-actionsheet-item"), a = 0; a < o.length; a++)
					!function(t) {
						o[t].addEventListener("click", function() {
							s();
							e({
								buttonIndex: t + 1
							})
						})
					}(a);
				function s() {
					r.querySelector(".avm-actionsheet-wrap").style.transform = "translate3d(0,100%,0)";
					r.querySelector(".avm-actionsheet-wrap").style.webkitTransform = "translate3d(0,100%,0)";
					setTimeout(function() {
						r.parentNode.removeChild(r)
					}, 300)
				}
			},
			getPicture: function(t, i) {
				var r = "video" == t.mediaValue ? "video" : "image"
				  , o = t.destinationType || "url"
				  , a = document.createElement("input");
				a.type = "file";
				a.accept = r + "/*";
				a.click();
				a.onchange = function() {
					var t = a.files[0];
					if (!t.type.indexOf(r))
						if ("image" != r || "base64" != o) {
							var e = (u.URL || u.webkitURL).createObjectURL(t);
							d[e] = t;
							i({
								data: e
							})
						} else {
							var n = new FileReader;
							n.readAsDataURL(t);
							n.onloadend = function() {
								i({
									base64Data: base64Data
								})
							}
						}
					else
						f.toast({
							msg: "选择的文件类型有误"
						})
				}
			},
			windows: function() {
				return (JSON.parse(JSON.stringify(u.history.state.$routerArr))).map(function(t) {
					return {
						name: t
					}
				})
			},
			winWidth: window.innerWidth,
			winHeight: window.innerHeight,
			appVersion: "WEB latest",
			safeArea: {
				top: 0,
				left: 0,
				bottom: 0,
				right: 0
			},
			_getRoute: v,
			_insertIframe: function(e, t, n) {
				if (e.navigationBar) {
					var i = "";
					s.forEach(function(t) {
						i += "$" + (e.navigationBar[t] || "")
					});
					i = i.substr(1);
					e.navigationBar = i
				}
				n = n ? " " + n : "";
				var r = location.origin + location.pathname
				  , o = {};
				Object.keys(e).forEach(function(t) {
					c[t] && (o[c[t]] = e[t])
				});
				var a = "?" + encodeURI(JSON.stringify(o));
				t.insertAdjacentHTML("beforeend", "<iframe name=" + e.name + " class='avm-iframe" + n + "' src='" + (r += a) + "' frameborder='0' >");
				return a
			},
			_loadTabbar: function() {
				var i = _avm_root_config.tabBar
				  , r = '<div class="avm-win-tabbar"><div class="avm-win-tabbar__page"></div><div class="avm-win-tabbar__footer">';
				i.frames.forEach(function(t, e) {
					var n = i.list[e];
					r += '<div class="avm-win-tabbar__item" data-bordercolor="#ccc" data-index=' + e + '><div class="avm-win-tabbar__img-wrap" style="position: relative;display:inline-block;"><img class="avm-win-tabbar__img" src="' + n.iconPath + '" /><img class="avm-win-tabbar__selectimg" src="' + n.selectedIconPath + '" /></div><div class="avm-win-tabbar__text">' + n.text + "</div></div>"
				});
				r += "</div></div>";
				document.body.insertAdjacentHTML("beforeend", r);
				this._setNavigationBar(_avm_root_config);
				for (var t = document.getElementsByClassName("avm-win-tabbar__item"), e = 0; e < t.length; e++) {
					var n = t[e];
					n.getElementsByClassName("avm-win-tabbar__text")[0].style.color = i.color;
					n.addEventListener("click", function(t) {
						f.setTabBarAttr({
							index: t.currentTarget.dataset.index
						})
					})
				}
				this.setTabBarAttr({
					index: i.index
				})
			},
			_setNavigationBar: function(t) {
				if (!t.hideNavigationBar) {
					var e = t.navigationBar || {};
					e.background = e.background || "#fff";
					e.shadow = e.shadow || "#ddd";
					e.color = e.color || "#000";
					e.fontSize = e.fontSize || "18";
					var n = '<div class="avm-navigation-bar" style="background-color:' + e.background + ";border-bottom:1px solid " + e.shadow + ";color:" + e.color + ";font-size:" + e.fontSize + 'px"><div class="avm-navigation-bar__left">' + (e.hideBackButton ? "" : '<div class="avm-navigation-bar__back"></div>') + '</div><p class="avm-navigation-bar__title">' + (t.title || _avm_root_config.appName) + "</p></div>";
					document.body.insertAdjacentHTML("afterBegin", n);
					var i = document.getElementsByClassName("avm-navigation-bar__back")[0];
					i && i.addEventListener("click", function() {
						f.closeWin()
					})
				}
			},
			_initWebCmpt: function(t, e, r, n) {
				r ? function(t) {
					for (var e in t)
						t[e.toLowerCase()] = t[e]
				}(r) : r = {};
				"refresh" == t ? function(t, e) {
					var n = r.onstatechange
					  , i = r.state;
					if (t.isRef && t.parentNode)
						if ("normal" == i) {
							t.style["min-height"] = "0px";
							l && l()
						} else
							"refreshing" != i || t.style["min-height"] && "0px" != t.style["min-height"] || (t.style["min-height"] = "50px");
					if (!t.isRef && t.parentNode) {
						t.isRef = !0;
						index.init({
							mainElement: t.parentNode,
							ptrElement: t,
							onRefresh: function(t) {
								l = t
							},
							onStateChange: function(t) {
								"releasing" == t ? n.bind(e)({
									detail: {
										state: "dragging"
									}
								}) : "refreshing" == t ? n.bind(e)({
									detail: {
										state: "refreshing"
									}
								}) : "pulling" != t && "pending" != t || n.bind(e)({
									detail: {
										state: "normal"
									}
								})
							}
						})
					}
				}(e, n) : "scroll-view" == t ? function t(i, r, o) {
					if (i.parentNode)
						if (!i.isRef) {
							r["refresher-enabled"] && d();
							i.isRef = !0;
							var e = !(!r.scrollX && !r["scroll-x"]);
							var n = !(!r.scrollY && !r["scroll-y"]);
							if (e) {
								i.style.display = "block";
								i.style["overflow-x"] = "scroll";
								i.style["white-space"] = "nowrap";
								"false" !== r.scrollXBar && !1 !== r.scrollXBar || i.classList.add("avm-hide-scrollbar")
							} else
								n && (i.style["overflow-y"] = "scroll");
							if (r.onscrolltolower || r.onscrolltoupper) {
								var a = e ? "Width" : "Height"
								  , s = e ? "Left" : "Top"
								  , c = void 0 === r["upper-threshold"] ? 50 : r["upper-threshold"]
								  , u = void 0 === r["lower-threshold"] ? 50 : r["lower-threshold"]
								  , f = !0
								  , h = !0;
								i.addEventListener("scroll", function() {
									if (!(i["scroll" + a] <= i["offset" + a] + u)) {
										if (r.onscrolltolower && i["scroll" + a] - i["offset" + a] - u <= i["scroll" + s]) {
											h && r.onscrolltolower.bind(o)();
											h = !1
										} else
											h = !0;
										if (r.onscrolltoupper && i["scroll" + s] <= c) {
											f && r.onscrolltoupper.bind(o)();
											f = !1
										} else
											f = !0
									}
								})
							}
						} else {
							if (!r["refresher-triggered"] && "refreshing" == i._pullState) {
								i._pull.reset();
								i._pull.destroy();
								d()
							}
							i._oldAvmAttr && !isNaN(r["scroll-top"]) && i._oldAvmAttr["scroll-top"] != r["scroll-top"] && (i.scrollTop = r["scroll-top"]);
							if (r["scroll-into-view"] && (!i._oldAvmAttr || !i._oldAvmAttr["scroll-into-view"] || i._oldAvmAttr["scroll-into-view"] != r["scroll-into-view"])) {
								var l = document.createElement("a");
								l.href = "#" + r["scroll-into-view"];
								l.click()
							}
							i._oldAvmAttr = JSON.parse(JSON.stringify(r))
						}
					else
						setTimeout(function() {
							t(i, r, o)
						}, 10);
					function d() {
						var e = r.onrefresherrefresh
						  , n = r.onrefresherrestore;
						i._pullState = "pending";
						i._pull = index.init({
							mainElement: i,
							instructionsRefreshing: "加载中",
							instructionsReleaseToRefresh: "松开刷新",
							instructionsPullToRefresh: "下拉刷新",
							onRefresh: function() {
								return !0
							},
							onStateChange: function(t) {
								"releasing" == (i._pullState = t) || ("refreshing" == t && e ? e.bind(o)() : "pending" == t && n && n.bind(o)())
							}
						})
					}
				}(e, r, n) : "swiper" == t ? function t(e, n, i, r) {
					if (!e.avmSwiper || isNaN(+n.current)) {
						if (!(e.avmSwiper && e.parentNode && e.children.length))
							if (e.parentNode && e.children.length) {
								var o = {};
								n.autoplay && (o.auto = n.interval || 5e3);
								"function" == typeof n.onchange && (o.callback = function(t) {
									n.onchange.bind(i)({
										detail: {
											current: t
										}
									})
								}
								);
								e.avmSwiper = Swipe(e, o)
							} else {
								if (!0 === r)
									return;
								setTimeout(function() {
									t(e, n, i, !0)
								}, 100)
							}
					} else
						e.avmSwiper.slide(n.current)
				}(e, r, n) : "picker" == t ? function(e, r, n) {
					var t;
					if (null != (t = r.range) && t.length)
						e._avmPicker ? "block" === e._avmPicker.pickerEl.style.display && e._avmPicker.refill(i()) : function() {
							var t = i();
							e._avmPicker = new Picker({
								data: t,
								selectedIndex: r.value,
								title: ""
							});
							e._avmPicker.on("picker.change", function(t, e) {
								r.oncolumnchange && r.oncolumnchange.bind(n)({
									detail: {
										column: t,
										value: e
									}
								})
							});
							e._avmPicker.on("picker.select", function(t) {
								t = t.filter(function(t) {
									return null != t && null != t
								});
								var e = r.onchange || r.onChange;
								e && e.bind(n)({
									detail: {
										value: t
									}
								})
							});
							e.addEventListener("click", function(t) {
								t.currentTarget._avmPicker.show();
								var e = t.currentTarget;
								setTimeout(function() {
									e._avmPicker.refill(i(e.avmDataset.range))
								}, 10)
							})
						}();
					function i(t) {
						t = t || r.range;
						for (var e = [], n = 0; n < t.length; n++) {
							var i = [];
							t[n].forEach(function(t, e) {
								i.push({
									text: r["range-key"] ? t[r["range-key"]] : t,
									value: e
								})
							});
							e.push(i)
						}
						return e
					}
				}(e, r, n) : "frame" == t ? function(t) {
					if (!t.src || t.src !== r.url) {
						t.src = r.url;
						t.setAttribute("scrolling", "no");
						t.setAttribute("frameborder", "no")
					}
				}(e) : "rich-text" == t ? function(t) {
					if (t.hasAttribute("nodes")) {
						t.innerHTML = t.getAttribute("nodes");
						t.removeAttribute("nodes")
					}
				}(e) : "image" == t ? function(t) {
					if (!r.src)
						t.getAttribute("src") || (t.src = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWP4////fwAJ+wP9CNHoHgAAAABJRU5ErkJggg==")
				}(e) : "switch" == t ? function(t, e) {
					if (!t.classList.contains("avm-switch")) {
						t.classList.add("avm-switch");
						t.checked = !!e.checked;
						t.disabled = !!e.disabled;
						e.color && (t.style["background-color"] = e.color)
					} else {
						!!e.checked != !!t.checked && (t.checked = !!e.checked);
						!!e.disabled != !!t.disabled && (t.disabled = !!e.disabled)
					}
				}(e, r) : "checkbox-group" == t ? function(t, n) {
					if (!t.classList.contains("avm-checkbox-group")) {
						t.classList.add("avm-checkbox-group");
						if (t._listeners && t._listeners.change) {
							var i = t._listeners.change;
							t._listeners.change = function(t) {
								var e = function t(e) {
									var n = [];
									for (var i = 0; i < e.children.length; i++) {
										var r = e.children[i];
										r.classList.contains("avm-checkbox") && r.checked && n.push(r);
										var o = t(r);
										o.length && (n = n.concat(o))
									}
									return n
								}(t.currentTarget).map(function(t) {
									return t.value
								});
								i.bind(n)({
									detail: {
										value: e
									}
								})
							}
						}
					}
				}(e, n) : "checkbox" == t ? function(e, n) {
					if (!e.classList.contains("avm-checkbox")) {
						e.classList.add("avm-checkbox");
						e.avmchecked = !!n.checked;
						e.disabled = !!n.disabled;
						e.addEventListener("change", function() {
							e.avmchecked = e.checked;
							t(e.checked)
						});
						n.color && (e.style.color = n.color);
						if (n.icon && n.selectedIcon) {
							e.classList.add("avm-checkbox__custom-icon");
							t(n.checked)
						} else
							e.classList.add("avm-checkbox__default-icon")
					} else {
						!!n.disabled != !!e.disabled && (e.disabled = !!n.disabled);
						e.checked = e.avmchecked
					}
					function t(t) {
						e.style.backgroundImage = "url(" + (t ? n.selectedIcon : n.icon) + ")"
					}
				}(e, r) : "radio-group" == t ? function(t, n) {
					if (!t.classList.contains("avm-radio-group")) {
						t.classList.add("avm-radio-group");
						t.name = ("" + Math.random() * Date.now()).substr(0, 10);
						if (t._listeners && t._listeners.change) {
							var i = t._listeners.change;
							t._listeners.change = function(t) {
								var e = function t(e) {
									var n = [];
									for (var i = 0; i < e.children.length; i++) {
										var r = e.children[i];
										r.classList.contains("avm-radio") && r.checked && n.push(r);
										var o = t(r);
										o.length && (n = n.concat(o))
									}
									return n
								}(t.currentTarget).map(function(t) {
									return t.value
								});
								i.bind(n)({
									detail: {
										value: e
									}
								})
							}
						}
					}
				}(e, n) : "radio" == t ? function t(n, e) {
					if (n.parentNode)
						if (!n.classList.contains("avm-radio")) {
							n.classList.add("avm-radio");
							n.avmchecked = !!e.checked;
							n.disabled = !!e.disabled;
							e.checked = !1;
							if (e.color) {
								n.style["background-color"] = e.color;
								n.style["border-color"] = e.color
							}
							if (!e.name) {
								var i = function t(e) {
									return e.parentNode.classList.contains("avm-radio-group") ? e.parentNode : "BODY" == e.tagName ? void 0 : t(e.parentNode)
								}(n);
								i && i.name && n.setAttribute("name", i.name)
							}
							n.addEventListener("change", function() {
								for (var t = document.querySelectorAll('[name="' + n.name + '"]'), e = 0; e < t.length; e++)
									t[e].avmchecked = t[e].checked
							})
						} else {
							!!e.disabled != !!n.disabled && (n.disabled = !!e.disabled);
							n.checked = n.avmchecked
						}
					else
						setTimeout(function() {
							t(n, e)
						}, 10)
				}(e, r) : "input" == t ? function t(e, n, i) {
					if (e.parentNode) {
						if (!e.isRef) {
							e.isRef = !0;
							if (e._listeners && e._listeners.foucs)
								e._listeners.foucs = function() {}
								;
							n.onconfirm && e.addEventListener("keydown", function(t) {
								13 == t.keyCode && n.onconfirm.bind(i)({
									detail: {
										value: t.currentTarget.value
									}
								})
							})
						}
					} else
						setTimeout(function() {
							t(e, n, i)
						}, 10)
				}(e, r, n) : "slider" == t ? function t(e, f, h) {
					if (e.parentNode) {
						if (!e.classList.contains("avm-slider")) {
							e.classList.add("avm-slider");
							var n = "avm-slider-" + ("" + Math.random() * Date.now()).substr(0, 10);
							e.classList.add(n);
							var r = parseFloat(f.min || 0);
							var o = parseFloat(f.max || 100);
							var a = parseFloat(f.step || 1);
							var i = parseFloat(f.value || 0);
							var s = f.backgroundColor || f["background-color"] || "#ebebeb";
							var c = f.activeColor || f["active-color"] || "#006bff";
							var u = f["block-size"] || f.blockSize || 28;
							document.styleSheets[0].insertRule(".avm-slider." + n + ":before{background-color:" + (f["block-color"] || f.blockColor || "#ffffff") + ";}", 0);
							document.styleSheets[0].insertRule(".avm-slider." + n + ":before{height:" + u + "px;width:" + u + "px;}", 0);
							var l = e;
							var d = !1;
							l.style.paddingLeft = i + "px";
							l.style.backgroundImage = "linear-gradient(to right," + c + " " + i + "px, " + s + " 0%)";
							l.sliderVal = i;
							l.addEventListener("click", function(t) {
								var e = v(t.offsetX);
								e && f.onchange && f.onchange.bind(h)({
									detail: {
										value: e
									}
								})
							});
							l.addEventListener("touchstart", function(t) {
								if (1 < t.touches.length)
									d = !0;
								else {
									var e = getComputedStyle(l)
									  , n = getComputedStyle(l, "before")
									  , i = t.touches[0]
									  , r = l.offsetLeft + parseFloat(e.paddingLeft)
									  , o = {
										startX: r - parseFloat(n.width) / 2,
										endX: r + parseFloat(n.width) / 2
									}
									  , a = i.clientX
									  , s = l.sliderVal;
									if (o.startX < a && a < o.endX) {
										function c(t) {
											if (!d) {
												t.preventDefault();
												var e = v(t.touches[0].clientX - l.offsetLeft);
												e && f.onchangeing && f.onchangeing.bind(h)({
													detail: {
														value: e
													}
												})
											}
										}
										function u() {
											l.sliderVal != s && f.onchange && f.onchange.bind(h)({
												detail: {
													value: l.sliderVal
												}
											});
											l.removeEventListener("touchmove", c);
											l.removeEventListener("touchend", u);
											l.removeEventListener("touchcancel", u)
										}
										l.addEventListener("touchmove", c);
										l.addEventListener("touchend", u);
										l.addEventListener("touchcancel", u)
									}
								}
							})
						}
					} else
						setTimeout(function() {
							t(e, f, h)
						}, 10);
					function v(t) {
						t < 0 ? t = 0 : l.offsetWidth < t && (t = l.offsetWidth);
						var e = t / l.offsetWidth
						  , n = o - r
						  , i = n * e;
						e = (i = a / 2 < i % a ? Math.floor(i / a) * a + a : Math.floor(i / a) * a) / n;
						if (l.sliderVal != (i += r)) {
							l.sliderVal = i;
							l.style.paddingLeft = l.offsetWidth * e + "px";
							l.style.backgroundImage = "linear-gradient(to right," + c + " " + l.offsetWidth * e + "px, " + s + " 0%)";
							return i
						}
					}
				}(e, r, n) : "textarea" == t && function t(e, n) {
					if (e.parentNode) {
						if (!e.classList.contains("avm-textarea")) {
							e.classList.add("avm-textarea");
							n.value && (e.value = n.value);
							if (n["auto-height"] || n.autoHeight) {
								function i() {
									e.style.height = "auto";
									e.style.height = e.scrollHeight + 2 + "px"
								}
								e.addEventListener("input", i);
								i()
							}
						}
					} else
						setTimeout(function() {
							t(e, n)
						}, 10)
				}(e, r)
			}
		};
		function e(t) {
			var e = Date.now()
			  , n = JSON.parse(JSON.stringify(u.history.state.$routerArr));
			n.push(t.name);
			var i = {
				ct: e,
				$routerArr: n
			};
			u._avmState = i;
			var r = f._insertIframe(t, u.document.body);
			u.history.pushState(i, null, r)
		}
		var i, r, o = {};
		function h(t, e) {
			t.buttons = t.buttons || ["取消", "确定"];
			var n = "avm-confirm" + Date.now()
			  , i = '<div id="' + n + '" class="avm-confirm-mask"><div class="avm-confirm">' + (t.title ? '<h5 class="avm-confirm-title">' + t.title + "</h5>" : "") + (t.msg ? '<p class="avm-confirm-msg">' + t.msg + "</p>" : "") + '<div class="avm-confirm-btn-wrap">' + t.buttons.reduce(function(t, e) {
				return t + '<div class="avm-confirm-btn">' + e + "</div>"
			}, "") + "</div></div></div>";
			document.body.insertAdjacentHTML("beforeend", i);
			var r = document.querySelector("#" + n)
			  , o = r.querySelector(".avm-confirm");
			setTimeout(function() {
				o.style.transform = "scale3d(1, 1, 1)";
				o.style.webkitTransform = "scale3d(1, 1, 1)"
			}, 0);
			for (var a = r.querySelectorAll(".avm-confirm-btn"), s = 0; s < a.length; s++)
				!function(t) {
					a[t].addEventListener("click", function() {
						o.style.transform = "scale3d(0, 0, 0)";
						o.style.webkitTransform = "scale3d(0, 0, 0)";
						setTimeout(function() {
							r.parentNode.removeChild(r);
							e && e({
								buttonIndex: t + 1
							})
						}, 200)
					})
				}(s)
		}
		var l, d = {};
		function v() {
			if (!location.search)
				return {
					name: "root"
				};
			var t = decodeURI(location.href.substr(1 + location.href.indexOf("?")));
			if ("{" != (t)[0])
				return {
					name: "root"
				};
			t = t.substring(0, t.lastIndexOf("}") + 1);
			var e = JSON.parse(t)
			  , n = {};
			Object.keys(e).forEach(function(t) {
				n[c[t]] = e[t]
			});
			if (n.navigationBar) {
				var i = n.navigationBar.split("$")
				  , r = {};
				i.forEach(function(t, e) {
					r[s[e]] = t
				});
				n.navigationBar = r
			}
			return n
		}
		t.api && Object.defineProperty(t.api, "pageParam", {
			writable: !0
		});
		t.apiweb = Object.assign(t.api || {}, f);
		window._T = t;
	}(global);
	return vm$1
}();
