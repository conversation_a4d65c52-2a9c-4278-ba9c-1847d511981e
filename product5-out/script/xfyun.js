const APPID = "5b581b4e";
const API_SECRET = "eee998bdcdd78c375f7488e5a4eb294e";
const API_KEY = "5edcf0f7b2364482a5b35528ae6a8b67";

function getWebSocketUrl(_type) {
		// 请求地址根据语种不同变化
		var url = `wss://${_type}-api.xfyun.cn/v2/${_type}`;
		var host = location.host;
		var date = new Date().toGMTString();
		var algorithm = "hmac-sha256";
		var headers = "host date request-line";
		var signatureOrigin = `host: ${host}\ndate: ${date}\nGET /v2/${_type} HTTP/1.1`;
		var signatureSha = CryptoJS.HmacSHA256(signatureOrigin, API_SECRET);
		var signature = CryptoJS.enc.Base64.stringify(signatureSha);
		var authorizationOrigin = `api_key="${API_KEY}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`;
		var authorization = btoa(authorizationOrigin);
		url = `${url}?authorization=${authorization}&date=${date}&host=${host}`;
		return url;
	}

//start 语音合成>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
var ttsWS,audioPlayer;
function startRead(_str,_param,_callback) {
	var ilength = 500,nowLength = 0,allLength = parseInt(_str.length / ilength) + 1;
	startReadIng(_str.substr(nowLength, ilength),_param,_callback, nowLength, allLength, _str, ilength);
}
function startReadIng(_nStr,_param,_callback, _nowLength, _allLength, _readStr, _length){
	audioPlayer.onPlay = () => {
		console.log("onPlay");
	};
	audioPlayer.onStop = (audioDatas) => {
		if (_nowLength < _allLength - 1) {
			_nowLength++;
			startReadIng(_readStr.substr(_nowLength * _length, _length),_param,_callback, _nowLength, _allLength, _readStr, _length);
		}else{
			_callback && _callback();
		}
	};
	const url = getWebSocketUrl("tts");
	if ("WebSocket" in window) {
		ttsWS = new WebSocket(url);
	} else if ("MozWebSocket" in window) {
		ttsWS = new MozWebSocket(url);
	} else {
		alert("浏览器不支持WebSocket");
		return;
	}
	ttsWS.onopen = (e) => {
		audioPlayer.start({
			autoPlay: true,
			sampleRate: 16000,
			resumePlayDuration: 1000
		});
		var params = {
			common: {
				app_id: APPID,
			},
			business: {
				aue: "raw",
				auf: "audio/L16;rate=16000",
				vcn: _param.voice,
				speed: Number(_param.speed),
				volume: Number(_param.volume),
				pitch: 50,
				bgs: 1,
				tte:"UTF8",
			},
			data: {
				status: 2,
				text: Base64.encode(_nStr),
			},
		};
		ttsWS.send(JSON.stringify(params));
	};
	ttsWS.onmessage = (e) => {
		let jsonData = JSON.parse(e.data);
		// 合成失败
		if (jsonData.code !== 0) {
			console.error(jsonData);
			return;
		}
		audioPlayer.postMessage({
			type: "base64",
			data: jsonData.data.audio,
			isLastData: jsonData.data.status === 2,
		});
		if (jsonData.code === 0 && jsonData.data.status === 2) {
			ttsWS.close();
		}
	};
	ttsWS.onerror = (e) => {
		console.error(e);
	};
	ttsWS.onclose = (e) => {
		// console.log(e);
	};
}
function stopRead(){
	ttsWS?.close();
	audioPlayer?.reset();
}
//end >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

//start 语音听写>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
var iatWS,recorder,resultText,resultTextTemp;
function startRecognize(_callback){
	resultText = "";
    resultTextTemp = "";
	recorder.onFrameRecorded = ({ isLastFrame, frameBuffer }) => {
		if (iatWS.readyState === iatWS.OPEN) {
			iatWS.send(
				JSON.stringify({
					data: {
					status: isLastFrame ? 2 : 1,
					format: "audio/L16;rate=16000",
					encoding: "raw",
					audio: toBase64(frameBuffer),
					},
				})
			);
		}
	};
	recorder.onStop = () => {
		//_callback("",true);
	};
	const url = getWebSocketUrl("iat");
	if ("WebSocket" in window) {
		iatWS = new WebSocket(url);
	} else if ("MozWebSocket" in window) {
		iatWS = new MozWebSocket(url);
	} else {
		alert("浏览器不支持WebSocket");
		return;
	}
	iatWS.onopen = (e) => {
		// 开始录音
		recorder.start({
			sampleRate: 16000,
			frameSize: 1280,
		});
		var params = {
			common: {
				app_id: APPID,
			},
			business: {
				language: "zh_cn",
				domain: "iat",
				accent: "mandarin",
				vad_eos: 5000,
				dwa: "wpgs",
			},
			data: {
				status: 0,
				format: "audio/L16;rate=16000",
				encoding: "raw",
			},
		};
		iatWS.send(JSON.stringify(params));
	};
	iatWS.onmessage = (e) => {
		// 识别结束
		let jsonData = JSON.parse(e.data);
		if (jsonData.data && jsonData.data.result) {
			let data = jsonData.data.result;
			let str = "";
			let ws = data.ws;
			for (let i = 0; i < ws.length; i++) {
				str = str + ws[i].cw[0].w;
			}
			// 开启wpgs会有此字段(前提：在控制台开通动态修正功能)
			// 取值为 "apd"时表示该片结果是追加到前面的最终结果；取值为"rpl" 时表示替换前面的部分结果，替换范围为rg字段
			if (data.pgs) {
				if (data.pgs === "apd") {
					// 将resultTextTemp同步给resultText
					resultText = resultTextTemp;
				}
				// 将结果存储在resultTextTemp中
				resultTextTemp = resultText + str;
			} else {
				resultText = resultText + str;
			}
			_callback(resultTextTemp || resultText || "");
		}
	};
	iatWS.onerror = (e) => {
		console.error(e);
		recorder.stop();
	};
	iatWS.onclose = (e) => {
		recorder.stop();
	};
}
function stopRecognize(){
	recorder.stop();
}
//end >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>

const srcPath = "../../script";
function loadScripts(scripts, callback) {
	var loadedCount = 0;
	scripts.forEach(function(src) {
		var script = document.createElement('script');
		script.src = srcPath+"/"+src;
		script.async = false; // 确保按顺序加载，如果需要按顺序执行
		// 监听 script 的 onload 事件，脚本加载完成时触发
		script.onload = function() {
			loadedCount++;
			if (loadedCount === scripts.length) {
				callback(); // 当所有脚本加载完成后，执行回调
			}
		};
		// 监听加载错误事件
		script.onerror = function() {
			console.error('Error loading script: ' + src);
		};
		document.head.appendChild(script);
	});
}
loadScripts([
	'base64.js',
	'crypto-js.js',
	'index.umd.tts.js'
],function(){
	audioPlayer = new AudioPlayer(srcPath);
});

function toBase64(buffer) {
	var binary = "";
	var bytes = new Uint8Array(buffer);
	var len = bytes.byteLength;
	for (var i = 0; i < len; i++) {
		binary += String.fromCharCode(bytes[i]);
	}
	return window.btoa(binary);
}

loadScripts([
	'index.umd.iat.js'
],function(){
	recorder = new RecorderManager(srcPath);
});

loadScripts([
	'recorder.mp3.min.js'
],function(){
});


loadScripts([
	'sm4.js'
],function(){
});


